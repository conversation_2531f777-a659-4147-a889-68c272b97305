// resources/js/config.js
var TiptapEditor = {
  extensions: [],
  registerExtension: (key, script) => {
    (void 0).extensions[key] = [script];
  }
};
var config_default = TiptapEditor;
export {
  config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsiLi4vanMvY29uZmlnLmpzIl0sCiAgInNvdXJjZXNDb250ZW50IjogWyJjb25zdCBUaXB0YXBFZGl0b3IgPSB7XG4gICAgZXh0ZW5zaW9uczogW10sXG4gICAgcmVnaXN0ZXJFeHRlbnNpb246IChrZXksIHNjcmlwdCkgPT4ge1xuICAgICAgICB0aGlzLmV4dGVuc2lvbnNba2V5XSA9IFtzY3JpcHRdO1xuICAgIH1cbn1cblxuZXhwb3J0IGRlZmF1bHQgVGlwdGFwRWRpdG9yOyJdLAogICJtYXBwaW5ncyI6ICI7QUFBQSxJQUFNLGVBQWU7QUFBQSxFQUNqQixZQUFZLENBQUM7QUFBQSxFQUNiLG1CQUFtQixDQUFDLEtBQUssV0FBVztBQUNoQyxhQUFLLFdBQVcsR0FBRyxJQUFJLENBQUMsTUFBTTtBQUFBLEVBQ2xDO0FBQ0o7QUFFQSxJQUFPLGlCQUFROyIsCiAgIm5hbWVzIjogW10KfQo=
