<?php

namespace Database\Faker\Providers;

use Faker\Provider\Base;

class HtmlProvider extends Base
{
    /**
     * HTML tags that can be used in the generated content
     */
    protected static array $tags = [
        'p', 'h1', 'h2', 'h3', 'strong', 'em', 'ul', 'ol', 'li', 'blockquote'
    ];

    /**
     * Generate a single HTML paragraph with optional formatting
     */
    public function htmlParagraph(): string
    {
        $text = $this->generator->paragraph();
        $shouldFormat = $this->generator->boolean(30); // 30% chance to add formatting

        if ($shouldFormat) {
            // Randomly wrap parts of text in formatting tags
            $words = explode(' ', $text);
            $formattedWords = array_map(function ($word) {
                if ($this->generator->boolean(20)) {
                    $tag = $this->generator->randomElement(['strong', 'em']);
                    return "<{$tag}>{$word}</{$tag}>";
                }
                return $word;
            }, $words);
            $text = implode(' ', $formattedWords);
        }

        return "<p>{$text}</p>";
    }

    /**
     * Generate a complete HTML rich text block
     */
    public function htmlRichText(int $maxParagraphs = 5): string
    {
        $paragraphs = [];
        $numParagraphs = $this->generator->numberBetween(1, $maxParagraphs);

        for ($i = 0; $i < $numParagraphs; $i++) {
            // Use weighted random selection
            $types = [
                'paragraph' => 60,
                'heading' => 10,
                'list' => 20,
                'blockquote' => 10,
            ];

            $total = array_sum($types);
            $rand = $this->generator->numberBetween(1, $total);
            $type = 'paragraph'; // default

            foreach ($types as $t => $weight) {
                if ($rand <= $weight) {
                    $type = $t;
                    break;
                }
                $rand -= $weight;
            }

            switch ($type) {
                case 'paragraph':
                    $paragraphs[] = $this->htmlParagraph();
                    break;

                case 'heading':
                    $level = $this->generator->numberBetween(1, 3);
                    $text = $this->generator->sentence();
                    $paragraphs[] = "<h{$level}>{$text}</h{$level}>";
                    break;

                case 'list':
                    $listType = $this->generator->randomElement(['ul', 'ol']);
                    $items = [];
                    $numItems = $this->generator->numberBetween(2, 5);

                    for ($j = 0; $j < $numItems; $j++) {
                        $items[] = "<li>" . $this->generator->sentence() . "</li>";
                    }

                    $paragraphs[] = "<{$listType}>" . implode('', $items) . "</{$listType}>";
                    break;

                case 'blockquote':
                    $text = $this->generator->paragraph();
                    $paragraphs[] = "<blockquote>{$text}</blockquote>";
                    break;
            }
        }

        return implode("\n", $paragraphs);
    }

    /**
     * Generate a short HTML snippet (1-2 paragraphs)
     */
    public function htmlSnippet(): string
    {
        return $this->htmlRichText(2);
    }
}
