<?php

namespace Database\Seeders;

use App\Models\Topic;
use Illuminate\Database\Seeder;

class TopicsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $topics = [
            'Balance',
            'Compassion',
            'Courage',
            'Discipline',
            'Focus',
            'Integrity',
            'Leadership',
            'Purpose',
            'Responsibility',
            'Team',
        ];


        foreach ($topics as $name) {
            Topic::query()->updateOrCreate(['name' => $name], ['name' => $name]);
        }
    }
}
