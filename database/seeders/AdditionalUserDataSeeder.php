<?php

namespace Database\Seeders;

use App\Enums\NominationType;
use App\Enums\ProfileType;
use App\Enums\NominationStatus;
use App\Models\User;
use App\Models\Interest;
use App\Models\Endorsement;
use App\Models\Nomination;
use App\Models\CustomSport;
use App\Models\Achievement;
use App\Models\Sport;
use Illuminate\Database\Seeder;
use Faker\Factory as Faker;
use Illuminate\Support\Facades\DB;

class AdditionalUserDataSeeder extends Seeder
{
    private function generateAiScore(string $status, ?string $relationship = null): int
    {
        // Base ranges for different nomination types
        $ranges = [
            'approved' => [65, 85],   // Consistent higher range for approved
            'pending' => [20, 55],    // Lower range for pending
            'parent' => [40, 75],     // Middle range for parent nominations
        ];

        // Add some variety based on relationship
        if ($status === 'pending' && $relationship === 'Parent') {
            return fake()->numberBetween(...$ranges['parent']);
        }

        return fake()->numberBetween(...$ranges[$status === 'approved' ? 'approved' : 'pending']);
    }

    private function generateNominationStatus(?string $relationship = null): string
    {
        // Distribution weights for different statuses
        $weights = [
            NominationStatus::PENDING_AD_VERIFICATION->value => 40,  // 40% chance
            NominationStatus::AD_VERIFIED->value => 30,             // 30% chance
            NominationStatus::NOMINEE_NOTIFIED->value => 20,        // 20% chance
            NominationStatus::NOMINEE_ACKNOWLEDGED->value => 10,    // 10% chance
        ];

        // Adjust weights based on relationship
        if ($relationship === 'Parent') {
            // Parents tend to have more pending nominations
            $weights[NominationStatus::PENDING_AD_VERIFICATION->value] += 20;
            $weights[NominationStatus::AD_VERIFIED->value] -= 10;
            $weights[NominationStatus::NOMINEE_NOTIFIED->value] -= 5;
            $weights[NominationStatus::NOMINEE_ACKNOWLEDGED->value] -= 5;
        } elseif ($relationship === 'Coach') {
            // Coaches tend to have more verified nominations
            $weights[NominationStatus::PENDING_AD_VERIFICATION->value] -= 10;
            $weights[NominationStatus::AD_VERIFIED->value] += 20;
            $weights[NominationStatus::NOMINEE_NOTIFIED->value] -= 5;
            $weights[NominationStatus::NOMINEE_ACKNOWLEDGED->value] -= 5;
        }

        // Ensure all weights are at least 0
        $weights = array_map(fn ($weight) => max(0, $weight), $weights);

        // Create an array where each status appears according to its weight
        $weighted = [];
        foreach ($weights as $status => $weight) {
            for ($i = 0; $i < $weight; $i++) {
                $weighted[] = $status;
            }
        }

        // If no valid weights, return pending
        if (empty($weighted)) {
            return NominationStatus::PENDING_AD_VERIFICATION->value;
        }

        return fake()->randomElement($weighted);
    }

    public function run(): void
    {
        $faker = Faker::create();

        // Get all athletes and coaches (only these types can be nominated)
        $nominatableUsers = User::query()
            ->whereIn('profile_type', [
                ProfileType::POSITIVE_ATHLETE->value,
                ProfileType::POSITIVE_COACH->value,
            ])
            ->get();

        if ($nominatableUsers->isEmpty()) {
            $this->command->warn("No nominatable users found. Make sure InitialUsersSeeder was run.");
            return;
        }

        // Get the first athlete (by creation date) to ensure they always get endorsements
        $firstAthlete = User::query()
            ->where('profile_type', ProfileType::POSITIVE_ATHLETE->value)
            ->orderBy('created_at')
            ->first();

        if (!$firstAthlete) {
            $this->command->warn("No athletes found. Make sure InitialUsersSeeder was run.");
        } else {
            $this->command->info("First athlete identified: {$firstAthlete->first_name} {$firstAthlete->last_name} (ID: {$firstAthlete->id})");
        }

        // Fetch all interests and endorsements
        $interests = Interest::query()->get();
        $endorsements = Endorsement::query()->get();
        $sports = Sport::query()->get();

        if ($interests->isEmpty()) {
            $this->command->warn("No interests found. Run InterestsSeeder first.");
        }

        if ($endorsements->isEmpty()) {
            $this->command->warn("No endorsements found. Run EndorsementsSeeder first.");
        }

        // Get all users to use as potential endorsers
        $allUsers = User::query()->get();
        if ($allUsers->count() < 5) {
            $this->command->warn("Not enough users found for endorsements. Make sure InitialUsersSeeder was run.");
        }

        foreach ($nominatableUsers as $user) {
            // Skip users with invalid profile types (extra safety check)
            if (!in_array($user->profile_type->value, [ProfileType::POSITIVE_ATHLETE->value, ProfileType::POSITIVE_COACH->value])) {
                $this->command->warn("Skipping user {$user->email} with invalid profile type: {$user->profile_type->value}");
                continue;
            }

            // Verify user has a school
            if (!$user->school) {
                $this->command->warn("User {$user->email} has no school assigned.");
                continue;
            }

            // Add custom sports to some users
            if ($faker->boolean(40)) { // 40% chance
                $numCustomSports = $faker->numberBetween(1, 3);
                for ($cs = 1; $cs <= $numCustomSports; $cs++) {
                    CustomSport::query()->create([
                        'user_id' => $user->id,
                        'name' => ucfirst($faker->word),
                        'order' => $cs,
                    ]);
                }
            }

            // Assign interests (2-4 interests)
            if (!$interests->isEmpty()) {
                $userInterests = $interests->random($faker->numberBetween(2, 4));
                $user->interests()->syncWithoutDetaching($userInterests->pluck('id'));
            }

            // Assign sports (1-2 sports)
            if (!$sports->isEmpty()) {
                $userSports = $sports->random($faker->numberBetween(1, 2));
                $user->sports()->syncWithoutDetaching($userSports->pluck('id'));
            }

            // Get the user's sports after assignment
            $userSports = $user->sports;
            if ($userSports->isEmpty()) {
                $this->command->warn("User {$user->email} has no sports assigned.");
                continue;
            }

            // Get coach and parent relationships
            $team = $user->teams()->first();
            $coach = $team?->users()->wherePivot('type', 'coach')->first();
            $parent = $user->parent;

            // Determine nomination type based on user's profile type
            $nominationType = match($user->profile_type) {
                ProfileType::POSITIVE_ATHLETE => NominationType::ATHLETE->value,
                ProfileType::POSITIVE_COACH => NominationType::COACH->value,
                default => throw new \Exception("Invalid profile type for nomination"),
            };

            // Ensure at least one nomination exists for each user
            $existingNomination = Nomination::query()
                ->where('email', $user->email)
                ->first();

            if (!$existingNomination) {
                // If no nomination exists, create one from either coach, parent, or a generic nominator
                $nominator = $coach ?? $parent ?? null;

                // Get a random sport from user's assigned sports
                $nominationSport = $userSports->random();

                $relationship = $nominator ? ($nominator->id === $parent?->id ? 'Parent' : 'Coach') : 'Teacher';
                $status = $this->generateNominationStatus($relationship);

                // Generate grade and graduation year for athletes
                $grade = null;
                if ($user->profile_type === ProfileType::POSITIVE_ATHLETE) {
                    $currentGrade = $faker->randomElement(['9', '10', '11', '12']);
                    $grade = (int) $currentGrade; // Store as integer instead of "Grade X" format
                }

                // Generate realistic location data
                $counties = \App\Models\County::with('market.region')->inRandomOrder()->take(10)->get();
                $county = $counties->isNotEmpty() ? $counties->random() : null;

                // Generate additional sports (30% chance for multi-sport athletes)
                $sport2 = $faker->boolean(30) && $userSports->count() > 1 ? $userSports->where('name', '!=', $nominationSport->name)->random()->name : null;
                $sport3 = $faker->boolean(15) && $userSports->count() > 2 ? $userSports->whereNotIn('name', [$nominationSport->name, $sport2])->random()->name : null;

                // Generate parent/guardian info for minors (if athlete and grade indicates high school)
                $parentInfo = [];
                if ($user->profile_type === ProfileType::POSITIVE_ATHLETE && $grade) {
                    $parentInfo = [
                        'parent_guardian_first_name' => $nominator && $nominator->id === $parent?->id ? $nominator->first_name : $faker->firstName,
                        'parent_guardian_last_name' => $nominator && $nominator->id === $parent?->id ? $nominator->last_name : $faker->lastName,
                        'parent_guardian_email' => $nominator && $nominator->id === $parent?->id ? $nominator->email : $faker->email,
                        'parent_guardian_phone' => $faker->phoneNumber,
                    ];
                }

                Nomination::query()->create([
                    // Original fields
                    'nominator_email' => $nominator ? $nominator->email : $faker->email,
                    'nominator_first_name' => $nominator ? $nominator->first_name : $faker->firstName,
                    'nominator_last_name' => $nominator ? $nominator->last_name : $faker->lastName,
                    'email' => $user->email,
                    'first_name' => $user->first_name,
                    'last_name' => $user->last_name,
                    'school_name' => $user->school->name,
                    'school_id' => $user->school->id,
                    'sport' => $nominationSport->name,
                    'relationship' => $relationship,
                    'note' => $faker->paragraph,
                    'status' => $status,
                    'type' => $nominationType,
                    'ai_score' => $this->generateAiScore($status, $relationship),

                    // Location Information
                    'state_code' => $county?->state_code ?? $faker->stateAbbr,
                    'county' => $county?->name ?? $faker->city . ' County',

                    // Enhanced Contact Information
                    'nominee_phone' => $faker->phoneNumber,
                    'nominator_phone' => $faker->phoneNumber,

                    // Multiple Sports Support
                    'sport_2' => $sport2,
                    'sport_3' => $sport3,
                    'other_sport' => $faker->boolean(10) ? $faker->randomElement(['Adapted Basketball', 'Unified Soccer', 'Special Olympics Track']) : null,

                    // Demographic & Academic Information
                    'gender' => $faker->randomElement(['Male', 'Female', 'Non-binary', 'Prefer not to say']),
                    'grade' => $grade,

                    // Parent/Guardian Information
                    ...$parentInfo,

                    // Social Media & Digital Presence
                    'instagram_handle' => $faker->boolean(60) ? '@' . $faker->userName : null,
                    'twitter_handle' => $faker->boolean(40) ? '@' . $faker->userName : null,

                    // Marketing & Attribution Data
                    'how_did_you_hear' => $faker->randomElement([
                        'School counselor',
                        'Coach recommendation',
                        'Social media',
                        'Friend/family',
                        'School newsletter',
                        'Athletic department',
                        'Website search'
                    ]),
                    'referral_source_name' => $faker->boolean(50) ? $faker->company : null,

                    // Processing Workflow Enhancement
                    'processing_status' => $faker->randomElement(['received', 'validated', 'invited', 'onboarded']),
                    'processed_at' => $faker->boolean(70) ? $faker->dateTimeBetween('-3 months', 'now') : null,

                    // JotForm Integration Metadata
                    'jotform_submission_id' => $faker->uuid,
                    'jotform_form_id' => $faker->randomElement(['************', '************', '************']),
                    'location_resolution_notes' => $faker->boolean(20) ? [
                        'status' => 'resolved',
                        'method' => 'automated_geocoding',
                        'confidence' => $faker->randomFloat(2, 0.8, 1.0)
                    ] : null,
                ]);
            }

            // Additional nominations only for a subset of users (30% chance)
            if ($faker->boolean(30)) {
                // Additional nomination from coach
                if ($coach) {
                    $relationship = 'Coach';
                    $status = $this->generateNominationStatus($relationship);

                    // Generate grade for athletes
                    $grade = null;
                    if ($user->profile_type === ProfileType::POSITIVE_ATHLETE) {
                        $currentGrade = $faker->randomElement(['9', '10', '11', '12']);
                        $grade = (int) $currentGrade; // Store as integer instead of "Grade X" format
                    }

                    // Generate realistic location data
                    $counties = \App\Models\County::with('market.region')->inRandomOrder()->take(10)->get();
                    $county = $counties->isNotEmpty() ? $counties->random() : null;

                    // Generate additional sports
                    $nominationSport = $userSports->random();
                    $sport2 = $faker->boolean(30) && $userSports->count() > 1 ? $userSports->where('name', '!=', $nominationSport->name)->random()->name : null;

                    // Generate parent/guardian info for minors
                    $parentInfo = [];
                    if ($user->profile_type === ProfileType::POSITIVE_ATHLETE && $grade && $parent) {
                        $parentInfo = [
                            'parent_guardian_first_name' => $parent->first_name,
                            'parent_guardian_last_name' => $parent->last_name,
                            'parent_guardian_email' => $parent->email,
                            'parent_guardian_phone' => $faker->phoneNumber,
                        ];
                    }

                    Nomination::query()->create([
                        // Original fields
                        'nominator_email' => $coach->email,
                        'nominator_first_name' => $coach->first_name,
                        'nominator_last_name' => $coach->last_name,
                        'email' => $user->email,
                        'first_name' => $user->first_name,
                        'last_name' => $user->last_name,
                        'school_name' => $user->school->name,
                        'school_id' => $user->school->id,
                        'sport' => $nominationSport->name,
                        'relationship' => $relationship,
                        'note' => $faker->paragraph,
                        'status' => $status,
                        'type' => $nominationType,
                        'ai_score' => $this->generateAiScore($status, $relationship),

                        // Location Information
                        'state_code' => $county?->state_code ?? $faker->stateAbbr,
                        'county' => $county?->name ?? $faker->city . ' County',

                        // Enhanced Contact Information
                        'nominee_phone' => $faker->phoneNumber,
                        'nominator_phone' => $faker->phoneNumber,

                        // Multiple Sports Support
                        'sport_2' => $sport2,
                        'sport_3' => null,
                        'other_sport' => $faker->boolean(10) ? $faker->randomElement(['Adapted Basketball', 'Unified Soccer']) : null,

                        // Demographic & Academic Information
                        'gender' => $faker->randomElement(['Male', 'Female', 'Non-binary', 'Prefer not to say']),
                        'grade' => $grade,

                        // Parent/Guardian Information
                        ...$parentInfo,

                        // Social Media & Digital Presence
                        'instagram_handle' => $faker->boolean(60) ? '@' . $faker->userName : null,
                        'twitter_handle' => $faker->boolean(40) ? '@' . $faker->userName : null,

                        // Marketing & Attribution Data
                        'how_did_you_hear' => $faker->randomElement([
                            'Coach recommendation',
                            'Athletic department',
                            'School newsletter'
                        ]),
                        'referral_source_name' => $faker->boolean(50) ? $faker->company : null,

                        // Processing Workflow Enhancement
                        'processing_status' => $faker->randomElement(['received', 'validated', 'invited', 'onboarded']),
                        'processed_at' => $faker->boolean(70) ? $faker->dateTimeBetween('-3 months', 'now') : null,

                        // JotForm Integration Metadata
                        'jotform_submission_id' => $faker->uuid,
                        'jotform_form_id' => $faker->randomElement(['************', '************', '************']),
                        'location_resolution_notes' => $faker->boolean(20) ? [
                            'status' => 'resolved',
                            'method' => 'automated_geocoding',
                            'confidence' => $faker->randomFloat(2, 0.8, 1.0)
                        ] : null,
                    ]);
                }

                // Additional nomination from parent (only for athletes)
                if ($parent && $user->profile_type === ProfileType::POSITIVE_ATHLETE) {
                    $relationship = 'Parent';
                    $status = $this->generateNominationStatus($relationship);

                    // Generate grade
                    $currentGrade = $faker->randomElement(['9', '10', '11', '12']);
                    $grade = (int) $currentGrade; // Store as integer instead of "Grade X" format

                    // Generate realistic location data
                    $counties = \App\Models\County::with('market.region')->inRandomOrder()->take(10)->get();
                    $county = $counties->isNotEmpty() ? $counties->random() : null;

                    // Generate sports
                    $nominationSport = $userSports->random();

                    Nomination::query()->create([
                        // Original fields
                        'nominator_email' => $parent->email,
                        'nominator_first_name' => $parent->first_name,
                        'nominator_last_name' => $parent->last_name,
                        'email' => $user->email,
                        'first_name' => $user->first_name,
                        'last_name' => $user->last_name,
                        'school_name' => $user->school->name,
                        'school_id' => $user->school->id,
                        'sport' => $nominationSport->name,
                        'relationship' => $relationship,
                        'note' => $faker->paragraph,
                        'status' => $status,
                        'type' => $nominationType,
                        'ai_score' => $this->generateAiScore($status, $relationship),

                        // Location Information
                        'state_code' => $county?->state_code ?? $faker->stateAbbr,
                        'county' => $county?->name ?? $faker->city . ' County',

                        // Enhanced Contact Information
                        'nominee_phone' => $faker->phoneNumber,
                        'nominator_phone' => $faker->phoneNumber,

                        // Multiple Sports Support
                        'sport_2' => null,
                        'sport_3' => null,
                        'other_sport' => null,

                        // Demographic & Academic Information
                        'gender' => $faker->randomElement(['Male', 'Female', 'Non-binary', 'Prefer not to say']),
                        'grade' => $grade,

                        // Parent/Guardian Information (self-nomination by parent)
                        'parent_guardian_first_name' => $parent->first_name,
                        'parent_guardian_last_name' => $parent->last_name,
                        'parent_guardian_email' => $parent->email,
                        'parent_guardian_phone' => $faker->phoneNumber,

                        // Social Media & Digital Presence
                        'instagram_handle' => $faker->boolean(60) ? '@' . $faker->userName : null,
                        'twitter_handle' => $faker->boolean(40) ? '@' . $faker->userName : null,

                        // Marketing & Attribution Data
                        'how_did_you_hear' => $faker->randomElement([
                            'Friend/family',
                            'Social media',
                            'Website search'
                        ]),
                        'referral_source_name' => $faker->boolean(50) ? $faker->company : null,

                        // Processing Workflow Enhancement
                        'processing_status' => $faker->randomElement(['received', 'validated', 'invited', 'onboarded']),
                        'processed_at' => $faker->boolean(70) ? $faker->dateTimeBetween('-3 months', 'now') : null,

                        // JotForm Integration Metadata
                        'jotform_submission_id' => $faker->uuid,
                        'jotform_form_id' => $faker->randomElement(['************', '************', '************']),
                        'location_resolution_notes' => $faker->boolean(20) ? [
                            'status' => 'resolved',
                            'method' => 'automated_geocoding',
                            'confidence' => $faker->randomFloat(2, 0.8, 1.0)
                        ] : null,
                    ]);
                }
            }

            // Achievements: Some users get 1-3 achievements
            if ($faker->boolean(60)) { // 60% chance user has achievements
                $numAchievements = $faker->numberBetween(1, 3);
                for ($ach = 1; $ach <= $numAchievements; $ach++) {
                    Achievement::query()->create([
                        'user_id' => $user->id,
                        'order' => $ach,
                        'name' => $faker->randomElement([
                            'MVP Award',
                            'All-Star Medal',
                            'Conference Champion',
                            'Athlete of the Week',
                            'Sportsmanship Award',
                            'Academic All-Conference'
                        ]),
                        'date' => $faker->date(),
                        'description' => $faker->paragraph,
                    ]);
                }
            }

            // Add endorsements for each user (70% chance)
            if ($faker->boolean(70) || $user->id === $firstAthlete?->id) {
                // Get 2-5 random endorsement categories
                // For the first athlete, ensure they get all endorsement categories
                $userEndorsements = $user->id === $firstAthlete?->id
                    ? $endorsements
                    : $endorsements->random($faker->numberBetween(2, 5));

                foreach ($userEndorsements as $endorsement) {
                    // For each endorsement category, add 1-3 endorsers
                    // For the first athlete, ensure they get 2-4 endorsers per category
                    $numEndorsers = $user->id === $firstAthlete?->id
                        ? $faker->numberBetween(2, 4)
                        : $faker->numberBetween(1, 3);

                    // Get random users as endorsers (excluding the current user)
                    $potentialEndorsers = $allUsers->where('id', '!=', $user->id)->random(min($numEndorsers, $allUsers->count() - 1));

                    foreach ($potentialEndorsers as $endorser) {
                        // Define possible relations based on user types
                        $possibleRelations = ['Teammate', 'Friend', 'Teacher', 'Coach', 'Mentor'];

                        // Add parent relation only for athletes
                        if ($user->profile_type === ProfileType::POSITIVE_ATHLETE->value) {
                            $possibleRelations[] = 'Parent';
                        }

                        // Insert the endorsement
                        DB::table('user_endorsement')->insert([
                            'user_id' => $user->id,
                            'endorsement_id' => $endorsement->id,
                            'endorser_id' => $endorser->id,
                            'relation' => $faker->randomElement($possibleRelations),
                            'created_at' => now()->subDays($faker->numberBetween(1, 60)),
                            'updated_at' => now(),
                        ]);
                    }
                }
            }
        }

        $this->command->info("Additional user data seeded: custom sports, endorsements, interests, nominations (with AI scores), and achievements added for nominatable users.");
    }
}
