<?php

namespace Database\Seeders;

use App\Enums\ProfileType;
use App\Enums\QuestionType;
use App\Enums\TestStatus;
use App\Events\ModuleCompleted;
use App\Models\Module;
use App\Models\QuestionResponse;
use App\Models\TestAttempt;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StandaloneModuleCompletionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get athletes using profile_type
        $athletes = User::query()
            ->where('profile_type', ProfileType::POSITIVE_ATHLETE->value)
            ->limit(10)
            ->get();

        if ($athletes->count() < 5) {
            $this->command->warn("Not enough athletes found (need at least 5). Using existing users...");
            $athletes = User::query()->limit(5)->get();
        }

        // Get standalone modules (modules not associated with any course)
        $standaloneModules = Module::query()
            ->whereDoesntHave('courses')
            ->with('test.questions.answers')
            ->get();

        if ($standaloneModules->isEmpty()) {
            $this->command->warn("No standalone modules found. Run CoursesAndModulesSeeder first.");
            return;
        }

        $this->command->info("Found {$standaloneModules->count()} standalone modules.");

        // Distribute modules among athletes with varying completion patterns
        $this->command->info("Simulating module completion for {$athletes->count()} users...");

        // First athlete completes all modules (to achieve highest badge)
        $firstAthlete = $athletes->first();
        $this->command->info("User {$firstAthlete->id} ({$firstAthlete->first_name} {$firstAthlete->last_name}) will complete all modules.");

        foreach ($standaloneModules as $module) {
            $this->completeModuleForUser($firstAthlete, $module);
        }

        // Second athlete completes 75% of modules
        $secondAthlete = $athletes->get(1);
        $modulesToComplete = $standaloneModules->random(intval($standaloneModules->count() * 0.75));
        $this->command->info("User {$secondAthlete->id} ({$secondAthlete->first_name} {$secondAthlete->last_name}) will complete {$modulesToComplete->count()} modules.");

        foreach ($modulesToComplete as $module) {
            $this->completeModuleForUser($secondAthlete, $module);
        }

        // Third athlete completes 50% of modules
        $thirdAthlete = $athletes->get(2);
        $modulesToComplete = $standaloneModules->random(intval($standaloneModules->count() * 0.5));
        $this->command->info("User {$thirdAthlete->id} ({$thirdAthlete->first_name} {$thirdAthlete->last_name}) will complete {$modulesToComplete->count()} modules.");

        foreach ($modulesToComplete as $module) {
            $this->completeModuleForUser($thirdAthlete, $module);
        }

        // Fourth athlete completes 25% of modules
        $fourthAthlete = $athletes->get(3);
        $modulesToComplete = $standaloneModules->random(intval($standaloneModules->count() * 0.25));
        $this->command->info("User {$fourthAthlete->id} ({$fourthAthlete->first_name} {$fourthAthlete->last_name}) will complete {$modulesToComplete->count()} modules.");

        foreach ($modulesToComplete as $module) {
            $this->completeModuleForUser($fourthAthlete, $module);
        }

        // Fifth athlete completes just a few modules
        $fifthAthlete = $athletes->get(4);
        $modulesToComplete = $standaloneModules->random(5); // Just 5 modules
        $this->command->info("User {$fifthAthlete->id} ({$fifthAthlete->first_name} {$fifthAthlete->last_name}) will complete {$modulesToComplete->count()} modules.");

        foreach ($modulesToComplete as $module) {
            $this->completeModuleForUser($fifthAthlete, $module);
        }

        // Remaining athletes complete random modules
        foreach ($athletes->slice(5) as $athlete) {
            $randomCount = rand(1, 20); // Random number of modules between 1 and 20
            $modulesToComplete = $standaloneModules->random(min($randomCount, $standaloneModules->count()));
            $this->command->info("User {$athlete->id} ({$athlete->first_name} {$athlete->last_name}) will complete {$modulesToComplete->count()} modules.");

            foreach ($modulesToComplete as $module) {
                $this->completeModuleForUser($athlete, $module);
            }
        }

        $this->command->info("Standalone module completion simulation finished.");
    }

    /**
     * Complete a module for a user and trigger the ModuleCompleted event.
     */
    private function completeModuleForUser(User $user, Module $module): void
    {
        try {
            DB::beginTransaction();

            // Create or update module_user record
            $module->users()->syncWithoutDetaching([
                $user->id => [
                    'started_at' => now()->subMinutes(rand(15, 60)),
                    'completed_at' => now(),
                    'completion_metadata' => [
                        'demo_completed' => true,
                        'completion_type' => $module->test ? 'test' : 'view',
                    ],
                ]
            ]);

            // If module has a test, complete it with perfect score
            if ($module->test) {
                // Create test attempt
                $attempt = new TestAttempt([
                    'user_id' => $user->id,
                    'test_id' => $module->test->id,
                    'started_at' => now()->subMinutes(rand(5, 15)),
                    'ends_at' => now()->addMinutes(45),
                    'completed_at' => now(),
                    'score' => 100,
                    'status' => TestStatus::Complete,
                ]);
                $attempt->save();

                // Create responses for all questions
                foreach ($module->test->questions as $question) {
                    if ($question->type === QuestionType::MultipleChoice) {
                        // Find the correct answer for multiple choice
                        $correctAnswer = $question->answers->where('is_correct', true)->first();

                        if ($correctAnswer) {
                            // Create the response
                            QuestionResponse::query()->create([
                                'user_id' => $user->id,
                                'question_id' => $question->id,
                                'test_attempt_id' => $attempt->id,
                                'response' => $correctAnswer->answer,
                                'correct' => true,
                            ]);
                        }
                    }
                    if ($question->type === QuestionType::LongText) {
                        // For free response questions, create a detailed response
                        QuestionResponse::query()->create([
                            'user_id' => $user->id,
                            'question_id' => $question->id,
                            'test_attempt_id' => $attempt->id,
                            'response' => "This is a detailed response to the question based on the module content.",
                            'correct' => null,
                        ]);
                    }
                }
            }

            DB::commit();

            // Manually trigger the ModuleCompleted event to update badges
            event(new ModuleCompleted($user, $module));

        } catch (\Exception $e) {
            DB::rollBack();
            ray('FAILED TO COMPLETE MODULE FOR USER', $user->id, $module->id, $e->getMessage());
            Log::error("Failed to complete module for user", [
                'user_id' => $user->id,
                'module_id' => $module->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
