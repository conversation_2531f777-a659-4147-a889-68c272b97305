<?php

namespace Database\Seeders;

use App\Models\Sport;
use Illuminate\Database\Seeder;

class SportsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $sports = [
            ['name' => 'Football', 'slug' => 'football', 'icon' => 'fa-football'],
            ['name' => 'Basketball', 'slug' => 'basketball', 'icon' => 'fa-basketball'],
            ['name' => 'Baseball', 'slug' => 'baseball', 'icon' => 'fa-baseball'],
            ['name' => 'Softball', 'slug' => 'softball', 'icon' => 'fa-baseball'],
            ['name' => 'Soccer', 'slug' => 'soccer', 'icon' => 'fa-futbol'],
            ['name' => 'Track & Field', 'slug' => 'track-field', 'icon' => 'fa-person-running'],
            ['name' => 'Cross Country', 'slug' => 'cross-country', 'icon' => 'fa-person-running'],
            ['name' => 'Volleyball', 'slug' => 'volleyball', 'icon' => 'fa-volleyball'],
            ['name' => 'Tennis', 'slug' => 'tennis', 'icon' => 'fa-table-tennis-paddle-ball'],
            ['name' => 'Golf', 'slug' => 'golf', 'icon' => 'fa-golf-ball-tee'],
            ['name' => 'Wrestling', 'slug' => 'wrestling', 'icon' => 'fa-user-group'],
            ['name' => 'Swimming & Diving', 'slug' => 'swimming-diving', 'icon' => 'fa-person-swimming'],
            ['name' => 'Lacrosse', 'slug' => 'lacrosse', 'icon' => 'fa-lacrosse-stick'],
            ['name' => 'Field Hockey', 'slug' => 'field-hockey', 'icon' => 'fa-hockey-stick'],
            ['name' => 'Ice Hockey', 'slug' => 'ice-hockey', 'icon' => 'fa-hockey-puck'],
            ['name' => 'Cheerleading', 'slug' => 'cheerleading', 'icon' => 'fa-pompom'],
            ['name' => 'Dance', 'slug' => 'dance', 'icon' => 'fa-person-dancing'],
            ['name' => 'Gymnastics', 'slug' => 'gymnastics', 'icon' => 'fa-medal'],
            ['name' => 'Water Polo', 'slug' => 'water-polo', 'icon' => 'fa-water'],
            ['name' => 'Rowing', 'slug' => 'rowing', 'icon' => 'fa-oar'],
            ['name' => 'Bowling', 'slug' => 'bowling', 'icon' => 'fa-bowling-ball'],
            ['name' => 'Ultimate Frisbee', 'slug' => 'ultimate-frisbee', 'icon' => 'fa-disc'],
            ['name' => 'Rugby', 'slug' => 'rugby', 'icon' => 'fa-rugby-ball'],
            ['name' => 'Skiing', 'slug' => 'skiing', 'icon' => 'fa-person-skiing'],
            ['name' => 'Snowboarding', 'slug' => 'snowboarding', 'icon' => 'fa-person-snowboarding'],
            ['name' => 'Mountain Biking', 'slug' => 'mountain-biking', 'icon' => 'fa-person-biking-mountain'],
            ['name' => 'Cycling', 'slug' => 'cycling', 'icon' => 'fa-person-biking'],
            ['name' => 'Archery', 'slug' => 'archery', 'icon' => 'fa-bullseye'],
            ['name' => 'Badminton', 'slug' => 'badminton', 'icon' => 'fa-shuttlecock'],
            ['name' => 'Fencing', 'slug' => 'fencing', 'icon' => 'fa-sword'],
        ];

        foreach ($sports as $sport) {
            Sport::query()->updateOrCreate(['slug' => $sport['slug']], $sport);
        }
    }
}
