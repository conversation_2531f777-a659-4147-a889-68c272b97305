<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Industry;
use App\Models\Message;
use App\Models\Opportunity;
use App\Models\Connection;
use App\Enums\ConnectionStatus;
use Database\Faker\Providers\HtmlProvider;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Faker\Generator as Faker;
use App\Enums\OpportunityType;
use App\Enums\OpportunitySubtype;
use App\Models\Interest;

class SponsorDataSeeder extends Seeder
{
    /**
     * The Faker instance
     */
    protected Faker $faker;

    /**
     * Constructor to set up the faker instance with the HTML provider
     */
    public function __construct()
    {
        $this->faker = fake();
        $this->faker->addProvider(new HtmlProvider($this->faker));
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // No need to initialize faker again, already done in constructor

        // 1. Get all sponsor users
        $sponsors = User::where('profile_type', 'sponsor')->get();

        if ($sponsors->isEmpty()) {
            $this->command->error("No sponsor users found.");
            return;
        }

        $this->command->info("Found {$sponsors->count()} sponsors.");

        // 2. Create opportunities for each sponsor
        foreach ($sponsors as $sponsor) {
            $this->createOpportunitiesForSponsor($sponsor);
        }

        // 3. Create conversations and messages between sponsors and athletes
        foreach ($sponsors as $sponsor) {
            $this->createConversationsForSponsor($sponsor);
        }
    }

    private function createOpportunitiesForSponsor(User $sponsor): void
    {
        // Get the organization from the relationship
        $organization = $sponsor->organizations()->first();

        // Skip if sponsor has no organization
        if (!$organization) {
            $this->command->warn("Sponsor {$sponsor->id} has no organization in the pivot table.");
            return;
        }

        $this->command->info("Creating opportunities for sponsor {$sponsor->id} with organization {$organization->name}.");

        $industries = Industry::all();
        if ($industries->isEmpty()) {
            $this->command->warn("No industries found.");
            return;
        }

        $interests = Interest::all();
        if ($interests->isEmpty()) {
            $this->command->warn("No interests found.");
            return;
        }

        $opportunities = [
            [
                'type' => OpportunityType::Education,
                'subtype' => OpportunitySubtype::CertificateProgram,
            ],
            [
                'type' => OpportunityType::Employment,
                'subtype' => OpportunitySubtype::Internship,
            ],
            [
                'type' => OpportunityType::Education,
                'subtype' => OpportunitySubtype::DegreeProgram,
            ],
            [
                'type' => OpportunityType::Employment,
                'subtype' => OpportunitySubtype::Apprenticeship,
            ],
            [
                'type' => OpportunityType::Employment,
                'subtype' => OpportunitySubtype::FullTimeJob,
            ]
        ];

        foreach ($opportunities as $opp) {
            $opportunity = Opportunity::create([
                'organization_id' => $organization->id,
                'user_id' => $sponsor->id, // Set the user ID
                'title' => "Sample " . $opp['subtype']->value . " Opportunity",
                'description' => $this->faker->htmlRichText(3),
                'details' => $this->faker->htmlRichText(2),
                'qualifications' => $this->faker->htmlRichText(2),
                'responsibilities' => $this->faker->htmlRichText(2),
                'benefits' => $this->faker->htmlRichText(2),
                'type' => $opp['type'],
                'subtype' => $opp['subtype'],
                'status' => rand(0, 1) ? 'listed' : 'unlisted',
                'apply_url' => $this->faker->url,
                'created_at' => now()->subDays(rand(1, 180)),
                'updated_at' => now()->subDays(rand(1, 180)),
            ]);

            // Also ensure the organization has website and about fields if not already set
            if (empty($organization->website) || empty($organization->about)) {
                $this->command->info("Updating organization {$organization->id} with website and about.");
                $organization->update([
                    'website' => $organization->website ?? $this->faker->url,
                    'about' => $organization->about ?? $this->faker->htmlRichText(3),
                ]);
            }

            // Attach random industries (1-2 per opportunity)
            $opportunity->industries()->attach(
                $industries->random(rand(1, 2))->pluck('id')->toArray()
            );

            // Attach random interests (1-2 per opportunity)
            $opportunity->interests()->attach(
                $interests->random(rand(1, 2))->pluck('id')->toArray()
            );
        }
    }

    private function createConversationsForSponsor(User $sponsor): void
    {
        // Get 5-10 random athletes
        $athletes = User::where('profile_type', 'athlete')
            ->inRandomOrder()
            ->take(rand(5, 10))
            ->get();

        // Track pinned conversations for this sponsor
        $pinnedCount = 0;
        $maxPinnedConversations = min(2, $athletes->count());

        foreach ($athletes as $athlete) {
            // Create connection (70% accepted, 30% pending)
            $isAccepted = rand(1, 10) <= 7;
            $status = $isAccepted ? ConnectionStatus::ACCEPTED : ConnectionStatus::PENDING;

            // Determine who initiated the connection
            $requesterId = rand(0, 1) === 0 ? $sponsor->id : $athlete->id;
            $recipientId = $requesterId === $sponsor->id ? $athlete->id : $sponsor->id;

            // Create the connection
            $connection = Connection::create([
                'requester_id' => $requesterId,
                'recipient_id' => $recipientId,
                'status' => $status,
            ]);

            // Only create messages if connection is accepted or if sponsor is requester
            if ($isAccepted || $requesterId === $sponsor->id) {
                // Create 5-12 messages per conversation
                $messageCount = rand(5, 12);
                $messages = [];

                for ($i = 0; $i < $messageCount; $i++) {
                    // If connection is pending, only the requester can send messages
                    if (!$isAccepted && $i > 0) {
                        $senderId = $requesterId;
                        $recipientId = $requesterId === $sponsor->id ? $athlete->id : $sponsor->id;
                    } else {
                        // Determine sender and recipient
                        $isAthleteMessage = rand(0, 1) === 0;
                        $senderId = $isAthleteMessage ? $athlete->id : $sponsor->id;
                        $recipientId = $isAthleteMessage ? $sponsor->id : $athlete->id;
                    }

                    // Create message with appropriate state
                    $message = Message::create([
                        'sender_id' => $senderId,
                        'recipient_id' => $recipientId,
                        'content' => $this->generateMessageContent($i + 1),
                        'created_at' => now()->subDays(rand(1, 30))->subHours(rand(1, 24)),
                        'read_at' => rand(1, 10) <= 7 ? now()->subMinutes(rand(1, 60)) : null, // 70% read
                        'is_flagged' => rand(1, 20) === 1, // 5% chance of being flagged
                        'moderation_result' => null // Would be set by actual moderation service
                    ]);

                    $messages[] = $message;
                }

                // Determine if conversation should be pinned (20% chance if accepted)
                if ($isAccepted && $pinnedCount < $maxPinnedConversations && rand(1, 5) === 1) {
                    // Add to sponsor's pinned conversations
                    $pinnedConversations = $sponsor->pinned_conversations ?? [];
                    if (!in_array($athlete->id, $pinnedConversations)) {
                        $pinnedConversations[] = $athlete->id;
                        $sponsor->pinned_conversations = $pinnedConversations;
                        $sponsor->save();
                        $pinnedCount++;
                    }
                }
            }
        }
    }

    /**
     * Generate realistic message content for seeding
     */
    private function generateMessageContent(int $messageNumber): string
    {
        $templates = [
            "Hi there! I noticed your athletic achievements and wanted to connect.",
            "Thanks for accepting my connection request! I'd love to discuss opportunities with our organization.",
            "We have some exciting programs that might interest you.",
            "Could you tell me more about your sports experience?",
            "I'd be happy to provide more information about our scholarship programs.",
            "Your achievements in {sport} are impressive!",
            "Have you considered opportunities in our industry?",
            "Would you be interested in learning more about our internship program?",
            "Congratulations on your recent accomplishments!",
            "Let's schedule a time to discuss potential opportunities."
        ];

        return $templates[array_rand($templates)];
    }
}
