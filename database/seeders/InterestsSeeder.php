<?php

namespace Database\Seeders;

use App\Models\Interest;
use Illuminate\Database\Seeder;

class InterestsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $interests = [
            ['name' => 'Agriculture, Food & Natural Resources', 'icon' => 'fa-tractor'],
            ['name' => 'Architecture', 'icon' => 'fa-ruler-combined'],
            ['name' => 'Arts, Culture & Entertainment', 'icon' => 'fa-palette'],
            ['name' => 'Audio/Video Technology', 'icon' => 'fa-video'],
            ['name' => 'Automotive Service & Sales', 'icon' => 'fa-car'],
            ['name' => 'Business Management & Administration', 'icon' => 'fa-chart-line'],
            ['name' => 'Coaching', 'icon' => 'fa-whistle'],
            ['name' => 'Communications', 'icon' => 'fa-comments'],
            ['name' => 'Construction', 'icon' => 'fa-hard-hat'],
            ['name' => 'Culinary', 'icon' => 'fa-utensils'],
            ['name' => 'Education', 'icon' => 'fa-graduation-cap'],
            ['name' => 'Energy', 'icon' => 'fa-bolt'],
            ['name' => 'Finance', 'icon' => 'fa-money-bill'],
            ['name' => 'Government & Public Administration', 'icon' => 'fa-landmark'],
            ['name' => 'Health Science', 'icon' => 'fa-heart-pulse'],
            ['name' => 'Hospitality & Tourism', 'icon' => 'fa-hotel'],
            ['name' => 'Human Services', 'icon' => 'fa-users'],
            ['name' => 'Information Technology', 'icon' => 'fa-desktop'],
            ['name' => 'Installation, Repair & Maintenance', 'icon' => 'fa-wrench'],
            ['name' => 'Law', 'icon' => 'fa-gavel'],
            ['name' => 'Public Safety & Corrections', 'icon' => 'fa-shield'],
            ['name' => 'Manufacturing', 'icon' => 'fa-industry'],
            ['name' => 'Marketing', 'icon' => 'fa-bullhorn'],
            ['name' => 'Military', 'icon' => 'fa-medal'],
            ['name' => 'Health & Medical', 'icon' => 'fa-stethoscope'],
            ['name' => 'Sales', 'icon' => 'fa-handshake'],
            ['name' => 'Science & Research', 'icon' => 'fa-flask'],
            ['name' => 'Social Media Management', 'icon' => 'fa-hashtag'],
            ['name' => 'Social Services', 'icon' => 'fa-heart'],
            ['name' => 'Sports Marketing/Management', 'icon' => 'fa-trophy'],
            ['name' => 'Technology, Engineering & Math', 'icon' => 'fa-code'],
            ['name' => 'Transportation, Distribution & Logistics', 'icon' => 'fa-truck'],
            ['name' => "I'm Not Sure Yet", 'icon' => 'fa-question'],
            ['name' => 'Other', 'icon' => 'fa-ellipsis']
        ];

        foreach ($interests as $interest) {
            Interest::query()->updateOrCreate(
                ['name' => $interest['name']],
                ['name' => $interest['name'], 'icon' => $interest['icon']]
            );
        }
    }
}
