<?php

namespace Database\Seeders;

use App\Enums\OpportunityLocationType;
use App\Enums\OpportunitySubtype;
use App\Enums\OpportunityTerm;
use App\Enums\OpportunityType;
use App\Models\State;
use Database\Faker\Providers\HtmlProvider;
use Database\Seeders\Traits\HasOrganizationNames;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Organization;
use App\Models\Opportunity;
use App\Models\Industry;
use Faker\Generator as Faker;
use Illuminate\Support\Collection;
use App\Enums\ProfileType;
use App\Models\Interest;

class OpportunitiesSeeder extends Seeder
{
    use HasOrganizationNames;

    /**
     * The Faker instance.
     */
    protected Faker $faker;

    // Constants for controlling distribution
    private const OPPORTUNITIES_PER_TYPE = 200;
    private const FEATURED_PERCENTAGE = 10;

    // Location type distribution (percentages)
    private const LOCATION_TYPE_DISTRIBUTION = [
        OpportunityLocationType::Remote->value => 30,
        OpportunityLocationType::Hybrid->value => 30,
        OpportunityLocationType::Onsite->value => 40,
    ];

    // Term distribution (percentages)
    private const TERM_DISTRIBUTION = [
        OpportunityTerm::Indefinite->value => 30,
        OpportunityTerm::ThreeToFourYears->value => 20,
        OpportunityTerm::OneToTwoYears->value => 30,
        OpportunityTerm::LessThanOneYear->value => 20,
    ];

    /**
     * Constructor to set up the faker instance with the HTML provider
     */
    public function __construct()
    {
        $this->faker = fake();
        $this->faker->addProvider(new HtmlProvider($this->faker));
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Use the class-level faker instance instead of creating a new one
        $faker = $this->faker;

        // Load valid US cities
        $usCities = require database_path('data/us_cities.php');

        // Get existing sponsor users and their organizations
        $sponsors = User::where('profile_type', ProfileType::SPONSOR->value)
            ->whereHas('organizations')
            ->with('organizations')
            ->get();

        if ($sponsors->isEmpty()) {
            $this->command->error("No sponsor users found. Run SponsorUserSeeder first.");
            return;
        }

        $industries = Industry::all();
        if ($industries->isEmpty()) {
            $this->command->warn("No industries found. Run IndustriesSeeder first.");
            return;
        }

        // Get all interests for random assignment
        $interests = Interest::all();
        if ($interests->isEmpty()) {
            $this->command->warn("No interests found. Run InterestsSeeder first.");
            return;
        }

        // Get all states for random assignment
        $states = State::all();
        if ($states->isEmpty()) {
            $this->command->warn("No states found. Run StatesSeeder first.");
            return;
        }

        // Create opportunities for each type
        foreach ([OpportunityType::Education, OpportunityType::Employment] as $type) {
            $this->command->info("Creating {$type->value} opportunities...");

            $remainingCount = self::OPPORTUNITIES_PER_TYPE;
            $progressBar = $this->command->getOutput()->createProgressBar(self::OPPORTUNITIES_PER_TYPE);

            while ($remainingCount > 0) {
                // Select random sponsor and their organization
                $sponsor = $sponsors->random();
                $org = $sponsor->organizations->first();

                // Get appropriate subtypes based on opportunity type
                $subtypes = $this->getSubtypesForType($type);

                // Get a random valid US city/state combination
                $location = $faker->randomElement($usCities);

                // Get a random sponsor from the organization
                $orgSponsor = $org->sponsors()->inRandomOrder()->first();

                // Create opportunity with controlled distribution
                $opportunity = Opportunity::query()->create([
                    'organization_id' => $org->id,
                    'user_id' => $orgSponsor->id,
                    'title' => $this->generateTitle($faker, $type),
                    'description' => $faker->htmlRichText(3),
                    'type' => $type,
                    'subtype' => $subtypes[array_rand($subtypes)],
                    'status' => $faker->randomElement(['listed', 'unlisted']),
                    'city' => $location['city'],
                    'state_code' => $location['state_code'],
                    'details' => $faker->htmlRichText(2),
                    'qualifications' => $faker->htmlRichText(2),
                    'responsibilities' => $faker->htmlRichText(2),
                    'benefits' => $faker->htmlRichText(2),
                    'apply_url' => $faker->url,
                    'term' => $this->getWeightedRandom(self::TERM_DISTRIBUTION),
                    'location_type' => $this->getWeightedRandom(self::LOCATION_TYPE_DISTRIBUTION),
                    'is_featured' => $faker->boolean(self::FEATURED_PERCENTAGE),
                    'created_at' => $faker->dateTimeBetween('-6 months', '-1 day'),
                    'updated_at' => $faker->dateTimeBetween('-6 months', '-1 day'),
                ]);

                // Attach 1-3 industries, ensuring good distribution
                $numIndustries = rand(1, 3);
                $chosenIndustries = $industries->random($numIndustries)
                    ->pluck('id')
                    ->unique()
                    ->values()
                    ->toArray();
                $opportunity->industries()->sync($chosenIndustries);

                // Attach 1-3 interests, ensuring good distribution
                $numInterests = rand(1, 3);
                $chosenInterests = $interests->random($numInterests)
                    ->pluck('id')
                    ->unique()
                    ->values()
                    ->toArray();
                $opportunity->interests()->sync($chosenInterests);

                $remainingCount--;
                $progressBar->advance();
            }

            $progressBar->finish();
            $this->command->info("\nCompleted {$type->value} opportunities.");
        }

        // Add bookmarks for the first athlete user
        $this->addBookmarksForAthlete();

        $this->command->info("\nOpportunity seeding completed successfully.");
    }

    /**
     * Get appropriate subtypes based on opportunity type
     */
    private function getSubtypesForType(OpportunityType $type): array
    {
        return match ($type) {
            OpportunityType::Education => [
                OpportunitySubtype::ContinuingEducation,
                OpportunitySubtype::DegreeProgram,
                OpportunitySubtype::CertificateProgram,
                OpportunitySubtype::Internship,
                OpportunitySubtype::Apprenticeship,
            ],
            OpportunityType::Employment => [
                OpportunitySubtype::FullTimeJob,
                OpportunitySubtype::PartTimeJob,
                OpportunitySubtype::ShortTermContracting,
                OpportunitySubtype::Internship,
            ],
        };
    }

    /**
     * Generate a realistic title based on opportunity type
     */
    private function generateTitle(Faker $faker, OpportunityType $type): string
    {
        if ($type === OpportunityType::Education) {
            $programs = [
                'Certificate in', 'Degree in', 'Training Program:',
                'Apprenticeship:', 'Educational Program:', 'Course:'
            ];
            $subjects = [
                'Business Administration', 'Healthcare Management',
                'Digital Marketing', 'Data Analytics', 'Software Development',
                'Project Management', 'Leadership', 'Engineering'
            ];
            return $programs[array_rand($programs)] . ' ' . $subjects[array_rand($subjects)];
        }

        return $faker->jobTitle();
    }

    /**
     * Get a random value based on weighted distribution
     */
    private function getWeightedRandom(array $weights): string
    {
        $rand = mt_rand(1, 100);
        $total = 0;

        foreach ($weights as $value => $weight) {
            $total += $weight;
            if ($rand <= $total) {
                return $value;
            }
        }

        return array_key_first($weights);
    }

    /**
     * Add bookmarks for the first athlete user
     */
    private function addBookmarksForAthlete(): void
    {
        // Find the first athlete user
        $athlete = User::query()
            ->where('profile_type', ProfileType::POSITIVE_ATHLETE->value)
            ->first();

        if (!$athlete) {
            $this->command->warn("No athlete user found. Skipping bookmark creation.");
            return;
        }

        // Get random opportunities to bookmark (up to 4)
        $opportunities = Opportunity::query()
            ->inRandomOrder()
            ->limit(rand(2, 4))
            ->get();

        if ($opportunities->isEmpty()) {
            $this->command->warn("No opportunities found. Skipping bookmark creation.");
            return;
        }

        // Attach the opportunities to the athlete user as bookmarks
        $athlete->bookmarkedOpportunities()->attach($opportunities->pluck('id'));

        $this->command->info("Added " . $opportunities->count() . " bookmarked opportunities for athlete: " . $athlete->first_name . ' ' . $athlete->last_name);
    }
}
