<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Organization;
use Database\Seeders\Traits\HasOrganizationNames;
use Illuminate\Database\Seeder;

class SponsorOrganizationSeeder extends Seeder
{
    use HasOrganizationNames;

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all sponsor users without organizations
        $sponsors = User::where('profile_type', 'sponsor')
            ->get();

        $this->command->info("Found {$sponsors->count()} sponsors.");

        $count = 0;
        foreach ($sponsors as $sponsor) {
            // See if the user already has an organization in the pivot table
            $hasOrg = $sponsor->organizations()->exists();

            if (!$hasOrg) {
                // Create a new organization for this sponsor
                $organization = $this->createOrganizationFromList();

                // Associate the sponsor with the organization in pivot table
                $sponsor->organizations()->attach($organization->id, [
                    'role' => 'admin',
                ]);

                $count++;

                if ($count % 10 === 0) {
                    $this->command->info("Created and associated {$count} organizations so far...");
                }
            }
        }

        $this->command->info("Created and associated organizations for {$count} sponsors.");
    }
}
