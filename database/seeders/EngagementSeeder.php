<?php

namespace Database\Seeders;

use App\Enums\OpportunityStatus;
use App\Models\Advertisement;
use App\Models\Engagement;
use App\Models\EngagementAggregate;
use App\Models\Opportunity;
use App\Models\User;
use Carbon\Carbon;
use Faker\Factory as Faker;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Queue;

class EngagementSeeder extends Seeder
{
    /**
     * User agent data for realistic metadata
     */
    private const BROWSERS = [
        'Chrome' => 55,
        'Firefox' => 15,
        'Safari' => 20,
        'Edge' => 8,
        'Opera' => 2,
    ];

    private const DEVICE_TYPES = [
        'Desktop' => 45,
        'Mobile' => 40,
        'Tablet' => 15,
    ];

    private const OPERATING_SYSTEMS = [
        'Windows' => 40,
        'macOS' => 20,
        'iOS' => 20,
        'Android' => 18,
        'Linux' => 2,
    ];

    /**
     * Engagement volume tiers with CTR ranges
     */
    private const TIERS = [
        'high' => [
            'distribution' => 20, // 20% of items get high engagement
            'impression_multiplier' => 5.0,
            'ctr_min' => 3.0,
            'ctr_max' => 5.0,
        ],
        'moderate' => [
            'distribution' => 50, // 50% of items get moderate engagement
            'impression_multiplier' => 2.0,
            'ctr_min' => 1.0,
            'ctr_max' => 3.0,
        ],
        'low' => [
            'distribution' => 30, // 30% of items get low engagement
            'impression_multiplier' => 1.0,
            'ctr_min' => 0.5,
            'ctr_max' => 1.0,
        ],
    ];

    /**
     * Time-of-day distribution weights for engagement probability
     * (24-hour format, index is the hour, value is the relative probability)
     */
    private const HOUR_WEIGHTS = [
        0 => 0.1,  // 12 AM
        1 => 0.05,
        2 => 0.02,
        3 => 0.01,
        4 => 0.01,
        5 => 0.05,
        6 => 0.1,
        7 => 0.3,
        8 => 0.7,  // Morning work hours
        9 => 1.0,
        10 => 1.2,
        11 => 1.3,
        12 => 1.4, // Lunch hour
        13 => 1.2,
        14 => 1.1, // Afternoon work hours
        15 => 1.0,
        16 => 0.9,
        17 => 0.8,
        18 => 0.7, // Evening
        19 => 0.6,
        20 => 0.5,
        21 => 0.4,
        22 => 0.3,
        23 => 0.2,
    ];

    /**
     * Day-of-week weights (0 = Sunday, 6 = Saturday)
     */
    private const DAY_WEIGHTS = [
        0 => 0.6, // Sunday
        1 => 1.0, // Monday
        2 => 1.1, // Tuesday
        3 => 1.2, // Wednesday
        4 => 1.1, // Thursday
        5 => 1.0, // Friday
        6 => 0.5, // Saturday
    ];

    /**
     * Configuration options
     */
    private $config = [
        'start_date' => null, // Will be set to 90 days ago in constructor
        'end_date' => null,   // Will be set to today in constructor
        'base_daily_impressions' => 100, // Base number of impressions per day
        'clear_existing' => true,
        'volume_multiplier' => 1.0,
        'metadata_percentage' => 70, // Percentage of records with metadata
        'batch_size' => 5000, // Batch insert size for performance
        'user_association_percentage' => 40, // Percentage of engagements to associate with users
        'chunk_size' => 10, // Number of items to process in each chunk
        'use_queue' => true, // Whether to use queues for processing
    ];

    /**
     * Run the database seeds.
     */
    public function run(array $options = []): void
    {
        // Set default dates if not provided - use past dates
        $this->config['start_date'] = Carbon::now()->subDays(90)->startOfDay();
        $this->config['end_date'] = Carbon::now()->subDays(1)->endOfDay();

        // Override config with provided options
        foreach ($options as $key => $value) {
            if (array_key_exists($key, $this->config)) {
                $this->config[$key] = $value;
            }
        }

        $faker = Faker::create();

        // Clear existing engagement data if configured
        if ($this->config['clear_existing']) {
            $this->clearExistingData();
        }

        // Get all advertisements and opportunities
        $advertisements = Advertisement::where('is_listed', true)->get();
        $opportunities = Opportunity::where('status', OpportunityStatus::LISTED->value)->get();

        // Get all active users to associate with engagements and cache IDs for better performance
        $userIds = User::query()->whereNotNull('email_verified_at')->inRandomOrder()->limit(100)->pluck('id')->toArray();

        $this->command->info('Found ' . count($userIds) . ' users for engagement associations');

        // Calculate total chunks
        $totalAdvertChunks = ceil($advertisements->count() / $this->config['chunk_size']);
        $totalOpportunityChunks = ceil($opportunities->count() / $this->config['chunk_size']);
        $totalChunks = $totalAdvertChunks + $totalOpportunityChunks;

        $this->command->info("Will process data in {$totalChunks} chunks");

        // Only use queue if specifically enabled
        if ($this->config['use_queue']) {
            $this->command->info("Using queues for data generation");

            // Prepare jobs for advertisements
            $this->command->info('Queueing engagement data for ' . $advertisements->count() . ' advertisements');
            $advertJobs = [];

            $advertisements->chunk($this->config['chunk_size'])->each(function ($chunk) use ($faker, $userIds, &$advertJobs) {
                // Serialize the chunk IDs only to reduce memory usage
                $chunkIds = $chunk->pluck('id')->toArray();
                $advertJobs[] = new \App\Jobs\GenerateEngagementDataJob(
                    'advertisement',
                    $chunkIds,
                    $this->config,
                    $userIds
                );
            });

            // Prepare jobs for opportunities
            $this->command->info('Queueing engagement data for ' . $opportunities->count() . ' opportunities');
            $opportunityJobs = [];

            $opportunities->chunk($this->config['chunk_size'])->each(function ($chunk) use ($faker, $userIds, &$opportunityJobs) {
                // Serialize the chunk IDs only to reduce memory usage
                $chunkIds = $chunk->pluck('id')->toArray();
                $opportunityJobs[] = new \App\Jobs\GenerateEngagementDataJob(
                    'opportunity',
                    $chunkIds,
                    $this->config,
                    $userIds
                );
            });

            // Dispatch all jobs and wait for completion
            $this->command->info('Dispatching ' . count($advertJobs) . ' advertisement jobs and ' . count($opportunityJobs) . ' opportunity jobs');

            // Combine all jobs
            $allJobs = array_merge($advertJobs, $opportunityJobs);

            // Dispatch batch with callback for completion
            Bus::batch($allJobs)
                ->name('Engagement Seeding')
                ->allowFailures()
                ->dispatch();

            $this->command->info('Jobs dispatched. Please run queue:work to process them.');
            $this->command->info('After queue processing completes, run the aggregation command:');
            $this->command->info('   php artisan engagement:aggregate');

            return;
        } else {
            // Process synchronously with progress bar
            $progressBar = $this->command->getOutput()->createProgressBar($totalChunks);
            $progressBar->start();

            // Process advertisements
            $this->command->info('Generating engagement data for ' . $advertisements->count() . ' advertisements');

            $advertisements->chunk($this->config['chunk_size'])->each(function ($chunk) use ($faker, $userIds, $progressBar) {
                $this->generateEngagementDataForChunk($chunk, 'advertisement', $faker, $userIds);
                $progressBar->advance();
            });

            // Process opportunities
            $this->command->info('Generating engagement data for ' . $opportunities->count() . ' opportunities');

            $opportunities->chunk($this->config['chunk_size'])->each(function ($chunk) use ($faker, $userIds, $progressBar) {
                $this->generateEngagementDataForChunk($chunk, 'opportunity', $faker, $userIds);
                $progressBar->advance();
            });

            $progressBar->finish();
            $this->command->info('');

            $this->command->info('Aggregating engagement data...');
            $this->aggregateEngagementData();

            $this->command->info('Engagement data seeding completed');
        }
    }

    /**
     * Clear existing engagement data
     */
    private function clearExistingData(): void
    {
        $this->command->info('Clearing existing engagement data...');
        DB::table('engagements')->truncate();
        DB::table('engagement_aggregates')->truncate();
    }

    /**
     * Generate engagement data for a chunk of models
     */
    private function generateEngagementDataForChunk($chunk, string $type, $faker, array $userIds): void
    {
        // Assign engagement tiers to models
        $tieredCollection = $this->assignTiers($chunk);

        $engagementBatch = [];
        $batchCount = 0;
        $userIdCount = count($userIds);

        // Pre-compute user association decision arrays for better performance
        $userAssocDecisions = [];
        for ($i = 0; $i < 100; $i++) {
            $userAssocDecisions[] = $faker->randomFloat(2, 0, 100) <= $this->config['user_association_percentage'];
        }

        $clickUserAssocDecisions = [];
        for ($i = 0; $i < 100; $i++) {
            $clickUserAssocDecisions[] = $faker->randomFloat(2, 0, 100) <= ($this->config['user_association_percentage'] * 1.5);
        }

        foreach ($tieredCollection as $item) {
            // Generate data for each day in the date range
            for ($date = Carbon::parse($this->config['start_date']); $date <= Carbon::parse($this->config['end_date']); $date->addDay()) {
                // Day of week adjustment
                $dayOfWeekMultiplier = self::DAY_WEIGHTS[$date->dayOfWeek];

                // Calculate impressions for this day based on tier and day of week
                $dailyImpressions = $this->calculateDailyImpressions($item['tier'], $dayOfWeekMultiplier);

                // Calculate clicks based on CTR
                $dailyClicks = (int) round($dailyImpressions * ($item['ctr'] / 100));

                // Generate hourly distribution for impressions and clicks
                $hourlyImpressions = $this->distributeByHour($dailyImpressions);
                $hourlyClicks = $this->distributeByHour($dailyClicks);

                // Generate impression records
                foreach ($hourlyImpressions as $hour => $count) {
                    for ($i = 0; $i < $count; $i++) {
                        $timestamp = $date->copy()->setHour($hour)->setMinute($faker->numberBetween(0, 59))->setSecond($faker->numberBetween(0, 59));

                        $metadata = $this->shouldGenerateMetadata($faker) ? $this->generateMetadata($faker) : null;

                        // Determine if this engagement should be associated with a user (approximately 40%)
                        // Use pre-computed decisions for better performance
                        $userId = null;
                        if ($userIdCount > 0 && $userAssocDecisions[$i % 100]) {
                            $userId = $userIds[$faker->numberBetween(0, $userIdCount - 1)];
                        }

                        $engagementBatch[] = [
                            'event_type' => Engagement::EVENT_IMPRESSION,
                            'engageable_id' => $item['model']->id,
                            'engageable_type' => get_class($item['model']),
                            'user_id' => $userId,
                            'ip_address' => $faker->ipv4,
                            'user_agent' => $metadata ? $metadata['user_agent'] : null,
                            'metadata' => $metadata ? json_encode($metadata) : null,
                            'created_at' => $timestamp,
                        ];

                        $batchCount++;

                        // Insert in batches for better performance
                        if ($batchCount >= $this->config['batch_size']) {
                            DB::table('engagements')->insert($engagementBatch);
                            $engagementBatch = [];
                            $batchCount = 0;
                        }
                    }
                }

                // Generate click records (should be less than impressions)
                foreach ($hourlyClicks as $hour => $count) {
                    for ($i = 0; $i < $count; $i++) {
                        $timestamp = $date->copy()->setHour($hour)->setMinute($faker->numberBetween(0, 59))->setSecond($faker->numberBetween(0, 59));

                        $metadata = $this->shouldGenerateMetadata($faker) ? $this->generateMetadata($faker) : null;

                        // Clicks are more likely to be associated with users (60% of the click traffic)
                        // Use pre-computed decisions for better performance
                        $userId = null;
                        if ($userIdCount > 0 && $clickUserAssocDecisions[$i % 100]) {
                            $userId = $userIds[$faker->numberBetween(0, $userIdCount - 1)];
                        }

                        $engagementBatch[] = [
                            'event_type' => Engagement::EVENT_CLICK,
                            'engageable_id' => $item['model']->id,
                            'engageable_type' => get_class($item['model']),
                            'user_id' => $userId,
                            'ip_address' => $faker->ipv4,
                            'user_agent' => $metadata ? $metadata['user_agent'] : null,
                            'metadata' => $metadata ? json_encode($metadata) : null,
                            'created_at' => $timestamp,
                        ];

                        $batchCount++;

                        // Insert in batches for better performance
                        if ($batchCount >= $this->config['batch_size']) {
                            DB::table('engagements')->insert($engagementBatch);
                            $engagementBatch = [];
                            $batchCount = 0;
                        }
                    }
                }
            }
        }

        // Insert any remaining records
        if (!empty($engagementBatch)) {
            DB::table('engagements')->insert($engagementBatch);
        }
    }

    /**
     * Generate engagement data for a collection of models
     */
    private function generateEngagementData($collection, string $type, $faker, array $userIds): void
    {
        // This is now a wrapper for chunks processing
        $dayCount = Carbon::parse($this->config['start_date'])->diffInDays(Carbon::parse($this->config['end_date'])) + 1;
        $totalItems = count($collection) * $dayCount;

        $this->command->info("Generating data for {$totalItems} item-days across {$dayCount} days");

        // Process collection in chunks
        $collection->chunk($this->config['chunk_size'])->each(function ($chunk) use ($type, $faker, $userIds) {
            $this->generateEngagementDataForChunk($chunk, $type, $faker, $userIds);
        });
    }

    /**
     * Assign engagement tiers to models
     */
    private function assignTiers($collection): array
    {
        $faker = Faker::create();
        $tieredCollection = [];

        foreach ($collection as $model) {
            // Determine tier based on distribution weights
            $tier = $this->getWeightedTier($faker);

            // Assign a specific CTR within the tier's range
            $ctr = $faker->randomFloat(2, self::TIERS[$tier]['ctr_min'], self::TIERS[$tier]['ctr_max']);

            $tieredCollection[] = [
                'model' => $model,
                'tier' => $tier,
                'ctr' => $ctr,
            ];
        }

        return $tieredCollection;
    }

    /**
     * Get a weighted tier based on distribution
     */
    private function getWeightedTier($faker): string
    {
        $rand = $faker->randomFloat(2, 0, 100);
        $cumulative = 0;

        foreach (self::TIERS as $tier => $config) {
            $cumulative += $config['distribution'];
            if ($rand <= $cumulative) {
                return $tier;
            }
        }

        return 'low'; // Default fallback
    }

    /**
     * Calculate impressions for a day based on tier and day of week multiplier
     */
    private function calculateDailyImpressions(string $tier, float $dayOfWeekMultiplier): int
    {
        $baseImpressions = $this->config['base_daily_impressions'] * $this->config['volume_multiplier'];
        $tierMultiplier = self::TIERS[$tier]['impression_multiplier'];

        return (int) round($baseImpressions * $tierMultiplier * $dayOfWeekMultiplier);
    }

    /**
     * Distribute a count across hours of the day based on hourly weights
     */
    private function distributeByHour(int $count): array
    {
        $distribution = [];
        $totalWeight = array_sum(self::HOUR_WEIGHTS);

        // First pass: calculate raw distribution
        foreach (self::HOUR_WEIGHTS as $hour => $weight) {
            $distribution[$hour] = (int) round(($weight / $totalWeight) * $count);
        }

        // Adjust to ensure we have exactly $count items
        $currentTotal = array_sum($distribution);
        $difference = $count - $currentTotal;

        if ($difference != 0) {
            // Get hours sorted by weight to prioritize adjustments
            $hoursByWeight = array_keys(self::HOUR_WEIGHTS);
            usort($hoursByWeight, function ($a, $b) {
                return self::HOUR_WEIGHTS[$b] <=> self::HOUR_WEIGHTS[$a];
            });

            // Add or subtract from hours starting with highest weight
            $i = 0;
            while ($difference != 0) {
                $hour = $hoursByWeight[$i % count($hoursByWeight)];
                if ($difference > 0) {
                    $distribution[$hour]++;
                    $difference--;
                } else if ($distribution[$hour] > 0) { // Don't go negative
                    $distribution[$hour]--;
                    $difference++;
                }
                $i++;
            }
        }

        return $distribution;
    }

    /**
     * Determine if we should generate metadata for this record
     */
    private function shouldGenerateMetadata($faker): bool
    {
        return $faker->randomFloat(2, 0, 100) <= $this->config['metadata_percentage'];
    }

    /**
     * Generate realistic metadata for an engagement
     */
    private function generateMetadata($faker): array
    {
        // Select browser, OS, and device type using weighted randomness
        $browser = $this->getWeightedRandom(self::BROWSERS, $faker);
        $os = $this->getWeightedRandom(self::OPERATING_SYSTEMS, $faker);
        $deviceType = $this->getWeightedRandom(self::DEVICE_TYPES, $faker);

        // Generate plausible user agent
        $userAgent = $this->generateUserAgent($browser, $os, $deviceType);

        // 40% chance of including referrer
        $hasReferrer = $faker->boolean(40);
        $referrer = $hasReferrer ? $this->generateReferrer($faker) : null;

        // 80% chance of including geo data
        $hasGeo = $faker->boolean(80);
        $geoData = $hasGeo ? [
            'country' => $faker->countryCode,
            'region' => $faker->state,
        ] : null;

        // Build metadata
        $metadata = [
            'user_agent' => $userAgent,
            'browser' => $browser,
            'os' => $os,
            'device_type' => $deviceType,
            'session_id' => $faker->uuid,
            'utm_source' => $faker->optional(0.3)->word,
        ];

        if ($hasReferrer) {
            $metadata['referrer'] = $referrer;
        }

        if ($hasGeo) {
            $metadata['geo'] = $geoData;
        }

        return $metadata;
    }

    /**
     * Get a weighted random value from an associative array
     */
    private function getWeightedRandom(array $weightedValues, $faker): string
    {
        $totalWeight = array_sum($weightedValues);
        $randomWeight = $faker->randomFloat(2, 0, $totalWeight);

        $currentWeight = 0;
        foreach ($weightedValues as $value => $weight) {
            $currentWeight += $weight;
            if ($randomWeight <= $currentWeight) {
                return $value;
            }
        }

        // Fallback (shouldn't happen but just in case)
        return array_key_first($weightedValues);
    }

    /**
     * Generate a plausible user agent string
     */
    private function generateUserAgent(string $browser, string $os, string $deviceType): string
    {
        // Very simplified for example purposes
        $version = rand(70, 120);

        return "Mozilla/5.0 ({$os}; {$deviceType}) {$browser}/{$version}.0";
    }

    /**
     * Generate a plausible referrer URL
     */
    private function generateReferrer($faker): string
    {
        $referrers = [
            'https://www.google.com/search?q=positive+athlete',
            'https://www.facebook.com/positiveathlete',
            'https://twitter.com/search?q=positive%20athlete',
            'https://www.instagram.com/positive_athlete/',
            'https://www.linkedin.com/search/results/all/?keywords=positive%20athlete',
            'https://www.bing.com/search?q=positive+athlete+scholarships',
            'https://www.yahoo.com/search?p=positive+athlete',
            'https://duckduckgo.com/?q=positive+athlete',
            'https://www.youtube.com/results?search_query=positive+athlete',
            'https://positiveathlete.org',
            'https://positiveathlete.org/programs',
            'https://positiveathlete.org/about',
            'https://positiveathlete.org/testimonials',
            'https://positiveathlete.org/contact',
        ];

        return $faker->randomElement($referrers);
    }

    /**
     * Aggregate all engagement records into daily totals
     */
    private function aggregateEngagementData(): void
    {
        $startDate = Carbon::parse($this->config['start_date']);
        $endDate = Carbon::parse($this->config['end_date']);

        $progressBar = $this->command->getOutput()->createProgressBar($startDate->diffInDays($endDate) + 1);
        $progressBar->start();

        // Instantiate the service
        $engagementService = app(\App\Services\EngagementService::class);

        // Go through each day and aggregate
        for ($date = $startDate->copy(); $date <= $endDate; $date->addDay()) {
            $count = $engagementService->aggregateEngagementsForDate($date);
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->command->info('');
        $this->command->info("Aggregated engagement data for " . $startDate->diffInDays($endDate) + 1 . " days");
    }
}
