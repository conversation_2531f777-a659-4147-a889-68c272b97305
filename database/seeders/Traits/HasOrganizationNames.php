<?php

namespace Database\Seeders\Traits;

use App\Models\Organization;
use Faker\Generator;
use Illuminate\Support\Str;

trait HasOrganizationNames
{
    /**
     * List of organization names to use in seeders and factories
     */
    public static array $organizationNames = [
        'Home Depot',
        'Delta',
        'Micron Technology',
        'Raytheon Technologies',
        'Humana',
        'Southwest Airlines',
        'Bath & Body Works',
        'eBay',
        'RealPage',
        'Smithsonian Institution',
        'Daimler Truck North America',
        'Valero Energy',
        'Rocket Mortgage',
        'Cadence Bank',
        'Discover Financial Services',
        'JPMorgan Chase',
        'Fidelity National Financial',
        'BP',
        'Coca-Cola',
        'Chanel',
        'UnitedHealth Group',
        'Bayer',
        'Honda Motor',
        'Nestlé',
        'CNA Financial',
        'Genentech',
        'Veolia North America',
        'Cushman & Wakefield',
        'Clayton Homes',
        'Edwards Lifesciences',
        'Radisson Hotel Group Americas',
        'McKesson',
        'Saks Fifth Avenue',
        'Nucor Corporation',
        'Ace Hardware',
        'Schreiber Foods',
        'PSEG (Public Service Enterprise Group)',
        'Clorox Company',
        'PwC (PricewaterhouseCoopers)',
        'Unilever',
        'CenterPoint Energy',
        'Walt Disney Company',
        'Phillips 66',
        'MetLife Inc.',
        'Salesforce Inc.',
        'Merck & Co.',
        'Abbott Laboratories',
        'Dell Technologies'
    ];

    /**
     * Get a random organization name from the list
     */
    public function getRandomOrganizationName(): string
    {
        // Make sure we have a faker instance
        if (!isset($this->faker) && method_exists($this, 'faker')) {
            $this->faker = $this->faker();
        } elseif (!isset($this->faker)) {
            // For factories that don't have a faker property
            $this->faker = app(Generator::class);
        }

        return $this->faker->randomElement(self::$organizationNames);
    }

    /**
     * Create an organization with a name from the predefined list
     *
     * @param string $type The organization type
     * @return Organization
     */
    public function createOrganizationFromList(string $type = 'sponsor'): Organization
    {
        // Get a random organization name
        $name = $this->getRandomOrganizationName();

        // Check if organization with this name already exists
        $existingOrg = Organization::query()->where('name', $name)->first();
        if ($existingOrg) {
            return $existingOrg;
        }

        // Create the organization
        $organization = Organization::create([
            'name' => $name,
            'type' => $type,
        ]);

        // Add organization logo using UI Avatars
        $this->addOrganizationLogo($organization);

        return $organization;
    }

    /**
     * Add a logo to an organization using UI Avatars
     *
     * @param Organization $organization
     * @return void
     */
    public function addOrganizationLogo(Organization $organization): void
    {
        // Generate a logo URL using UI Avatars
        $logoUrl = 'https://ui-avatars.com/api/?name=' . urlencode($organization->name) . '&background=random&color=fff&size=256&bold=true';

        // Add the logo to the organization
        $organization->addMediaFromUrl($logoUrl)
            ->toMediaCollection('logo');
    }
}
