<?php

namespace Database\Seeders;

use App\Enums\ProfileType;
use App\Enums\LifeStage;
use App\Enums\NominationType;
use App\Enums\NominationStatus;
use App\Models\User;
use App\Models\School;
use App\Models\Region;
use App\Models\County;
use App\Models\Nomination;
use App\Models\Sport;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Faker\Factory as Faker;
use Illuminate\Support\Str;

class RegionalUsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faker = Faker::create();

        // Get all regions to ensure coverage
        $regions = Region::with('markets.counties')->get();

        foreach ($regions as $region) {
            $this->command->info("Seeding users for {$region->name} region");

            // Get counties for this region
            $counties = $region->counties;
            if ($counties->isEmpty()) {
                $this->command->warn("No counties found for region {$region->name}");
                continue;
            }

            // Create a school in this region
            $schoolCounty = $counties->random();
            $school = School::query()->create([
                'name' => "Regional {$region->name} High School",
                'county_id' => $schoolCounty->id,
                'address' => $faker->streetAddress,
                'city' => $faker->city,
                'zip_code' => $faker->postcode,
            ]);

            // Create a coach
            $coachCounty = $counties->where('state', $schoolCounty->state)->random();
            $coach = User::query()->create([
                'email' => "coach.{$region->slug}@example.com",
                'first_name' => "{$region->name}",
                'last_name' => "Coach",
                'handle' => "coach_{$region->slug}",
                'profile_type' => ProfileType::POSITIVE_COACH->value,
                'password' => Hash::make('password'),
                'recruiter_enabled' => $faker->boolean,
                'phone' => $faker->phoneNumber,
                'street_address_1' => $faker->streetAddress,
                'street_address_2' => $faker->boolean(30) ? $faker->secondaryAddress : null,
                'city' => $faker->city,
                'state_code' => $coachCounty->state_code,
                'zip' => $faker->postcode,
                'county_id' => $coachCounty->id,
                'school_id' => $school->id,
                'gender' => $faker->randomElement(['male', 'female']),
                'content' => $faker->paragraph,
            ]);

            $coach->assignRole('user');

            // Create nomination for this positive_coach user (business logic requirement)
            $this->createNominationForUser($coach, $faker, $school, NominationType::COACH);

            // Create 2-3 athletes per region
            for ($i = 1; $i <= $faker->numberBetween(2, 3); $i++) {
                $athleteCounty = $counties->where('state', $schoolCounty->state)->random();

                // Maybe create a parent (60% chance)
                $parentId = null;
                if ($faker->boolean(60)) {
                    $parentCounty = $counties->where('state', $athleteCounty->state)->random();
                    $parent = User::query()->create([
                        'email' => "parent.{$region->slug}.{$i}@example.com",
                        'first_name' => $faker->firstName,
                        'last_name' => $faker->lastName,
                        'handle' => "parent_{$region->slug}_{$i}",
                        'profile_type' => ProfileType::PARENT->value,
                        'password' => Hash::make('password'),
                        'recruiter_enabled' => $faker->boolean,
                        'phone' => $faker->phoneNumber,
                        'street_address_1' => $faker->streetAddress,
                        'street_address_2' => $faker->boolean(30) ? $faker->secondaryAddress : null,
                        'city' => $faker->city,
                        'state_code' => $parentCounty->state_code,
                        'zip' => $faker->postcode,
                        'county_id' => $parentCounty->id,
                        'gender' => $faker->randomElement(['male', 'female']),
                        'content' => $faker->paragraph,
                    ]);

                    $parent->assignRole('user');
                    $parentId = $parent->id;
                }

                // Create the athlete
                $athlete = User::query()->create([
                    'email' => "athlete.{$region->slug}.{$i}@example.com",
                    'first_name' => $faker->firstName,
                    'last_name' => "{$region->name} Athlete {$i}",
                    'handle' => "athlete_{$region->slug}_{$i}",
                    'profile_type' => ProfileType::POSITIVE_ATHLETE->value,
                    'life_stage' => $faker->boolean ? LifeStage::HIGH_SCHOOL_STUDENT->value : LifeStage::HIGH_SCHOOL_GRADUATE->value,
                    'password' => Hash::make('password'),
                    'recruiter_enabled' => $faker->boolean,
                    'phone' => $faker->phoneNumber,
                    'street_address_1' => $faker->streetAddress,
                    'street_address_2' => $faker->boolean(30) ? $faker->secondaryAddress : null,
                    'city' => $faker->city,
                    'state_code' => $athleteCounty->state_code,
                    'zip' => $faker->postcode,
                    'county_id' => $athleteCounty->id,
                    'school_id' => $school->id,
                    'graduation_year' => $faker->numberBetween(2024, 2027),
                    'gpa' => $faker->randomFloat(2, 2.0, 4.0),
                    'class_rank' => $faker->boolean(70) ? $faker->randomElement(['top 10%', 'top 20%', 'top 50%', 'top 1%']) : null,
                    'gender' => $faker->randomElement(['male', 'female']),
                    'height_in_inches' => $faker->numberBetween(60, 80),
                    'weight' => $faker->numberBetween(120, 250),
                    'content' => $faker->paragraph,
                    'parent_id' => $parentId,
                ]);

                $athlete->assignRole('user');

                // Create nomination for this positive_athlete user (business logic requirement)
                $this->createNominationForUser($athlete, $faker, $school, NominationType::ATHLETE);
            }

            // Create a professional user in this region
            $profCounty = $counties->random();
            $professional = User::query()->create([
                'email' => "pro.{$region->slug}@example.com",
                'first_name' => "Professional",
                'last_name' => "{$region->name}",
                'handle' => "pro_{$region->slug}",
                'profile_type' => ProfileType::PROFESSIONAL->value,
                'life_stage' => LifeStage::PROFESSIONAL->value,
                'password' => Hash::make('password'),
                'recruiter_enabled' => true,
                'phone' => $faker->phoneNumber,
                'street_address_1' => $faker->streetAddress,
                'street_address_2' => $faker->boolean(30) ? $faker->secondaryAddress : null,
                'city' => $faker->city,
                'state_code' => $profCounty->state_code,
                'zip' => $faker->postcode,
                'county_id' => $profCounty->id,
                'gender' => $faker->randomElement(['male', 'female']),
                'content' => $faker->paragraph,
            ]);

            $professional->assignRole('user');
        }

        $this->command->info('Additional regional users seeded successfully.');
    }

    /**
     * Create a nomination for a positive_athlete or positive_coach user (business logic requirement)
     */
    private function createNominationForUser(User $user, $faker, School $school, NominationType $nominationType): void
    {
        // Get a random sport
        $sports = Sport::all();
        if ($sports->isEmpty()) {
            $this->command->warn("No sports found for nomination. Skipping nomination creation for {$user->email}");
            return;
        }
        $sport = $sports->random();

        // Generate realistic nomination data
        $relationships = ['Coach', 'Teacher', 'Principal', 'Athletic Director'];
        if ($nominationType === NominationType::ATHLETE) {
            $relationships[] = 'Parent';
        }
        $relationship = $faker->randomElement($relationships);

        // Generate appropriate status based on relationship
        $status = $this->generateNominationStatus($relationship, $faker);

        // Generate grade and graduation year for athletes
        $grade = null;
        $graduationYear = null;
        if ($nominationType === NominationType::ATHLETE) {
            $currentGrade = $faker->randomElement(['9', '10', '11', '12']);
            $grade = (int) $currentGrade;

            // Calculate graduation year based on grade (assuming current academic year)
            $currentYear = now()->year;
            $academicYearStart = now()->month >= 8 ? $currentYear : $currentYear - 1;
            $graduationYear = $academicYearStart + (12 - (int)$currentGrade) + 1;
        }

        // Generate realistic location data
        $counties = \App\Models\County::with('market.region')->inRandomOrder()->take(10)->get();
        $county = $counties->isNotEmpty() ? $counties->random() : null;

        // Generate additional sports (30% chance for multi-sport athletes)
        $sport2 = $faker->boolean(30) && $sports->count() > 1 ? $sports->where('name', '!=', $sport->name)->random()->name : null;
        $sport3 = $faker->boolean(15) && $sports->count() > 2 ? $sports->whereNotIn('name', [$sport->name, $sport2])->random()->name : null;

        // Generate parent/guardian info for minors (if athlete and grade indicates high school)
        $parentInfo = [];
        if ($nominationType === NominationType::ATHLETE && $grade) {
            $parentInfo = [
                'parent_guardian_first_name' => $faker->firstName,
                'parent_guardian_last_name' => $faker->lastName,
                'parent_guardian_email' => $faker->email,
                'parent_guardian_phone' => $faker->phoneNumber,
            ];
        }

        Nomination::query()->create([
            // Original fields
            'nominator_email' => $faker->email,
            'nominator_first_name' => $faker->firstName,
            'nominator_last_name' => $faker->lastName,
            'email' => $user->email,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'school_name' => $school->name,
            'school_id' => $school->id,
            'sport' => $sport->name,
            'relationship' => $relationship,
            'note' => $faker->paragraph,
            'status' => $status,
            'type' => $nominationType->value,
            'ai_score' => $this->generateAiScore($status, $relationship, $faker),

            // Location Information
            'state_code' => $county?->state_code ?? $faker->stateAbbr,
            'county' => $county?->name ?? $faker->city . ' County',

            // Enhanced Contact Information
            'nominee_phone' => $faker->phoneNumber,
            'nominator_phone' => $faker->phoneNumber,

            // Multiple Sports Support
            'sport_2' => $sport2,
            'sport_3' => $sport3,
            'other_sport' => $faker->boolean(10) ? $faker->randomElement(['Adapted Basketball', 'Unified Soccer', 'Special Olympics Track']) : null,

            // Demographic & Academic Information
            'gender' => $faker->randomElement(['Male', 'Female', 'Non-binary', 'Prefer not to say']),
            'grade' => $grade,

            // Parent/Guardian Information
            ...$parentInfo,

            // Social Media & Digital Presence
            'instagram_handle' => $faker->boolean(60) ? '@' . $faker->userName : null,
            'twitter_handle' => $faker->boolean(40) ? '@' . $faker->userName : null,

            // Marketing & Attribution Data
            'how_did_you_hear' => $faker->randomElement([
                'School counselor',
                'Coach recommendation',
                'Social media',
                'Friend/family',
                'School newsletter',
                'Athletic department',
                'Website search'
            ]),
            'referral_source_name' => $faker->boolean(50) ? $faker->company : null,

            // Processing Workflow Enhancement
            'processing_status' => $faker->randomElement(['received', 'validated', 'invited', 'onboarded']),
            'processed_at' => $faker->boolean(70) ? $faker->dateTimeBetween('-3 months', 'now') : null,

            // JotForm Integration Metadata
            'jotform_submission_id' => $faker->uuid,
            'jotform_form_id' => $faker->randomElement(['************', '************', '************']),
            'location_resolution_notes' => $faker->boolean(20) ? [
                'status' => 'resolved',
                'method' => 'automated_geocoding',
                'confidence' => $faker->randomFloat(2, 0.8, 1.0)
            ] : null,
        ]);
    }

    private function generateNominationStatus(string $relationship, $faker): string
    {
        // Distribution weights for different statuses
        $weights = [
            NominationStatus::PENDING_AD_VERIFICATION->value => 30,
            NominationStatus::AD_VERIFIED->value => 40,
            NominationStatus::NOMINEE_NOTIFIED->value => 20,
            NominationStatus::NOMINEE_ACKNOWLEDGED->value => 10,
        ];

        // Adjust weights based on relationship
        if ($relationship === 'Parent') {
            $weights[NominationStatus::PENDING_AD_VERIFICATION->value] += 20;
            $weights[NominationStatus::AD_VERIFIED->value] -= 10;
        } elseif (in_array($relationship, ['Coach', 'Athletic Director'])) {
            $weights[NominationStatus::AD_VERIFIED->value] += 20;
            $weights[NominationStatus::PENDING_AD_VERIFICATION->value] -= 10;
        }

        // Create weighted array
        $weighted = [];
        foreach ($weights as $status => $weight) {
            for ($i = 0; $i < max(1, $weight); $i++) {
                $weighted[] = $status;
            }
        }

        return $faker->randomElement($weighted);
    }

    private function generateAiScore(string $status, string $relationship, $faker): int
    {
        // Base ranges for different nomination statuses
        $ranges = [
            NominationStatus::PENDING_AD_VERIFICATION->value => [20, 55],
            NominationStatus::AD_VERIFIED->value => [45, 75],
            NominationStatus::NOMINEE_NOTIFIED->value => [55, 80],
            NominationStatus::NOMINEE_ACKNOWLEDGED->value => [65, 90],
        ];

        $range = $ranges[$status] ?? [30, 70];

        // Adjust for relationship
        if ($relationship === 'Parent') {
            $range[0] = max(20, $range[0] - 10);
            $range[1] = min(80, $range[1] - 5);
        } elseif (in_array($relationship, ['Coach', 'Athletic Director'])) {
            $range[0] = min(90, $range[0] + 10);
            $range[1] = min(95, $range[1] + 10);
        }

        return $faker->numberBetween($range[0], $range[1]);
    }
}
