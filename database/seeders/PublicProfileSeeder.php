<?php

namespace Database\Seeders;

use App\Enums\ProfileType;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PublicProfileSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * Set a good mix of users to have public profiles so that the recommendation
     * system has users to recommend to each profile type.
     */
    public function run(): void
    {
        $this->command->info('Setting users to have public profiles for recommendations...');

        // Set a percentage of users in each profile type to have public profiles
        $profileConfigs = [
            ProfileType::POSITIVE_ATHLETE->value => [
                'percentage' => 30, // 30% of positive athletes should be public
                'min_count' => 10,  // At least 10 public positive athletes
            ],
            ProfileType::COLLEGE_ATHLETE->value => [
                'percentage' => 40, // 40% of college athletes should be public
                'min_count' => 8,   // At least 8 public college athletes
            ],
            ProfileType::PROFESSIONAL->value => [
                'percentage' => 50, // 50% of professionals should be public
                'min_count' => 8,   // At least 8 public professionals
            ],
            ProfileType::POSITIVE_COACH->value => [
                'percentage' => 35, // 35% of coaches should be public
                'min_count' => 5,   // At least 5 public coaches
            ],
            ProfileType::ATHLETICS_DIRECTOR->value => [
                'percentage' => 60, // 60% of athletics directors should be public
                'min_count' => 3,   // At least 3 public athletics directors
            ],
            ProfileType::SPONSOR->value => [
                'percentage' => 70, // 70% of sponsors should be public
                'min_count' => 3,   // At least 3 public sponsors
            ],
            ProfileType::ALUMNI->value => [
                'percentage' => 45, // 45% of alumni should be public
                'min_count' => 5,   // At least 5 public alumni
            ],
        ];

        foreach ($profileConfigs as $profileType => $config) {
            $this->setPublicProfilesForType($profileType, $config['percentage'], $config['min_count']);
        }

        $this->command->info('Public profile seeding completed!');
    }

    /**
     * Set public profiles for a specific user type
     *
     * @param string $profileType
     * @param int $percentage
     * @param int $minCount
     */
    private function setPublicProfilesForType(string $profileType, int $percentage, int $minCount): void
    {
        // Get total count of users for this profile type
        $totalUsers = User::where('profile_type', $profileType)->count();

        if ($totalUsers === 0) {
            $this->command->warn("No users found for profile type: {$profileType}");
            return;
        }

        // Calculate how many should be public
        $targetPublicCount = max($minCount, intval($totalUsers * ($percentage / 100)));

        // Don't exceed total users
        $targetPublicCount = min($targetPublicCount, $totalUsers);

        // Get users that are currently not public, ordered randomly
        $usersToUpdate = User::where('profile_type', $profileType)
            ->where('public_profile', false)
            ->inRandomOrder()
            ->limit($targetPublicCount)
            ->pluck('id');

        if ($usersToUpdate->isEmpty()) {
            $this->command->info("All {$profileType} users are already public or no users exist");
            return;
        }

        // Update the selected users to have public profiles
        $updatedCount = User::whereIn('id', $usersToUpdate)
            ->update(['public_profile' => true]);

        $this->command->info("Set {$updatedCount} {$profileType} users to public (target: {$targetPublicCount}, total: {$totalUsers})");
    }
}
