<?php

namespace Database\Seeders;

use App\Models\Scholarship;
use App\Models\Region;
use App\Models\Market;
use App\Models\State;
use App\Models\SubRegion;
use Database\Faker\Providers\HtmlProvider;
use Faker\Factory as Faker;
use Illuminate\Database\Seeder;

class ScholarshipSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Set up faker with HTML provider
        $faker = Faker::create();
        $faker->addProvider(new HtmlProvider($faker));

        $currentYear = date('Y');

        // Get available geographic entities
        $regions = Region::all();
        $markets = Market::all();
        $states = State::all();
        $subRegions = SubRegion::all();

        // Create more scholarships for the current year (higher distribution)
        for ($i = 0; $i < 30; $i++) {
            // Select random geographic entities
            $region = $regions->random();
            $market = $markets->where('region_id', $region->id)->random();
            $state = $states->random();

            // 70% chance to have a subregion
            $subRegion = ($faker->boolean(70) && $subRegions->where('market_id', $market->id)->count() > 0)
                ? $subRegions->where('market_id', $market->id)->random()
                : null;

            // Create scholarship with a more diverse range of limits
            Scholarship::create([
                'name' => "PA Scholarship {$currentYear} #" . ($i+1),
                'details' => $this->generateScholarshipDetails($faker, $currentYear, true),
                'year' => $currentYear,
                'is_active' => true,
                'limit' => $faker->randomElement([1, 1, 2, 2, 3, 5, 10]), // More diverse range of limits
                'region_id' => $region->id,
                'market_id' => $market->id,
                'state_id' => $state->code,
                'subregion_id' => $subRegion?->id,
            ]);
        }

        // Create some scholarships for past years (for college athletes and professionals)
        for ($yearOffset = 1; $yearOffset <= 5; $yearOffset++) {
            $pastYear = $currentYear - $yearOffset;

            // Create fewer scholarships for past years
            $count = 10 - $yearOffset; // 9, 8, 7, 6, 5 scholarships for past years
            if ($count < 2) $count = 2; // At least 2 scholarships per year

            for ($i = 0; $i < $count; $i++) {
                // Select random geographic entities
                $region = $regions->random();
                $market = $markets->where('region_id', $region->id)->random();
                $state = $states->random();

                // 70% chance to have a subregion
                $subRegion = ($faker->boolean(70) && $subRegions->where('market_id', $market->id)->count() > 0)
                    ? $subRegions->where('market_id', $market->id)->random()
                    : null;

                Scholarship::create([
                    'name' => "PA Scholarship {$pastYear} #" . ($i+1),
                    'details' => $this->generateScholarshipDetails($faker, $pastYear, false),
                    'year' => $pastYear,
                    'is_active' => false, // Past scholarships are inactive
                    'limit' => $faker->randomElement([1, 1, 2, 3]), // Past scholarships have fewer winners
                    'region_id' => $region->id,
                    'market_id' => $market->id,
                    'state_id' => $state->code,
                    'subregion_id' => $subRegion?->id,
                ]);
            }
        }

        // Create some scholarships for future years (fewer than current year)
        for ($yearOffset = 1; $yearOffset <= 2; $yearOffset++) {
            $futureYear = $currentYear + $yearOffset;

            // Create fewer scholarships for future years, but more than before
            $count = ($yearOffset == 1) ? 15 : 8; // More scholarships for next year

            for ($i = 0; $i < $count; $i++) {
                // Select random geographic entities
                $region = $regions->random();
                $market = $markets->where('region_id', $region->id)->random();
                $state = $states->random();

                // 70% chance to have a subregion
                $subRegion = ($faker->boolean(70) && $subRegions->where('market_id', $market->id)->count() > 0)
                    ? $subRegions->where('market_id', $market->id)->random()
                    : null;

                Scholarship::create([
                    'name' => "PA Scholarship {$futureYear} #" . ($i+1),
                    'details' => $this->generateFutureScholarshipDetails($faker, $futureYear),
                    'year' => $futureYear,
                    'is_active' => true,
                    'limit' => $faker->randomElement([1, 2, 3, 5, 10]), // Future scholarships may have more winners
                    'region_id' => $region->id,
                    'market_id' => $market->id,
                    'state_id' => $state->code,
                    'subregion_id' => $subRegion?->id,
                ]);
            }
        }
    }

    /**
     * Generate rich text content for scholarship details
     *
     * @param \Faker\Generator $faker
     * @param int $year
     * @param bool $isCurrent
     * @return string
     */
    private function generateScholarshipDetails($faker, $year, $isCurrent = true): string
    {
        $verb = $isCurrent ? 'is awarded' : 'was awarded';
        $tense = $isCurrent ? 'demonstrate' : 'demonstrated';

        $intro = "<h3>Positive Athlete Scholarship {$year}</h3>";
        $intro .= "<p>This annual scholarship {$verb} to outstanding student athletes who {$tense} positive character and leadership during the {$year} academic year.</p>";

        // Add a dynamic paragraph using the HTML provider
        $description = $faker->htmlRichText(1);

        // Add scholarship details
        $details = "<h4>Scholarship Details</h4>";
        $details .= "<p>" . $faker->paragraph(rand(2, 3)) . "</p>";

        // Add eligibility criteria
        $criteria = "<h4>Eligibility Requirements</h4><ul>";
        $criteria .= "<li>Must {$tense} positive character on and off the field</li>";
        $criteria .= "<li>Must maintain academic excellence</li>";
        $criteria .= "<li>Must be involved in community service</li>";

        // Add 1-2 additional random criteria
        $additionalCriteria = [
            "Must be nominated by a coach, teacher, or athletic director",
            "Must have played at least one season of school or community sports",
            "Must submit a personal essay",
            "Must provide references from teachers or coaches",
            "Must show financial need",
            "Must plan to pursue further education",
            "Must demonstrate leadership among peers"
        ];

        $randomCriteria = array_rand(array_flip($additionalCriteria), rand(1, 2));
        if (is_array($randomCriteria)) {
            foreach ($randomCriteria as $criterion) {
                $criteria .= "<li>{$criterion}</li>";
            }
        } else {
            $criteria .= "<li>{$randomCriteria}</li>";
        }
        $criteria .= "</ul>";

        // Add application process if current or future
        $application = "";
        if ($isCurrent) {
            $application = "<h4>Application Process</h4>";
            $application .= "<p>" . $faker->paragraph(rand(2, 3)) . "</p>";
        }

        return $intro . $description . $details . $criteria . $application;
    }

    /**
     * Generate rich text content for future scholarship details
     *
     * @param \Faker\Generator $faker
     * @param int $year
     * @return string
     */
    private function generateFutureScholarshipDetails($faker, $year): string
    {
        $intro = "<h3>Upcoming Positive Athlete Scholarship {$year}</h3>";
        $intro .= "<p>This upcoming scholarship will be awarded to outstanding student athletes who demonstrate positive character and leadership for the {$year} academic year.</p>";

        // Add a dynamic paragraph about the future scholarship
        $description = $faker->htmlRichText(1);

        // Add scholarship details
        $details = "<h4>Scholarship Details</h4>";
        $details .= "<p>" . $faker->paragraph(rand(2, 3)) . "</p>";

        // Add projected eligibility criteria
        $criteria = "<h4>Projected Eligibility Requirements</h4><ul>";
        $criteria .= "<li>Will need to demonstrate positive character on and off the field</li>";
        $criteria .= "<li>Will need to maintain academic excellence</li>";
        $criteria .= "<li>Will need to be involved in community service</li>";

        // Add 1-2 additional random criteria for future scholarships
        $additionalCriteria = [
            "Will require nomination by a coach, teacher, or athletic director",
            "Will need to have played at least one season of school or community sports",
            "Will require submission of a personal essay",
            "Will need references from teachers or coaches",
            "Will consider financial need",
            "Will be for students pursuing further education",
            "Will look for demonstrated leadership among peers"
        ];

        $randomCriteria = array_rand(array_flip($additionalCriteria), rand(1, 2));
        if (is_array($randomCriteria)) {
            foreach ($randomCriteria as $criterion) {
                $criteria .= "<li>{$criterion}</li>";
            }
        } else {
            $criteria .= "<li>{$randomCriteria}</li>";
        }
        $criteria .= "</ul>";

        // Add application timeline
        $application = "<h4>Application Timeline</h4>";
        $application .= "<p>Applications for this scholarship will open in " . $faker->monthName() . " " . ($year - 1) . ". " . $faker->sentence(rand(10, 15)) . "</p>";

        return $intro . $description . $details . $criteria . $application;
    }
}
