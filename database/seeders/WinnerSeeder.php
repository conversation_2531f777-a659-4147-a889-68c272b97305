<?php

namespace Database\Seeders;

use App\Models\Award;
use App\Models\Scholarship;
use App\Models\User;
use App\Models\Winner;
use Illuminate\Database\Seeder;
use Faker\Factory as FakerFactory;

class WinnerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $currentYear = date('Y');
        $faker = FakerFactory::create();

        // Get existing users by profile type
        $positiveAthletes = User::query()->where('profile_type', 'positive_athlete')->get();
        $collegeAthletes = User::query()->where('profile_type', 'college_athlete')->get();
        $professionals = User::query()->where('profile_type', 'professional')->get();

        if ($positiveAthletes->isEmpty()) {
            $this->command->warn('No positive athletes found. Winner seeding may be incomplete.');
        }

        // Get awards and scholarships by year
        $currentYearScholarships = Scholarship::query()->where('year', $currentYear)->get();
        $pastYearScholarships = Scholarship::query()->where('year', '<', $currentYear)->get();
        $futureYearScholarships = Scholarship::query()->where('year', '>', $currentYear)->get();

        $currentYearAwards = Award::query()->where('year', $currentYear)->get();
        $pastYearAwards = Award::query()->where('year', '<', $currentYear)->get();
        $futureYearAwards = Award::query()->where('year', '>', $currentYear)->get();

        // CURRENT YEAR - Positive Athletes (majority)
        // ==========================================

        // Create winners for current year scholarships
        // Keep track of scholarships that already have a winner
        $scholarshipsWithWinners = [];

        // Randomly select 30% of scholarships to remain without winners
        $scholarshipsWithoutWinners = $currentYearScholarships->random(max(1, intval($currentYearScholarships->count() * 0.3)));
        $scholarshipsWithoutWinnerIds = $scholarshipsWithoutWinners->pluck('id')->toArray();

        foreach ($currentYearScholarships as $scholarship) {
            if ($positiveAthletes->isEmpty()) break;

            // Skip scholarships we've decided to leave without winners
            if (in_array($scholarship->id, $scholarshipsWithoutWinnerIds)) {
                // Still create finalists for these scholarships
                $finalistCount = rand(1, 3);
                for ($i = 0; $i < $finalistCount; $i++) {
                    if ($positiveAthletes->isEmpty()) break;

                    $finalist = new Winner([
                        'user_id' => $positiveAthletes->random()->id,
                        'scholarship_id' => $scholarship->id,
                        'award_id' => null,
                        'year' => $scholarship->year,
                        'is_finalist' => true,
                        'is_winner' => false,
                        'verification_state' => 'verified',
                        'verified_at' => now(),
                        'notified_at' => now(),
                        'acknowledged_at' => rand(0, 1) ? now() : null,
                        'tshirt_size' => $this->getRandomTshirtSize(),
                    ]);
                    $finalist->save();
                }
                continue;
            }

            // Create ONLY ONE winner per scholarship (not up to the limit)
            $user = $positiveAthletes->random();

            $winner = new Winner([
                'user_id' => $user->id,
                'scholarship_id' => $scholarship->id,
                'award_id' => null,
                'year' => $scholarship->year,
                'is_finalist' => true,
                'is_winner' => true,
                'verification_state' => 'verified',
                'verified_at' => now(),
                'notified_at' => now(),
                'acknowledged_at' => rand(0, 1) ? now() : null,
                'tshirt_size' => $this->getRandomTshirtSize(),
            ]);
            $winner->save();

            $scholarshipsWithWinners[] = $scholarship->id;

            // Create 2-4 finalists for this scholarship (not winners)
            $finalistCount = rand(2, 4);

            // Track used user IDs to avoid duplicates
            $usedUserIds = [$user->id]; // Start with the winner

            for ($i = 0; $i < $finalistCount; $i++) {
                // Get a random user that hasn't been used yet for this scholarship
                $availableUsers = $positiveAthletes->whereNotIn('id', $usedUserIds);

                if ($availableUsers->isEmpty()) break;

                $user = $availableUsers->random();
                $usedUserIds[] = $user->id;

                $finalist = new Winner([
                    'user_id' => $user->id,
                    'scholarship_id' => $scholarship->id,
                    'award_id' => null,
                    'year' => $scholarship->year,
                    'is_finalist' => true,
                    'is_winner' => false,
                    'verification_state' => 'verified',
                    'verified_at' => now(),
                    'notified_at' => now(),
                    'acknowledged_at' => rand(0, 1) ? now() : null,
                    'tshirt_size' => $this->getRandomTshirtSize(),
                ]);
                $finalist->save();
            }
        }

        // Create winners for current year awards
        foreach ($currentYearAwards as $award) {
            if ($positiveAthletes->isEmpty()) break;

            // Create a winner for this award
            $winner = new Winner([
                'user_id' => $positiveAthletes->random()->id,
                'scholarship_id' => null,
                'award_id' => $award->id,
                'year' => $award->year,
                'is_finalist' => true,
                'is_winner' => true,
                'verification_state' => 'verified',
                'verified_at' => now(),
                'notified_at' => now(),
                'acknowledged_at' => rand(0, 1) ? now() : null,
                'tshirt_size' => $this->getRandomTshirtSize(),
            ]);
            $winner->save();

            // Create 1-3 finalists for this award
            $finalistCount = rand(1, 3);
            for ($i = 0; $i < $finalistCount; $i++) {
                if ($positiveAthletes->isEmpty()) break;

                $finalist = new Winner([
                    'user_id' => $positiveAthletes->random()->id,
                    'scholarship_id' => null,
                    'award_id' => $award->id,
                    'year' => $award->year,
                    'is_finalist' => true,
                    'is_winner' => false,
                    'verification_state' => 'verified',
                    'verified_at' => now(),
                    'notified_at' => now(),
                    'acknowledged_at' => rand(0, 1) ? now() : null,
                    'tshirt_size' => $this->getRandomTshirtSize(),
                ]);
                $finalist->save();
            }
        }

        // PAST YEARS - College Athletes and Professionals
        // ==============================================

        // Combine college athletes and professionals for past years
        $pastAthletes = $collegeAthletes->merge($professionals);

        // If not enough college/professional athletes, add some positive athletes too
        if ($pastAthletes->count() < 10 && $positiveAthletes->isNotEmpty()) {
            $pastAthletes = $pastAthletes->merge($positiveAthletes->take(min(10, $positiveAthletes->count())));
        }

        if ($pastAthletes->isNotEmpty()) {
            // Create winners for past year scholarships
            foreach ($pastYearScholarships as $scholarship) {
                // For past years, create only ONE winner per scholarship
                // 70% chance to have a winner, 30% chance to have no winner
                if ($faker->boolean(70)) {
                    $user = $pastAthletes->random();

                    $winner = new Winner([
                        'user_id' => $user->id,
                        'scholarship_id' => $scholarship->id,
                        'award_id' => null,
                        'year' => $scholarship->year,
                        'is_finalist' => true,
                        'is_winner' => true,
                        'verification_state' => 'verified',
                        'verified_at' => now()->subYears($currentYear - $scholarship->year),
                        'notified_at' => now()->subYears($currentYear - $scholarship->year),
                        'acknowledged_at' => now()->subYears($currentYear - $scholarship->year),
                        'tshirt_size' => $this->getRandomTshirtSize(),
                    ]);
                    $winner->save();

                    // Create 1-3 finalists (not winners) for past scholarship
                    $finalistCount = rand(1, 3);
                    for ($i = 0; $i < $finalistCount; $i++) {
                        $availableUsers = $pastAthletes->where('id', '!=', $user->id);
                        if ($availableUsers->isEmpty()) break;

                        $finalist = new Winner([
                            'user_id' => $availableUsers->random()->id,
                            'scholarship_id' => $scholarship->id,
                            'award_id' => null,
                            'year' => $scholarship->year,
                            'is_finalist' => true,
                            'is_winner' => false,
                            'verification_state' => 'verified',
                            'verified_at' => now()->subYears($currentYear - $scholarship->year),
                            'notified_at' => now()->subYears($currentYear - $scholarship->year),
                            'acknowledged_at' => now()->subYears($currentYear - $scholarship->year),
                            'tshirt_size' => $this->getRandomTshirtSize(),
                        ]);
                        $finalist->save();
                    }
                }
            }

            // Create winners for past year awards
            foreach ($pastYearAwards as $award) {
                // For past years, only create winners (not finalists)
                $winner = new Winner([
                    'user_id' => $pastAthletes->random()->id,
                    'scholarship_id' => null,
                    'award_id' => $award->id,
                    'year' => $award->year,
                    'is_finalist' => true,
                    'is_winner' => true,
                    'verification_state' => 'verified',
                    'verified_at' => now()->subYears($currentYear - $award->year),
                    'notified_at' => now()->subYears($currentYear - $award->year),
                    'acknowledged_at' => now()->subYears($currentYear - $award->year),
                    'tshirt_size' => $this->getRandomTshirtSize(),
                ]);
                $winner->save();
            }
        }

        // FUTURE YEARS - Pending winners and finalists
        // ===========================================
        if ($positiveAthletes->isNotEmpty()) {
            // For future scholarships, just create finalists in "pending" state
            foreach ($futureYearScholarships->take(5) as $scholarship) {
                $finalistCount = rand(3, 6);
                for ($i = 0; $i < $finalistCount; $i++) {
                    $finalist = new Winner([
                        'user_id' => $positiveAthletes->random()->id,
                        'scholarship_id' => $scholarship->id,
                        'award_id' => null,
                        'year' => $scholarship->year,
                        'is_finalist' => true,
                        'is_winner' => false,
                        'verification_state' => 'pending',
                        'verified_at' => null,
                        'notified_at' => null,
                        'acknowledged_at' => null,
                        'tshirt_size' => $this->getRandomTshirtSize(),
                    ]);
                    $finalist->save();
                }
            }

            // For future awards, just create finalists in "pending" state
            foreach ($futureYearAwards->take(5) as $award) {
                $finalistCount = rand(3, 6);
                for ($i = 0; $i < $finalistCount; $i++) {
                    $finalist = new Winner([
                        'user_id' => $positiveAthletes->random()->id,
                        'scholarship_id' => null,
                        'award_id' => $award->id,
                        'year' => $award->year,
                        'is_finalist' => true,
                        'is_winner' => false,
                        'verification_state' => 'pending',
                        'verified_at' => null,
                        'notified_at' => null,
                        'acknowledged_at' => null,
                        'tshirt_size' => $this->getRandomTshirtSize(),
                    ]);
                    $finalist->save();
                }
            }
        }
    }

    /**
     * Get a random t-shirt size
     *
     * @return string
     */
    private function getRandomTshirtSize(): string
    {
        return collect(['XS', 'S', 'M', 'L', 'XL', 'XXL'])->random();
    }
}
