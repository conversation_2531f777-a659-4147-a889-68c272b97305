<?php

namespace Database\Seeders;

use App\Models\Region;
use App\Models\Market;
use App\Models\SubRegion;
use App\Models\County;
use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;

class RegionalHierarchySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define our regional structure based on the map
        $structure = [
            'West' => [
                'California' => [
                    'sub_regions' => [
                        'Northern California' => [
                            'counties' => [
                                ['name' => 'San Francisco', 'state' => 'CA'],
                                ['name' => 'Alameda', 'state' => 'CA'],
                                ['name' => 'Santa Clara', 'state' => 'CA'],
                            ],
                        ],
                        'Southern California' => [
                            'counties' => [
                                ['name' => 'Los Angeles', 'state' => 'CA'],
                                ['name' => 'San Diego', 'state' => 'CA'],
                                ['name' => 'Orange', 'state' => 'CA'],
                            ],
                        ],
                    ],
                ],
                'Oregon' => [
                    'direct_counties' => [
                        ['name' => 'Multnomah', 'state' => 'OR'],
                        ['name' => 'Washington', 'state' => 'OR'],
                    ],
                ],
            ],
            'Mountain' => [
                'Colorado' => [
                    'sub_regions' => [
                        'Front Range' => [
                            'counties' => [
                                ['name' => 'Denver', 'state' => 'CO'],
                                ['name' => 'Boulder', 'state' => 'CO'],
                                ['name' => 'Jefferson', 'state' => 'CO'],
                            ],
                        ],
                    ],
                ],
                'Utah' => [
                    'direct_counties' => [
                        ['name' => 'Salt Lake', 'state' => 'UT'],
                        ['name' => 'Utah', 'state' => 'UT'],
                    ],
                ],
            ],
            'Great Lakes' => [
                'Michigan' => [
                    'direct_counties' => [
                        ['name' => 'Wayne', 'state' => 'MI'],
                        ['name' => 'Oakland', 'state' => 'MI'],
                        ['name' => 'Macomb', 'state' => 'MI'],
                    ],
                ],
                'Minnesota' => [
                    'direct_counties' => [
                        ['name' => 'Hennepin', 'state' => 'MN'],
                        ['name' => 'Ramsey', 'state' => 'MN'],
                    ],
                ],
                'Wisconsin' => [
                    'direct_counties' => [
                        ['name' => 'Milwaukee', 'state' => 'WI'],
                        ['name' => 'Dane', 'state' => 'WI'],
                    ],
                ],
            ],
            'South Central' => [
                'Texas' => [
                    'sub_regions' => [
                        'North Texas' => [
                            'counties' => [
                                ['name' => 'Dallas', 'state' => 'TX'],
                                ['name' => 'Tarrant', 'state' => 'TX'],
                                ['name' => 'Collin', 'state' => 'TX'],
                            ],
                        ],
                    ],
                ],
                'Oklahoma' => [
                    'direct_counties' => [
                        ['name' => 'Oklahoma', 'state' => 'OK'],
                        ['name' => 'Tulsa', 'state' => 'OK'],
                    ],
                ],
            ],
            'Southeast' => [
                'Georgia' => [
                    'sub_regions' => [
                        'Metro Atlanta' => [
                            'counties' => [
                                ['name' => 'Fulton', 'state' => 'GA'],
                                ['name' => 'DeKalb', 'state' => 'GA'],
                                ['name' => 'Gwinnett', 'state' => 'GA'],
                            ],
                        ],
                    ],
                ],
                'Florida' => [
                    'sub_regions' => [
                        'South Florida' => [
                            'counties' => [
                                ['name' => 'Miami-Dade', 'state' => 'FL'],
                                ['name' => 'Broward', 'state' => 'FL'],
                                ['name' => 'Palm Beach', 'state' => 'FL'],
                            ],
                        ],
                    ],
                ],
            ],
            'Mid-Atlantic' => [
                'Virginia' => [
                    'direct_counties' => [
                        ['name' => 'Fairfax', 'state' => 'VA'],
                        ['name' => 'Arlington', 'state' => 'VA'],
                        ['name' => 'Loudoun', 'state' => 'VA'],
                    ],
                ],
                'Maryland' => [
                    'direct_counties' => [
                        ['name' => 'Montgomery', 'state' => 'MD'],
                        ['name' => 'Prince Georges', 'state' => 'MD'],
                    ],
                ],
            ],
            'Northeast' => [
                'New York' => [
                    'sub_regions' => [
                        'New York Metro' => [
                            'counties' => [
                                ['name' => 'New York', 'state' => 'NY'],
                                ['name' => 'Kings', 'state' => 'NY'],
                                ['name' => 'Queens', 'state' => 'NY'],
                            ],
                        ],
                    ],
                ],
                'Massachusetts' => [
                    'sub_regions' => [
                        'Greater Boston' => [
                            'counties' => [
                                ['name' => 'Suffolk', 'state' => 'MA'],
                                ['name' => 'Middlesex', 'state' => 'MA'],
                                ['name' => 'Essex', 'state' => 'MA'],
                            ],
                        ],
                    ],
                ],
            ],
        ];

        foreach ($structure as $regionName => $markets) {
            $region = Region::query()->create([
                'name' => $regionName,
                'slug' => str($regionName)->slug(),
                'color' => $this->getRegionColor($regionName),
            ]);

            foreach ($markets as $marketName => $marketData) {
                $market = Market::query()->create([
                    'region_id' => $region->id,
                    'name' => $marketName,
                    'slug' => str($marketName)->slug(),
                ]);

                // Create sub-regions if they exist
                if (isset($marketData['sub_regions'])) {
                    foreach ($marketData['sub_regions'] as $subRegionName => $subRegionData) {
                        $subRegion = SubRegion::query()->create([
                            'market_id' => $market->id,
                            'name' => $subRegionName,
                            'slug' => str($subRegionName)->slug(),
                        ]);

                        // Create counties for this sub-region
                        foreach ($subRegionData['counties'] as $countyData) {
                            County::query()->create([
                                'market_id' => $market->id,
                                'sub_region_id' => $subRegion->id,
                                'name' => $countyData['name'],
                                'state_code' => $countyData['state'],
                            ]);
                        }
                    }
                }

                // Create counties directly under the market
                if (isset($marketData['direct_counties'])) {
                    foreach ($marketData['direct_counties'] as $countyData) {
                        County::query()->create([
                            'market_id' => $market->id,
                            'sub_region_id' => null,
                            'name' => $countyData['name'],
                            'state_code' => $countyData['state'],
                        ]);
                    }
                }
            }
        }

        $this->command->info('Regional hierarchy seeded successfully.');
    }

    /**
     * Get a consistent color for each region based on the map.
     */
    private function getRegionColor(string $region): string
    {
        return match ($region) {
            'Mountain', 'Southeast', 'Mid-Atlantic', 'West' => '#dc2626',    // Red
            'Great Lakes', 'South Central', 'Northeast' => '#1e3a8a',   // Navy Blue
            default => '#6366f1',        // Fallback Indigo
        };
    }
}
