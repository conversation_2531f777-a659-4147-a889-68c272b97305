<?php

namespace Database\Seeders;

use App\Enums\ProfileType;
use App\Enums\LifeStage;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Course;
use App\Models\Test;
use App\Models\TestAttempt;
use Faker\Factory as Faker;
use Carbon\Carbon;
use Illuminate\Support\Facades\Artisan;
use App\Enums\ModuleType;

class CourseParticipationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faker = Faker::create();

        // Get athletes using profile_type - include high school athletes, college athletes, and professionals
        $highSchoolAthletes = User::query()
            ->where('profile_type', ProfileType::POSITIVE_ATHLETE->value)
            ->limit(4)
            ->get();

        $collegeAthletes = User::query()
            ->where('profile_type', ProfileType::COLLEGE_ATHLETE->value)
            ->limit(2)
            ->get();

        $professionals = User::query()
            ->where('profile_type', ProfileType::PROFESSIONAL->value)
            ->limit(2)
            ->get();

        // Combine all users who should have course participation
        $allUsers = $highSchoolAthletes->concat($collegeAthletes)->concat($professionals);

        // Check if we have enough users (need 8 total)
        if ($allUsers->count() < 8) {
            $this->command->warn("Not enough users found (need 8). Creating additional users...");

            // Create the additional users needed
            $neededUsers = 8 - $allUsers->count();

            // Calculate how many of each type to create
            $neededHighSchool = min(4 - $highSchoolAthletes->count(), $neededUsers);
            $neededUsers -= $neededHighSchool;

            $neededCollege = min(2 - $collegeAthletes->count(), $neededUsers);
            $neededUsers -= $neededCollege;

            $neededProfessionals = min(2 - $professionals->count(), $neededUsers);

            // Create missing high school athletes
            for ($i = 0; $i < $neededHighSchool; $i++) {
                $user = User::factory()->create([
                    'profile_type' => ProfileType::POSITIVE_ATHLETE->value,
                    'life_stage' => $faker->boolean ? LifeStage::HIGH_SCHOOL_STUDENT->value : LifeStage::HIGH_SCHOOL_GRADUATE->value,
                    'first_name' => $faker->firstName,
                    'last_name' => $faker->lastName,
                ]);
                $highSchoolAthletes->push($user);
                $allUsers->push($user);
            }

            // Create missing college athletes
            for ($i = 0; $i < $neededCollege; $i++) {
                $user = User::factory()->create([
                    'profile_type' => ProfileType::COLLEGE_ATHLETE->value,
                    'life_stage' => $faker->boolean ? LifeStage::COLLEGE_STUDENT->value : LifeStage::COLLEGE_GRADUATE->value,
                    'first_name' => $faker->firstName,
                    'last_name' => $faker->lastName,
                ]);
                $collegeAthletes->push($user);
                $allUsers->push($user);
            }

            // Create missing professionals
            for ($i = 0; $i < $neededProfessionals; $i++) {
                $user = User::factory()->create([
                    'profile_type' => ProfileType::PROFESSIONAL->value,
                    'life_stage' => LifeStage::PROFESSIONAL->value,
                    'first_name' => $faker->firstName,
                    'last_name' => $faker->lastName,
                ]);
                $professionals->push($user);
                $allUsers->push($user);
            }
        }

        $courses = Course::with([
            'modules.test.questions.answers',
            'modules' => function($query) {
                $query->orderBy('course_module.order');
            }
        ])->get();

        if ($courses->count() < 3) {
            $this->command->warn("Not enough courses. Need at least 3 courses seeded.");
            return;
        }

        // Assign users and courses
        // We'll make sure each course has at least 2 completions
        foreach ($courses as $course) {
            // Use the first high school athlete and first college athlete instead of random selection
            $courseUsers = [
                $highSchoolAthletes[0], // First high school athlete will be our partial completion user
                $collegeAthletes[0],    // First college athlete will be our multiple attempts user
            ];

            // For the first featured course
            if ($course->featured && $course === $courses->first()) {
                // First user partially completes the course (up to the second exam)
                Artisan::call('demo:complete-course', [
                    'courseId' => $course->id,
                    'userId' => $courseUsers[0]->id,
                    '--stop-at-module-order' => 7 // Stop before the second exam
                ]);

                // Create test attempts for the first user (up to second exam)
                $examModules = $course->modules()
                    ->where('type', ModuleType::Exam)
                    ->orderBy('course_module.order')
                    ->take(1) // Only take the first exam since we stop before the second
                    ->get();

                // First user gets perfect scores on completed exams
                foreach ($examModules as $examModule) {
                    if ($examModule->test) {
                        TestAttempt::query()->create([
                            'user_id' => $courseUsers[0]->id,
                            'test_id' => $examModule->test->id,
                            'started_at' => Carbon::now()->subDays(2),
                            'completed_at' => Carbon::now()->subDays(2)->addMinutes(45),
                            'ends_at' => Carbon::now()->subDays(2)->addHour(),
                            'score' => 100,
                            'status' => 'complete'
                        ]);
                    }
                }

                // Second user completes the course with multiple attempts
                Artisan::call('demo:complete-course', [
                    'courseId' => $course->id,
                    'userId' => $courseUsers[1]->id
                ]);

                // Create multiple test attempts for the second user
                $examModules = $course->modules()
                    ->where('type', ModuleType::Exam)
                    ->orderBy('course_module.order')
                    ->get();

                foreach ($examModules as $examModule) {
                    if ($examModule->test) {
                        // Create failed attempts
                        TestAttempt::query()->create([
                            'user_id' => $courseUsers[1]->id,
                            'test_id' => $examModule->test->id,
                            'started_at' => Carbon::now()->subDays(2),
                            'completed_at' => Carbon::now()->subDays(2)->addMinutes(45),
                            'ends_at' => Carbon::now()->subDays(2)->addHour(),
                            'score' => 65,
                            'status' => 'complete'
                        ]);

                        TestAttempt::query()->create([
                            'user_id' => $courseUsers[1]->id,
                            'test_id' => $examModule->test->id,
                            'started_at' => Carbon::now()->subDays(1),
                            'completed_at' => Carbon::now()->subDays(1)->addMinutes(45),
                            'ends_at' => Carbon::now()->subDays(1)->addHour(),
                            'score' => 75,
                            'status' => 'complete'
                        ]);

                        // Final passing attempt
                        TestAttempt::query()->create([
                            'user_id' => $courseUsers[1]->id,
                            'test_id' => $examModule->test->id,
                            'started_at' => Carbon::now()->subHours(2),
                            'completed_at' => Carbon::now()->subHours(1),
                            'ends_at' => Carbon::now()->addHours(1),
                            'score' => 100,
                            'status' => 'complete'
                        ]);
                    }
                }
            } else {
                // For other courses, both users complete them
                foreach ($courseUsers as $user) {
                    Artisan::call('demo:complete-course', [
                        'courseId' => $course->id,
                        'userId' => $user->id
                    ]);

                    // Create test attempts for all exams
                    $examModules = $course->modules()
                        ->where('type', ModuleType::Exam)
                        ->orderBy('course_module.order')
                        ->get();

                    foreach ($examModules as $examModule) {
                        if ($examModule->test) {
                            TestAttempt::query()->create([
                                'user_id' => $user->id,
                                'test_id' => $examModule->test->id,
                                'started_at' => Carbon::now()->subDays(1),
                                'completed_at' => Carbon::now()->subDays(1)->addMinutes(45),
                                'ends_at' => Carbon::now()->subDays(1)->addHour(),
                                'score' => 90,
                                'status' => 'complete'
                            ]);
                        }
                    }
                }
            }
        }

        // Create some partial completions for variety
        // Use professionals and remaining high school athletes for partial completions
        $partialUsers = $professionals->take(2)->concat($highSchoolAthletes->skip(1)->take(2));

        foreach ($partialUsers as $user) {
            // Randomly select a course for partial completion
            $course = $courses->random();
            Artisan::call('demo:complete-course', [
                'courseId' => $course->id,
                'userId' => $user->id,
                '--partial' => true
            ]);
        }

        $this->command->info("Created course participation for high school athletes, college athletes, and professionals with varied completion levels.");
    }
}
