<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Team;
use App\Models\Meeting;
use Illuminate\Support\Facades\DB;
use Faker\Factory as Faker;
use Carbon\Carbon;

class MeetingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faker = Faker::create();

        // Find all coaches:
        // A coach is a user who has at least one team_user record with type = 'coach'.
        $coaches = User::whereHas('teams', function ($q) {
            $q->where('type', 'coach');
        })->get();

        if ($coaches->isEmpty()) {
            $this->command->warn("No coaches found. Make sure initial user seeding was done.");
            return;
        }

        $totalMeetingsDesired = 30;
        $meetingsCreated = 0;

        // We will loop through coaches and create meetings until we hit at least 30 meetings.
        // If you have multiple coaches, they will share the load of creating meetings.
        while ($meetingsCreated < $totalMeetingsDesired) {
            foreach ($coaches as $coach) {
                // Get all teams this coach belongs to as a coach.
                $coachTeams = $coach->teams()->wherePivot('type', 'coach')->get();

                if ($coachTeams->isEmpty()) {
                    continue;
                }

                // For each coach, create a few meetings until we reach our goal
                $numMeetingsForThisCoach = $faker->numberBetween(1, 3); // random small number to spread out
                for ($i = 1; $i <= $numMeetingsForThisCoach; $i++) {
                    if ($meetingsCreated >= $totalMeetingsDesired) {
                        break 2; // break out of both loops if we hit our target
                    }

                    // Pick a random team for this meeting
                    $team = $coachTeams->random();

                    // Meeting time can be up to 1 month in the past or 1 month in the future
                    // We'll pick a random date between -30 days and +30 days.
                    $meetingTime = Carbon::now()->addDays($faker->numberBetween(-30, 30));

                    // Create meeting
                    $meeting = Meeting::create([
                        'name' => $faker->sentence(3),
                        'meeting_time' => $meetingTime,
                        'team_id' => $team->id,
                        'owner_id' => $coach->id,
                        'content' => $faker->paragraph,
                        'location' => $faker->address,
                    ]);

                    // Invite some athletes from the team
                    // Get athletes on this team:
                    $athletes = $team->users()->wherePivot('type', 'athlete')->get();
                    if ($athletes->isNotEmpty()) {
                        // Invite a random subset of athletes
                        $numInvitees = $faker->numberBetween(2, min(10, $athletes->count()));
                        $invitees = $athletes->random($numInvitees);

                        // Attach them to meeting_invitees
                        $meeting->invitees()->attach($invitees->pluck('id'));
                    }

                    $meetingsCreated++;
                }

                if ($meetingsCreated >= $totalMeetingsDesired) {
                    break;
                }
            }
        }

        $this->command->info("Created a total of {$meetingsCreated} meetings with invites.");
    }
}
