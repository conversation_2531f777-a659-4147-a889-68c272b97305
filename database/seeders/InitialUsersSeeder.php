<?php

namespace Database\Seeders;

use App\Enums\ProfileType;
use App\Enums\LifeStage;
use App\Enums\NominationType;
use App\Enums\NominationStatus;
use App\Models\User;
use App\Models\School;
use App\Models\Team;
use App\Models\Group;
use App\Models\Sport;
use App\Models\TeamInvite;
use App\Models\Nomination;
use App\Enums\TeamUserType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Faker\Factory as Faker;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\File;
use App\Models\County;
use App\Models\Tag;
use App\Services\UserService;

class InitialUsersSeeder extends Seeder
{
    /**
     * Sample tags
     */
    protected array $tags = [
        'Scholarship Potential',
        'Team Captain',
        'Academic Excellence',
        'Leadership',
        'Community Service',
        'Multi-Sport',
        'Rising Star',
        'Senior',
        'Junior',
        'Sophomore',
        'Freshman',
        'Head Coach',
        'Assistant Coach',
        'Varsity',
        'JV',
        'Experienced',
        'New Coach',
        'Mentor',
    ];

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faker = Faker::create();

        // Create tags first
        collect($this->tags)->each(function ($tagName) {
            Tag::query()->firstOrCreate(['name' => $tagName]);
        });

        // Get all profile images
        $profileImages = collect(File::files(database_path('images')))
            ->filter(fn($file) => str_starts_with($file->getFilename(), 'card-'))
            ->values();

        if ($profileImages->isEmpty()) {
            $this->command->warn("No profile images found in database/images directory.");
            return;
        }

        // Get all sports to randomly assign to teams
        $sports = Sport::all();
        if ($sports->isEmpty()) {
            $this->command->warn("No sports found. Run SportsSeeder first.");
            return;
        }

        // Get available counties to assign to users and schools
        $counties = County::with('market.region')->get();
        if ($counties->isEmpty()) {
            $this->command->warn("No counties found. Run RegionalHierarchySeeder first.");
            return;
        }

        // Create two coaches
        for ($i = 1; $i <= 2; $i++) {
            // Get a random county for this coach's school
            $schoolCounty = $counties->random();

            // Create a school for this coach
            $school = School::query()->create([
                'name' => "School {$i}",
                'county_id' => $schoolCounty->id,
                'address' => $faker->streetAddress,
                'city' => $faker->city,
                'zip_code' => $faker->postcode,
            ]);

            // Coach details
            $coachEmail = "coach{$i}@example.com";
            $coachCounty = $counties->where('state', $schoolCounty->state)->random(); // Same state as school
            $coach = User::query()->updateOrCreate(
                ['email' => $coachEmail],
                [
                    'first_name' => "Coach",
                    'last_name' => "Number {$i}",
                    'handle' => "coach{$i}",
                    'profile_type' => ProfileType::POSITIVE_COACH->value,
                    'password' => Hash::make('password'),
                    'recruiter_enabled' => $faker->boolean,
                    'phone' => $faker->phoneNumber,
                    'street_address_1' => $faker->streetAddress,
                    'street_address_2' => $faker->boolean(30) ? $faker->secondaryAddress : null,
                    'city' => $faker->city,
                    'state_code' => $coachCounty->state_code,
                    'zip' => $faker->postcode,
                    'county_id' => $coachCounty->id,
                    'graduation_year' => null,
                    'gpa' => null,
                    'class_rank' => null,
                    'gender' => $faker->randomElement(['male', 'female']),
                    'height_in_inches' => null,
                    'weight' => null,
                    'twitter' => $faker->boolean(50) ? $faker->userName : null,
                    'linkedin' => $faker->boolean(50) ? 'https://www.linkedin.com/in/' . $faker->userName : null,
                    'instagram' => $faker->boolean(50) ? $faker->userName : null,
                    'facebook' => $faker->boolean(50) ? $faker->userName : null,
                    'content' => $faker->paragraph,
                    'parent_id' => null,
                    'school_id' => $school->id,
                ]
            );

            $coach->assignRole('user');

            // Create nomination for this positive_coach user (business logic requirement)
            $this->createNominationForUser($coach, $faker, $school, NominationType::COACH, $sports);

            // Add profile photo
            $coach->addMedia($profileImages->random()->getPathname())
                ->preservingOriginal()
                ->toMediaCollection('profile_photos');

            // Add random tags to coach
            $coachTags = Tag::query()
                ->inRandomOrder()
                ->take($faker->numberBetween(2, 4))
                ->get();
            $coach->tags()->attach($coachTags);

            // Create a team for this school & assign a random sport
            $sport = $sports->random();
            $team = Team::query()->create([
                'name' => "{$coach->name}'s Team",
                'sport_id' => $sport->id,
                'school_id' => $school->id,
            ]);

            // Add the coach to the team_user pivot as a coach
            $team->users()->attach($coach->id, ['type' => TeamUserType::Coach->value]);

            // Create a couple of groups under this team
            $groupA = Group::query()->create(['name' => 'Group A', 'team_id' => $team->id]);
            $groupB = Group::query()->create(['name' => 'Group B', 'team_id' => $team->id]);

            // Create an assistant coach
            $assistantEmail = "assistant{$i}@example.com";
            $assistantCounty = $counties->where('state', $schoolCounty->state)->random(); // Same state as school
            $assistant = User::query()->updateOrCreate(
                ['email' => $assistantEmail],
                [
                    'first_name' => "Assistant",
                    'last_name' => "Coach {$i}",
                    'handle' => "assistant{$i}",
                    'profile_type' => ProfileType::POSITIVE_COACH->value,
                    'password' => Hash::make('password'),
                    'recruiter_enabled' => $faker->boolean,
                    'phone' => $faker->phoneNumber,
                    'street_address_1' => $faker->streetAddress,
                    'street_address_2' => $faker->boolean(30) ? $faker->secondaryAddress : null,
                    'city' => $faker->city,
                    'state_code' => $assistantCounty->state_code,
                    'zip' => $faker->postcode,
                    'county_id' => $assistantCounty->id,
                    'graduation_year' => null,
                    'gpa' => null,
                    'class_rank' => null,
                    'gender' => $faker->randomElement(['male', 'female']),
                    'height_in_inches' => null,
                    'weight' => null,
                    'twitter' => $faker->boolean(50) ? $faker->userName : null,
                    'linkedin' => $faker->boolean(50) ? 'https://www.linkedin.com/in/' . $faker->userName : null,
                    'instagram' => $faker->boolean(50) ? $faker->userName : null,
                    'facebook' => $faker->boolean(50) ? $faker->userName : null,
                    'content' => $faker->paragraph,
                    'school_id' => $school->id,
                    'parent_id' => null,
                ]
            );

            $assistant->assignRole('user');

            // Create nomination for this positive_coach user (business logic requirement)
            $this->createNominationForUser($assistant, $faker, $school, NominationType::COACH, $sports);

            // Add profile photo
            $assistant->addMedia($profileImages->random()->getPathname())
                ->preservingOriginal()
                ->toMediaCollection('profile_photos');

            // Add assistant to the team
            $team->users()->attach($assistant->id, ['type' => TeamUserType::Assistant->value]);

            // Assign assistant to Group A
            $groupA->users()->attach($assistant->id);

            // Add random tags to assistant
            $assistantTags = Tag::query()
                ->inRandomOrder()
                ->take($faker->numberBetween(2, 4))
                ->get();
            $assistant->tags()->attach($assistantTags);

            // Create a few athletes
            for ($a = 1; $a <= 5; $a++) {
                $athleteEmail = "athlete{$i}_{$a}@example.com";
                $athleteCounty = $counties->where('state', $schoolCounty->state)->random(); // Same state as school

                // Optionally create a parent for the athlete
                $parentId = null;
                if ($faker->boolean(40)) {
                    // 40% chance athlete has a parent
                    $parentCounty = $counties->where('state', $athleteCounty->state)->random(); // Same state as athlete
                    $parent = User::query()->create([
                        'email' => $faker->unique()->safeEmail,
                        'first_name' => $faker->firstName,
                        'last_name' => $faker->lastName,
                        'handle' => "parent_{$i}_{$a}_" . Str::random(5),
                        'profile_type' => ProfileType::PARENT->value,
                        'password' => Hash::make('password'),
                        'recruiter_enabled' => $faker->boolean,
                        'phone' => $faker->phoneNumber,
                        'street_address_1' => $faker->streetAddress,
                        'street_address_2' => $faker->boolean(30) ? $faker->secondaryAddress : null,
                        'city' => $faker->city,
                        'state_code' => $parentCounty->state_code,
                        'zip' => $faker->postcode,
                        'county_id' => $parentCounty->id,
                        'graduation_year' => null,
                        'gpa' => null,
                        'class_rank' => null,
                        'gender' => $faker->randomElement(['male', 'female']),
                        'height_in_inches' => null,
                        'weight' => null,
                        'twitter' => $faker->boolean(50) ? $faker->userName : null,
                        'linkedin' => $faker->boolean(50) ? 'https://www.linkedin.com/in/' . $faker->userName : null,
                        'instagram' => $faker->boolean(50) ? $faker->userName : null,
                        'facebook' => $faker->boolean(50) ? $faker->userName : null,
                        'content' => $faker->paragraph,
                        'school_id' => null,
                        'parent_id' => null,
                    ]);

                    $parent->assignRole('user');

                    // Add profile photo for parent
                    $parent->addMedia($profileImages->random()->getPathname())
                        ->preservingOriginal()
                        ->toMediaCollection('profile_photos');

                    $parentId = $parent->id;
                }

                // Alternate between HIGH_SCHOOL_STUDENT and HIGH_SCHOOL_GRADUATE life stages for positive athletes
                $lifeStage = $a % 2 === 0
                    ? LifeStage::HIGH_SCHOOL_STUDENT->value
                    : LifeStage::HIGH_SCHOOL_GRADUATE->value;

                $athlete = User::query()->updateOrCreate(
                    ['email' => $athleteEmail],
                    [
                        'first_name' => "Athlete",
                        'last_name' => "{$i}-{$a}",
                        'profile_type' => ProfileType::POSITIVE_ATHLETE->value,
                        'life_stage' => $lifeStage,
                        'handle' => "athlete{$i}_{$a}",
                        'password' => Hash::make('password'),
                        'recruiter_enabled' => $faker->boolean,
                        'phone' => $faker->phoneNumber,
                        'street_address_1' => $faker->streetAddress,
                        'street_address_2' => $faker->boolean(30) ? $faker->secondaryAddress : null,
                        'city' => $faker->city,
                        'state_code' => $athleteCounty->state_code,
                        'zip' => $faker->postcode,
                        'county_id' => $athleteCounty->id,
                        'graduation_year' => $faker->numberBetween(2024, 2027),
                        'gpa' => $faker->randomFloat(2, 2.0, 4.0),
                        'class_rank' => $faker->boolean(70) ? $faker->randomElement(['top 10%', 'top 20%', 'top 50%', 'top 1%']) : null,
                        'gender' => $faker->randomElement(['male', 'female']),
                        'height_in_inches' => $faker->numberBetween(60, 80),
                        'weight' => $faker->numberBetween(120, 250),
                        'twitter' => $faker->boolean(50) ? $faker->userName : null,
                        'linkedin' => $faker->boolean(50) ? 'https://www.linkedin.com/in/' . $faker->userName : null,
                        'instagram' => $faker->boolean(50) ? $faker->userName : null,
                        'facebook' => $faker->boolean(50) ? $faker->userName : null,
                        'content' => $faker->paragraph,
                        'school_id' => $school->id,
                        'parent_id' => $parentId,
                    ]
                );

                $athlete->assignRole('user');

                // Create nomination for this positive_athlete user (business logic requirement)
                $this->createNominationForUser($athlete, $faker, $school, NominationType::ATHLETE, $sports);

                // Add profile photo for athlete
                $athlete->addMedia($profileImages->random()->getPathname())
                    ->preservingOriginal()
                    ->toMediaCollection('profile_photos');

                // Add random tags to athlete
                $athleteTags = Tag::query()
                    ->inRandomOrder()
                    ->take($faker->numberBetween(2, 5))
                    ->get();
                $athlete->tags()->attach($athleteTags);

                // Add athlete to the team
                $team->users()->attach($athlete->id, ['type' => TeamUserType::Student->value]);

                // Assign athletes alternately to groups
                if ($a % 2 == 0) {
                    $groupA->users()->attach($athlete->id);
                } else {
                    $groupB->users()->attach($athlete->id);
                }
            }

            // Optionally, create a few team invites (for demonstration):
            TeamInvite::query()->create([
                'team_id' => $team->id,
                'name' => 'Invited Athlete 1',
                'email' => "invited_athlete1_{$i}@example.com",
                'groups' => json_encode(['Group A']),
            ]);
        }

        $this->command->info("Two coaches, their schools, teams, groups, assistants, athletes (with some parents), and invites have been seeded.");

        // Add College Athletes
        for ($c = 1; $c <= 4; $c++) {
            $collegeEmail = "college_athlete{$c}@example.com";
            $collegeCounty = $counties->random();

            // Alternate between COLLEGE_STUDENT and COLLEGE_GRADUATE life stages
            $lifeStage = $c % 2 === 0
                ? LifeStage::COLLEGE_STUDENT->value
                : LifeStage::COLLEGE_GRADUATE->value;

            $collegeAthlete = User::query()->updateOrCreate(
                ['email' => $collegeEmail],
                [
                    'first_name' => "College",
                    'last_name' => "Athlete {$c}",
                    'handle' => "college_{$c}",
                    'profile_type' => ProfileType::COLLEGE_ATHLETE->value,
                    'life_stage' => $lifeStage,
                    'password' => Hash::make('password'),
                    'recruiter_enabled' => true,
                    'phone' => $faker->phoneNumber,
                    'street_address_1' => $faker->streetAddress,
                    'street_address_2' => $faker->boolean(30) ? $faker->secondaryAddress : null,
                    'city' => $faker->city,
                    'state_code' => $collegeCounty->state_code,
                    'zip' => $faker->postcode,
                    'county_id' => $collegeCounty->id,
                    // College athletes have graduation years in the past or very near future
                    'graduation_year' => $faker->numberBetween(2022, 2024),
                    'gpa' => $faker->randomFloat(2, 2.5, 4.0),
                    'class_rank' => $faker->boolean(50) ? $faker->randomElement(['top 10%', 'top 20%', 'top 50%']) : null,
                    'gender' => $faker->randomElement(['male', 'female']),
                    'height_in_inches' => $faker->numberBetween(65, 82),
                    'weight' => $faker->numberBetween(140, 260),
                    'twitter' => $faker->boolean(60) ? $faker->userName : null,
                    'linkedin' => $faker->boolean(70) ? 'https://www.linkedin.com/in/' . $faker->userName : null,
                    'instagram' => $faker->boolean(70) ? $faker->userName : null,
                    'facebook' => $faker->boolean(50) ? $faker->userName : null,
                    'content' => $faker->paragraph,
                    'school_id' => null,
                    'parent_id' => null,
                ]
            );

            $collegeAthlete->assignRole('user');

            // Add profile photo for college athlete
            $collegeAthlete->addMedia($profileImages->random()->getPathname())
                ->preservingOriginal()
                ->toMediaCollection('profile_photos');

            // Add random tags to college athlete
            $collegeTags = Tag::query()
                ->inRandomOrder()
                ->take($faker->numberBetween(2, 5))
                ->get();
            $collegeAthlete->tags()->attach($collegeTags);
        }

        $this->command->info("College athletes have been seeded.");

        // After seeding coaches, athletes, etc., add professional users
        for ($p = 1; $p <= 4; $p++) {
            $profEmail = "professional{$p}@example.com";
            $profCounty = $counties->random(); // Professionals can be anywhere

            // Always use PROFESSIONAL life stage for professional users
            $professional = User::query()->updateOrCreate(
                ['email' => $profEmail],
                [
                    'first_name' => "Professional",
                    'last_name' => "User {$p}",
                    'handle' => "pro_{$p}",
                    'profile_type' => ProfileType::PROFESSIONAL->value,
                    'life_stage' => LifeStage::PROFESSIONAL->value,
                    'password' => Hash::make('password'),
                    'recruiter_enabled' => true,
                    'phone' => $faker->phoneNumber,
                    'street_address_1' => $faker->streetAddress,
                    'street_address_2' => $faker->boolean(30) ? $faker->secondaryAddress : null,
                    'city' => $faker->city,
                    'state_code' => $profCounty->state_code,
                    'zip' => $faker->postcode,
                    'county_id' => $profCounty->id,
                    'graduation_year' => null,
                    'gpa' => null,
                    'class_rank' => null,
                    'gender' => $faker->randomElement(['male', 'female']),
                    'height_in_inches' => null,
                    'weight' => null,
                    'twitter' => $faker->boolean(50) ? $faker->userName : null,
                    'linkedin' => $faker->boolean(50) ? 'https://www.linkedin.com/in/' . $faker->userName : null,
                    'instagram' => $faker->boolean(50) ? $faker->userName : null,
                    'facebook' => $faker->boolean(50) ? $faker->userName : null,
                    'content' => $faker->paragraph,
                    'school_id' => null,
                    'parent_id' => null,
                ]
            );

            $professional->assignRole('user');

            // Add profile photo for professional
            $professional->addMedia($profileImages->random()->getPathname())
                ->preservingOriginal()
                ->toMediaCollection('profile_photos');
        }

        $this->command->info("Professional users have been seeded.");

        // Create athletics directors using our service
        $userService = app(UserService::class);

        for ($ad = 1; $ad <= 3; $ad++) {
            $adEmail = "ad{$ad}@example.com";
            $adCounty = $counties->random();

            // Create a school for this AD
            $school = School::query()->create([
                'name' => "School AD{$ad}",
                'county_id' => $adCounty->id,
                'address' => $faker->streetAddress,
                'city' => $faker->city,
                'zip_code' => $faker->postcode,
            ]);

            // Create AD using our service to ensure proper profile creation
            $athleticsDirector = $userService->createAthleticsDirector([
                'email' => $adEmail,
                'first_name' => "Athletics",
                'last_name' => "Director {$ad}",
                'handle' => "ad_{$ad}",
                'profile_type' => ProfileType::ATHLETICS_DIRECTOR->value,
                'password' => Hash::make('password'),
                'recruiter_enabled' => true,
                'phone' => $faker->phoneNumber,
                'street_address_1' => $faker->streetAddress,
                'street_address_2' => $faker->boolean(30) ? $faker->secondaryAddress : null,
                'city' => $faker->city,
                'state_code' => $adCounty->state_code,
                'zip' => $faker->postcode,
                'county_id' => $adCounty->id,
                'graduation_year' => null,
                'gpa' => null,
                'class_rank' => null,
                'gender' => $faker->randomElement(['male', 'female']),
                'height_in_inches' => null,
                'weight' => null,
                'twitter' => $faker->boolean(50) ? $faker->userName : null,
                'linkedin' => $faker->boolean(50) ? 'https://www.linkedin.com/in/' . $faker->userName : null,
                'instagram' => $faker->boolean(50) ? $faker->userName : null,
                'facebook' => $faker->boolean(50) ? $faker->userName : null,
                'content' => $faker->paragraph,
                'school_id' => $school->id,
                'parent_id' => null,
            ]);

            $athleticsDirector->assignRole('user');

            // Add profile photo for AD
            $athleticsDirector->addMedia($profileImages->random()->getPathname())
                ->preservingOriginal()
                ->toMediaCollection('profile_photos');

            // Add random tags to AD
            $adTags = Tag::query()
                ->inRandomOrder()
                ->take($faker->numberBetween(2, 4))
                ->get();
            $athleticsDirector->tags()->attach($adTags);
        }

        $this->command->info("Athletics directors have been seeded.");
    }

    /**
     * Create a nomination for a positive_athlete or positive_coach user (business logic requirement)
     */
    private function createNominationForUser(User $user, $faker, School $school, NominationType $nominationType, $sports): void
    {
        // Get a random sport
        if ($sports->isEmpty()) {
            $this->command->warn("No sports found for nomination. Skipping nomination creation for {$user->email}");
            return;
        }
        $sport = $sports->random();

        // Generate realistic nomination data
        $relationships = ['Coach', 'Teacher', 'Principal', 'Athletic Director'];
        if ($nominationType === NominationType::ATHLETE) {
            $relationships[] = 'Parent';
        }
        $relationship = $faker->randomElement($relationships);

        // Generate appropriate status based on relationship
        $status = $this->generateNominationStatus($relationship, $faker);

        // Generate grade and graduation year for athletes
        $grade = null;
        $graduationYear = null;
        if ($nominationType === NominationType::ATHLETE) {
            $currentGrade = $faker->randomElement(['9', '10', '11', '12']);
            $grade = (int) $currentGrade; // Store as integer instead of "Grade X" format

            // Calculate graduation year based on grade (assuming current academic year)
            $currentYear = now()->year;
            $academicYearStart = now()->month >= 8 ? $currentYear : $currentYear - 1;
            $graduationYear = $academicYearStart + (12 - (int)$currentGrade) + 1;
        }

        // Generate realistic location data
        $counties = \App\Models\County::with('market.region')->inRandomOrder()->take(10)->get();
        $county = $counties->isNotEmpty() ? $counties->random() : null;

        // Generate additional sports (30% chance for multi-sport athletes)
        $sport2 = $faker->boolean(30) && $sports->count() > 1 ? $sports->where('name', '!=', $sport->name)->random()->name : null;

        // Filter out null values from the exclusion array to prevent database issues
        $excludedSports = array_filter([$sport->name, $sport2], fn($value) => $value !== null);
        $sport3 = $faker->boolean(15) && $sports->count() > 2 ? $sports->whereNotIn('name', $excludedSports)->random()->name : null;

        // Generate parent/guardian info for minors (if athlete and grade indicates high school)
        $parentInfo = [];
        if ($nominationType === NominationType::ATHLETE && $grade) {
            $parentInfo = [
                'parent_guardian_first_name' => $faker->firstName,
                'parent_guardian_last_name' => $faker->lastName,
                'parent_guardian_email' => $faker->email,
                'parent_guardian_phone' => $faker->phoneNumber,
            ];
        }

        Nomination::query()->create([
            // Original fields
            'nominator_email' => $faker->email,
            'nominator_first_name' => $faker->firstName,
            'nominator_last_name' => $faker->lastName,
            'email' => $user->email,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'school_name' => $school->name,
            'school_id' => $school->id,
            'sport' => $sport->name,
            'relationship' => $relationship,
            'note' => $faker->paragraph,
            'status' => $status,
            'type' => $nominationType->value,
            'ai_score' => $this->generateAiScore($status, $relationship, $faker),

            // Location Information
            'state_code' => $county?->state_code ?? $faker->stateAbbr,
            'county' => $county?->name ?? $faker->city . ' County',

            // Enhanced Contact Information
            'nominee_phone' => $faker->phoneNumber,
            'nominator_phone' => $faker->phoneNumber,

            // Multiple Sports Support
            'sport_2' => $sport2,
            'sport_3' => $sport3,
            'other_sport' => $faker->boolean(10) ? $faker->randomElement(['Adapted Basketball', 'Unified Soccer', 'Special Olympics Track']) : null,

            // Demographic & Academic Information
            'gender' => $faker->randomElement(['Male', 'Female', 'Non-binary', 'Prefer not to say']),
            'grade' => $grade,

            // Parent/Guardian Information
            ...$parentInfo,

            // Social Media & Digital Presence
            'instagram_handle' => $faker->boolean(60) ? '@' . $faker->userName : null,
            'twitter_handle' => $faker->boolean(40) ? '@' . $faker->userName : null,

            // Marketing & Attribution Data
            'how_did_you_hear' => $faker->randomElement([
                'School counselor',
                'Coach recommendation',
                'Social media',
                'Friend/family',
                'School newsletter',
                'Athletic department',
                'Website search'
            ]),
            'referral_source_name' => $faker->boolean(50) ? $faker->company : null,

            // Processing Workflow Enhancement
            'processing_status' => $faker->randomElement(['received', 'validated', 'invited', 'onboarded']),
            'processed_at' => $faker->boolean(70) ? $faker->dateTimeBetween('-3 months', 'now') : null,

            // JotForm Integration Metadata
            'jotform_submission_id' => $faker->uuid,
            'jotform_form_id' => $faker->randomElement(['************', '************', '************']),
            'location_resolution_notes' => $faker->boolean(20) ? [
                'status' => 'resolved',
                'method' => 'automated_geocoding',
                'confidence' => $faker->randomFloat(2, 0.8, 1.0)
            ] : null,
        ]);
    }

    private function generateNominationStatus(string $relationship, $faker): string
    {
        // Distribution weights for different statuses
        $weights = [
            NominationStatus::PENDING_AD_VERIFICATION->value => 30,
            NominationStatus::AD_VERIFIED->value => 40,
            NominationStatus::NOMINEE_NOTIFIED->value => 20,
            NominationStatus::NOMINEE_ACKNOWLEDGED->value => 10,
        ];

        // Adjust weights based on relationship
        if ($relationship === 'Parent') {
            $weights[NominationStatus::PENDING_AD_VERIFICATION->value] += 20;
            $weights[NominationStatus::AD_VERIFIED->value] -= 10;
        } elseif (in_array($relationship, ['Coach', 'Athletic Director'])) {
            $weights[NominationStatus::AD_VERIFIED->value] += 20;
            $weights[NominationStatus::PENDING_AD_VERIFICATION->value] -= 10;
        }

        // Create weighted array
        $weighted = [];
        foreach ($weights as $status => $weight) {
            for ($i = 0; $i < max(1, $weight); $i++) {
                $weighted[] = $status;
            }
        }

        return $faker->randomElement($weighted);
    }

    private function generateAiScore(string $status, string $relationship, $faker): int
    {
        // Base ranges for different nomination statuses
        $ranges = [
            NominationStatus::PENDING_AD_VERIFICATION->value => [20, 55],
            NominationStatus::AD_VERIFIED->value => [45, 75],
            NominationStatus::NOMINEE_NOTIFIED->value => [55, 80],
            NominationStatus::NOMINEE_ACKNOWLEDGED->value => [65, 90],
        ];

        $range = $ranges[$status] ?? [30, 70];

        // Adjust for relationship
        if ($relationship === 'Parent') {
            $range[0] = max(20, $range[0] - 10);
            $range[1] = min(80, $range[1] - 5);
        } elseif (in_array($relationship, ['Coach', 'Athletic Director'])) {
            $range[0] = min(90, $range[0] + 10);
            $range[1] = min(95, $range[1] + 10);
        }

        return $faker->numberBetween($range[0], $range[1]);
    }
}
