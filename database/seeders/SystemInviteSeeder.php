<?php

namespace Database\Seeders;

use App\Enums\ProfileType;
use App\Models\SystemInvite;
use Illuminate\Database\Seeder;

class SystemInviteSeeder extends Seeder
{
    public function run(): void
    {
        // Create pending nominations
        SystemInvite::factory(10)
            ->nomination()
            ->create();

        // Create completed nominations
        SystemInvite::factory(5)
            ->nomination()
            ->completed()
            ->create();

        // Create expired nominations
        SystemInvite::factory(3)
            ->nomination()
            ->expired()
            ->create();

        // Create coach referrals
        SystemInvite::factory(5)
            ->state([
                'type' => ProfileType::POSITIVE_COACH->value,
            ])
            ->create();
    }
}
