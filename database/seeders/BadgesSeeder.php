<?php

namespace Database\Seeders;

use App\Models\Badge;
use Illuminate\Database\Seeder;

class BadgesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $badges = [
            [
                'name' => 'Rookie',
                'module_requirement' => 1,
                'achieved_asset_url' => 'https://prod.spline.design/uhdYHBpKStE9D527/scene.splinecode',
                'unachieved_asset_url' => 'https://prod.spline.design/8CgEnmxCFEW2MgWU/scene.splinecode'
            ],
            [
                'name' => 'JV',
                'module_requirement' => 5,
                'achieved_asset_url' => 'https://prod.spline.design/cJitc3i6dpnpOlnR/scene.splinecode',
                'unachieved_asset_url' => 'https://prod.spline.design/r3QwkYd5pY3uFurn/scene.splinecode'
            ],
            [
                'name' => 'Varsity',
                'module_requirement' => 15,
                'achieved_asset_url' => 'https://prod.spline.design/iSvuGEzFsqwnkZ-r/scene.splinecode',
                'unachieved_asset_url' => 'https://prod.spline.design/34Nr4-jb6IhhpHzk/scene.splinecode'
            ],
            [
                'name' => 'Starter',
                'module_requirement' => 25,
                'achieved_asset_url' => 'https://prod.spline.design/R35GZvR2rLjYqO1v/scene.splinecode',
                'unachieved_asset_url' => 'https://prod.spline.design/uKCsiipqkGGxpOgI/scene.splinecode'
            ],
            [
                'name' => 'Playmaker',
                'module_requirement' => 35,
                'achieved_asset_url' => 'https://prod.spline.design/6Kl4URx9MK3WVjVp/scene.splinecode',
                'unachieved_asset_url' => 'https://prod.spline.design/jJe5La-6BiathdxO/scene.splinecode'
            ],
            [
                'name' => 'Vice-Captain',
                'module_requirement' => 50,
                'achieved_asset_url' => 'https://prod.spline.design/GttNcYiAnjM4CMb0/scene.splinecode',
                'unachieved_asset_url' => 'https://prod.spline.design/CazYZJgf1YtkNKo5/scene.splinecode'
            ],
            [
                'name' => 'Captain',
                'module_requirement' => 65,
                'achieved_asset_url' => 'https://prod.spline.design/8pLYCBcbFzlIOAfa/scene.splinecode',
                'unachieved_asset_url' => 'https://prod.spline.design/ZhUhglL5DFmpbe-L/scene.splinecode'
            ],
            [
                'name' => 'MVP',
                'module_requirement' => 80,
                'achieved_asset_url' => 'https://prod.spline.design/tCMEgi6NoVKV2DKG/scene.splinecode',
                'unachieved_asset_url' => 'https://prod.spline.design/LOKWt6QtNCSXu1qk/scene.splinecode'
            ],
            [
                'name' => 'All-State',
                'module_requirement' => 100,
                'achieved_asset_url' => 'https://prod.spline.design/k2x3vQinkPLxHJjG/scene.splinecode',
                'unachieved_asset_url' => 'https://prod.spline.design/tiQAbXCDX-4XEnHi/scene.splinecode'
            ],
            [
                'name' => 'Champion',
                'module_requirement' => 125,
                'achieved_asset_url' => 'https://prod.spline.design/8omWaawon80HptYH/scene.splinecode',
                'unachieved_asset_url' => 'https://prod.spline.design/facIbYGUu4HEW3qc/scene.splinecode'
            ],
            [
                'name' => 'Legend',
                'module_requirement' => 150,
                'achieved_asset_url' => 'https://prod.spline.design/e6vIounYUOoCnhwz/scene.splinecode',
                'unachieved_asset_url' => 'https://prod.spline.design/GB-e9HGLKe3YDeQ7/scene.splinecode'
            ],
            [
                'name' => 'Hall of Fame',
                'module_requirement' => 175,
                'achieved_asset_url' => 'https://prod.spline.design/ked4JLCvVLqqDhyF/scene.splinecode',
                'unachieved_asset_url' => 'https://prod.spline.design/OyCqtSb6hqCpYjzp/scene.splinecode'
            ],
            [
                'name' => 'Goat Level 1',
                'module_requirement' => 200,
                'achieved_asset_url' => 'https://prod.spline.design/OciUrGBbIoBPpRYa/scene.splinecode',
                'unachieved_asset_url' => 'https://prod.spline.design/iwwCaSHnySx0uEAo/scene.splinecode'
            ],
            [
                'name' => 'Goat Level 2',
                'module_requirement' => 225,
                'achieved_asset_url' => 'https://prod.spline.design/fYxSG80JfZwi5dlP/scene.splinecode',
                'unachieved_asset_url' => 'https://prod.spline.design/ZBQXtEMNrQXEvsVv/scene.splinecode'
            ],
            [
                'name' => 'Goat Level 3',
                'module_requirement' => 250,
                'achieved_asset_url' => 'https://prod.spline.design/pbS6xLUT4FDZK6BC/scene.splinecode',
                'unachieved_asset_url' => 'https://prod.spline.design/hMhYTP63XOd5Qnv5/scene.splinecode'
            ],
            [
                'name' => 'Goat Level 4',
                'module_requirement' => 275,
                'achieved_asset_url' => 'https://prod.spline.design/kjNZwmjFshQQuma5/scene.splinecode',
                'unachieved_asset_url' => 'https://prod.spline.design/E7qlND7MbRqxHd26/scene.splinecode'
            ],
            [
                'name' => 'Goat Level 5',
                'module_requirement' => 300,
                'achieved_asset_url' => 'https://prod.spline.design/fh4KgfHo99UN-Qyf/scene.splinecode',
                'unachieved_asset_url' => 'https://prod.spline.design/AqtPdY112XTTRjTf/scene.splinecode'
            ],
        ];

        foreach ($badges as $badge) {
            Badge::query()->updateOrCreate(['name' => $badge['name']], $badge);
        }
    }
}
