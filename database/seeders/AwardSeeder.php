<?php

namespace Database\Seeders;

use App\Enums\AwardType;
use App\Models\Award;
use App\Models\Region;
use App\Models\Market;
use App\Models\State;
use App\Models\SubRegion;
use Database\Faker\Providers\HtmlProvider;
use Faker\Factory as Faker;
use Illuminate\Database\Seeder;

class AwardSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Set up faker with HTML provider
        $faker = Faker::create();
        $faker->addProvider(new HtmlProvider($faker));

        // Get geographical data
        $regions = Region::all();
        $markets = Market::all();
        $states = State::all();
        $subRegions = SubRegion::all();

        if ($regions->isEmpty() || $markets->isEmpty() || $states->isEmpty()) {
            $this->command->warn('Required related data (regions, markets, states) not found. Awards not seeded.');
            return;
        }

        $currentYear = date('Y');

        // Create awards for current year (higher distribution)
        for ($i = 0; $i < 15; $i++) {
            // Create one award of each type for the current year
            foreach (AwardType::cases() as $awardType) {
                // Randomly assign geographical data
                $region = $regions->random();
                $market = $markets->random();
                $state = $states->random();
                $subRegion = rand(0, 1) ? $subRegions->random() : null;

                Award::create([
                    'name' => "{$awardType->value} Award {$currentYear} #" . ($i+1),
                    'details' => $this->generateAwardDetails($faker, $awardType->value, $currentYear, $region->name, true),
                    'type' => $awardType,
                    'region_id' => $region->id,
                    'market_id' => $market->id,
                    'state_id' => $state->code,
                    'subregion_id' => $subRegion?->id,
                    'year' => $currentYear,
                    'is_active' => true,
                ]);
            }
        }

        // Create awards for past years
        for ($yearOffset = 1; $yearOffset <= 5; $yearOffset++) {
            $pastYear = $currentYear - $yearOffset;

            // Use fewer awards for past years
            $pastAwardsCount = max(5, 15 - ($yearOffset * 2)); // 13, 11, 9, 7, 5

            for ($i = 0; $i < $pastAwardsCount; $i++) {
                // Use a random subset of award types for older years
                $awardTypes = collect(AwardType::cases())
                              ->random($yearOffset >= 3 ? 1 : 2);

                foreach ($awardTypes as $awardType) {
                    // Randomly assign geographical data
                    $region = $regions->random();
                    $market = $markets->random();
                    $state = $states->random();
                    $subRegion = rand(0, 1) ? $subRegions->random() : null;

                    Award::create([
                        'name' => "{$awardType->value} Award {$pastYear} #" . ($i+1),
                        'details' => $this->generateAwardDetails($faker, $awardType->value, $pastYear, $region->name, false),
                        'type' => $awardType,
                        'region_id' => $region->id,
                        'market_id' => $market->id,
                        'state_id' => $state->code,
                        'subregion_id' => $subRegion?->id,
                        'year' => $pastYear,
                        'is_active' => false, // Past awards are inactive
                    ]);
                }
            }
        }

        // Create awards for future years (fewer than current year)
        for ($yearOffset = 1; $yearOffset <= 2; $yearOffset++) {
            $futureYear = $currentYear + $yearOffset;

            // Use fewer awards for future years
            $futureAwardsCount = 10 - ($yearOffset * 3); // 7, 4

            for ($i = 0; $i < $futureAwardsCount; $i++) {
                // For future years, only create regional awards as those are typically planned first
                // Randomly assign geographical data
                $region = $regions->random();
                $market = $markets->random();
                $state = $states->random();
                $subRegion = rand(0, 1) ? $subRegions->random() : null;

                Award::create([
                    'name' => "Regional Award {$futureYear} #" . ($i+1),
                    'details' => $this->generateFutureAwardDetails($faker, $futureYear, $region->name),
                    'type' => AwardType::REGIONAL,
                    'region_id' => $region->id,
                    'market_id' => $market->id,
                    'state_id' => $state->code,
                    'subregion_id' => $subRegion?->id,
                    'year' => $futureYear,
                    'is_active' => true,
                ]);
            }
        }
    }

    /**
     * Generate rich text content for award details
     *
     * @param \Faker\Generator $faker
     * @param string $awardType
     * @param int $year
     * @param string $regionName
     * @param bool $isCurrent
     * @return string
     */
    private function generateAwardDetails($faker, $awardType, $year, $regionName, $isCurrent = true): string
    {
        $verb = $isCurrent ? 'recognizes' : 'recognized';
        $demonstrateVerb = $isCurrent ? 'demonstrate' : 'demonstrated';

        $intro = "<h3>About the {$awardType} Award {$year}</h3>";
        $intro .= "<p>This award {$verb} outstanding student athletes who {$demonstrateVerb} exceptional positive character, leadership, and athletic achievement in the {$regionName} region" . ($isCurrent ? "" : " during {$year}") . ".</p>";

        // Add a dynamic paragraph using the HTML provider
        $description = $faker->htmlRichText(1);

        // Add qualification criteria as bullet points
        $criteria = "<h4>Award Criteria</h4><ul>";
        $criteria .= "<li>" . ucfirst($verb) . " excellence in character and leadership</li>";
        $criteria .= "<li>" . ($isCurrent ? "Celebrates" : "Celebrated") . " academic and athletic achievements</li>";
        $criteria .= "<li>" . ($isCurrent ? "Honors" : "Honored") . " community service and positive impact</li>";

        // Add 1-2 additional random criteria
        $additionalCriteria = [
            "Shows exemplary sportsmanship on and off the field",
            "Maintains high academic standards while excelling in athletics",
            "Demonstrates leadership qualities among peers",
            "Actively participates in community service initiatives",
            "Overcomes challenges with a positive attitude",
            "Acts as a role model for younger athletes",
            "Exhibits commitment to personal growth and team success"
        ];

        $randomCriteria = array_rand(array_flip($additionalCriteria), rand(1, 2));
        if (is_array($randomCriteria)) {
            foreach ($randomCriteria as $criterion) {
                $criteria .= "<li>{$criterion}</li>";
            }
        } else {
            $criteria .= "<li>{$randomCriteria}</li>";
        }
        $criteria .= "</ul>";

        // Add a closing statement
        $closing = "<p>Recipients of this award {$demonstrateVerb} the core values of Positive Athlete both on and off the field.</p>";

        return $intro . $description . $criteria . $closing;
    }

    /**
     * Generate rich text content for future award details
     *
     * @param \Faker\Generator $faker
     * @param int $year
     * @param string $regionName
     * @return string
     */
    private function generateFutureAwardDetails($faker, $year, $regionName): string
    {
        $intro = "<h3>About the Regional Award {$year}</h3>";
        $intro .= "<p>This upcoming award will recognize outstanding student athletes who demonstrate exceptional positive character, leadership, and athletic achievement in the {$regionName} region for the {$year} season.</p>";

        // Add a dynamic paragraph about the future award
        $description = $faker->htmlRichText(1);

        // Add projected criteria
        $criteria = "<h4>Award Criteria</h4><ul>";
        $criteria .= "<li>Will recognize excellence in character and leadership</li>";
        $criteria .= "<li>Will celebrate academic and athletic achievements</li>";
        $criteria .= "<li>Will honor community service and positive impact</li>";

        // Add 1-2 additional random criteria for future awards
        $additionalCriteria = [
            "Will evaluate sportsmanship and fair play",
            "Will consider academic performance alongside athletic achievement",
            "Will look for leadership potential and influence",
            "Will assess community engagement and volunteer work",
            "Will recognize perseverance and positive attitude",
            "Will highlight mentorship of younger athletes",
            "Will consider growth mindset and continuous improvement"
        ];

        $randomCriteria = array_rand(array_flip($additionalCriteria), rand(1, 2));
        if (is_array($randomCriteria)) {
            foreach ($randomCriteria as $criterion) {
                $criteria .= "<li>{$criterion}</li>";
            }
        } else {
            $criteria .= "<li>{$randomCriteria}</li>";
        }
        $criteria .= "</ul>";

        // Add nomination information
        $closing = "<p>Nominations for this award will open closer to the {$year} season. " . $faker->sentence(rand(10, 15)) . "</p>";

        return $intro . $description . $criteria . $closing;
    }
}
