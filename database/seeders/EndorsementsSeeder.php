<?php

namespace Database\Seeders;

use App\Models\Endorsement;
use Illuminate\Database\Seeder;

class EndorsementsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $endorsements = [
            ['name' => 'Optimistic', 'icon' => 'sun-bright'],
            ['name' => 'Puts Team First', 'icon' => 'people-group'],
            ['name' => 'Encouraging', 'icon' => 'smile'],
            ['name' => 'Respectful', 'icon' => 'handshake'],
            ['name' => 'Admits Imperfections', 'icon' => 'puzzle-piece'],
            ['name' => 'True Heart for Others', 'icon' => 'heart'],
            ['name' => 'Embraces Service', 'icon' => 'hand-holding-seedling'],
        ];

        foreach ($endorsements as $endorsement) {
            Endorsement::updateOrCreate(
                ['name' => $endorsement['name']],
                ['name' => $endorsement['name'], 'icon' => $endorsement['icon']]
            );
        }
    }
}
