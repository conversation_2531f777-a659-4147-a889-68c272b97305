<?php

namespace Database\Seeders;

use App\Enums\ConnectionStatus;
use App\Enums\ProfileType;
use App\Models\Connection;
use App\Models\Message;
use App\Models\Opportunity;
use App\Models\Organization;
use App\Models\Region;
use App\Models\User;
use App\Services\MessageService;
use App\Services\NetworkingService;
use Database\Seeders\Traits\HasOrganizationNames;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Faker\Factory as Faker;
use Faker\Generator;
use App\Enums\OpportunityType;
use App\Enums\OpportunitySubtype;
use App\Enums\OpportunityLocationType;
use App\Enums\OpportunityTerm;
use Illuminate\Database\Eloquent\Collection;
use Database\Faker\Providers\HtmlProvider;

class SponsorUserSeeder extends Seeder
{
    use HasOrganizationNames;

    /**
     * The Faker instance.
     */
    protected Generator $faker;

    /**
     * Constructor to set up the faker instance with the HTML provider
     */
    public function __construct()
    {
        $this->faker = Faker::create();
        $this->faker->addProvider(new HtmlProvider($this->faker));
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // The faker is already initialized in the constructor, no need to create it again

        // Get all regions
        $regions = Region::all();

        if ($regions->isEmpty()) {
            $this->command->error('No regions found. Please run RegionalHierarchySeeder first.');
            return;
        }

        // Create organizations
        $organizations = [];
        for ($i = 0; $i < 25; $i++) {
            $organizations[] = $this->createOrganizationFromList('sponsor');
        }

        // Create sponsor users
        $sponsors = [];
        foreach ($organizations as $organization) {
            // Create 3-5 sponsors per organization
            $numSponsors = $this->faker->numberBetween(3, 5);

            for ($i = 0; $i < $numSponsors; $i++) {
                $user = User::create([
                    'first_name' => $this->faker->firstName,
                    'last_name' => $this->faker->lastName,
                    'email' => $this->faker->unique()->safeEmail,
                    'password' => Hash::make('password'),
                    'profile_type' => ProfileType::SPONSOR->value,
                    'state_code' => $this->faker->stateAbbr,
                    'city' => $this->faker->city,
                    'phone' => $this->faker->phoneNumber,
                    'street_address_1' => $this->faker->streetAddress,
                    'zip' => $this->faker->postcode,
                    'region_id' => $regions->random()->id,
                ]);

                // Associate with organization using the new relationship
                $organization->sponsors()->attach($user->id, [
                    'role' => $i === 0 ? 'primary_contact' : 'sponsor',
                    'metadata' => json_encode(['is_original_sponsor' => $i === 0])
                ]);

                // Add profile photo
                $user->addMediaFromUrl('https://i.pravatar.cc/300?u=' . $user->id)
                    ->toMediaCollection('avatar');

                // Assign user role
                $user->assignRole('user');

                $sponsors[] = $user;
            }

            // Add organization logo
            $organization->addMediaFromUrl('https://ui-avatars.com/api/?name=' . urlencode($organization->name) . '&background=random')
                ->toMediaCollection('logo');
        }

        // Update existing sponsors without regions
        User::where('profile_type', ProfileType::SPONSOR->value)
            ->whereNull('region_id')
            ->each(function ($user) use ($regions) {
                $user->update(['region_id' => $regions->random()->id]);
            });

        // Ensure all existing sponsors have organizations
        $this->ensureAllSponsorsHaveOrganizations();

        $this->command->info('Created ' . count($organizations) . ' sponsor users with organizations.');

        // Create opportunities for each sponsor
        $this->createOpportunities($sponsors);

        // Create connections and messages between sponsors and athletes
        $this->createConnectionsAndMessages($sponsors);
    }

    /**
     * Ensure all sponsors have organizations
     */
    private function ensureAllSponsorsHaveOrganizations(): void
    {
        // Get all sponsors without organizations
        $sponsorsWithoutOrgs = User::where('profile_type', ProfileType::SPONSOR->value)
            ->whereDoesntHave('organizations')
            ->get();

        foreach ($sponsorsWithoutOrgs as $sponsor) {
            // Create organization
            $organization = $this->createOrganizationFromList('sponsor');

            // Associate sponsor with organization
            $organization->sponsors()->attach($sponsor->id, [
                'role' => 'primary_contact',
                'metadata' => json_encode(['is_original_sponsor' => true])
            ]);

            // Add organization logo
            $organization->addMediaFromUrl('https://ui-avatars.com/api/?name=' . urlencode($organization->name) . '&background=random')
                ->toMediaCollection('logo');
        }
    }

    /**
     * Create opportunities for sponsors
     */
    private function createOpportunities(array $sponsors): void
    {
        $this->command->info('Creating opportunities for sponsors...');

        $statuses = ['listed', 'unlisted'];
        $terms = [
            OpportunityTerm::Indefinite->value,
            OpportunityTerm::ThreeToFourYears->value,
            OpportunityTerm::OneToTwoYears->value,
            OpportunityTerm::LessThanOneYear->value
        ];
        $locationTypes = [
            OpportunityLocationType::Remote->value,
            OpportunityLocationType::Hybrid->value,
            OpportunityLocationType::Onsite->value
        ];

        foreach ($sponsors as $sponsor) {
            // Get the first organization for this sponsor
            $organization = $sponsor->organizations()->first();

            if (!$organization) {
                continue;
            }

            // Create 2-4 opportunities per sponsor
            $count = $this->faker->numberBetween(2, 4);

            for ($i = 0; $i < $count; $i++) {
                $type = $this->faker->randomElement([
                    OpportunityType::Education->value,
                    OpportunityType::Employment->value
                ]);

                $subtype = match($type) {
                    OpportunityType::Education->value => $this->faker->randomElement([
                        OpportunitySubtype::ContinuingEducation->value,
                        OpportunitySubtype::DegreeProgram->value,
                        OpportunitySubtype::CertificateProgram->value
                    ]),
                    OpportunityType::Employment->value => $this->faker->randomElement([
                        OpportunitySubtype::FullTimeJob->value,
                        OpportunitySubtype::PartTimeJob->value,
                        OpportunitySubtype::ShortTermContracting->value,
                        OpportunitySubtype::Internship->value,
                        OpportunitySubtype::Apprenticeship->value
                    ])
                };

                $opportunity = Opportunity::create([
                    'organization_id' => $organization->id,
                    'title' => ucfirst($type) . ' ' . ucfirst(str_replace('_', ' ', $subtype)) . ' at ' . $organization->name,
                    'description' => $this->faker->htmlRichText(3),
                    'type' => $type,
                    'subtype' => $subtype,
                    'status' => $this->faker->randomElement($statuses),
                    'city' => $this->faker->city,
                    'state_code' => $this->faker->stateAbbr,
                    'details' => $this->faker->htmlRichText(2),
                    'qualifications' => $this->faker->htmlRichText(2),
                    'responsibilities' => $this->faker->htmlRichText(2),
                    'benefits' => $this->faker->htmlRichText(2),
                    'apply_url' => $this->faker->url,
                    'term' => $this->faker->randomElement($terms),
                    'location_type' => $this->faker->randomElement($locationTypes),
                    'created_at' => $this->faker->dateTimeBetween('-6 months', '-1 day'),
                    'updated_at' => $this->faker->dateTimeBetween('-6 months', '-1 day'),
                ]);

                // Attach random industries (1-3)
                $industryCount = $this->faker->numberBetween(1, 3);
                $industryIds = \App\Models\Industry::inRandomOrder()
                    ->limit($industryCount)
                    ->pluck('id')
                    ->unique()
                    ->values()
                    ->toArray();
                $opportunity->industries()->sync($industryIds);

                // Attach random interests (1-3)
                $interestCount = $this->faker->numberBetween(1, 3);
                $interestIds = \App\Models\Interest::inRandomOrder()
                    ->limit($interestCount)
                    ->pluck('id')
                    ->unique()
                    ->values()
                    ->toArray();
                $opportunity->interests()->sync($interestIds);
            }
        }

        $this->command->info('Created opportunities for sponsors.');
    }

    /**
     * Create connections and messages between sponsors and athletes
     */
    private function createConnectionsAndMessages(array $sponsors): void
    {
        $this->command->info('Creating connections and messages between sponsors and athletes...');

        // Get athletes
        $athletes = User::where('profile_type', ProfileType::POSITIVE_ATHLETE->value)
            ->inRandomOrder()
            ->limit(10)
            ->get();

        if ($athletes->isEmpty()) {
            $this->command->warn('No athletes found. Skipping connection and message creation.');
            return;
        }

        $messageService = app(MessageService::class);
        $networkingService = app(NetworkingService::class);

        foreach ($sponsors as $sponsor) {
            // Connect with 2-5 random athletes
            $athleteCount = $this->faker->numberBetween(2, 5);
            $selectedAthletes = $athletes->random($athleteCount);

            foreach ($selectedAthletes as $athlete) {
                try {
                    // Create connection
                    $connection = Connection::firstOrCreate(
                        [
                            'requester_id' => $sponsor->id,
                            'recipient_id' => $athlete->id,
                        ],
                        [
                            'status' => ConnectionStatus::ACCEPTED->value,
                        ]
                    );

                    // If connection already existed, make sure it's accepted
                    if ($connection->status !== ConnectionStatus::ACCEPTED->value) {
                        $connection->update(['status' => ConnectionStatus::ACCEPTED->value]);
                    }

                    // Create 3-10 messages between them
                    $messageCount = $this->faker->numberBetween(3, 10);

                    for ($i = 0; $i < $messageCount; $i++) {
                        // Determine sender and recipient
                        if ($this->faker->boolean(70)) {
                            // 70% chance sponsor is the sender
                            $senderId = $sponsor->id;
                            $recipientId = $athlete->id;
                        } else {
                            $senderId = $athlete->id;
                            $recipientId = $sponsor->id;
                        }

                        // Create message
                        $message = Message::create([
                            'sender_id' => $senderId,
                            'recipient_id' => $recipientId,
                            'content' => $this->faker->sentence(10),
                            'created_at' => $this->faker->dateTimeBetween('-3 months', 'now'),
                        ]);

                        // 80% chance message is read if recipient is sponsor
                        if ($recipientId === $sponsor->id && $this->faker->boolean(80)) {
                            $message->update(['read_at' => $this->faker->dateTimeBetween($message->created_at, 'now')]);
                        }

                        // 60% chance message is read if recipient is athlete
                        if ($recipientId === $athlete->id && $this->faker->boolean(60)) {
                            $message->update(['read_at' => $this->faker->dateTimeBetween($message->created_at, 'now')]);
                        }
                    }
                } catch (\Exception $e) {
                    $this->command->error("Error creating connection/messages between sponsor {$sponsor->id} and athlete {$athlete->id}: " . $e->getMessage());
                }
            }
        }

        $this->command->info('Created connections and messages between sponsors and athletes.');
    }
}
