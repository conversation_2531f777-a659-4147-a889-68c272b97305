<?php

namespace Database\Seeders;

use App\Enums\ProfileType;
use App\Enums\UiRegion;
use App\Models\Advertisement;
use App\Models\AdvertisementProfileType;
use App\Models\AdvertisementUiRegion;
use App\Models\County;
use App\Models\Market;
use App\Models\Region;
use App\Models\State;
use App\Models\SubRegion;
use App\Models\User;
use Database\Faker\Providers\HtmlProvider;
use Illuminate\Database\Seeder;
use Faker\Factory as Faker;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileCannotBeAdded;

class AdvertisementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faker = Faker::create();
        $faker->addProvider(new HtmlProvider($faker));

        // Get sponsor users to associate with advertisements
        $sponsors = User::query()
            ->where('profile_type', ProfileType::SPONSOR->value)
            ->get();

        if ($sponsors->isEmpty()) {
            $this->command->warn('No sponsor users found. Please run SponsorUserSeeder first.');
            return;
        }

        // Get geographic entities to target
        $regions = Region::all();
        $markets = Market::all();
        $subRegions = SubRegion::all();
        $counties = County::all();
        $states = State::all();

        // Advertisement campaign names
        $campaigns = [
            'Summer Special',
            'Back to School',
            'Championship Season',
            'Scholarship Opportunity',
            'College Readiness',
            'Athletic Performance',
            'Team Builder',
            'Recruiting Special',
            'Sports Equipment',
            'Training Program',
            'Academic Excellence',
            'Coach Support',
            'Athletic Director Resources',
            'Parent Resources',
            'Student Athlete Special',
            'Graduate Success',
            'Year-End Promotion',
            'Spring Training',
            'Fall Sports',
            'Winter Athletics',
        ];

        // CTA text options
        $ctaOptions = [
            'Learn More',
            'Sign Up Now',
            'Get Started',
            'Apply Today',
            'Contact Us',
            'Download Info',
            'View Details',
            'Join Program',
            'Get a Demo',
            'Schedule a Call',
            'Register Now',
            'Start Free Trial',
            'Request Info',
            'See More',
            'Visit Website',
        ];

        // Create a diverse set of advertisements (25 total)
        $this->command->info('Creating advertisements...');

        // Create 25 advertisements with varying configurations
        for ($i = 0; $i < 25; $i++) {
            $sponsor = $sponsors->random();
            $isListed = $faker->boolean(80); // 80% chance of being active
            $impressions = $isListed ? $faker->numberBetween(0, 10000) : 0;
            $clicks = $isListed ? min($impressions, $faker->numberBetween(0, $impressions)) : 0;

            DB::beginTransaction();

            try {
                // Create the advertisement
                $advertisement = Advertisement::create([
                    'name' => $sponsor->first_name . '\'s ' . Arr::random($campaigns) . ' Campaign',
                    'user_id' => $sponsor->id,
                    'is_listed' => $isListed,
                    'copy' => $faker->htmlRichText(2),
                    'cta_text' => Arr::random($ctaOptions),
                    'cta_url' => $faker->url(),
                    'impressions' => $impressions,
                    'clicks' => $clicks,
                ]);

                // Assign profile types (1-3 random profile types)
                $profileTypes = Arr::random([
                    ProfileType::POSITIVE_ATHLETE,
                    ProfileType::POSITIVE_COACH,
                    ProfileType::ATHLETICS_DIRECTOR,
                    ProfileType::PARENT,
                    ProfileType::COLLEGE_ATHLETE,
                    ProfileType::PROFESSIONAL,
                ], $faker->numberBetween(1, 3));

                foreach ($profileTypes as $profileType) {
                    AdvertisementProfileType::create([
                        'advertisement_id' => $advertisement->id,
                        'profile_type' => $profileType->value,
                    ]);
                }

                // Assign UI regions (1-2 random UI regions)
                $uiRegions = Arr::random([
                    UiRegion::DASHBOARD,
                    UiRegion::PROFILE,
                    UiRegion::SIDEBAR,
                    UiRegion::HEADER,
                    UiRegion::FOOTER,
                    UiRegion::FEED,
                    UiRegion::OPPORTUNITY_DETAILS,
                    UiRegion::X_FACTOR,
                    UiRegion::MESSAGING,
                ], $faker->numberBetween(1, 2));

                foreach ($uiRegions as $uiRegion) {
                    AdvertisementUiRegion::create([
                        'advertisement_id' => $advertisement->id,
                        'ui_region' => $uiRegion->value,
                    ]);
                }

                // Geographical targeting (40% chance for each type)

                // Regions (40% chance)
                if ($faker->boolean(40) && $regions->isNotEmpty()) {
                    $advertisement->regions()->attach(
                        $regions->random($faker->numberBetween(1, min(3, $regions->count())))->pluck('id')->toArray()
                    );
                }

                // Markets (40% chance)
                if ($faker->boolean(40) && $markets->isNotEmpty()) {
                    $advertisement->markets()->attach(
                        $markets->random($faker->numberBetween(1, min(3, $markets->count())))->pluck('id')->toArray()
                    );
                }

                // SubRegions (40% chance)
                if ($faker->boolean(40) && $subRegions->isNotEmpty()) {
                    $advertisement->subRegions()->attach(
                        $subRegions->random($faker->numberBetween(1, min(3, $subRegions->count())))->pluck('id')->toArray()
                    );
                }

                // Counties (40% chance)
                if ($faker->boolean(40) && $counties->isNotEmpty()) {
                    $advertisement->counties()->attach(
                        $counties->random($faker->numberBetween(1, min(5, $counties->count())))->pluck('id')->toArray()
                    );
                }

                // States (40% chance)
                if ($faker->boolean(40) && $states->isNotEmpty()) {
                    $advertisement->states()->attach(
                        $states->random($faker->numberBetween(1, min(5, $states->count())))->pluck('code')->toArray()
                    );
                }

                // Add logo and background images
                $this->addAdvertisementMedia($advertisement, $faker);

                // Add tags (50% chance)
                if ($faker->boolean(50)) {
                    $tagCount = $faker->numberBetween(1, 4);
                    $tags = [];

                    for ($t = 0; $t < $tagCount; $t++) {
                        // Create or get tags with the proper ID-based reference
                        $tagName = $faker->words(2, true);
                        $tag = \App\Models\Tag::firstOrCreate(['name' => $tagName]);
                        $tags[] = $tag->id;
                    }

                    $advertisement->syncTags($tags);
                }

                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                $this->command->error("Error creating advertisement: " . $e->getMessage());
            }
        }

        $this->command->info('Successfully created ' . Advertisement::count() . ' advertisements');
    }

    /**
     * Add media to an advertisement.
     *
     * @param Advertisement $advertisement
     * @param \Faker\Generator $faker
     * @return void
     */
    private function addAdvertisementMedia(Advertisement $advertisement, $faker): void
    {
        try {
            // Add logo (using placeholder images)
            $logoWidth = $faker->numberBetween(200, 400);
            $logoHeight = $faker->numberBetween(150, 300);
            $logoUrl = "https://picsum.photos/{$logoWidth}/{$logoHeight}?random=" . $faker->numberBetween(1, 1000);

            $advertisement->addMediaFromUrl($logoUrl)
                ->toMediaCollection('logo');

            // Add background (using placeholder images)
            $bgWidth = $faker->numberBetween(800, 1200);
            $bgHeight = $faker->numberBetween(400, 600);
            $bgUrl = "https://picsum.photos/{$bgWidth}/{$bgHeight}?random=" . $faker->numberBetween(1, 1000);

            $advertisement->addMediaFromUrl($bgUrl)
                ->toMediaCollection('background');
        } catch (FileCannotBeAdded $e) {
            // Log the error but continue with seeding
            logger()->error("Could not add media to advertisement: " . $e->getMessage());
        }
    }
}
