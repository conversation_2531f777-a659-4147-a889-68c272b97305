<?php

namespace Database\Seeders;

use App\Models\Industry;
use Illuminate\Database\Seeder;

class IndustriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $industries = [
            'Construction',
            'Healthcare',
            'Technology',
            'Manufacturing',
            'Retail',
            'Education',
            'Finance & Banking',
            'Hospitality',
            'Transportation & Logistics',
            'Media & Entertainment',
        ];

        foreach ($industries as $name) {
            Industry::updateOrCreate(['name' => $name], ['name' => $name]);
        }
    }
}
