<?php

namespace Database\Seeders;

use App\Enums\ProfileType;
use App\Enums\NominationType;
use App\Enums\NominationStatus;
use App\Models\Badge;
use App\Models\Module;
use App\Models\Nomination;
use App\Models\School;
use App\Models\Sport;
use App\Models\User;
use App\Models\County;
use Carbon\Carbon;
use Faker\Factory as Faker;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class XFactorLeaderboardTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faker = Faker::create();

        // Check if we have the required data
        $modulesCount = Module::query()->where('published', true)->count();
        if ($modulesCount < 10) {
            $this->command->warn("Not enough modules found. Make sure XFactorModuleSeeder was run first.");
            return;
        }

        // Get all counties and group by state
        $counties = County::query()->with('market.region')->get();
        if ($counties->isEmpty()) {
            $this->command->warn("No counties found. Run RegionalHierarchySeeder first.");
            return;
        }

        // Get available states with counties
        $stateCountMap = [];
        foreach ($counties as $county) {
            if (!isset($stateCountMap[$county->state_code])) {
                $stateCountMap[$county->state_code] = 0;
            }
            $stateCountMap[$county->state_code]++;
        }

        // If no states have counties, we can't proceed
        if (empty($stateCountMap)) {
            $this->command->error("No states found with counties. Make sure RegionalHierarchySeeder was run properly.");
            return;
        }

        // Create a distribution based on available states
        $stateDistribution = [];
        $totalCounties = array_sum($stateCountMap);

        foreach ($stateCountMap as $stateCode => $countyCount) {
            // Weight distribution by number of counties in each state
            $stateDistribution[$stateCode] = $countyCount / $totalCounties;
        }

        $this->command->info("Found " . count($stateDistribution) . " states with a total of {$totalCounties} counties.");

        // Log the state distribution for debugging
        foreach ($stateDistribution as $stateCode => $weight) {
            $this->command->info("  {$stateCode}: " . round($weight * 100, 2) . "% (" . $stateCountMap[$stateCode] . " counties)");
        }

        $sports = Sport::all();
        if ($sports->isEmpty()) {
            $this->command->warn("No sports found. Run SportsSeeder first.");
            return;
        }

        // Get all badges for assignment
        $badges = Badge::all();
        if ($badges->isEmpty()) {
            $this->command->warn("No badges found. Make sure badges are seeded.");
        }

        // Get all modules that will be completed
        $modules = Module::query()->where('published', true)->get();

        // Get or create profile images
        $profileImages = collect(File::files(database_path('images')))
            ->filter(fn($file) => str_starts_with($file->getFilename(), 'card-'))
            ->values();

        if ($profileImages->isEmpty()) {
            $this->command->warn("No profile images found in database/images directory. Users will be created without profile images.");
        }

        // Distribution parameters for testing
        $totalUsers = 200;
        $graduationYears = [2022, 2023, 2024, 2025, 2026];
        $yearDistribution = [0.15, 0.15, 0.3, 0.25, 0.15]; // Weight for each year

        // Module completion distribution
        $moduleCompletionRanges = [
            // Low completions (10% of users)
            ['min' => 0, 'max' => 5, 'weight' => 0.1],
            // Medium-low completions (20% of users)
            ['min' => 5, 'max' => 15, 'weight' => 0.2],
            // Medium completions (40% of users)
            ['min' => 15, 'max' => 40, 'weight' => 0.4],
            // Medium-high completions (20% of users)
            ['min' => 40, 'max' => 80, 'weight' => 0.2],
            // High completions (10% of users)
            ['min' => 80, 'max' => 150, 'weight' => 0.1],
        ];

        // Starting user number (to avoid conflicts with existing test data)
        $userNumber = 100;

        // Find the highest existing test user number to avoid email collisions
        $existingTestUsers = User::query()
            ->where('email', 'like', '<EMAIL>')
            ->orderBy('email', 'desc')
            ->first();

        if ($existingTestUsers) {
            // Extract number from email like "<EMAIL>"
            preg_match('/athlete_test_(\d+)@/', $existingTestUsers->email, $matches);
            if (!empty($matches[1])) {
                $userNumber = (int)$matches[1];
            }
        }

        // Create users based on distribution
        $this->command->info("Generating {$totalUsers} users for leaderboard testing...");
        $progressBar = $this->command->getOutput()->createProgressBar($totalUsers);
        $progressBar->start();

        $createdUsers = 0;
        while ($createdUsers < $totalUsers) {
            // Determine graduation year based on distribution
            $yearIndex = $this->getWeightedRandomIndex($yearDistribution);
            $graduationYear = $graduationYears[$yearIndex];

            // Determine state based on distribution
            $stateCode = $this->getWeightedRandomKey($stateDistribution);

            // Get a random county from the selected state
            $stateCounties = $counties->where('state_code', $stateCode);
            if ($stateCounties->isEmpty()) {
                $this->command->warn("No counties found for state {$stateCode} despite having it in our distribution. Skipping.");
                continue;
            }

            $county = $stateCounties->random();

            // Get or create a school in this county
            $school = School::query()
                ->where('county_id', $county->id)
                ->inRandomOrder()
                ->first();

            if (!$school) {
                $school = School::query()->create([
                    'name' => "{$county->name} High School",
                    'county_id' => $county->id,
                    'region_id' => $county->market->region->id ?? null,
                    'address' => $faker->streetAddress,
                    'city' => $faker->city,
                    'zip_code' => $faker->postcode,
                ]);
            }

            // Create the user
            $userNumber++;
            $firstName = $faker->firstName;
            $lastName = $faker->lastName;
            $user = User::query()->create([
                'first_name' => $firstName,
                'last_name' => $lastName,
                'email' => "athlete_test_{$userNumber}@example.com",
                'handle' => "athlete_test_{$userNumber}",
                'profile_type' => ProfileType::POSITIVE_ATHLETE->value,
                'password' => Hash::make('password'),
                'recruiter_enabled' => $faker->boolean(30),
                'phone' => $faker->phoneNumber,
                'street_address_1' => $faker->streetAddress,
                'street_address_2' => $faker->boolean(30) ? $faker->secondaryAddress : null,
                'city' => $county->name,
                'state_code' => $stateCode,
                'zip' => $faker->postcode,
                'county_id' => $county->id,
                'region_id' => $county->market->region->id ?? null,
                'graduation_year' => $graduationYear,
                'gpa' => $faker->randomFloat(2, 2.0, 4.0),
                'class_rank' => $faker->boolean(70) ? $faker->randomElement(['top 10%', 'top 20%', 'top 50%', 'top 1%']) : null,
                'gender' => $faker->randomElement(['male', 'female']),
                'height_in_inches' => $faker->numberBetween(60, 80),
                'weight' => $faker->numberBetween(120, 250),
                'twitter' => $faker->boolean(50) ? $faker->userName : null,
                'linkedin' => $faker->boolean(50) ? 'https://www.linkedin.com/in/' . $faker->userName : null,
                'instagram' => $faker->boolean(50) ? $faker->userName : null,
                'facebook' => $faker->boolean(50) ? $faker->userName : null,
                'school_id' => $school->id,
                'created_at' => now()->subDays($faker->numberBetween(30, 365)),
            ]);

            // Assign the user role
            $user->assignRole('user');

            // Add profile photo if available
            if ($profileImages->isNotEmpty()) {
                $user->addMedia($profileImages->random()->getPathname())
                    ->preservingOriginal()
                    ->toMediaCollection('profile_photos');
            }

            // Assign random sports (1-3)
            $userSports = $sports->random($faker->numberBetween(1, 3));
            $user->sports()->syncWithoutDetaching($userSports->pluck('id'));

            // Create nomination for this positive_athlete user (business logic requirement)
            $this->createNominationForUser($user, $faker, $userSports, $school, $counties);

            // Determine module completion range based on distribution
            $rangeIndex = $this->getWeightedRandomIndex(
                array_column($moduleCompletionRanges, 'weight')
            );
            $range = $moduleCompletionRanges[$rangeIndex];

            // Number of modules to complete
            $numModulesToComplete = min(
                $faker->numberBetween($range['min'], $range['max']),
                $modules->count()
            );

            // Complete modules
            if ($numModulesToComplete > 0) {
                // Randomly select modules to complete
                $modulesToComplete = $modules->random($numModulesToComplete);

                // Create date ranges for completions (more recent for current year, older for past years)
                $yearsSinceGraduation = max(0, date('Y') - $graduationYear);
                $maxDaysAgo = 365 + ($yearsSinceGraduation * 180); // More days ago for older graduation years
                $minDaysAgo = max(1, $yearsSinceGraduation * 30);  // More recent for current students

                foreach ($modulesToComplete as $module) {
                    // Create module completion with completion date
                    $completedAt = now()->subDays($faker->numberBetween($minDaysAgo, $maxDaysAgo));

                    DB::table('module_user')->insert([
                        'user_id' => $user->id,
                        'module_id' => $module->id,
                        'completed_at' => $completedAt,
                        'started_at' => $completedAt->copy()->subMinutes($faker->numberBetween(5, 60)),
                        'created_at' => $completedAt->copy()->subMinutes($faker->numberBetween(5, 60)),
                        'updated_at' => $completedAt,
                    ]);
                }
            }

            // Assign badges based on module completions
            if ($badges->isNotEmpty()) {
                foreach ($badges as $badge) {
                    if ($badge->module_requirement && $numModulesToComplete >= $badge->module_requirement) {
                        DB::table('badge_user')->insert([
                            'user_id' => $user->id,
                            'badge_id' => $badge->id,
                            'is_achieved' => true,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }
                }
            }

            $createdUsers++;
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->command->newLine(2);
        $this->command->info("Successfully generated {$totalUsers} users with varying module completions for leaderboard testing.");
    }

    /**
     * Get a random index based on weight distribution
     */
    private function getWeightedRandomIndex(array $weights): int
    {
        $sum = array_sum($weights);
        $rand = mt_rand(1, (int)($sum * 100)) / 100;

        $cumulative = 0;
        foreach ($weights as $index => $weight) {
            $cumulative += $weight;
            if ($rand <= $cumulative) {
                return $index;
            }
        }

        return 0; // Fallback to first index
    }

    /**
     * Get a random key based on weight distribution
     */
    private function getWeightedRandomKey(array $weights): string
    {
        $sum = array_sum($weights);
        $rand = mt_rand(1, (int)($sum * 100)) / 100;

        $cumulative = 0;
        foreach ($weights as $key => $weight) {
            $cumulative += $weight;
            if ($rand <= $cumulative) {
                return $key;
            }
        }

        return array_key_first($weights); // Fallback to first key
    }

    /**
     * Create a nomination for a positive_athlete user (business logic requirement)
     */
    private function createNominationForUser(User $user, $faker, $userSports, School $school, $counties): void
    {
        // Generate realistic nomination data
        $relationships = ['Coach', 'Teacher', 'Principal', 'Athletic Director', 'Parent'];
        $relationship = $faker->randomElement($relationships);

        // Generate appropriate status based on relationship
        $status = $this->generateNominationStatus($relationship, $faker);

        // Get a random sport from user's assigned sports
        $sport = $userSports->random();

        // Generate grade for athletes
        $currentGrade = $faker->randomElement(['9', '10', '11', '12']);
        $grade = (int) $currentGrade; // Store as integer instead of "Grade X" format

        // Pick a random county from the pre-loaded collection to avoid repeated queries
        $county = $counties->random();

        $eligibleSport2 = $userSports->where('name', '!=', $sport->name);
        $sport2 = $faker->boolean(30) && $eligibleSport2->isNotEmpty()
            ? $eligibleSport2->random()->name
            : null;
        $sport3 = $faker->boolean(15) && $userSports->count() > 2 ? $userSports->whereNotIn('name', [$sport->name, $sport2])->random()->name : null;

        // Generate parent/guardian info for minors
        $parentInfo = [
            'parent_guardian_first_name' => $faker->firstName,
            'parent_guardian_last_name' => $faker->lastName,
            'parent_guardian_email' => $faker->email,
            'parent_guardian_phone' => $faker->phoneNumber,
        ];

        Nomination::query()->create([
            // Original fields
            'nominator_email' => $faker->email,
            'nominator_first_name' => $faker->firstName,
            'nominator_last_name' => $faker->lastName,
            'email' => $user->email,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'school_name' => $school->name,
            'school_id' => $school->id,
            'sport' => $sport->name,
            'relationship' => $relationship,
            'note' => $faker->paragraph,
            'status' => $status,
            'type' => NominationType::ATHLETE->value,
            'ai_score' => $this->generateAiScore($status, $relationship, $faker),

            // Location Information
            'state_code' => $county?->state_code ?? $faker->stateAbbr,
            'county' => $county?->name ?? $faker->city . ' County',

            // Enhanced Contact Information
            'nominee_phone' => $faker->phoneNumber,
            'nominator_phone' => $faker->phoneNumber,

            // Multiple Sports Support
            'sport_2' => $sport2,
            'sport_3' => $sport3,
            'other_sport' => $faker->boolean(10) ? $faker->randomElement(['Adapted Basketball', 'Unified Soccer', 'Special Olympics Track']) : null,

            // Demographic & Academic Information
            'gender' => $faker->randomElement(['Male', 'Female', 'Non-binary', 'Prefer not to say']),
            'grade' => $grade,

            // Parent/Guardian Information
            ...$parentInfo,

            // Social Media & Digital Presence
            'instagram_handle' => $faker->boolean(60) ? '@' . $faker->userName : null,
            'twitter_handle' => $faker->boolean(40) ? '@' . $faker->userName : null,

            // Marketing & Attribution Data
            'how_did_you_hear' => $faker->randomElement([
                'School counselor',
                'Coach recommendation',
                'Social media',
                'Friend/family',
                'School newsletter',
                'Athletic department',
                'Website search'
            ]),
            'referral_source_name' => $faker->boolean(50) ? $faker->company : null,

            // Processing Workflow Enhancement
            'processing_status' => $faker->randomElement(['received', 'validated', 'invited', 'onboarded']),
            'processed_at' => $faker->boolean(70) ? $faker->dateTimeBetween('-3 months', 'now') : null,

            // JotForm Integration Metadata
            'jotform_submission_id' => $faker->uuid,
            'jotform_form_id' => $faker->randomElement(['************', '************', '************']),
            'location_resolution_notes' => $faker->boolean(20) ? [
                'status' => 'resolved',
                'method' => 'automated_geocoding',
                'confidence' => $faker->randomFloat(2, 0.8, 1.0)
            ] : null,
        ]);
    }

    private function generateNominationStatus(string $relationship, $faker): string
    {
        // Distribution weights for different statuses
        $weights = [
            NominationStatus::PENDING_AD_VERIFICATION->value => 30,
            NominationStatus::AD_VERIFIED->value => 40,
            NominationStatus::NOMINEE_NOTIFIED->value => 20,
            NominationStatus::NOMINEE_ACKNOWLEDGED->value => 10,
        ];

        // Adjust weights based on relationship
        if ($relationship === 'Parent') {
            $weights[NominationStatus::PENDING_AD_VERIFICATION->value] += 20;
            $weights[NominationStatus::AD_VERIFIED->value] -= 10;
        } elseif (in_array($relationship, ['Coach', 'Athletic Director'])) {
            $weights[NominationStatus::AD_VERIFIED->value] += 20;
            $weights[NominationStatus::PENDING_AD_VERIFICATION->value] -= 10;
        }

        // Create weighted array
        $weighted = [];
        foreach ($weights as $status => $weight) {
            for ($i = 0; $i < max(1, $weight); $i++) {
                $weighted[] = $status;
            }
        }

        return $faker->randomElement($weighted);
    }

    private function generateAiScore(string $status, string $relationship, $faker): int
    {
        // Base ranges for different nomination statuses
        $ranges = [
            NominationStatus::PENDING_AD_VERIFICATION->value => [20, 55],
            NominationStatus::AD_VERIFIED->value => [45, 75],
            NominationStatus::NOMINEE_NOTIFIED->value => [55, 80],
            NominationStatus::NOMINEE_ACKNOWLEDGED->value => [65, 90],
        ];

        $range = $ranges[$status] ?? [30, 70];

        // Adjust for relationship
        if ($relationship === 'Parent') {
            $range[0] = max(20, $range[0] - 10);
            $range[1] = min(80, $range[1] - 5);
        } elseif (in_array($relationship, ['Coach', 'Athletic Director'])) {
            $range[0] = min(90, $range[0] + 10);
            $range[1] = min(95, $range[1] + 10);
        }

        return $faker->numberBetween($range[0], $range[1]);
    }
}
