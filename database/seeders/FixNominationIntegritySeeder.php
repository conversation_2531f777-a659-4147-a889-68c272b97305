<?php

namespace Database\Seeders;

use App\Enums\NominationType;
use App\Enums\ProfileType;
use App\Enums\NominationStatus;
use App\Models\User;
use App\Models\Nomination;
use App\Models\Sport;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Faker\Factory as Faker;

class FixNominationIntegritySeeder extends Seeder
{
    public function run(): void
    {
        $faker = Faker::create();

        $this->command->info('Starting nomination integrity fix...');

        // Step 1: Clean up duplicate nominations
        $this->cleanupDuplicateNominations();

        // Step 2: Get all positive_athlete and positive_coach users without nominations
        $usersWithoutNominations = User::query()
            ->whereIn('profile_type', [
                ProfileType::POSITIVE_ATHLETE->value,
                ProfileType::POSITIVE_COACH->value,
            ])
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('nominations')
                    ->whereColumn('nominations.email', 'users.email');
            })
            ->get();

        $this->command->info("Found {$usersWithoutNominations->count()} users without nominations");

        // Step 3: Create nominations for users without them
        $sports = Sport::all();
        if ($sports->isEmpty()) {
            $this->command->error('No sports found. Run SportsSeeder first.');
            return;
        }

        foreach ($usersWithoutNominations as $user) {
            $this->createNominationForUser($user, $faker, $sports);
        }

        $this->command->info('Nomination integrity fix completed successfully!');
    }

    private function cleanupDuplicateNominations(): void
    {
        $this->command->info('Cleaning up duplicate nominations...');

        // Find nominations with same email, type, and status
        $duplicates = Nomination::query()
            ->select('email', 'type', 'status', DB::raw('COUNT(*) as count'), DB::raw('MIN(id) as keep_id'))
            ->groupBy('email', 'type', 'status')
            ->havingRaw('COUNT(*) > 1')
            ->get();

        $deletedCount = 0;
        foreach ($duplicates as $duplicate) {
            // Keep the first nomination, delete the rest
            $duplicateIds = Nomination::query()
                ->where('email', $duplicate->email)
                ->where('type', $duplicate->type)
                ->where('status', $duplicate->status)
                ->where('id', '!=', $duplicate->keep_id)
                ->pluck('id');

            $deletedCount += Nomination::destroy($duplicateIds);
        }

        $this->command->info("Removed {$deletedCount} duplicate nominations");
    }

    private function createNominationForUser(User $user, $faker, $sports): void
    {
        // Determine nomination type
        $nominationType = match($user->profile_type) {
            ProfileType::POSITIVE_ATHLETE => NominationType::ATHLETE->value,
            ProfileType::POSITIVE_COACH => NominationType::COACH->value,
            default => throw new \Exception("Invalid profile type for nomination: {$user->profile_type->value}"),
        };

        // Get school name (use user's school or generate one)
        $schoolName = $user->school?->name ?? "{$user->city} High School";

        // Get a random sport
        $sport = $sports->random();

        // Generate realistic nomination data
        $relationships = ['Coach', 'Teacher', 'Principal', 'Athletic Director'];
        if ($user->profile_type === ProfileType::POSITIVE_ATHLETE) {
            $relationships[] = 'Parent';
        }

        $relationship = $faker->randomElement($relationships);

        // Generate appropriate status based on relationship
        $status = $this->generateNominationStatus($relationship, $faker);

        // Generate grade and graduation year for athletes
        $grade = null;
        if ($nominationType === NominationType::ATHLETE) {
            $currentGrade = $faker->randomElement(['9', '10', '11', '12']);
            $grade = (int) $currentGrade; // Store as integer instead of "Grade X" format
        }

        // Generate realistic location data
        $counties = \App\Models\County::with('market.region')->inRandomOrder()->take(10)->get();
        $county = $counties->isNotEmpty() ? $counties->random() : null;

        // Generate additional sports (30% chance for multi-sport athletes)
        $sport2 = $faker->boolean(30) && $sports->count() > 1 ? $sports->where('name', '!=', $sport->name)->random()->name : null;
        $sport3 = $faker->boolean(15) && $sports->count() > 2 ? $sports->whereNotIn('name', [$sport->name, $sport2])->random()->name : null;

        // Generate parent/guardian info for minors (if athlete and grade indicates high school)
        $parentInfo = [];
        if ($nominationType === NominationType::ATHLETE && $grade) {
            $parentInfo = [
                'parent_guardian_first_name' => $faker->firstName,
                'parent_guardian_last_name' => $faker->lastName,
                'parent_guardian_email' => $faker->email,
                'parent_guardian_phone' => $faker->phoneNumber,
            ];
        }

        Nomination::query()->create([
            // Original fields
            'nominator_email' => $faker->email,
            'nominator_first_name' => $faker->firstName,
            'nominator_last_name' => $faker->lastName,
            'email' => $user->email,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'school_name' => $schoolName,
            'school_id' => $user->school_id,
            'sport' => $sport->name,
            'relationship' => $relationship,
            'note' => $faker->paragraph,
            'status' => $status,
            'type' => $nominationType,
            'ai_score' => $this->generateAiScore($status, $relationship, $faker),

            // Location Information
            'state_code' => $county?->state_code ?? $faker->stateAbbr,
            'county' => $county?->name ?? $faker->city . ' County',

            // Enhanced Contact Information
            'nominee_phone' => $faker->phoneNumber,
            'nominator_phone' => $faker->phoneNumber,

            // Multiple Sports Support
            'sport_2' => $sport2,
            'sport_3' => $sport3,
            'other_sport' => $faker->boolean(10) ? $faker->randomElement(['Adapted Basketball', 'Unified Soccer', 'Special Olympics Track']) : null,

            // Demographic & Academic Information
            'gender' => $faker->randomElement(['Male', 'Female', 'Non-binary', 'Prefer not to say']),
            'grade' => $grade,

            // Parent/Guardian Information
            ...$parentInfo,

            // Social Media & Digital Presence
            'instagram_handle' => $faker->boolean(60) ? '@' . $faker->userName : null,
            'twitter_handle' => $faker->boolean(40) ? '@' . $faker->userName : null,

            // Marketing & Attribution Data
            'how_did_you_hear' => $faker->randomElement([
                'School counselor',
                'Coach recommendation',
                'Social media',
                'Friend/family',
                'School newsletter',
                'Athletic department',
                'Website search'
            ]),
            'referral_source_name' => $faker->boolean(50) ? $faker->company : null,

            // Processing Workflow Enhancement
            'processing_status' => $faker->randomElement(['received', 'validated', 'invited', 'onboarded']),
            'processed_at' => $faker->boolean(70) ? $faker->dateTimeBetween('-3 months', 'now') : null,

            // JotForm Integration Metadata
            'jotform_submission_id' => $faker->uuid,
            'jotform_form_id' => $faker->randomElement(['************', '************', '************']),
            'location_resolution_notes' => $faker->boolean(20) ? [
                'status' => 'resolved',
                'method' => 'automated_geocoding',
                'confidence' => $faker->randomFloat(2, 0.8, 1.0)
            ] : null,
        ]);

        $this->command->info("Created nomination for {$user->email} ({$user->profile_type->value})");
    }

    private function generateNominationStatus(string $relationship, $faker): string
    {
        // Distribution weights for different statuses
        $weights = [
            NominationStatus::PENDING_AD_VERIFICATION->value => 30,
            NominationStatus::AD_VERIFIED->value => 40,
            NominationStatus::NOMINEE_NOTIFIED->value => 20,
            NominationStatus::NOMINEE_ACKNOWLEDGED->value => 10,
        ];

        // Adjust weights based on relationship
        if ($relationship === 'Parent') {
            $weights[NominationStatus::PENDING_AD_VERIFICATION->value] += 20;
            $weights[NominationStatus::AD_VERIFIED->value] -= 10;
        } elseif (in_array($relationship, ['Coach', 'Athletic Director'])) {
            $weights[NominationStatus::AD_VERIFIED->value] += 20;
            $weights[NominationStatus::PENDING_AD_VERIFICATION->value] -= 10;
        }

        // Create weighted array
        $weighted = [];
        foreach ($weights as $status => $weight) {
            for ($i = 0; $i < max(1, $weight); $i++) {
                $weighted[] = $status;
            }
        }

        return $faker->randomElement($weighted);
    }

    private function generateAiScore(string $status, string $relationship, $faker): int
    {
        // Base ranges for different nomination statuses
        $ranges = [
            NominationStatus::PENDING_AD_VERIFICATION->value => [20, 55],
            NominationStatus::AD_VERIFIED->value => [45, 75],
            NominationStatus::NOMINEE_NOTIFIED->value => [55, 80],
            NominationStatus::NOMINEE_ACKNOWLEDGED->value => [65, 90],
        ];

        $range = $ranges[$status] ?? [30, 70];

        // Adjust for relationship
        if ($relationship === 'Parent') {
            $range[0] = max(20, $range[0] - 10);
            $range[1] = min(80, $range[1] - 5);
        } elseif (in_array($relationship, ['Coach', 'Athletic Director'])) {
            $range[0] = min(90, $range[0] + 10);
            $range[1] = min(95, $range[1] + 10);
        }

        return $faker->numberBetween($range[0], $range[1]);
    }
}
