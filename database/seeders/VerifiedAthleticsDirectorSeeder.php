<?php

namespace Database\Seeders;

use App\Enums\ProfileType;
use App\Models\User;
use App\Models\School;
use App\Models\County;
use App\Models\Tag;
use App\Services\UserService;
use App\Services\AthleticsDirectorProfileService;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Faker\Factory as Faker;
use Illuminate\Support\Facades\File;

class VerifiedAthleticsDirectorSeeder extends Seeder
{
    public function run(): void
    {
        $faker = Faker::create();

        // Get all profile images
        $profileImages = collect(File::files(database_path('images')))
            ->filter(fn($file) => str_starts_with($file->getFilename(), 'card-'))
            ->values();

        if ($profileImages->isEmpty()) {
            $this->command->warn("No profile images found in database/images directory.");
            return;
        }

        // Get available counties and schools
        $counties = County::with('market.region')->get();
        if ($counties->isEmpty()) {
            $this->command->warn("No counties found. Run RegionalHierarchySeeder first.");
            return;
        }

        // Get an admin user to verify the ADs
        $verifier = User::where('profile_type', ProfileType::ADMIN->value)->first();
        if (!$verifier) {
            $this->command->warn("No admin user found. Run AdminUserSeeder first.");
            return;
        }

        // First, create a verified AD for a school that already has a pending AD
        $pendingAD = User::whereHas('athleticsDirectorProfile', function ($query) {
            $query->where('is_verified', false);
        })->first();

        if ($pendingAD && $pendingAD->school) {
            // Create a verified AD for the same school
            $userService = app(UserService::class);
            $adProfileService = app(AthleticsDirectorProfileService::class);

            $verifiedAD = $userService->createAthleticsDirector([
                'email' => "<EMAIL>",
                'first_name' => "Verified",
                'last_name' => "Conflicting AD",
                'handle' => "verified_conflicting_ad",
                'profile_type' => ProfileType::ATHLETICS_DIRECTOR->value,
                'password' => Hash::make('password'),
                'recruiter_enabled' => true,
                'phone' => $faker->phoneNumber,
                'street_address_1' => $faker->streetAddress,
                'street_address_2' => null,
                'city' => $faker->city,
                'state_code' => $pendingAD->school->county->state_code,
                'zip' => $faker->postcode,
                'county_id' => $pendingAD->school->county_id,
                'school_id' => $pendingAD->school_id,
                'content' => "Verified AD for school that already has a pending AD",
            ]);

            $verifiedAD->assignRole('user');

            // Add profile photo
            $verifiedAD->addMedia($profileImages->random()->getPathname())
                ->preservingOriginal()
                ->toMediaCollection('profile_photos');

            // Verify this AD
            $adProfile = $verifiedAD->athleticsDirectorProfile;
            if ($adProfile) {
                $adProfileService->verify($adProfile, $verifier);
            }

            $this->command->info("Created verified AD for school {$pendingAD->school->name} which already has a pending AD.");
        }

        // Get the services we need for the rest of the seeding
        $userService = app(UserService::class);
        $adProfileService = app(AthleticsDirectorProfileService::class);

        // Get all schools that don't have any AD profiles at all
        $availableSchools = School::whereDoesntHave('athleticsDirectorProfiles')->get();

        // Create 25 verified ADs (good number for pagination testing)
        // We'll distribute them across different schools and regions
        for ($i = 1; $i <= 25; $i++) {
            // If we're running out of available schools, create a new one
            if ($availableSchools->isEmpty()) {
                $county = $counties->random();
                $school = School::create([
                    'name' => "Regional " . $faker->city . " High School",
                    'county_id' => $county->id,
                    'address' => $faker->streetAddress,
                    'city' => $faker->city,
                    'zip_code' => $faker->postcode,
                ]);
                $availableSchools = collect([$school]);
            }

            // Select a random available school
            $school = $availableSchools->random();
            // Remove it from the available schools to maintain uniqueness
            $availableSchools = $availableSchools->filter(fn($s) => $s->id !== $school->id);

            if(in_array($school->id, [3, 4, 5])) {
                ray('SELECTING SCHOOL', $school->id);
                ray($availableSchools->pluck('id'));
                ray($school->athleticsDirectorProfiles->pluck('id')->toArray());
            }

            $county = $counties->where('id', $school->county_id)->first();
            $adEmail = "verified.ad{$i}@example.com";

            // Create AD using our service
            $athleticsDirector = $userService->createAthleticsDirector([
                'email' => $adEmail,
                'first_name' => $faker->firstName,
                'last_name' => $faker->lastName,
                'handle' => "verified_ad_{$i}",
                'profile_type' => ProfileType::ATHLETICS_DIRECTOR->value,
                'password' => Hash::make('password'),
                'recruiter_enabled' => true,
                'phone' => $faker->phoneNumber,
                'street_address_1' => $faker->streetAddress,
                'street_address_2' => $faker->boolean(30) ? $faker->secondaryAddress : null,
                'city' => $faker->city,
                'state_code' => $county->state_code,
                'zip' => $faker->postcode,
                'county_id' => $county->id,
                'graduation_year' => null,
                'gpa' => null,
                'class_rank' => null,
                'gender' => $faker->randomElement(['male', 'female']),
                'height_in_inches' => null,
                'weight' => null,
                'twitter' => $faker->boolean(50) ? $faker->userName : null,
                'linkedin' => $faker->boolean(50) ? 'https://www.linkedin.com/in/' . $faker->userName : null,
                'instagram' => $faker->boolean(50) ? $faker->userName : null,
                'facebook' => $faker->boolean(50) ? $faker->userName : null,
                'content' => $faker->paragraph,
                'school_id' => $school->id,
                'parent_id' => null,
            ]);

            $athleticsDirector->assignRole('user');

            // Add profile photo
            $athleticsDirector->addMedia($profileImages->random()->getPathname())
                ->preservingOriginal()
                ->toMediaCollection('profile_photos');

            // Add random tags
            $adTags = Tag::query()
                ->inRandomOrder()
                ->take($faker->numberBetween(2, 4))
                ->get();
            $athleticsDirector->tags()->attach($adTags);

            // Verify the AD profile
            $adProfile = $athleticsDirector->athleticsDirectorProfile;
            if ($adProfile) {
                $adProfileService->verify($adProfile, $verifier);
            }
        }

        $this->command->info("25 verified athletics directors have been seeded across different schools and regions.");
    }
}
