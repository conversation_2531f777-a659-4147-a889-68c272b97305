<?php

namespace Database\Seeders;

use Database\Seeders\AwardSeeder;
use Database\Seeders\ScholarshipSeeder;
use Database\Seeders\WinnerSeeder;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Artisan;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        $this->call([
            StateSeeder::class,
            RoleAndPermissionSeeder::class,
            RegionalHierarchySeeder::class,
            SportsSeeder::class,
            InterestsSeeder::class,
            BadgesSeeder::class,
            IndustriesSeeder::class,
            TopicsSeeder::class,
            EndorsementsSeeder::class,
            ScholarshipSeeder::class,
            AwardSeeder::class,
            InitialUsersSeeder::class,
            RegionalUsersSeeder::class,
            AdditionalUserDataSeeder::class,
            CoursesAndModulesSeeder::class,
            CourseParticipationSeeder::class,
            StandaloneModuleCompletionSeeder::class,
            MeetingsSeeder::class,
            SystemInviteSeeder::class,
            AdminUserSeeder::class,
            VerifiedAthleticsDirectorSeeder::class,
            MessagingSeeder::class,
            SponsorUserSeeder::class,
            SponsorDataSeeder::class,
            OpportunitiesSeeder::class,
            AdvertisementSeeder::class,
            XFactorLeaderboardTestSeeder::class,
            EngagementSeeder::class,
            WinnerSeeder::class,
            // Set public profiles for recommendations after all users are created
            PublicProfileSeeder::class,
            // Fix data integrity issues last
            FixNominationIntegritySeeder::class,
        ]);

        Artisan::call('locations:import-csv', ['--force' => true], $this->command->getOutput());
        Artisan::call('import:xfactor', [], $this->command->getOutput());

        if (App::isLocal()) {
            Artisan::call('opportunities:rebuild-index');
            Artisan::call('networking:rebuild-index');
        }
    }
}
