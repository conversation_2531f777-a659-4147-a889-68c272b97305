<?php

namespace Database\Seeders;

use App\Enums\ConnectionStatus;
use App\Enums\ProfileType;
use App\Models\Connection;
use App\Models\Message;
use App\Models\User;
use App\Models\UserBlock;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MessagingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing messages, connections, and blocks
        Message::query()->delete();
        Connection::query()->forceDelete();
        UserBlock::query()->delete();

        // Track total pinned conversations
        $totalPinnedConversations = 0;

        // Get all positive athlete users
        $athletes = User::query()
            ->where('profile_type', ProfileType::POSITIVE_ATHLETE->value)
            ->get();

        if ($athletes->isEmpty()) {
            Log::info('No positive athletes found for messaging seeder.');
            return;
        }

        // Get other users for conversations (coaches, admins, etc.)
        $otherUsers = User::query()
            ->whereNot('profile_type', ProfileType::POSITIVE_ATHLETE->value)
            ->inRandomOrder()
            ->limit(20)
            ->get();

        if ($otherUsers->isEmpty()) {
            Log::info('No other users found for messaging seeder.');
            return;
        }

        // Create conversations for each athlete
        foreach ($athletes as $athlete) {
            // Determine how many conversations this athlete will have (2-5)
            $conversationCount = rand(2, 5);

            // Select random users for conversations
            $conversationPartners = $otherUsers->random(min($conversationCount, $otherUsers->count()));

            // Add some athlete-to-athlete conversations
            $athletePartners = $athletes->where('id', '!=', $athlete->id)->random(min(2, $athletes->count() - 1));
            $conversationPartners = $conversationPartners->merge($athletePartners);

            // Track pinned conversations for this athlete
            $pinnedCount = 0;
            $maxPinnedConversations = min(2, $conversationPartners->count());

            foreach ($conversationPartners as $partner) {
                // Check if a connection already exists between these users
                $existingConnection = Connection::query()
                    ->where(function ($query) use ($athlete, $partner) {
                        $query->where('requester_id', $athlete->id)
                              ->where('recipient_id', $partner->id);
                    })
                    ->orWhere(function ($query) use ($athlete, $partner) {
                        $query->where('requester_id', $partner->id)
                              ->where('recipient_id', $athlete->id);
                    })
                    ->withTrashed()
                    ->first();

                if ($existingConnection) {
                    continue; // Skip this pair if a connection already exists
                }

                // Determine if this is a blocked conversation (10% chance)
                $isBlocked = rand(1, 10) === 1;

                if ($isBlocked) {
                    // Determine who blocks whom
                    $blockerId = rand(0, 1) === 0 ? $athlete->id : $partner->id;
                    $blockedId = $blockerId === $athlete->id ? $partner->id : $athlete->id;

                    // Create the block
                    UserBlock::factory()->between($blockerId, $blockedId)->create();

                    // Create a rejected connection that is soft deleted
                    $connection = Connection::create([
                        'requester_id' => $blockerId,
                        'recipient_id' => $blockedId,
                        'status' => ConnectionStatus::REJECTED,
                    ]);
                    $connection->delete(); // Soft delete
                } else {
                    // Determine connection status (70% accepted, 30% pending)
                    $isAccepted = rand(1, 10) <= 7;
                    $status = $isAccepted ? ConnectionStatus::ACCEPTED : ConnectionStatus::PENDING;

                    // Determine who initiated the connection
                    $requesterId = rand(0, 1) === 0 ? $athlete->id : $partner->id;
                    $recipientId = $requesterId === $athlete->id ? $partner->id : $athlete->id;

                    // Create the connection
                    Connection::create([
                        'requester_id' => $requesterId,
                        'recipient_id' => $recipientId,
                        'status' => $status,
                    ]);

                    // Determine number of messages in this conversation (3-15)
                    $messageCount = rand(3, 15);

                    // Determine if this conversation should be pinned (for accepted connections)
                    $shouldPinConversation = $isAccepted && $pinnedCount < $maxPinnedConversations && rand(1, 3) === 1;
                    $lastMessage = null;

                    // Create messages
                    for ($i = 0; $i < $messageCount; $i++) {
                        // If connection is pending, only the requester can send messages
                        if (!$isAccepted && $i > 0) {
                            $senderId = $requesterId;
                            $recipientId = $requesterId === $athlete->id ? $partner->id : $athlete->id;
                        } else {
                            // Determine sender and recipient
                            $isAthleteMessage = rand(0, 1) === 0;
                            $senderId = $isAthleteMessage ? $athlete->id : $partner->id;
                            $recipientId = $isAthleteMessage ? $partner->id : $athlete->id;
                        }

                        // Create message with appropriate state
                        $message = Message::factory()
                            ->between($senderId, $recipientId)
                            ->create();

                        // Keep track of the last message for pinning
                        if ($i === $messageCount - 1) {
                            $lastMessage = $message;
                        }

                        // Apply read/unread status (70% read, 30% unread)
                        if (rand(1, 10) <= 7) {
                            $message->update(['read_at' => now()->subMinutes(rand(1, 60))]);
                        }

                        // Apply deletion status (10% deleted by sender, 10% deleted by recipient)
                        $deletionRoll = rand(1, 10);
                        if ($deletionRoll === 1) {
                            $message->update(['deleted_by_sender_at' => now()->subMinutes(rand(1, 60))]);
                        } elseif ($deletionRoll === 2) {
                            $message->update(['deleted_by_recipient_at' => now()->subMinutes(rand(1, 60))]);
                        }
                    }

                    // Pin the conversation if needed
                    if ($shouldPinConversation && $lastMessage) {
                        if ($lastMessage->sender_id === $athlete->id) {
                            // Add to athlete's pinned conversations
                            $pinnedConversations = $athlete->pinned_conversations ?? [];
                            if (!in_array($partner->id, $pinnedConversations)) {
                                $pinnedConversations[] = $partner->id;
                                $athlete->pinned_conversations = $pinnedConversations;
                                $athlete->save();
                            }
                        } else {
                            // Add to partner's pinned conversations
                            $pinnedConversations = $partner->pinned_conversations ?? [];
                            if (!in_array($athlete->id, $pinnedConversations)) {
                                $pinnedConversations[] = $athlete->id;
                                $partner->pinned_conversations = $pinnedConversations;
                                $partner->save();
                            }
                        }
                        $pinnedCount++;
                        $totalPinnedConversations++;
                    }
                }
            }
        }

        // Log completion
        Log::info("Messaging seeder completed successfully. Created {$totalPinnedConversations} pinned conversations.");
    }
}
