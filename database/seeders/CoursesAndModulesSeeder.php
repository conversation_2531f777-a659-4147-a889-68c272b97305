<?php

namespace Database\Seeders;

use App\Models\Course;
use App\Models\Module;
use App\Models\Test;
use App\Models\Question;
use App\Models\Answer;
use App\Models\Topic;
use App\Enums\ModuleType;
use App\Enums\QuestionType;
use Database\Faker\Providers\HtmlProvider;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class CoursesAndModulesSeeder extends Seeder
{
    private const YOUTUBE_VIDEO_URLS = [
        'https://www.youtube.com/watch?v=Pv8VemHl27s',
        'https://youtu.be/Nn0bZ0hBzCw?si=GfTMkbHA_yJrN5ax',
		'https://youtu.be/g031sw47JqA?si=2ktxNp8WkFS02gOj',
		'https://youtu.be/o96ygvuN6ZI?si=arFBl4bWVotCRnpU',
		'https://youtu.be/c4jMXY0OfPI?si=NlcIwtnoCD1w-7rN',
		'https://youtu.be/jHnX-nMl59U?si=Lpjnn527JvsI66zL',
		'https://youtu.be/ZC4eWrzxIqw?si=m_X_Tyw7td_BdtEm',
		'https://youtu.be/QbL0X3B4mjg?si=qn-5BXYq8aUJPj7v',
		'https://youtu.be/3bYDi9wNTac?si=uZKKw31mpWsKselw',
		'https://youtu.be/hvSDbX790rI?si=CGYcPjSWIe2A01pn',
		'https://youtu.be/ODF7qHx9QcU?si=-ovWwOO5w0lbe3nM',
		'https://youtu.be/dTRBnHtHehQ?si=_edhdMGJZ0hyKYgB',
		'https://youtu.be/eyKNsoEEpI0?si=nLpKSWN4PhyMDt5t',
		'https://youtu.be/FnS6sFIs5Tg?si=PRHKzu400Bnbp_Ck',
		'https://youtu.be/WycCdmYBess?si=y43KE-RbnLYlTVE9',
		'https://youtu.be/jPpRo3RKcn0?si=IHPJ3fz7qXejkO45',
		'https://youtu.be/-6XkpBd62P0?si=0of4sdyJe7FqW28s',
		'https://youtu.be/aJCqpJKQ4KI?si=6Ewa85zJ2cQEv_-3',
		'https://youtu.be/tZ8cPONW1DI?si=OvHsufL6QlwSjiPt',
		'https://youtu.be/f2xrJsEP82U?si=YbVoKWYY4IRE9z6r',
		'https://youtu.be/Fm0G-4DEIuM?si=dlR6MVt_Hz4JS1Cs',
		'https://youtu.be/y_MPRteu5ZM?si=CTRboztLCtUE-fUF',
		'https://youtu.be/_iuPewWbp2U?si=ZtjgHkWKMf6lvda6',
		'https://youtu.be/VSceuiPBpxY?si=l6aUZ6GTDi-PatVV',
    ];

    private int $currentVideoIndex = 0;
    public $faker;

    public function __construct()
    {
        $this->faker = fake();
        $this->faker->addProvider(new HtmlProvider($this->faker));
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $topics = Topic::all();
        if ($topics->isEmpty()) {
            $this->command->warn("No topics found. Run TopicsSeeder first.");
            return;
        }

        // Get all sample images
        $sampleImages = collect(File::files(database_path('images')))
            ->filter(fn($file) => str_starts_with($file->getFilename(), 'card-'))
            ->values();

        if ($sampleImages->isEmpty()) {
            $this->command->warn("No sample images found in database/images directory.");
        }

        // Create featured courses for the slider (3 courses)
        $this->createFeaturedCourses($sampleImages, $topics);

        // Create regular courses (17 more courses to reach 20 total)
        $this->createRegularCourses($topics, $sampleImages);

        // Create standalone modules (about 40% of total modules)
        $this->createStandaloneModules($topics, $sampleImages);
    }

    /**
     * Attach a cover image to a model that uses HasMedia.
     */
    private function attachCoverImage($model, $sampleImages): void
    {
        if ($sampleImages->isEmpty()) {
            return;
        }

        $imageFile = $sampleImages->random();
        $model->addMedia($imageFile->getPathname())
            ->preservingOriginal()
            ->toMediaCollection('cover');
    }

    /**
     * Get the next video URL in round-robin fashion
     */
    private function getNextVideoUrl(): string
    {
        $url = self::YOUTUBE_VIDEO_URLS[$this->currentVideoIndex];
        $this->currentVideoIndex = ($this->currentVideoIndex + 1) % count(self::YOUTUBE_VIDEO_URLS);
        return $url;
    }

    /**
     * Create a module with the specified content distribution.
     */
    private function createModule(bool $forCourse = true): Module
    {
        if (!$forCourse) {
            // Standalone modules are always video type
            $name = $this->faker->unique()->sentence(3) . ' - Video Lesson';

            return Module::factory()
                ->published()
                ->state([
                    'name' => $name,
                    'slug' => Str::slug($name),
                    'type' => ModuleType::Video->value,
                    'content' => null,
                    'video_url' => $this->getNextVideoUrl(),
                    'minutes' => $this->faker->numberBetween(5, 45),
                ])
                ->create();
        }

        // For course modules, keep the original mixed content logic
        $hasVideo = $forCourse ? $this->faker->boolean(80) : true;
        $hasContent = $forCourse ? $this->faker->boolean(50) : false;

        // Ensure at least one type of content
        if (!$hasVideo && !$hasContent) {
            // If neither was selected, randomly choose one
            if ($this->faker->boolean()) {
                $hasVideo = true;
            } else {
                $hasContent = true;
            }
        }

        // Generate a descriptive name for the module based on its type
        $name = match(true) {
            $hasContent => $this->faker->unique()->sentence(3) . ' - Learning Guide',
            $hasVideo => $this->faker->unique()->sentence(3) . ' - Video Lesson',
            default => $this->faker->unique()->sentence(3) . ' - Module',
        };

        // Start with a base factory query
        /** @var ModuleFactory $factory */
        $factory = Module::factory()
            ->published()
            ->state([
                'name' => $name,
                'slug' => Str::slug($name),
            ]);

        if ($hasContent) {
            $factory = $factory->article();
        } else if ($hasVideo) {
            $factory = $factory->state([
                'type' => ModuleType::Video->value,
                'content' => null,
                'video_url' => $this->getNextVideoUrl(),
                'minutes' => $this->faker->numberBetween(5, 45),
            ]);
        }

        return $factory->create();
    }

    /**
     * Create featured courses for the slider.
     */
    private function createFeaturedCourses($sampleImages, $topics): void
    {
        $featuredCourses = [
            [
                'title' => 'Peak Performance Leadership Certification',
                'description' => 'Master the art of leadership through proven techniques from top athletes and coaches.',
                'modules_count' => 15,
                'exam_positions' => [4, 8, 15], // Exams at positions 4, 8, and final
                'topic' => $topics->where('name', 'Leadership')->first() ?? $topics->random(),
            ],
            [
                'title' => 'Mental Toughness Mastery',
                'description' => 'Develop unshakeable mental strength with lessons from elite athletes.',
                'modules_count' => 12,
                'exam_positions' => [12], // Only final exam
                'topic' => $topics->where('name', 'Mental Health')->first() ?? $topics->random(),
            ],
            [
                'title' => 'Team Building Excellence',
                'description' => 'Learn to build and lead high-performing teams from championship coaches.',
                'modules_count' => 10,
                'exam_positions' => [10], // Only final exam
                'topic' => $topics->where('name', 'Team Building')->first() ?? $topics->random(),
            ],
        ];

        foreach ($featuredCourses as $index => $courseData) {
            $course = Course::factory()->create([
                'title' => $courseData['title'],
                'description' => $courseData['description'],
                'published' => true,
                'featured' => true,
            ]);

            // Create and attach modules with proper ordering
            $order = 1;
            for ($i = 1; $i <= $courseData['modules_count']; $i++) {
                if (in_array($i, $courseData['exam_positions'])) {
                    // Create exam module
                    $examModule = Module::factory()
                        ->exam()
                        ->published()
                        ->create([
                            'name' => $i === $courseData['modules_count']
                                ? $courseData['title'] . ' - Final Assessment'
                                : $courseData['title'] . ' - Module ' . $i . ' Assessment',
                            'description' => $i === $courseData['modules_count']
                                ? 'Complete this final assessment to earn your certificate.'
                                : 'Complete this assessment to demonstrate your understanding of the previous modules.',
                            'slug' => Str::slug($courseData['title']) . '-assessment-' . $i,
                        ]);
                    $course->modules()->attach($examModule->id, ['order' => $order++]);

                    // Create exam test with more comprehensive questions for the first course
                    if ($index === 0) {
                        $this->createEnhancedExamTest($examModule);
                    } else {
                        $this->createCourseTest($course, $examModule);
                    }
                } else {
                $module = $this->createModule();
                $course->modules()->attach($module->id, ['order' => $order++]);

                // 90% chance of having a module quiz
                if ($this->faker->boolean(90)) {
                    $this->createModuleTest($module);
                }

                // Attach 1-3 topics to each module
                $moduleTopics = $topics->random($this->faker->numberBetween(1, 3));
                $module->topics()->attach($moduleTopics);

                // Attach a cover image to the module
                $this->attachCoverImage($module, $sampleImages);
            }
            }

            // Attach single topic to the course
            $course->topics()->attach($courseData['topic']);

            // Attach a cover image to the course
            $this->attachCoverImage($course, $sampleImages);
        }
    }

    /**
     * Create an enhanced exam test with multiple free response questions.
     */
    private function createEnhancedExamTest(Module $module): void
    {
        $test = new Test([
            'type' => 'exam',
            'testable_type' => Module::class,
            'testable_id' => $module->id,
            'passing_score' => 70,
            'time_limit' => 3600, // 60 minutes for comprehensive exams
            'wait_period' => 20 * 24 * 60 * 60, // 20 days in seconds
        ]);
        $test->save();

        // Create 7 multiple choice questions
        for ($i = 1; $i <= 7; $i++) {
            $question = new Question([
                'test_id' => $test->id,
                'question' => $this->faker->sentence() . "?",
                'type' => QuestionType::MultipleChoice->value
            ]);
            $question->save();

            // Create 4 answers, one correct
            $correctIndex = rand(1, 4);
            for ($a = 1; $a <= 4; $a++) {
                $answer = new Answer([
                    'question_id' => $question->id,
                    'answer' => $this->faker->words(3, true),
                    'is_correct' => $a === $correctIndex
                ]);
                $answer->save();
            }
        }

        // Create 3 free response questions
        $freeResponsePrompts = [
            "Describe a challenging leadership situation you've faced and how you applied the principles from this module to address it. Include specific examples and outcomes.",
            "Analyze the key differences between traditional leadership approaches and the athlete-centered leadership model presented in this module. How would you implement these differences in a real-world scenario?",
            "Based on the concepts covered in this module, develop a detailed action plan for building and maintaining team motivation during high-pressure situations. Include specific strategies and potential challenges.",
        ];

        foreach ($freeResponsePrompts as $prompt) {
            $question = new Question([
                'test_id' => $test->id,
                'question' => $prompt,
                'type' => QuestionType::LongText->value
            ]);
            $question->save();
        }
    }

    /**
     * Create a test for a course module.
     */
    private function createCourseTest(Course $course, Module $module): void
    {
        $test = new Test([
            'type' => 'exam',
            'testable_type' => Module::class,
            'testable_id' => $module->id,
            'passing_score' => 70,
            'time_limit' => 1800, // 30 minutes for regular course exams
            'wait_period' => 20 * 24 * 60 * 60, // 20 days in seconds
        ]);
        $test->save();

        // Create 9 multiple choice questions
        for ($i = 1; $i <= 9; $i++) {
            $question = new Question([
                'test_id' => $test->id,
                'question' => $this->faker->sentence() . "?",
                'type' => QuestionType::MultipleChoice->value
            ]);
            $question->save();

            // Create 4 answers, one correct
            $correctIndex = rand(1, 4);
            for ($a = 1; $a <= 4; $a++) {
                $answer = new Answer([
                    'question_id' => $question->id,
                    'answer' => $this->faker->words(3, true),
                    'is_correct' => $a === $correctIndex
                ]);
                $answer->save();
            }
        }

        // Create 1 free response question
        $question = new Question([
            'test_id' => $test->id,
            'question' => $this->faker->paragraph() . "?",
            'type' => QuestionType::LongText->value
        ]);
        $question->save();
    }

    /**
     * Create regular courses to reach a total of 20 courses.
     */
    private function createRegularCourses($topics, $sampleImages): void
    {
        $existingCount = Course::count();
        $remainingCount = 20 - $existingCount;

        for ($i = 0; $i < $remainingCount; $i++) {
            $course = Course::factory()->create([
                'published' => true,
                'featured' => false,
            ]);

            // Create 5-8 modules for each course
            $moduleCount = $this->faker->numberBetween(5, 8);
            $order = 1;

            for ($j = 0; $j < $moduleCount - 1; $j++) {
                $module = $this->createModule();
                $course->modules()->attach($module->id, ['order' => $order++]);

                // 90% chance of having a module quiz
                if ($this->faker->boolean(90)) {
                    $this->createModuleTest($module);
                }

                // Attach 1-3 topics to each module
                $moduleTopics = $topics->random($this->faker->numberBetween(1, 3));
                $module->topics()->attach($moduleTopics);

                // Attach a cover image to the module
                $this->attachCoverImage($module, $sampleImages);
            }

            // Create and attach final exam module
            $examModule = Module::factory()
                ->exam()
                ->published()
                ->create([
                    'name' => $course->title . ' - Final Assessment',
                    'description' => 'Complete this final assessment to earn your certificate.',
                    'slug' => Str::slug($course->title) . '-final-assessment',
                ]);
            $course->modules()->attach($examModule->id, ['order' => $order]);
            $this->createCourseTest($course, $examModule);

            // Attach single topic to the course
            $course->topics()->attach($topics->random());

            // Attach a cover image to the course
            $this->attachCoverImage($course, $sampleImages);
        }
    }

    /**
     * Create standalone modules (not associated with any course).
     */
    private function createStandaloneModules($topics, $sampleImages): void
    {
        // First, calculate how many modules each topic currently has
        $topicModuleCounts = [];
        foreach ($topics as $topic) {
            $count = Module::query()
                ->whereDoesntHave('courses')
                ->whereHas('topics', function ($query) use ($topic) {
                    $query->where('topics.id', $topic->id);
                })
                ->count();
            $topicModuleCounts[$topic->id] = $count;
        }

        // For each topic, create enough modules to reach minimum of 15
        foreach ($topics as $topic) {
            $currentCount = $topicModuleCounts[$topic->id];
            $neededModules = max(15 - $currentCount, 0);

            $this->command->info("Creating {$neededModules} standalone modules for topic: {$topic->name}");

            for ($i = 0; $i < $neededModules; $i++) {
                $module = $this->createModule(false); // false indicates not for a course

                // 90% chance of having a module quiz
                if ($this->faker->boolean(90)) {
                    $this->createModuleTest($module);
                }

                // Attach only this topic
                $module->topics()->attach([$topic->id]);

                // Attach a cover image to the module
                $this->attachCoverImage($module, $sampleImages);
            }
        }

        $this->command->info("Finished creating standalone modules for all topics");
    }

    /**
     * Create a test for a module.
     */
    private function createModuleTest(Module $module): void
    {
        $test = new Test([
            'type' => 'quiz',
            'testable_type' => Module::class,
            'testable_id' => $module->id,
            // No time limit for quizzes
        ]);
        $test->save();

        $this->createQuestionsForTest($test, 3);
    }

    /**
     * Create questions and answers for a test.
     */
    private function createQuestionsForTest(Test $test, int $questionCount): void
    {
        for ($i = 1; $i <= $questionCount; $i++) {
            $question = new Question([
                'test_id' => $test->id,
                'question' => $this->faker->sentence() . "?",
                'type' => 'multiple_choice'
            ]);
            $question->save();

            // Create 4 answers, one correct
            $correctIndex = rand(1, 4);
            for ($a = 1; $a <= 4; $a++) {
                $answer = new Answer([
                    'question_id' => $question->id,
                    'answer' => $this->faker->words(3, true),
                    'is_correct' => $a === $correctIndex
                ]);
                $answer->save();
            }
        }
    }
}
