<?php

namespace Database\Seeders;

use App\Enums\ProfileType;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    public function run(): void
    {
        // Create main admin user
        $admin = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'first_name' => 'Admin',
            'last_name' => 'User',
            'profile_type' => ProfileType::ADMIN->value,
        ]);

        $admin->assignRole('admin');

        // Create 5 additional admin users
        $additionalAdmins = [
            [
                'email' => '<EMAIL>',
                'first_name' => 'Regional',
                'last_name' => 'Admin One',
            ],
            [
                'email' => '<EMAIL>',
                'first_name' => 'Regional',
                'last_name' => 'Admin Two',
            ],
            [
                'email' => '<EMAIL>',
                'first_name' => 'Content',
                'last_name' => 'Manager',
            ],
            [
                'email' => '<EMAIL>',
                'first_name' => 'System',
                'last_name' => 'Administrator',
            ],
            [
                'email' => '<EMAIL>',
                'first_name' => 'Support',
                'last_name' => 'Manager',
            ],
        ];

        foreach ($additionalAdmins as $adminData) {
            $newAdmin = User::factory()->create([
                'email' => $adminData['email'],
                'password' => Hash::make('password'),
                'first_name' => $adminData['first_name'],
                'last_name' => $adminData['last_name'],
                'profile_type' => ProfileType::ADMIN->value,
            ]);

            $newAdmin->assignRole('admin');
        }
    }
}
