<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('filter_views', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('resource_type');
            $table->json('filter_definition');
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->boolean('is_public')->default(false);
            $table->timestamps();

            // Add indexes for common queries
            $table->index(['resource_type', 'user_id']);
            $table->index('is_public');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('filter_views');
    }
};
