<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('opportunities', function (Blueprint $table) {
            $table->timestamp('visible_start_date')->nullable()->default(null)->after('status')->comment('Date when the opportunity becomes visible');
            $table->timestamp('visible_end_date')->nullable()->default(null)->after('visible_start_date')->comment('Date when the opportunity ceases to be visible');

            // Optional: Add an index if querying by these dates will be frequent
            // $table->index(['visible_start_date', 'visible_end_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('opportunities', function (Blueprint $table) {
            // Drop index first if added
            // $table->dropIndex(['visible_start_date', 'visible_end_date']);
            $table->dropColumn(['visible_start_date', 'visible_end_date']);
        });
    }
};
