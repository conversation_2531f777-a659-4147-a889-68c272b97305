<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('outbound_messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->enum('channel', ['email', 'sms']);
            $table->string('to_address'); // Email address or phone number
            $table->string('subject')->nullable(); // Nullable for SMS
            $table->longText('content');
            $table->foreignId('template_id')->nullable()->constrained('email_templates')->onDelete('set null');
            $table->json('template_data')->nullable(); // Variables used for template rendering
            $table->enum('status', ['logged', 'sent', 'delivered', 'failed'])->default('logged');
            $table->string('external_id')->nullable(); // Campaign Nucleus/Twilio ID
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->timestamp('failed_at')->nullable();
            $table->text('error_message')->nullable();
            $table->string('environment'); // local, staging, production
            $table->timestamps();

            // Indexes for performance and common queries
            $table->index('user_id');
            $table->index('channel');
            $table->index('status');
            $table->index('template_id');
            $table->index('environment');
            $table->index(['user_id', 'channel']); // User's messages by channel
            $table->index(['status', 'channel']); // Status filtering by channel
            $table->index(['created_at', 'channel']); // Time-based queries
            $table->index('external_id'); // External service lookups
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('outbound_messages');
    }
};
