<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('opportunities', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->nullable()->constrained()->cascadeOnUpdate()->nullOnDelete();
            $table->string('title');
            $table->text('description');
            $table->string('type');
            $table->string('status');
            $table->string('subtype')->nullable();
            $table->string('city')->nullable();
            $table->string('state_code')->nullable()->constrained('states')->references('code')->nullOnDelete();
            $table->text('details')->nullable();
            $table->text('qualifications')->nullable();
            $table->text('responsibilities')->nullable();
            $table->text('benefits')->nullable();
            $table->string('apply_url')->nullable();
            $table->string('term')->nullable();
            $table->string('location_type')->nullable();
            $table->boolean('is_featured')->default(false);
            $table->timestamps();

            // Indexes
            $table->index('organization_id');
            $table->index('status');
            $table->index('type');
            $table->index('location_type');
            $table->index('state_code');
            $table->index(['city', 'state_code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('opportunities');
    }
};
