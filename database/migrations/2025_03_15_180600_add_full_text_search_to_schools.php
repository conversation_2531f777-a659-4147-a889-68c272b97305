<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add a tsvector column for full text search
        DB::statement('ALTER TABLE schools ADD COLUMN search_vector tsvector');

        // Create an index on the tsvector column for faster searches
        DB::statement('CREATE INDEX schools_search_idx ON schools USING GIN(search_vector)');

        // Update the tsvector column with the current data
        DB::statement("
            UPDATE schools
            SET search_vector =
                to_tsvector('english', coalesce(name, ''))
        ");

        // Create a trigger to keep the tsvector column updated
        DB::statement('
            CREATE TRIGGER schools_search_update
            BEFORE INSERT OR UPDATE ON schools
            FOR EACH ROW EXECUTE FUNCTION
            tsvector_update_trigger(search_vector, \'pg_catalog.english\', name)
        ');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the trigger
        DB::statement('DROP TRIGGER IF EXISTS schools_search_update ON schools');

        // Drop the index
        DB::statement('DROP INDEX IF EXISTS schools_search_idx');

        // Remove the tsvector column
        Schema::table('schools', function (Blueprint $table) {
            $table->dropColumn('search_vector');
        });
    }
};
