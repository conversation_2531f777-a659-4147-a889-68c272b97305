<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('advertisement_ui_region', function (Blueprint $table) {
            $table->id();
            $table->foreignId('advertisement_id')->constrained()->cascadeOnDelete();
            $table->string('ui_region');
            $table->timestamps();

            $table->unique(['advertisement_id', 'ui_region']);

            // Add an index on ui_region for faster lookups
            $table->index('ui_region');

            // Add a comment to explain that this references the UiRegion enum
            $table->comment('Stores which UI regions (from App\Enums\UiRegion) an advertisement can be displayed in');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('advertisement_ui_region');
    }
};
