<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contacts', function (Blueprint $table) {
            $table->id();
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email')->nullable()->index();
            $table->string('phone')->nullable();
            $table->string('type')->index(); // positive_athlete, positive_coach, etc.
            $table->string('status')->default('active')->index();
            $table->text('notes')->nullable();

            // Geo-structure relationships
            $table->foreignId('region_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('market_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('sub_region_id')->nullable()->constrained('sub_regions')->nullOnDelete();
            $table->foreignId('county_id')->nullable()->constrained()->nullOnDelete();
            $table->char('state_id', 2)->nullable();
            $table->foreign('state_id')->references('code')->on('states')->nullOnDelete();
            $table->foreignId('school_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('organization_id')->nullable()->constrained()->nullOnDelete();

            // Athlete-specific fields
            $table->string('gender')->nullable();
            $table->string('graduation_year')->nullable();

            // Flexible attributes
            $table->jsonb('sports')->nullable();
            $table->jsonb('metadata')->nullable();

            // Timestamps and soft deletes
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contacts');
    }
};
