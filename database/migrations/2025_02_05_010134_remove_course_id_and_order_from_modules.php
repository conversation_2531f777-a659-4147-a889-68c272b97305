<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('modules', function (Blueprint $table) {
            // Drop the foreign key first
            $table->dropForeign(['course_id']);

            // Then drop the columns
            $table->dropColumn(['course_id', 'order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('modules', function (Blueprint $table) {
            // Recreate the columns
            $table->foreignId('course_id')->nullable()->constrained()->cascadeOnDelete();
            $table->integer('order')->nullable();

            // Recreate the index that existed in the original schema
            $table->index(['course_id', 'order']);
        });
    }
};
