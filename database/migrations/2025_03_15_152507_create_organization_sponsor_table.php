<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('organization_sponsor', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained()->cascadeOnDelete();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->string('role')->nullable(); // For future use: primary_contact, admin, etc.
            $table->json('metadata')->nullable(); // For future extensibility
            $table->timestamp('deactivated_at')->nullable();
            $table->timestamps();

            $table->unique(['organization_id', 'user_id']);
        });

        // Create a trigger that automatically deactivates other active relationships
        DB::unprepared('
            CREATE OR REPLACE FUNCTION deactivate_other_sponsor_organizations()
            RETURNS TRIGGER AS $$
            BEGIN
                -- Only proceed if the new record is active (deactivated_at is null)
                IF NEW.deactivated_at IS NULL THEN
                    -- Update all other active records for this user to be deactivated
                    UPDATE organization_sponsor
                    SET deactivated_at = NOW()
                    WHERE user_id = NEW.user_id
                    AND id != NEW.id
                    AND deactivated_at IS NULL;
                END IF;
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;

            CREATE TRIGGER trg_deactivate_other_sponsor_organizations
            AFTER INSERT OR UPDATE ON organization_sponsor
            FOR EACH ROW
            EXECUTE FUNCTION deactivate_other_sponsor_organizations();
        ');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the trigger and function first
        DB::unprepared('
            DROP TRIGGER IF EXISTS trg_deactivate_other_sponsor_organizations ON organization_sponsor;
            DROP FUNCTION IF EXISTS deactivate_other_sponsor_organizations;
        ');

        Schema::dropIfExists('organization_sponsor');
    }
};
