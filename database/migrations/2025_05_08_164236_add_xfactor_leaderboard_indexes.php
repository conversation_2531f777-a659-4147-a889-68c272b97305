<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add necessary indexes for efficient X-Factor leaderboard queries

        // Index on module_user.completed_at for date range filtering
        Schema::table('module_user', function (Blueprint $table) {
            $table->index('completed_at', 'idx_module_user_completed_at');
        });

        // Index on users.region_id for regional filtering
        Schema::table('users', function (Blueprint $table) {
            if (!Schema::hasIndex('users', 'idx_users_region_id')) {
                $table->index('region_id', 'idx_users_region_id');
            }
        });

        // Index on users.graduation_year for academic year filtering
        Schema::table('users', function (Blueprint $table) {
            if (!Schema::hasIndex('users', 'idx_users_graduation_year')) {
                $table->index('graduation_year', 'idx_users_graduation_year');
            }
        });

        // Index on users.state_code for state filtering
        Schema::table('users', function (Blueprint $table) {
            if (!Schema::hasIndex('users', 'idx_users_state_code')) {
                $table->index('state_code', 'idx_users_state_code');
            }
        });

        // Composite index for module_user to optimize counting
        Schema::table('module_user', function (Blueprint $table) {
            $table->index(['user_id', 'module_id', 'completed_at'], 'idx_module_user_user_module_completed');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the added indexes
        Schema::table('module_user', function (Blueprint $table) {
            $table->dropIndex('idx_module_user_completed_at');
            $table->dropIndex('idx_module_user_user_module_completed');
        });

        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasIndex('users', 'idx_users_region_id')) {
                $table->dropIndex('idx_users_region_id');
            }

            if (Schema::hasIndex('users', 'idx_users_graduation_year')) {
                $table->dropIndex('idx_users_graduation_year');
            }

            if (Schema::hasIndex('users', 'idx_users_state_code')) {
                $table->dropIndex('idx_users_state_code');
            }
        });
    }
};
