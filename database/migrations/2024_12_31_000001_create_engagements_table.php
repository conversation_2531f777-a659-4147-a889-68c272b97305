<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('engagements', function (Blueprint $table) {
            $table->id();
            $table->string('event_type'); // impression, click, etc.

            // Polymorphic relationship
            $table->morphs('engageable'); // Creates engageable_id & engageable_type

            $table->foreignId('user_id')->nullable()->constrained()->nullOnDelete();
            $table->ipAddress('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            $table->json('metadata')->nullable(); // For additional context data
            $table->timestamp('created_at')->index(); // Only need created_at, not updated_at

            // Add indexes for common queries
            $table->index(['event_type', 'created_at']);
            $table->index(['engageable_id', 'engageable_type', 'event_type']);
            $table->index(['user_id', 'event_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('engagements');
    }
};
