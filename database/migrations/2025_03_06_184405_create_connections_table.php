<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('connections', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('requester_id');
            $table->unsignedBigInteger('recipient_id');
            $table->string('status');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('requester_id')->references('id')->on('users');
            $table->foreign('recipient_id')->references('id')->on('users');

            // Ensure a user can only have one connection with another user
            $table->unique(['requester_id', 'recipient_id']);

            // Add indexes for efficient queries
            $table->index('requester_id');
            $table->index('recipient_id');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('connections');
    }
};
