<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('opportunities', function (Blueprint $table) {
            $table->integer('preferred_graduation_year_start')->nullable();
            $table->integer('preferred_graduation_year_end')->nullable();
            $table->json('preferred_states')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('opportunities', function (Blueprint $table) {
            $table->dropColumn([
                'preferred_graduation_year_start',
                'preferred_graduation_year_end',
                'preferred_states',
            ]);
        });
    }
};
