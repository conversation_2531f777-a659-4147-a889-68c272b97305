<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop the old county column
            $table->dropColumn('county');

            // Add new county_id column
            $table->foreignId('county_id')->nullable()->constrained()->nullOnDelete();

            // Add index
            $table->index('county_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop the foreign key and column
            $table->dropForeign(['county_id']);
            $table->dropColumn('county_id');

            // Add back the original county column
            $table->string('county')->nullable();
        });
    }
};
