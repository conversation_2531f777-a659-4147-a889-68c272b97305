<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('winners', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('scholarship_id')->nullable();
            $table->unsignedBigInteger('award_id')->nullable();
            $table->integer('year');
            $table->boolean('is_finalist')->default(false);
            $table->boolean('is_winner')->default(false);
            $table->string('verification_state');
            $table->timestamp('verified_at')->nullable();
            $table->timestamp('notified_at')->nullable();
            $table->timestamp('acknowledged_at')->nullable();
            $table->string('tshirt_size');
            $table->timestamps();
            $table->softDeletes();

            // Add explicit foreign key constraints with unique names
            $table->foreign('user_id', 'winners_user_id_fk')
                ->references('id')
                ->on('users');

            $table->foreign('scholarship_id', 'winners_scholarship_id_fk')
                ->references('id')
                ->on('scholarships');

            $table->foreign('award_id', 'winners_award_id_fk')
                ->references('id')
                ->on('awards');

            // Add indexes
            $table->index('user_id');
            $table->index('scholarship_id');
            $table->index('award_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('winners');
    }
};
