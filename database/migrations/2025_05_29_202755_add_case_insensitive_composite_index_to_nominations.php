<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Primary index for DISTINCT ON query performance
        // This index supports: DISTINCT ON (COALESCE(user_id, 0), <PERSON>OW<PERSON>(TRIM(first_name)), <PERSON>OW<PERSON>(TRIM(last_name)))
        // Ordered by: COALESCE(user_id, 0), <PERSON>OW<PERSON>(TRIM(first_name)), <PERSON><PERSON><PERSON>(TRIM(last_name)), created_at DESC
        DB::statement('
            CREATE INDEX IF NOT EXISTS idx_nominations_distinct_nominees_optimized
            ON nominations (
                COALESCE(user_id, 0),
                <PERSON>OW<PERSON>(TRIM(first_name)),
                <PERSON>OW<PERSON>(TRIM(last_name)),
                created_at DESC
            )
        ');

        // Secondary index for case-insensitive nominee identification (fallback queries)
        DB::statement('
            CREATE INDEX IF NOT EXISTS idx_nominations_nominee_identity_case_insensitive
            ON nominations (
                LOWER(email),
                <PERSON><PERSON><PERSON>(TRIM(first_name)),
                LOWER(TRIM(last_name))
            )
        ');

        // Performance index for common admin filtering queries
        DB::statement('
            CREATE INDEX IF NOT EXISTS idx_nominations_admin_filters
            ON nominations (status, sport, grade, created_at DESC)
        ');

        // Index for email-based lookups (case-insensitive)
        DB::statement('
            CREATE INDEX IF NOT EXISTS idx_nominations_email_case_insensitive
            ON nominations (LOWER(TRIM(email)))
        ');

        // Index for school-based filtering
        DB::statement('
            CREATE INDEX IF NOT EXISTS idx_nominations_school_filtering
            ON nominations (LOWER(TRIM(school_name)), created_at DESC)
        ');

        // Index for user linkage queries (when user_id exists)
        DB::statement('
            CREATE INDEX IF NOT EXISTS idx_nominations_user_linkage
            ON nominations (user_id, email, status)
            WHERE user_id IS NOT NULL
        ');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the indexes
        DB::statement('DROP INDEX IF EXISTS idx_nominations_distinct_nominees_optimized');
        DB::statement('DROP INDEX IF EXISTS idx_nominations_nominee_identity_case_insensitive');
        DB::statement('DROP INDEX IF EXISTS idx_nominations_admin_filters');
        DB::statement('DROP INDEX IF EXISTS idx_nominations_email_case_insensitive');
        DB::statement('DROP INDEX IF EXISTS idx_nominations_school_filtering');
        DB::statement('DROP INDEX IF EXISTS idx_nominations_user_linkage');
    }
};
