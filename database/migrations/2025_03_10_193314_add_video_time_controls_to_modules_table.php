<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('modules', function (Blueprint $table) {
            $table->integer('video_start_time')->nullable()->after('video_url')
                ->comment('Start time in seconds from the beginning of the video');
            $table->integer('video_end_time')->nullable()->after('video_start_time')
                ->comment('End time in seconds from the beginning of the video');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('modules', function (Blueprint $table) {
            $table->dropColumn(['video_start_time', 'video_end_time']);
        });
    }
};
