<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Enums\NominationStatus;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('nominations', function (Blueprint $table) {
            $table->id();
            // Nominator info from form
            $table->string('nominator_email');
            $table->string('nominator_first_name')->nullable();
            $table->string('nominator_last_name')->nullable();

            // Nomination details
            $table->string('email');
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('school_name');
            $table->foreignId('school_id')
                ->nullable()
                ->constrained('schools')
                ->nullOnDelete();
            $table->string('sport');
            $table->unsignedTinyInteger('ai_score')->default(0);
            $table->string('relationship');
            $table->text('note')->nullable();
            $table->string('status')->default(NominationStatus::PENDING_AD_VERIFICATION->value);
            $table->string('type');

            // Add foreign key to system_invites
            $table->foreignId('system_invite_id')->nullable()
                ->constrained('system_invites')
                ->nullOnDelete();

            $table->timestamps();

            // Indexes
            $table->index('nominator_email');
            $table->index('status');
            $table->index('type');
            $table->index('system_invite_id');

            // New indexes for common queries
            // Composite index for nominee name searches
            $table->index(['last_name', 'first_name']);

            // Index for email searches
            $table->index('email');

            // Index for school name searches
            $table->index('school_name');

            // Index for sport filtering
            $table->index('sport');

            // Index for AI score sorting/filtering
            $table->index('ai_score');

            // Composite index for status and type filtering together
            $table->index(['status', 'type']);
        });

        // Add check constraint for ai_score range using raw SQL
        DB::statement('ALTER TABLE nominations ADD CONSTRAINT chk_nominations_ai_score CHECK (ai_score >= 0 AND ai_score <= 100)');
    }

    public function down(): void
    {
        Schema::dropIfExists('nominations');
    }
};
