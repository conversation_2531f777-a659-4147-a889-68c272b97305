<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('filter_view_shares', function (Blueprint $table) {
            $table->id();
            $table->foreignId('filter_view_id')->constrained('filter_views')->onDelete('cascade');
            $table->foreignId('group_id')->constrained('groups')->onDelete('cascade');
            $table->timestamps();

            // Add indexes for better query performance
            $table->index('filter_view_id');
            $table->index('group_id');

            // Add unique constraint to prevent duplicate shares
            $table->unique(['filter_view_id', 'group_id']);
        });

        // Add composite indexes to filter_views table for better performance
        Schema::table('filter_views', function (Blueprint $table) {
            $table->index(['resource_type', 'is_public']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove composite indexes from filter_views table
        Schema::table('filter_views', function (Blueprint $table) {
            $table->dropIndex(['resource_type', 'is_public']);
        });

        Schema::dropIfExists('filter_view_shares');
    }
};
