<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add a tsvector column for full text search on the 'name' field
        DB::statement('ALTER TABLE organizations ADD COLUMN search_vector tsvector');

        // Create an index on the tsvector column for faster searches
        DB::statement('CREATE INDEX organizations_search_idx ON organizations USING GIN(search_vector)');

        // Update the tsvector column with the current organization names
        // Use 'simple' dictionary as organization names might not always be standard English words
        DB::statement("            UPDATE organizations
            SET search_vector =
                to_tsvector('simple', coalesce(name, ''))
        ");

        // Create a trigger to keep the tsvector column updated automatically
        DB::statement("            CREATE TRIGGER organizations_search_update
            BEFORE INSERT OR UPDATE ON organizations
            FOR EACH ROW EXECUTE FUNCTION
            tsvector_update_trigger(search_vector, 'pg_catalog.simple', name)
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the trigger first to remove dependency
        DB::statement('DROP TRIGGER IF EXISTS organizations_search_update ON organizations');

        // Drop the index
        DB::statement('DROP INDEX IF EXISTS organizations_search_idx');

        // Remove the tsvector column
        Schema::table('organizations', function (Blueprint $table) {
            $table->dropColumn('search_vector');
        });
    }
};
