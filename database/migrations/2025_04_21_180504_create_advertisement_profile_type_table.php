<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('advertisement_profile_type', function (Blueprint $table) {
            $table->id();
            $table->foreignId('advertisement_id')->constrained()->cascadeOnDelete();
            $table->string('profile_type');
            $table->timestamps();

            $table->unique(['advertisement_id', 'profile_type']);

            // Add an index on profile_type for faster lookups
            $table->index('profile_type');

            // Add a comment to explain that this references the ProfileType enum
            $table->comment('Stores which profile types (from App\Enums\ProfileType) an advertisement targets');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('advertisement_profile_type');
    }
};
