<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('opportunities', function (Blueprint $table) {
            $table->boolean('admin_disabled')->default(false)->after('status');
            $table->timestamp('admin_disabled_at')->nullable()->after('admin_disabled');
            $table->bigInteger('admin_disabled_by')->unsigned()->nullable()->after('admin_disabled_at');

            $table->foreign('admin_disabled_by')->references('id')->on('users');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('opportunities', function (Blueprint $table) {
            $table->dropForeign(['admin_disabled_by']);
            $table->dropColumn(['admin_disabled', 'admin_disabled_at', 'admin_disabled_by']);
        });
    }
};
