<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('engagement_aggregates', function (Blueprint $table) {
            $table->id();

            // Polymorphic relationship
            $table->morphs('engageable'); // Creates engageable_id & engageable_type

            $table->string('event_type');
            $table->date('date')->index();
            $table->unsignedInteger('count')->default(0);
            $table->timestamps();

            // Add unique constraint to prevent duplicate entries
            $table->unique(['engageable_id', 'engageable_type', 'event_type', 'date'], 'unique_daily_aggregation');

            // Add indexes for common queries
            $table->index(['date', 'event_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('engagement_aggregates');
    }
};
