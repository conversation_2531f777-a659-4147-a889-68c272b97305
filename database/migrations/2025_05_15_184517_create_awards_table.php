<?php

use App\Enums\AwardType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('awards', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('details')->comment('Rich text content for award details')->nullable();
            $table->string('type')->default(AwardType::REGIONAL->value);
            $table->unsignedBigInteger('region_id');
            $table->unsignedBigInteger('market_id');
            $table->char('state_id', 2);
            $table->unsignedBigInteger('subregion_id')->nullable();
            $table->integer('year');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->softDeletes();

            // Add explicit foreign key constraints with unique names
            $table->foreign('region_id', 'awards_region_id_fk')
                ->references('id')
                ->on('regions');

            $table->foreign('market_id', 'awards_market_id_fk')
                ->references('id')
                ->on('markets');

            $table->foreign('state_id', 'awards_state_id_fk')
                ->references('code')
                ->on('states');

            $table->foreign('subregion_id', 'awards_subregion_id_fk')
                ->references('id')
                ->on('sub_regions');

            // Add indexes
            $table->index('type');
            $table->index('region_id');
            $table->index('market_id');
            $table->index('state_id');
            $table->index('subregion_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('awards');
    }
};
