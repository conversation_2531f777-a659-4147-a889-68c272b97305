<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tests', function (Blueprint $table) {
            $table->id();
            $table->string('type'); // 'exam', 'quiz'
            $table->string('testable_type'); // Polymorphic: 'App\\Models\\Course' or 'App\\Models\\Module'
            $table->unsignedBigInteger('testable_id');
            $table->integer('attempts_allowed')->default(3);
            $table->unsignedInteger('wait_period')->default(0)->comment('Wait period in seconds between attempts'); // Allows up to ~136 years
            $table->integer('passing_score')->nullable();
            $table->integer('time_limit')->nullable();
            $table->timestamps();

            $table->index(['testable_type', 'testable_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tests');
    }
};
