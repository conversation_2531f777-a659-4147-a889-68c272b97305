<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('scholarships', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->integer('year'); // Graduation year (e.g., 2024 for 2023-24)
            $table->text('details')->comment('Rich text content for scholarship details');
            $table->boolean('is_active')->default(true);

            // Geographic references
            $table->unsignedBigInteger('region_id');
            $table->unsignedBigInteger('market_id');
            $table->char('state_id', 2);
            $table->unsignedBigInteger('subregion_id')->nullable();

            $table->timestamps();
            $table->softDeletes();

            // Add explicit foreign key constraints with unique names
            $table->foreign('region_id', 'scholarships_region_id_fk')
                ->references('id')
                ->on('regions');

            $table->foreign('market_id', 'scholarships_market_id_fk')
                ->references('id')
                ->on('markets');

            $table->foreign('state_id', 'scholarships_state_id_fk')
                ->references('code')
                ->on('states');

            $table->foreign('subregion_id', 'scholarships_subregion_id_fk')
                ->references('id')
                ->on('sub_regions');

            // Add indexes
            $table->index('region_id');
            $table->index('market_id');
            $table->index('state_id');
            $table->index('subregion_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('scholarships');
    }
};
