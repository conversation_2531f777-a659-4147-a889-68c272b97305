<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('system_invites', function (Blueprint $table) {
            $table->id();
            $table->string('email');
            $table->string('token')->unique();
            $table->string('status')->default('pending');
            $table->string('type');
            $table->timestamp('expires_at');
            $table->jsonb('invite_data');
            $table->timestamps();

            $table->index(['email', 'status']);
            $table->index('token');
            $table->index('status');
            $table->index('type');
            $table->index('expires_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('system_invites');
    }
};
