<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('nominations', function (Blueprint $table) {
            // Location Information
            $table->string('state_code')->nullable()->comment('State abbreviation (e.g., IA, CA)');
            $table->string('county')->nullable()->comment('County name');

            // Enhanced Contact Information
            $table->string('nominee_phone')->nullable()->comment('Nominee phone number');
            $table->string('nominator_phone')->nullable()->comment('Nominator phone number');

            // Multiple Sports Support
            $table->string('sport_2')->nullable()->comment('Second sport (if applicable)');
            $table->string('sport_3')->nullable()->comment('Third sport (if applicable)');
            $table->string('other_sport')->nullable()->comment('Custom/adapted sport name');

            // Demographic & Academic Information
            $table->string('gender')->nullable()->comment('Nominee gender');
            $table->integer('grade')->nullable()->comment('Grade level (if student)');

            // Parent/Guardian Information (for minors)
            $table->string('parent_guardian_first_name')->nullable()->comment('Parent/Guardian first name');
            $table->string('parent_guardian_last_name')->nullable()->comment('Parent/Guardian last name');
            $table->string('parent_guardian_email')->nullable()->comment('Parent/Guardian email');
            $table->string('parent_guardian_phone')->nullable()->comment('Parent/Guardian phone number');

            // Social Media & Digital Presence
            $table->string('instagram_handle')->nullable()->comment('Instagram handle');
            $table->string('twitter_handle')->nullable()->comment('Twitter/X handle');

            // Marketing & Attribution Data
            $table->string('how_did_you_hear')->nullable()->comment('How they heard about Positive Athlete');
            $table->string('referral_source_name')->nullable()->comment('Name of referring entity');

            // Processing Workflow Enhancement
            $table->enum('processing_status', [
                'received',
                'validated',
                'invited',
                'onboarded'
            ])->default('received')->comment('Current processing stage');
            $table->timestamp('processed_at')->nullable()->comment('When processing was completed');

            // JotForm Integration Metadata
            $table->string('jotform_submission_id')->nullable()->comment('JotForm submission ID for tracking');
            $table->string('jotform_form_id')->nullable()->comment('JotForm form ID');
            $table->json('location_resolution_notes')->nullable()->comment('Notes from location data resolution');

            // Indexes for performance
            $table->index('state_code', 'idx_nominations_state');
            $table->index('county', 'idx_nominations_county');
            $table->index('processing_status', 'idx_nominations_processing_status');
            $table->index('jotform_submission_id', 'idx_nominations_jotform_submission');
            $table->index(['nominee_phone'], 'idx_nominations_nominee_phone');
            $table->index(['parent_guardian_email'], 'idx_nominations_parent_email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('nominations', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex('idx_nominations_state');
            $table->dropIndex('idx_nominations_county');
            $table->dropIndex('idx_nominations_processing_status');
            $table->dropIndex('idx_nominations_jotform_submission');
            $table->dropIndex('idx_nominations_nominee_phone');
            $table->dropIndex('idx_nominations_parent_email');

            // Drop columns
            $table->dropColumn([
                'state_code',
                'county',
                'nominee_phone',
                'nominator_phone',
                'sport_2',
                'sport_3',
                'other_sport',
                'gender',
                'grade',
                'parent_guardian_first_name',
                'parent_guardian_last_name',
                'parent_guardian_email',
                'parent_guardian_phone',
                'instagram_handle',
                'twitter_handle',
                'how_did_you_hear',
                'referral_source_name',
                'processing_status',
                'processed_at',
                'jotform_submission_id',
                'jotform_form_id',
                'location_resolution_notes',
            ]);
        });
    }
};
