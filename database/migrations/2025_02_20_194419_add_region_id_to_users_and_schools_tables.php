<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->foreignId('region_id')->nullable()->constrained()->nullOnDelete();
            $table->index('region_id');
        });

        Schema::table('schools', function (Blueprint $table) {
            $table->foreignId('region_id')->nullable()->constrained()->nullOnDelete();
            $table->index('region_id');
        });

        // Add triggers to maintain consistency when county changes
        DB::unprepared('
            CREATE OR REPLACE FUNCTION update_user_region_id()
            RETURNS TRIGGER AS $$
            BEGIN
                IF NEW.county_id IS NOT NULL THEN
                    NEW.region_id := (
                        SELECT r.id
                        FROM regions r
                        JOIN markets m ON m.region_id = r.id
                        JOIN counties c ON c.market_id = m.id
                        WHERE c.id = NEW.county_id
                        LIMIT 1
                    );
                END IF;
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;

            CREATE TRIGGER update_user_region_id
            BEFORE INSERT OR UPDATE ON users
            FOR EACH ROW
            EXECUTE FUNCTION update_user_region_id();
        ');

        DB::unprepared('
            CREATE OR REPLACE FUNCTION update_school_region_id()
            RETURNS TRIGGER AS $$
            BEGIN
                IF NEW.county_id IS NOT NULL THEN
                    NEW.region_id := (
                        SELECT r.id
                        FROM regions r
                        JOIN markets m ON m.region_id = r.id
                        JOIN counties c ON c.market_id = m.id
                        WHERE c.id = NEW.county_id
                        LIMIT 1
                    );
                END IF;
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;

            CREATE TRIGGER update_school_region_id
            BEFORE INSERT OR UPDATE ON schools
            FOR EACH ROW
            EXECUTE FUNCTION update_school_region_id();
        ');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop triggers and functions first
        DB::unprepared('DROP TRIGGER IF EXISTS update_user_region_id ON users');
        DB::unprepared('DROP FUNCTION IF EXISTS update_user_region_id');
        DB::unprepared('DROP TRIGGER IF EXISTS update_school_region_id ON schools');
        DB::unprepared('DROP FUNCTION IF EXISTS update_school_region_id');

        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['region_id']);
            $table->dropColumn('region_id');
        });

        Schema::table('schools', function (Blueprint $table) {
            $table->dropForeign(['region_id']);
            $table->dropColumn('region_id');
        });
    }
};
