<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('opportunities', function (Blueprint $table) {
            $table->uuid('location_coordinate_id')->nullable()->after('location_type')
                ->comment('UUID reference to the location_coordinates table');

            // Add foreign key constraint
            $table->foreign('location_coordinate_id')
                ->references('id')
                ->on('location_coordinates')
                ->onDelete('set null');

            // Add index for improved query performance
            $table->index('location_coordinate_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('opportunities', function (Blueprint $table) {
            $table->dropForeign(['location_coordinate_id']);
            $table->dropIndex(['location_coordinate_id']);
            $table->dropColumn('location_coordinate_id');
        });
    }
};
