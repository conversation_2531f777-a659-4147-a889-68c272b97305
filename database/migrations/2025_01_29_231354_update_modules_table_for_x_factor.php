<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('modules', function (Blueprint $table) {
            $table->integer('order')->after('video')->nullable();
            $table->boolean('published')->after('order')->default(false);

            $table->index('published');
            $table->index(['course_id', 'order']);
        });

        // Update module_user pivot table to track completion
        Schema::table('module_user', function (Blueprint $table) {
            // When the module was started
            $table->timestamp('started_at')->nullable();
            // Last position in video (if applicable)
            $table->integer('last_position')->nullable();
            // Any additional completion metadata
            $table->json('completion_metadata')->nullable();

            $table->index(['user_id', 'completed_at']);
            $table->index(['module_id', 'completed_at']);
        });
    }

    public function down(): void
    {
        Schema::table('modules', function (Blueprint $table) {
            $table->dropIndex(['published']);
            $table->dropIndex(['course_id', 'order']);
            $table->dropColumn(['order', 'published']);
        });

        Schema::table('module_user', function (Blueprint $table) {
            $table->dropIndex(['user_id', 'completed_at']);
            $table->dropIndex(['module_id', 'completed_at']);
            $table->dropColumn([
                'started_at',
                'completed_at',
                'last_position',
                'completion_metadata'
            ]);
        });
    }
};
