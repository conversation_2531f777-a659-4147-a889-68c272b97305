<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('import_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('import_id')->constrained()->cascadeOnDelete();
            $table->integer('row_number');
            $table->enum('status', ['created', 'updated', 'failed']);
            $table->json('raw_data');
            $table->json('processed_data')->nullable();
            $table->string('model_type')->nullable();
            $table->string('model_id')->nullable();
            $table->json('errors')->nullable();
            $table->timestamps();

            // Add index for faster queries
            $table->index(['import_id', 'status']);
            $table->index('row_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('import_records');
    }
};
