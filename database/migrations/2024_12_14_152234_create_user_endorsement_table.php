<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_endorsement', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->cascadeOnUpdate()->cascadeOnDelete();
            $table->foreignId('endorsement_id')->constrained()->cascadeOnUpdate()->cascadeOnDelete();
            $table->foreignId('endorser_id')->constrained('users')->cascadeOnUpdate()->cascadeOnDelete();
            $table->string('relation')->nullable();
            $table->timestamps();

            $table->unique(['user_id', 'endorsement_id', 'endorser_id'], 'user_endorsement_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_endorsement');
    }
};
