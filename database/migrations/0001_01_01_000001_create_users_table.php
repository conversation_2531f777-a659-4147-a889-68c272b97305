<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('registration_method')->nullable();
            $table->string('profile_type');
            $table->string('password');
            $table->string('phone')->nullable();
            $table->string('street_address_1')->nullable();
            $table->string('street_address_2')->nullable();
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('zip')->nullable();
            $table->string('county')->nullable();
            $table->foreignId('school_id')->nullable()->constrained('schools')->cascadeOnUpdate()->nullOnDelete();
            $table->integer('graduation_year')->nullable();
            $table->float('gpa')->nullable();
            $table->string('class_rank')->nullable();
            $table->string('gender')->nullable(); // e.g. 'male', 'female'
            $table->integer('height_in_inches')->nullable();
            $table->integer('weight')->nullable();
            $table->string('twitter')->nullable();
            $table->string('linkedin')->nullable();
            $table->string('instagram')->nullable();
            $table->string('facebook')->nullable();
            $table->string('hudl')->nullable();
            $table->string('custom_link')->nullable();
            $table->text('content')->nullable(); // possibly JSON in the future
            $table->text('notes')->nullable();
            $table->boolean('recruiter_enabled')->default(false);
            $table->string('handle')->unique()->nullable();
            $table->foreignId('parent_id')->nullable()->constrained('users')->cascadeOnUpdate()->nullOnDelete();

            $table->rememberToken();
            $table->timestamps();

            // Add composite index for name searches
            $table->index(['last_name', 'first_name']);

            // Add index for profile filtering
            $table->index('profile_type');

            // Add index for registration method filtering
            $table->index('registration_method');

            // Add index for location-based queries
            $table->index(['state', 'city']);

            // Add index for school-based queries
            $table->index('school_id');

            // Add index for graduation year filtering
            $table->index('graduation_year');

            // Add index for parent relationship queries
            $table->index('parent_id');
        });

        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('sessions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->foreignId('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
    }
};
