<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop the old state column
            $table->dropColumn('state');

            // Add the new state_code column with foreign key
            $table->string('state_code', 2)->nullable();
            $table->foreign('state_code')->references('code')->on('states')->nullOnDelete();
            $table->index('state_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop the foreign key and state_code column
            $table->dropForeign(['state_code']);
            $table->dropColumn('state_code');

            // Add back the original state column
            $table->string('state')->nullable();
        });
    }
};
