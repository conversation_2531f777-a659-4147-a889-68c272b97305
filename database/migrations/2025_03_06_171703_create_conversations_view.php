<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
            CREATE VIEW conversations AS
            SELECT
                LEAST(sender_id, recipient_id) AS user_one_id,
                GREATEST(sender_id, recipient_id) AS user_two_id,
                MAX(created_at) AS last_message_at,
                COUNT(*) AS message_count
            FROM messages
            WHERE
                deleted_by_sender_at IS NULL OR
                deleted_by_recipient_at IS NULL
            GROUP BY LEAST(sender_id, recipient_id), GREATEST(sender_id, recipient_id)
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement('DROP VIEW IF EXISTS conversations');
    }
};
