<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            // Rename existing name column to title if it exists
            $table->string('presenter')->nullable();
            if (Schema::hasColumn('courses', 'name')) {
                $table->renameColumn('name', 'title');
            } else {
                $table->string('title')->after('id');
            }

            $table->text('description')->after('title');
            $table->boolean('published')->default(false)->after('description');
            $table->integer('order')->nullable()->after('published');

            $table->index('published');
            $table->index(['published', 'order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            $table->dropIndex(['published', 'order']);
            $table->dropIndex(['published']);

            if (Schema::hasColumn('courses', 'title')) {
                $table->renameColumn('title', 'name');
            }

            $table->dropColumn(['description', 'published', 'order']);
        });
    }
};
