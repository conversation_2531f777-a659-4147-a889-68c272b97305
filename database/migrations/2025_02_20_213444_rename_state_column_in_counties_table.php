<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('counties', function (Blueprint $table) {
            $table->renameColumn('state', 'state_code');
        });

        Schema::table('counties', function (Blueprint $table) {
            $table->foreign('state_code')
                ->references('code')
                ->on('states')
                ->onDelete('restrict');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('counties', function (Blueprint $table) {
            $table->dropForeign(['state_code']);
            $table->renameColumn('state_code', 'state');
        });
    }
};
