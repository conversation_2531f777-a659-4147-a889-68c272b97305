<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Meeting>
 */
class MeetingFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->words(3, true),
            'meeting_time' => $this->faker->dateTimeBetween('-2 weeks', '+4 weeks'),
            'team_id' => null,
            'owner_id' => null,
            'content' => $this->faker->paragraph,
            'location' => $this->faker->address,
        ];
    }
}
