<?php

namespace Database\Factories;

use App\Enums\ProfileType;
use App\Enums\UiRegion;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Advertisement>
 */
class AdvertisementFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->company() . ' ' . $this->faker->word() . ' Campaign',
            'user_id' => User::factory()->sponsor(),
            'is_listed' => $this->faker->boolean(80), // 80% chance of being active
            'copy' => $this->faker->paragraph(3),
            'cta_text' => $this->faker->randomElement(['Learn More', 'Apply Now', 'Sign Up', 'Visit Website', 'Contact Us']),
            'cta_url' => $this->faker->url(),
            'impressions' => $this->faker->numberBetween(0, 10000),
            'clicks' => function (array $attributes) {
                // Ensure clicks are less than or equal to impressions
                $impressions = $attributes['impressions'];
                return $this->faker->numberBetween(0, min(1000, $impressions));
            },
        ];
    }

    /**
     * Create an active advertisement.
     */
    public function active(): static
    {
        return $this->state(function () {
            return [
                'is_listed' => true,
            ];
        });
    }

    /**
     * Create an inactive advertisement.
     */
    public function inactive(): static
    {
        return $this->state(function () {
            return [
                'is_listed' => false,
            ];
        });
    }

    /**
     * Set up the advertisement for a specific profile type.
     */
    public function forProfileType(ProfileType $profileType): static
    {
        return $this->afterCreating(function ($advertisement) use ($profileType) {
            $advertisement->addProfileType($profileType);
        });
    }

    /**
     * Set up the advertisement for a specific UI region.
     */
    public function forUiRegion(UiRegion $uiRegion): static
    {
        return $this->afterCreating(function ($advertisement) use ($uiRegion) {
            $advertisement->addUiRegion($uiRegion);
        });
    }

    /**
     * Set up the advertisement with no clicks/impressions for new campaigns.
     */
    public function newCampaign(): static
    {
        return $this->state(function () {
            return [
                'impressions' => 0,
                'clicks' => 0,
            ];
        });
    }

    /**
     * Set up the advertisement with a high click-through rate.
     */
    public function highPerforming(): static
    {
        return $this->state(function () {
            $impressions = $this->faker->numberBetween(1000, 10000);

            return [
                'impressions' => $impressions,
                'clicks' => (int) ($impressions * $this->faker->randomFloat(2, 0.05, 0.20)), // 5-20% CTR
            ];
        });
    }
}
