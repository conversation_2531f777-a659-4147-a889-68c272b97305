<?php

namespace Database\Factories;

use App\Models\Engagement;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Engagement>
 */
class EngagementFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'event_type' => $this->faker->randomElement([
                Engagement::EVENT_IMPRESSION,
                Engagement::EVENT_CLICK
            ]),
            'user_id' => function () {
                // 30% chance of having an associated user
                return $this->faker->boolean(30) ? User::factory() : null;
            },
            'ip_address' => $this->faker->ipv4,
            'user_agent' => $this->faker->userAgent,
            'metadata' => function () {
                // 70% chance of having metadata
                if ($this->faker->boolean(70)) {
                    return [
                        'source' => $this->faker->randomElement(['homepage', 'search', 'profile', 'feed']),
                        'position' => $this->faker->randomElement(['top', 'sidebar', 'bottom', 'featured']),
                        'session_id' => $this->faker->uuid,
                        'referrer' => $this->faker->optional(0.4)->url,
                    ];
                }

                return null;
            },
            'created_at' => Carbon::now()->subDays($this->faker->numberBetween(0, 30)),
        ];
    }

    /**
     * Configure the factory to generate impression events
     *
     * @return static
     */
    public function impression(): static
    {
        return $this->state(fn (array $attributes) => [
            'event_type' => Engagement::EVENT_IMPRESSION,
        ]);
    }

    /**
     * Configure the factory to generate click events
     *
     * @return static
     */
    public function click(): static
    {
        return $this->state(fn (array $attributes) => [
            'event_type' => Engagement::EVENT_CLICK,
        ]);
    }

    /**
     * Configure the factory to generate events for a specific date
     *
     * @param Carbon $date
     * @return static
     */
    public function forDate(Carbon $date): static
    {
        return $this->state(fn (array $attributes) => [
            'created_at' => $date,
        ]);
    }

    /**
     * Configure the factory to generate events for a random date within a range
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return static
     */
    public function withinDateRange(Carbon $startDate, Carbon $endDate): static
    {
        return $this->state(function (array $attributes) use ($startDate, $endDate) {
            $diff = $startDate->diffInSeconds($endDate);
            $randomOffset = $this->faker->numberBetween(0, $diff);

            return [
                'created_at' => $startDate->copy()->addSeconds($randomOffset),
            ];
        });
    }
}
