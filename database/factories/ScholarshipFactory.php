<?php

namespace Database\Factories;

use App\Models\Market;
use App\Models\Region;
use App\Models\State;
use App\Models\SubRegion;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Scholarship>
 */
class ScholarshipFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->sentence(3) . ' Scholarship',
            'description' => $this->faker->paragraph(),
            'amount' => $this->faker->randomFloat(2, 500, 10000),
            'currency' => 'USD',
            'year' => $this->faker->numberBetween(date('Y'), date('Y') + 5),
            'sponsor_name' => $this->faker->company(),
            'region_id' => Region::factory(),
            'market_id' => Market::factory(),
            'state_id' => State::factory(),
            'sub_region_id' => $this->faker->boolean(70) ? SubRegion::factory() : null,
            'is_active' => $this->faker->boolean(80), // 80% chance of being active
        ];
    }
}
