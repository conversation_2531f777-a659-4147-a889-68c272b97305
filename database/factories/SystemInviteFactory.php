<?php

namespace Database\Factories;

use App\Data\SystemInvites\PositiveAthleteInviteData;
use App\Models\SystemInvite;
use App\Models\Nomination;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Enums\ProfileType;

class SystemInviteFactory extends Factory
{
    protected $model = SystemInvite::class;

    public function definition(): array
    {
        return [
            'email' => fake()->email(),
            'token' => Str::random(64),
            'status' => 'pending',
            'type' => ProfileType::POSITIVE_ATHLETE->value,
            'expires_at' => now()->addDays(7),
            'invite_data' => new PositiveAthleteInviteData(
                email: fake()->email(),
                token: Str::random(64),
                created_at: now()->timestamp,
                nomination: Nomination::factory()->create(),
                nominator_email: fake()->email(),
                nominator_name: fake()->name(),
                school_name: fake()->company(),
                sport: fake()->randomElement(['Football', 'Basketball', 'Baseball']),
                relationship: fake()->randomElement(['Coach', 'Athletic Director', 'Teacher']),
                note: fake()->paragraph(),
            ),
        ];
    }

    public function nomination(): self
    {
        return $this->state(function (array $attributes) {
            $nomination = Nomination::factory()->create();

            return [
                'type' => ProfileType::POSITIVE_ATHLETE->value,
                'invite_data' => new PositiveAthleteInviteData(
                    email: $nomination->email,
                    token: Str::random(64),
                    created_at: now()->timestamp,
                    nomination: $nomination,
                    nominator_email: $nomination->nominator_email,
                    nominator_name: "{$nomination->nominator_first_name} {$nomination->nominator_last_name}",
                    school_name: $nomination->school_name,
                    sport: $nomination->sport,
                    relationship: $nomination->relationship,
                    note: $nomination->note,
                ),
            ];
        });
    }

    public function expired(): self
    {
        return $this->state(fn (array $attributes) => [
            'expires_at' => now()->subDay(),
            'status' => 'expired',
        ]);
    }

    public function completed(): self
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
        ]);
    }
}
