<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\FilterView;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\FilterView>
 */
class FilterViewFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->words(3, true),
            'resource_type' => $this->faker->randomElement(['users', 'athletes', 'coaches', 'sponsors']),
            'filter_definition' => [
                '0' => [
                    'conditions' => [
                        [
                            'field' => 'first_name',
                            'operator' => '=',
                            'value' => $this->faker->firstName
                        ]
                    ],
                    'conjunction' => 'and'
                ]
            ],
            'user_id' => User::factory(),
            'is_public' => false,
        ];
    }

    /**
     * Indicate that the filter view is public.
     */
    public function public(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_public' => true,
        ]);
    }
}
