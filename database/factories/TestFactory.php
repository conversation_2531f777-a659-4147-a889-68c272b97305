<?php

namespace Database\Factories;

use App\Enums\TestType;
use App\Models\Course;
use App\Models\Module;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Test>
 */
class TestFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'type' => $this->faker->randomElement([TestType::Exam->value, TestType::Quiz->value]),
            'testable_type' => $this->faker->randomElement([Course::class, Module::class]),
            'testable_id' => null,
        ];
    }
}
