<?php

namespace Database\Factories;

use App\Models\Message;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Message>
 */
class MessageFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'sender_id' => User::factory(),
            'recipient_id' => User::factory(),
            'content' => $this->faker->realText(rand(20, 200)),
            'read_at' => $this->faker->boolean(70) ? Carbon::now()->subMinutes(rand(1, 60)) : null,
            'created_at' => Carbon::now()->subMinutes(rand(1, 10080)), // Up to 1 week ago
            'updated_at' => Carbon::now(),
            'deleted_by_sender_at' => null,
            'deleted_by_recipient_at' => null,
        ];
    }

    /**
     * Indicate that the message has been read.
     *
     * @return $this
     */
    public function read(): static
    {
        return $this->state(fn (array $attributes) => [
            'read_at' => Carbon::now()->subMinutes(rand(1, 60)),
        ]);
    }

    /**
     * Indicate that the message is unread.
     *
     * @return $this
     */
    public function unread(): static
    {
        return $this->state(fn (array $attributes) => [
            'read_at' => null,
        ]);
    }

    /**
     * Indicate that the message has been deleted by the sender.
     *
     * @return $this
     */
    public function deletedBySender(): static
    {
        return $this->state(fn (array $attributes) => [
            'deleted_by_sender_at' => Carbon::now()->subMinutes(rand(1, 60)),
        ]);
    }

    /**
     * Indicate that the message has been deleted by the recipient.
     *
     * @return $this
     */
    public function deletedByRecipient(): static
    {
        return $this->state(fn (array $attributes) => [
            'deleted_by_recipient_at' => Carbon::now()->subMinutes(rand(1, 60)),
        ]);
    }

    /**
     * Create a message between specific users.
     *
     * @param int $senderId
     * @param int $recipientId
     * @return $this
     */
    public function between(int $senderId, int $recipientId): static
    {
        return $this->state(fn (array $attributes) => [
            'sender_id' => $senderId,
            'recipient_id' => $recipientId,
        ]);
    }
}
