<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use App\Enums\ProfileType;
use App\Models\State;

class UserFactory extends Factory
{
    protected $model = User::class;

    public function definition(): array
    {
        // Try to get a valid state from the database
        $state = State::query()->first();
        $stateCode = $state ? $state->code : null;

        return [
            'first_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'email' => $this->faker->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => Hash::make('password'),
            'remember_token' => Str::random(10),
            'handle' => $this->faker->userName(),
            // Add other required fields with reasonable defaults
            'phone' => $this->faker->phoneNumber(),
            'street_address_1' => $this->faker->streetAddress(),
            'city' => $this->faker->city(),
            'state_code' => $stateCode, // Use a valid state from the database
            'zip' => $this->faker->postcode(),
            'recruiter_enabled' => false,
            'profile_type' => ProfileType::POSITIVE_ATHLETE,
        ];
    }

    /**
     * Configure the factory for a Positive Athlete profile type.
     */
    public function positiveAthlete(): static
    {
        return $this->state(function () {
            return [
                'profile_type' => ProfileType::POSITIVE_ATHLETE->value,
            ];
        });
    }

    /**
     * Configure the factory for a Positive Coach profile type.
     */
    public function positiveCoach(): static
    {
        return $this->state(function () {
            return [
                'profile_type' => ProfileType::POSITIVE_COACH->value,
            ];
        });
    }

    /**
     * Configure the factory for an Athletics Director profile type.
     */
    public function athleticsDirector(): static
    {
        return $this->state(function () {
            return [
                'profile_type' => ProfileType::ATHLETICS_DIRECTOR->value,
            ];
        });
    }

    /**
     * Configure the factory for a Sponsor profile type.
     */
    public function sponsor(): static
    {
        return $this->state(function () {
            return [
                'profile_type' => ProfileType::SPONSOR->value,
            ];
        });
    }

    /**
     * Configure the factory for a Parent profile type.
     */
    public function parent(): static
    {
        return $this->state(function () {
            return [
                'profile_type' => ProfileType::PARENT->value,
            ];
        });
    }

    /**
     * Configure the factory for a College Athlete profile type.
     */
    public function collegeAthlete(): static
    {
        return $this->state(function () {
            return [
                'profile_type' => ProfileType::COLLEGE_ATHLETE->value,
            ];
        });
    }

    /**
     * Configure the factory for a Professional profile type.
     */
    public function professional(): static
    {
        return $this->state(function () {
            return [
                'profile_type' => ProfileType::PROFESSIONAL->value,
            ];
        });
    }
}
