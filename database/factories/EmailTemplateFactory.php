<?php

namespace Database\Factories;

use App\Models\EmailTemplate;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EmailTemplate>
 */
class EmailTemplateFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = EmailTemplate::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->words(3, true),
            'subject' => $this->faker->sentence(),
            'preview_text' => $this->faker->sentence(),
            'body' => $this->faker->paragraph(),
            'is_active' => true,
            'created_by' => User::factory(),
        ];
    }

    /**
     * Indicate that the template is inactive.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => false,
            ];
        });
    }

    /**
     * Create a template with specific variables for testing.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function withVariables()
    {
        return $this->state(function (array $attributes) {
            return [
                'subject' => 'Hello {user.firstname}',
                'preview_text' => 'Welcome {user.firstname} to {system.app_name}',
                'body' => 'Dear {user.firstname} {user.lastname}, your email is {user.email}.',
            ];
        });
    }

    /**
     * Create a nominee invitation template.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function nomineeInvitation()
    {
        return $this->state(function (array $attributes) {
            return [
                'name' => 'Nominee Invitation',
                'subject' => 'Invitation for {nominee.firstname}',
                'preview_text' => 'Nominee invitation from {nominator.name}',
                'body' => 'Dear {user.firstname}, {nominee.firstname} {nominee.lastname} has been nominated by {nominator.name}.',
            ];
        });
    }
}
