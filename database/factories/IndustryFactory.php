<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Industry;

class IndustryFactory extends Factory
{
    protected $model = Industry::class;

    protected array $industries = [
        'Technology',
        'Healthcare',
        'Finance',
        'Education',
        'Manufacturing',
        'Retail',
        'Construction',
        'Transportation',
        'Energy',
        'Agriculture',
        'Media',
        'Hospitality',
        'Real Estate',
        'Telecommunications',
        'Professional Services'
    ];

    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->randomElement($this->industries),
        ];
    }
}
