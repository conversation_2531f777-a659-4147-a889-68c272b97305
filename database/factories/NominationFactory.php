<?php

namespace Database\Factories;

use App\Enums\NominationType;
use App\Models\Sport;
use App\Models\County;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Nomination>
 */
class NominationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Get available sports for additional sport generation
        $sports = Sport::pluck('name')->toArray();
        $primarySport = $this->faker->randomElement($sports ?: ['Football', 'Basketball', 'Baseball', 'Soccer']);

        // Generate additional sports (30% chance for multi-sport athletes)
        $sport2 = $this->faker->boolean(30) && count($sports) > 1
            ? $this->faker->randomElement(array_diff($sports, [$primarySport]))
            : null;
        $sport3 = $this->faker->boolean(15) && count($sports) > 2
            ? $this->faker->randomElement(array_diff($sports, [$primarySport, $sport2]))
            : null;

        // Generate grade for athletes
        $type = $this->faker->randomElement([NominationType::ATHLETE->value, NominationType::COACH->value]);
        $grade = null;
        if ($type === NominationType::ATHLETE->value) {
            $currentGrade = $this->faker->randomElement(['9', '10', '11', '12']);
            $grade = (int) $currentGrade;
        }

        // Generate realistic location data
        $counties = County::with('market.region')->inRandomOrder()->take(10)->get();
        $county = $counties->isNotEmpty() ? $counties->random() : null;

        // Generate parent/guardian info for athletes with grades
        $parentInfo = [];
        if ($type === NominationType::ATHLETE->value && $grade) {
            $parentInfo = [
                'parent_guardian_first_name' => $this->faker->firstName,
                'parent_guardian_last_name' => $this->faker->lastName,
                'parent_guardian_email' => $this->faker->email,
                'parent_guardian_phone' => $this->faker->phoneNumber,
            ];
        }

        return [
            // Original fields
            'nominator_email' => $this->faker->email(),
            'nominator_first_name' => $this->faker->firstName(),
            'nominator_last_name' => $this->faker->lastName(),
            'email' => $this->faker->email(),
            'first_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'school_name' => $this->faker->company(),
            'sport' => $primarySport,
            'relationship' => $this->faker->randomElement(['Coach', 'Teacher', 'Parent', 'Athletic Director']),
            'note' => $this->faker->paragraph(),
            'status' => $this->faker->randomElement(['pending', 'verified', 'notified', 'acknowledged']),
            'type' => $type,
            'ai_score' => $this->faker->numberBetween(20, 90),

            // Location Information
            'state_code' => $county?->state_code ?? $this->faker->stateAbbr,
            'county' => $county?->name ?? $this->faker->city . ' County',

            // Enhanced Contact Information
            'nominee_phone' => $this->faker->phoneNumber,
            'nominator_phone' => $this->faker->phoneNumber,

            // Multiple Sports Support
            'sport_2' => $sport2,
            'sport_3' => $sport3,
            'other_sport' => $this->faker->boolean(10) ? $this->faker->randomElement(['Adapted Basketball', 'Unified Soccer', 'Special Olympics Track']) : null,

            // Demographic & Academic Information
            'gender' => $this->faker->randomElement(['Male', 'Female', 'Non-binary', 'Prefer not to say']),
            'grade' => $grade,

            // Parent/Guardian Information
            ...$parentInfo,

            // Social Media & Digital Presence
            'instagram_handle' => $this->faker->boolean(60) ? '@' . $this->faker->userName : null,
            'twitter_handle' => $this->faker->boolean(40) ? '@' . $this->faker->userName : null,

            // Marketing & Attribution Data
            'how_did_you_hear' => $this->faker->randomElement([
                'School counselor',
                'Coach recommendation',
                'Social media',
                'Friend/family',
                'School newsletter',
                'Athletic department',
                'Website search'
            ]),
            'referral_source_name' => $this->faker->boolean(50) ? $this->faker->company : null,

            // Processing Workflow Enhancement
            'processing_status' => $this->faker->randomElement(['received', 'validated', 'invited', 'onboarded']),
            'processed_at' => $this->faker->boolean(70) ? $this->faker->dateTimeBetween('-3 months', 'now') : null,

            // JotForm Integration Metadata
            'jotform_submission_id' => $this->faker->uuid,
            'jotform_form_id' => $this->faker->randomElement(['************', '************', '************']),
            'location_resolution_notes' => $this->faker->boolean(20) ? [
                'status' => 'resolved',
                'method' => 'automated_geocoding',
                'confidence' => $this->faker->randomFloat(2, 0.8, 1.0)
            ] : null,
        ];
    }
}
