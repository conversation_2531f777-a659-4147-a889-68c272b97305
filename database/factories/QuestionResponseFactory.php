<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\QuestionResponse>
 */
class QuestionResponseFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $correct = $this->faker->boolean;
        return [
            'user_id' => null,
            'question_id' => null,
            'response' => $this->faker->sentence,
            'correct' => $correct,
        ];
    }
}
