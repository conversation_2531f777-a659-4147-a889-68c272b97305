<?php

namespace Database\Factories;

use App\Models\County;
use App\Models\Market;
use App\Models\SubRegion;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\County>
 */
class CountyFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = County::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'market_id' => Market::factory(),
            'sub_region_id' => null, // Will be set in seeder when needed
            'name' => $this->faker->county,
            'state' => $this->faker->stateAbbr,
        ];
    }

    /**
     * Indicate that the county belongs to a sub-region.
     */
    public function withSubRegion(): static
    {
        return $this->state(fn (array $attributes) => [
            'sub_region_id' => SubRegion::factory()
                ->for(Market::find($attributes['market_id']) ?? Market::factory()),
        ]);
    }
}
