<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\UserBlock;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\UserBlock>
 */
class UserBlockFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'blocker_id' => User::factory(),
            'blocked_id' => User::factory(),
        ];
    }

    /**
     * Create a block between specific users.
     *
     * @param int $blockerId
     * @param int $blockedId
     * @return $this
     */
    public function between(int $blockerId, int $blockedId): static
    {
        return $this->state(fn (array $attributes) => [
            'blocker_id' => $blockerId,
            'blocked_id' => $blockedId,
        ]);
    }
}
