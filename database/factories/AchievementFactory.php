<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Achievement>
 */
class AchievementFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => null,
            'order' => $this->faker->randomDigit,
            'name' => $this->faker->sentence(3),
            'date' => $this->faker->date(),
            'description' => $this->faker->paragraph
        ];
    }
}
