<?php

namespace Database\Factories;

use App\Models\Course;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Course>
 */
class CourseFactory extends Factory
{
    protected $model = Course::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'presenter' => $this->faker->name(),
            'title' => $this->faker->sentence(3),
            'description' => $this->faker->paragraphs(2, true),
            'published' => $this->faker->boolean(80), // 80% chance of being published
            'order' => $this->faker->numberBetween(1, 100),
        ];
    }

    /**
     * Indicate that the course is published.
     */
    public function published()
    {
        return $this->state(function (array $attributes) {
            return [
                'published' => true,
            ];
        });
    }

    /**
     * Indicate that the course is unpublished.
     */
    public function unpublished()
    {
        return $this->state(function (array $attributes) {
            return [
                'published' => false,
            ];
        });
    }
}
