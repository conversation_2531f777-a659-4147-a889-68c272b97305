<?php

namespace Database\Factories;

use App\Models\Award;
use App\Models\Scholarship;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Winner>
 */
class WinnerFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $isWinner = fake()->boolean(60); // 60% chance of being a winner
        $isFinalist = $isWinner ? true : fake()->boolean(50); // Winners are always finalists

        // Use static arrays for verification_state and tshirt_size
        $verificationStates = ['pending', 'verified', 'rejected'];
        $tshirtSizes = ['XS', 'S', 'M', 'L', 'XL', 'XXL'];

        return [
            'user_id' => User::factory(),
            'scholarship_id' => fake()->boolean(50) ? Scholarship::factory() : null,
            'award_id' => fake()->boolean(50) ? Award::factory() : null,
            'year' => fake()->numberBetween(date('Y') - 3, date('Y') + 1),
            'is_finalist' => $isFinalist,
            'is_winner' => $isWinner,
            'verification_state' => $verificationStates[array_rand($verificationStates)],
            'verified_at' => fake()->optional(0.7)->dateTimeThisYear(), // 70% chance of having verification date
            'notified_at' => fake()->optional(0.8)->dateTimeThisYear(), // 80% chance of having notification date
            'acknowledged_at' => fake()->optional(0.6)->dateTimeThisYear(), // 60% chance of having acknowledgment date
            'tshirt_size' => $tshirtSizes[array_rand($tshirtSizes)],
        ];
    }

    /**
     * Configure the winner for a specific scholarship.
     */
    public function forScholarship(Scholarship $scholarship)
    {
        return $this->state(function (array $attributes) use ($scholarship) {
            return [
                'scholarship_id' => $scholarship->id,
                'award_id' => null,
                'year' => $scholarship->year,
            ];
        });
    }

    /**
     * Configure the winner for a specific award.
     */
    public function forAward(Award $award)
    {
        return $this->state(function (array $attributes) use ($award) {
            return [
                'award_id' => $award->id,
                'scholarship_id' => null,
                'year' => $award->year,
            ];
        });
    }

    /**
     * Configure the winner for a specific user.
     */
    public function forUser(User $user)
    {
        return $this->state(function (array $attributes) use ($user) {
            return [
                'user_id' => $user->id,
            ];
        });
    }

    /**
     * Configure the winner as a finalist.
     */
    public function asFinalist()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_finalist' => true,
                'is_winner' => false,
            ];
        });
    }

    /**
     * Configure the winner as a winner.
     */
    public function asWinner()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_finalist' => true,
                'is_winner' => true,
            ];
        });
    }
}
