<?php

namespace Database\Factories;

use App\Enums\AwardType;
use App\Models\Region;
use App\Models\Market;
use App\Models\State;
use App\Models\SubRegion;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Award>
 */
class AwardFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $types = [
            AwardType::REGIONAL,
            AwardType::MARKET,
            AwardType::SUBREGIONAL,
        ];

        // Get or create a default state in a robust way
        $state = State::query()->updateOrCreate(
            ['code' => 'CA'],
            ['name' => 'California']
        );

        return [
            'name' => fake()->sentence(3) . ' Award',
            'details' => fake()->paragraph(3),
            'type' => $types[array_rand($types)],
            'region_id' => Region::factory(),
            'market_id' => Market::factory(),
            'state_id' => $state->code,
            'subregion_id' => fake()->boolean(30) ? SubRegion::factory() : null, // 30% chance of having a subregion
            'year' => fake()->numberBetween(date('Y'), date('Y') + 5),
            'is_active' => fake()->boolean(80), // 80% chance of being active
        ];
    }

    /**
     * Configure the award for a specific region.
     */
    public function forRegion(Region $region)
    {
        return $this->state(function (array $attributes) use ($region) {
            return [
                'region_id' => $region->id,
                'type' => AwardType::REGIONAL,
            ];
        });
    }

    /**
     * Configure the award for a specific market.
     */
    public function forMarket(Market $market)
    {
        return $this->state(function (array $attributes) use ($market) {
            return [
                'market_id' => $market->id,
                'type' => AwardType::MARKET,
            ];
        });
    }

    /**
     * Configure the award for a specific subregion.
     */
    public function forSubRegion(SubRegion $subRegion)
    {
        return $this->state(function (array $attributes) use ($subRegion) {
            return [
                'subregion_id' => $subRegion->id,
                'type' => AwardType::SUBREGIONAL,
            ];
        });
    }
}
