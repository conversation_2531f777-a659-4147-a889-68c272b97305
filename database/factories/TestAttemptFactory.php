<?php

namespace Database\Factories;

use App\Enums\TestStatus;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\TestAttempt>
 */
class TestAttemptFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $started = $this->faker->dateTimeBetween('-1 day', 'now');
        $completed = $this->faker->boolean(50) ? $this->faker->dateTimeBetween($started, '+1 hour') : null;
        $status = $completed ? TestStatus::Complete->value : $this->faker->randomElement([
            TestStatus::InProgress->value,
            TestStatus::PendingReview->value,
        ]);

        return [
            'user_id' => null,
            'test_id' => null,
            'started_at' => $started,
            'ends_at' => $this->faker->dateTimeBetween('now', '+1 hour'),
            'completed_at' => $completed,
            'score' => $completed ? $this->faker->randomFloat(2, 0, 100) : null,
            'status' => $status,
        ];
    }
}
