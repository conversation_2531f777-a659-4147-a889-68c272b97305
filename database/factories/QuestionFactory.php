<?php

namespace Database\Factories;

use App\Enums\QuestionType;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Question>
 */
class QuestionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'test_id' => null,
            'question' => $this->faker->sentence,
            'type' => $this->faker->randomElement([
                QuestionType::MultipleChoice->value,
                QuestionType::LongText->value,
                QuestionType::ShortText->value
            ]),
        ];
    }
}
