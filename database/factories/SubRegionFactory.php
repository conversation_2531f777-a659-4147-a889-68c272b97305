<?php

namespace Database\Factories;

use App\Models\Market;
use App\Models\SubRegion;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SubRegion>
 */
class SubRegionFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = SubRegion::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $directions = ['Northern', 'Southern', 'Eastern', 'Western', 'Central'];
        $name = $directions[array_rand($directions)] . ' ' . fake()->city;

        return [
            'market_id' => Market::factory(),
            'name' => $name,
            'slug' => Str::slug($name),
        ];
    }
}
