<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Endorsement>
 */
class EndorsementFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Common FontAwesome icon names for endorsements
        $icons = [
            'sun-bright', 'people-group', 'smile', 'handshake',
            'puzzle-piece', 'heart', 'hand-holding-seedling',
            'thumbs-up', 'star', 'medal', 'trophy'
        ];

        return [
            'name' => fake()->word(),
            'icon' => fake()->randomElement($icons),
        ];
    }
}
