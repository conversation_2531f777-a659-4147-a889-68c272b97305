<?php

namespace Database\Factories;

use App\Models\Organization;
use App\Models\User;
use Database\Faker\Providers\HtmlProvider;
use Database\Seeders\Traits\HasOrganizationNames;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrganizationFactory extends Factory
{
    use HasOrganizationNames;

    protected $model = Organization::class;

    public function __construct()
    {
        parent::__construct();

        // Register the HTML provider
        $this->faker->addProvider(new HtmlProvider($this->faker));
    }

    public function definition(): array
    {
        return [
            'name' => $this->getRandomOrganizationName(),
            'type' => 'employer',
            'user_id' => User::factory(),
            'website' => $this->faker->url(),
            'about' => $this->faker->htmlRichText(3),
        ];
    }

    /**
     * Configure the model factory.
     *
     * @return $this
     */
    public function configure()
    {
        return $this->afterCreating(function (Organization $organization) {
            $this->addOrganizationLogo($organization);
        });
    }
}
