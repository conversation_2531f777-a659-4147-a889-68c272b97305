<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Sport>
 */
class SportFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->randomElement(['Basketball', 'Football', 'Baseball', 'Soccer', 'Tennis', 'Golf', 'Swimming', 'Basketball', 'Football', 'Baseball', 'Soccer', 'Tennis', 'Golf', 'Swimming']);
        return [
            'name' => ucfirst($name),
            'slug' => $this->faker->unique()->slug
        ];
    }
}
