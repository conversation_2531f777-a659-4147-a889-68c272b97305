<?php

namespace Database\Factories;

use App\Enums\ProfileType;
use App\Models\SystemInvite;
use App\States\Onboarding\OnboardingState;
use Illuminate\Database\Eloquent\Factories\Factory;

class OnboardingFactory extends Factory
{
    public function definition(): array
    {
        return [
            'system_invite_id' => SystemInvite::factory(),
            'state' => OnboardingState::class, // We'll need to define this
            'profile_type' => ProfileType::POSITIVE_ATHLETE,
            'data' => []
        ];
    }

    public function forProfileType(ProfileType $type): self
    {
        return $this->state(fn (array $attributes) => [
            'profile_type' => $type
        ]);
    }

    public function withData(array $data): self
    {
        return $this->state(fn (array $attributes) => [
            'data' => $data
        ]);
    }
}
