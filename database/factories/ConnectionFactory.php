<?php

namespace Database\Factories;

use App\Enums\ConnectionStatus;
use App\Models\Connection;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Connection>
 */
class ConnectionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'requester_id' => User::factory(),
            'recipient_id' => User::factory(),
            'status' => ConnectionStatus::PENDING,
        ];
    }

    /**
     * Indicate that the connection is accepted.
     *
     * @return $this
     */
    public function accepted(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => ConnectionStatus::ACCEPTED,
        ]);
    }

    /**
     * Indicate that the connection is rejected.
     *
     * @return $this
     */
    public function rejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => ConnectionStatus::REJECTED,
        ]);
    }

    /**
     * Create a connection between specific users.
     *
     * @param int $requesterId
     * @param int $recipientId
     * @return $this
     */
    public function between(int $requesterId, int $recipientId): static
    {
        return $this->state(fn (array $attributes) => [
            'requester_id' => $requesterId,
            'recipient_id' => $recipientId,
        ]);
    }
}
