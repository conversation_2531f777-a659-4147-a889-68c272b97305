<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Opportunity;
use App\Models\Organization;
use App\Enums\OpportunityType;
use App\Enums\OpportunityTerm;
use App\Enums\OpportunityStatus;
use App\Enums\OpportunitySubtype;
use App\Enums\OpportunityLocationType;
use App\Models\Industry;
use App\Models\Interest;
use Database\Faker\Providers\HtmlProvider;
use Illuminate\Support\Facades\Log;

class OpportunityFactory extends Factory
{
    protected $model = Opportunity::class;

    public function __construct()
    {
        parent::__construct();

        // Register the HTML provider
        $this->faker->addProvider(new HtmlProvider($this->faker));
    }

    public function definition(): array
    {
        // Get a random city/state combination from our list
        $usCities = require database_path('data/us_cities.php');
        $location = $this->faker->randomElement($usCities);

        return [
            'organization_id' => Organization::factory(),
            'user_id' => null, // This will be set when creating opportunities for specific sponsors
            'title' => $this->faker->jobTitle(),
            'description' => $this->faker->htmlRichText(3), // Rich HTML content
            'city' => $location['city'],
            'state_code' => $location['state_code'],
            'is_featured' => $this->faker->boolean(20), // 20% chance of being featured
            'created_at' => $this->faker->dateTimeBetween('-3 months', 'now'),
            'updated_at' => function (array $attributes) {
                return $attributes['created_at'];
            },
            'visible_start_date' => $this->faker->dateTimeBetween('now', '+1 week'),
            'visible_end_date' => $this->faker->dateTimeBetween('+1 week', '+3 months'),
            'type' => OpportunityType::Employment->value,
            'subtype' => OpportunitySubtype::FullTimeJob->value,
            'status' => OpportunityStatus::LISTED->value,
            'location_type' => OpportunityLocationType::Onsite->value,
            'details' => $this->faker->htmlRichText(2), // Rich HTML content
            'qualifications' => $this->faker->htmlRichText(2), // Rich HTML content
            'responsibilities' => $this->faker->htmlRichText(2), // Rich HTML content
            'benefits' => $this->faker->htmlRichText(2), // Rich HTML content
            'apply_url' => $this->faker->url,
            'term' => OpportunityTerm::Indefinite->value,
        ];
    }

    public function withIndustries(int $count = 2): self
    {
        return $this->afterCreating(function (Opportunity $opportunity) use ($count) {
            $opportunity->industries()->attach(
                Industry::query()->inRandomOrder()->limit($count)->pluck('id')
            );
        });
    }

    public function withInterests(int $count = 2): self
    {
        return $this->afterCreating(function (Opportunity $opportunity) use ($count) {
            // Get all available interests
            $interests = Interest::query()->inRandomOrder()->limit($count)->get();

            if ($interests->isEmpty()) {
                Log::warning("No interests found when using withInterests factory method");
                return;
            }

            // Attach interests
            $opportunity->interests()->attach($interests->pluck('id')->toArray());
        });
    }

    public function withSponsor(int $userId): self
    {
        return $this->state(function (array $attributes) use ($userId) {
            return [
                'user_id' => $userId,
            ];
        });
    }
}
