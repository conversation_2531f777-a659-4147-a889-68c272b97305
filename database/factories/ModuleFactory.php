<?php

namespace Database\Factories;

use App\Models\Module;
use App\Enums\ModuleType;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Module>
 */
class ModuleFactory extends Factory
{
    protected $model = Module::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = fake()->unique()->sentence(4);
        $baseSlug = Str::slug($name);

        // Add a unique suffix to ensure uniqueness
        $slug = $baseSlug;
        $counter = 1;

        while (Module::query()->where('slug', $slug)->exists()) {
            $slug = $baseSlug . '-' . $counter++;
        }

        return [
            'name' => $name,
            'slug' => $slug,
            'description' => fake()->paragraph(),
            'content' => null,
            'video_url' => null,
            'minutes' => fake()->numberBetween(5, 45),
            'published' => fake()->boolean(80),
            'type' => ModuleType::Video->value,
        ];
    }

    /**
     * Indicate that the module has HTML content.
     */
    public function withContent(): static
    {
        $content = fake()->htmlRichText(3);

        return $this->state(function (array $attributes) use ($content) {
            return [
                'content' => $content,
                'minutes' => fake()->numberBetween(10, 30),
                'type' => ModuleType::Article->value,
            ];
        });
    }

    /**
     * Create a video module.
     */
    public function video(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => ModuleType::Video->value,
                'content' => null,
                'video_url' => fake()->url(),
                'minutes' => fake()->numberBetween(5, 45),
            ];
        });
    }

    /**
     * Create an article module.
     */
    public function article(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => ModuleType::Article->value,
                'content' => fake()->htmlRichText(3),
                'video_url' => null,
                'minutes' => fake()->numberBetween(10, 30),
            ];
        });
    }

    /**
     * Create an exam module.
     */
    public function exam(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => ModuleType::Exam->value,
                'content' => null,
                'video_url' => null,
                'minutes' => null,
            ];
        });
    }

    /**
     * Indicate that the module is published.
     */
    public function published(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'published' => true,
            ];
        });
    }

    /**
     * Indicate that the module is unpublished.
     */
    public function unpublished(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'published' => false,
            ];
        });
    }
}
