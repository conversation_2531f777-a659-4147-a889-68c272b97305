<?php

namespace Database\Factories;

use App\Models\Engagement;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EngagementAggregate>
 */
class EngagementAggregateFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'event_type' => $this->faker->randomElement([
                Engagement::EVENT_IMPRESSION,
                Engagement::EVENT_CLICK
            ]),
            'date' => Carbon::now()->subDays($this->faker->numberBetween(0, 60))->startOfDay(),
            'count' => $this->faker->numberBetween(1, 500),
        ];
    }

    /**
     * Configure the factory to generate impression aggregates
     *
     * @return static
     */
    public function impression(): static
    {
        return $this->state(fn (array $attributes) => [
            'event_type' => Engagement::EVENT_IMPRESSION,
        ]);
    }

    /**
     * Configure the factory to generate click aggregates
     *
     * @return static
     */
    public function click(): static
    {
        return $this->state(fn (array $attributes) => [
            'event_type' => Engagement::EVENT_CLICK,
        ]);
    }

    /**
     * Configure the factory to generate aggregates for a specific date
     *
     * @param Carbon $date
     * @return static
     */
    public function forDate(Carbon $date): static
    {
        return $this->state(fn (array $attributes) => [
            'date' => $date->startOfDay(),
        ]);
    }

    /**
     * Configure the factory to generate a sequence of daily aggregates within a date range
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function sequenceWithinDateRange(Carbon $startDate, Carbon $endDate)
    {
        $dates = [];
        $currentDate = $startDate->copy()->startOfDay();

        while ($currentDate->lte($endDate)) {
            $dates[] = $currentDate->copy();
            $currentDate->addDay();
        }

        return $this->sequence(
            ...array_map(fn($date) => ['date' => $date], $dates)
        );
    }
}
