# Content Moderation with Google Natural Language API

This document provides information about the content moderation system implemented in the Positive Athlete platform using Google's Natural Language API.

## Overview

The content moderation system automatically analyzes message content for harmful or inappropriate content before it is delivered to recipients. This helps maintain a safe and positive environment for all users, especially students.

## Implementation Details

### Architecture

- The system uses Google's Natural Language API, specifically the `moderateText` endpoint
- Messages are analyzed before being saved to the database
- Results are stored with each message for reference and auditing

### Database Schema

Messages table includes the following moderation-related fields:

- `is_flagged` (boolean): Indicates if the message has been flagged for inappropriate content
- `moderation_result` (json): Stores the complete response from the Google Natural Language API

### Moderation Categories

The system checks for a comprehensive set of harmful and sensitive content categories, including:

#### High-Risk Categories (Lower Threshold)
- Sexual
- Toxic
- Insult
- Profanity
- Derogatory
- Violent
- Death, Harm & Tragedy
- Firearms & Weapons
- Public Safety
- Illicit Drugs

#### Medium-Risk Categories (Higher Threshold)
- Health
- War & Conflict

#### Low-Risk Categories (Very High Threshold)
- Religion & Belief
- Politics
- Finance
- Legal

Each category has a confidence threshold that determines when content should be flagged:

| Category | Threshold |
|----------|-----------|
| Sexual | 0.7 |
| Toxic | 0.7 |
| Insult | 0.7 |
| Profanity | 0.8 |
| Derogatory | 0.7 |
| Violent | 0.7 |
| Death, Harm & Tragedy | 0.8 |
| Firearms & Weapons | 0.8 |
| Public Safety | 0.8 |
| Illicit Drugs | 0.8 |
| Health | 0.9 |
| War & Conflict | 0.9 |
| Religion & Belief | 0.95 |
| Politics | 0.95 |
| Finance | 0.95 |
| Legal | 0.95 |

## Setup Instructions

### Google Cloud Setup

1. Create a Google Cloud project
2. Enable the Natural Language API
3. Create a service account with access to the Natural Language API
4. Download the JSON credentials file

### Application Setup

1. Place the JSON credentials file in `storage/app/google-credentials/google-credentials.json`
2. Ensure the directory is included in `.gitignore` to prevent credentials from being tracked
3. Run the database migration to add the moderation fields to the messages table:
   ```
   php artisan migrate
   ```

## Usage

The content moderation happens automatically when a message is sent through the `MessageService::sendMessage()` method. No additional steps are required to enable moderation.

### Viewing Moderation Results

Moderation results are stored in the `moderation_result` field of the message record. This can be used to provide context in the UI about why a message was flagged.

Example of accessing moderation data:

```php
$message = Message::find($messageId);

if ($message->is_flagged) {
    $moderationResult = $message->moderation_result;
    $categories = $moderationResult['moderationCategories'] ?? [];
    
    // Process categories...
}
```

### Technical Implementation

The content moderation is implemented in the `GoogleNaturalLanguageService` class, which:

1. Uses the Google Cloud PHP SDK to interact with the Natural Language API
2. Creates a `Document` object containing the message content
3. Creates a `ModerateTextRequest` object with the document
4. Sends the request to the API using the `LanguageServiceClient`
5. Processes the response to determine if the content should be flagged
6. Returns the result with the flag status and complete moderation data

The service explicitly passes the credentials file path to the client, avoiding environment variable manipulation:

```php
$languageClient = new LanguageServiceClient([
    'credentials' => $this->credentialsPath
]);
```

### Response Format

The API returns a comprehensive set of moderation categories with confidence scores. Each category includes:

- `name`: The name of the category (e.g., "Sexual", "Toxic", "Politics")
- `confidence`: A float value between 0 and 1 indicating how confident the model is
- `severity`: A severity score for the category

The system compares the confidence score against predefined thresholds to determine if content should be flagged.

## Troubleshooting

### Common Issues

1. **Missing Credentials**: Ensure the Google credentials file exists at the specified path
2. **API Quota Exceeded**: Check your Google Cloud console for quota usage
3. **Permission Issues**: Verify the service account has the correct permissions

### Logging

All moderation activities and errors are logged. Check the Laravel logs for details about any issues:

```
tail -f storage/logs/laravel.log
```

## Testing

For testing the content moderation without using actual harmful content:

1. Use mild examples that hint at problematic content without being explicit
2. Use the included test script (`test-moderation.php`) to test different inputs
3. Review the logs to see which categories are being triggered

## References

- [Google Natural Language API Documentation](https://cloud.google.com/natural-language/docs/reference/rest/v2/documents/moderateText)
- [Google Cloud PHP SDK Documentation](https://cloud.google.com/php/docs/reference/cloud-language/latest) 
