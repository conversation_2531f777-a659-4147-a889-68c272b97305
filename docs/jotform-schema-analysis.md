# JotForm Schema Alignment Analysis

## Executive Summary

This document provides a comprehensive analysis of the current `nominations` table schema compared with the JotForm field mapping requirements. We have successfully completed the initial alignment phase by adding the `user_id` foreign key and updating the onboarding services.

## Current Nominations Table Schema

### Existing Columns

| Column Name | Data Type | Nullable | Constraints | Purpose |
|------------|-----------|----------|-------------|---------|
| `id` | bigint | NO | PRIMARY KEY | Primary identifier |
| `system_invite_id` | bigint | YES | FOREIGN KEY (system_invites.id) | Links to invitation |
| `user_id` | bigint | YES | FOREIGN KEY (users.id) | **NEW**: Links to created user after onboarding |
| `nominator_email` | varchar(255) | YES | | Email of person making nomination |
| `nominator_first_name` | varchar(255) | YES | | Nominator's first name |
| `nominator_last_name` | varchar(255) | YES | | Nominator's last name |
| `email` | varchar(255) | YES | | Nominated person's email |
| `first_name` | varchar(255) | YES | | Nominated person's first name |
| `last_name` | varchar(255) | YES | | Nominated person's last name |
| `school_name` | varchar(255) | YES | | School name |
| `school_id` | bigint | YES | FOREIGN KEY (schools.id) | Links to schools table |
| `sport` | varchar(255) | YES | | Primary sport |
| `relationship` | varchar(255) | YES | | Nominator's relationship to nominee |
| `note` | text | YES | | Additional notes |
| `status` | varchar(255) | YES | DEFAULT 'pending' | Nomination status |
| `type` | varchar(255) | YES | | Nomination type (Athlete/Coach) |
| `created_at` | timestamp(0) | YES | | Created timestamp |
| `updated_at` | timestamp(0) | YES | | Updated timestamp |

## JotForm Field Mapping Analysis

Based on our `JotFormFieldMappingService` and actual form data, here are the key field mappings:

### ✅ Well-Mapped Fields

| JotForm Field | Current Column | Status |
|---------------|----------------|--------|
| `nominatedPerson.first` | `first_name` | ✅ Direct mapping |
| `nominatedPerson.last` | `last_name` | ✅ Direct mapping |
| `nomineeEmail` | `email` | ✅ Direct mapping |
| `nominatorFullName.first` | `nominator_first_name` | ✅ Direct mapping |
| `nominatorFullName.last` | `nominator_last_name` | ✅ Direct mapping |
| `nominatorEmail` | `nominator_email` | ✅ Direct mapping |
| `relationshipToNominee` | `relationship` | ✅ Direct mapping |
| `whyIsThisNominee` | `note` | ✅ Direct mapping |
| `myNomineeIs` | `type` | ✅ Mapped via enum |

### ⚠️ Fields Requiring Additional Consideration

| JotForm Field | Current Handling | Recommendation |
|---------------|------------------|----------------|
| **Location Data** | | |
| `state` | Not stored in nominations | Consider adding for filtering/reporting |
| `county` (dynamic per state) | Not stored in nominations | Consider adding for geographic analysis |
| **School Information** | | |
| `highSchool` or `nameOfSchool` | Maps to `school_name` | ✅ Good mapping |
| `otherSchool` | Maps to `school_name` if primary empty | ✅ Good fallback logic |
| **Sports Data** | | |
| `sport1`, `sport2`, `sport3` | Only `sport1` → `sport` | Consider storing multiple sports |
| `otherSport` | Maps to `sport` if others empty | ✅ Good fallback logic |
| **Contact Information** | | |
| `nomineePhone` | Not stored in nominations | Consider adding for outreach |
| `parentGuardianFullName` | Not stored in nominations | Consider adding for minors |
| `parentGuardianEmail` | Not stored in nominations | Consider adding for minors |
| `parentGuardianPhone` | Not stored in nominations | Consider adding for minors |
| `nominatorPhone` | Not stored in nominations | Consider adding for outreach |
| **Social Media** | | |
| `instagramHandle` | Not stored in nominations | Consider adding for profile building |
| `twitterHandle` | Not stored in nominations | Consider adding for profile building |
| **Additional Context** | | |
| `gender` | Not stored in nominations | Consider adding for demographic analysis |
| `grade` | Not stored in nominations | Consider adding for eligibility |
| `howDidYouHear` | Not stored in nominations | Consider adding for marketing analysis |

## Recent Changes Implemented

### 1. Database Schema Updates
- ✅ Added `user_id` column to `nominations` table
- ✅ Added foreign key constraint to `users` table
- ✅ Set up `nullOnDelete` behavior for data integrity
- ✅ Added index for performance

### 2. Model Updates
- ✅ Updated `Nomination` model to include `user_id` in fillable array
- ✅ Added `user()` relationship method
- ✅ Updated OpenAPI documentation

### 3. Data Class Updates
- ✅ Updated `NominationData` class to include optional `user_id` field
- ✅ Updated OpenAPI schema documentation

### 4. Onboarding Service Integration
- ✅ Updated `PositiveAthleteOnboardingService::completeOnboarding()` to link nominations
- ✅ Updated `PositiveCoachOnboardingService::completeOnboarding()` to link nominations
- ✅ Added `linkNominationToUser()` private method to both services

## Integration Flow

```mermaid
sequenceDiagram
    participant JF as JotForm
    participant WH as Webhook Handler
    participant FMS as Field Mapping Service
    participant NS as Nomination Service
    participant DB as Database
    participant OS as Onboarding Service
    
    JF->>WH: Form Submission
    WH->>FMS: Process Raw Data
    FMS->>NS: Create Nomination
    NS->>DB: Store Nomination (user_id: null)
    Note over DB: Nomination created without user
    
    rect rgb(200, 255, 200)
        Note over OS: Later: User completes onboarding
        OS->>DB: Create User
        OS->>DB: Update Nomination.user_id
    end
```

## Recommendations for Phase 3

### 1. Enhanced Field Storage (Optional)
Consider adding these optional fields to support richer nominations:

```sql
-- Potential additional fields for nominations table
ALTER TABLE nominations ADD COLUMN state_code VARCHAR(2);
ALTER TABLE nominations ADD COLUMN county_id BIGINT;
ALTER TABLE nominations ADD COLUMN sport_2 VARCHAR(255);
ALTER TABLE nominations ADD COLUMN sport_3 VARCHAR(255);
ALTER TABLE nominations ADD COLUMN gender VARCHAR(20);
ALTER TABLE nominations ADD COLUMN grade VARCHAR(20);
ALTER TABLE nominations ADD COLUMN nominee_phone VARCHAR(20);
ALTER TABLE nominations ADD COLUMN nominator_phone VARCHAR(20);
ALTER TABLE nominations ADD COLUMN instagram_handle VARCHAR(255);
ALTER TABLE nominations ADD COLUMN twitter_handle VARCHAR(255);
ALTER TABLE nominations ADD COLUMN how_did_you_hear VARCHAR(255);
```

### 2. Parent/Guardian Support
For nominations of minors, consider adding parent/guardian information:

```sql
-- Parent/Guardian support
ALTER TABLE nominations ADD COLUMN parent_guardian_first_name VARCHAR(255);
ALTER TABLE nominations ADD COLUMN parent_guardian_last_name VARCHAR(255);
ALTER TABLE nominations ADD COLUMN parent_guardian_email VARCHAR(255);
ALTER TABLE nominations ADD COLUMN parent_guardian_phone VARCHAR(20);
```

### 3. Processing Status Enhancement
Consider adding processing status tracking:

```sql
-- Processing status
ALTER TABLE nominations ADD COLUMN processing_status ENUM('received', 'validated', 'invited', 'onboarded') DEFAULT 'received';
ALTER TABLE nominations ADD COLUMN processed_at TIMESTAMP NULL;
```

## Testing Strategy

### 1. Unit Tests
- ✅ Test Nomination model relationships
- ✅ Test NominationData validation
- ✅ Test onboarding service nomination linking

### 2. Integration Tests
- ✅ Test JotForm webhook → nomination creation flow
- ✅ Test nomination → user creation → linking flow
- ✅ Test field mapping service with various form submissions

### 3. Data Integrity Tests
- ✅ Test foreign key constraints
- ✅ Test cascading behavior (user deletion → nomination.user_id = null)
- ✅ Test duplicate prevention

## Conclusion

The nominations schema is now well-aligned with the JotForm integration requirements. The core functionality for linking nominations to users post-onboarding has been implemented. Additional field storage can be added incrementally based on business requirements and usage patterns.

**Key Achievements:**
- ✅ User linking functionality implemented
- ✅ Data integrity maintained with proper foreign keys
- ✅ Onboarding services updated to create user-nomination relationships
- ✅ Field mapping service handles complex form structures
- ✅ Schema is extensible for future enhancements

**Status:** Phase 2 Complete ✅
**Next Steps:** Implement JotForm webhook → nomination creation pipeline integration 
