# School Import Tool

The School Import Tool allows administrators to import schools and their associated geographic data (counties, markets, sub-regions, and regions) from a CSV file.

## Accessing the Tool

1. Log in to the Positive Athlete Admin Panel
2. Navigate to the "Tools" section in the sidebar
3. Click on "School Import"

## CSV File Format

Your CSV file must include the following columns:

- **Account Name** - The name of the school
- **State** - The state code (e.g., PA, OH)
- **County** - The county where the school is located
- **Market** - The market name
- **Region** - The region name
- **Sub-Region** - The sub-region name (optional)
- **School Address** - The school's street address (optional)
- **School City** - The city where the school is located (optional)
- **School Zip Code** - The school's ZIP code (optional)

### Example CSV Format

```csv
Account Name,State,County,Market,Region,Sub-Region,School Address,School City,School Zip Code
Central High School,PA,Allegheny,Pittsburgh,Pennsylvania,Western PA,123 Main St,Pittsburgh,15213
East High School,OH,Franklin,Columbus,Ohio,Central OH,456 Oak Ave,Columbus,43215
```

## Import Process

1. Prepare your CSV file according to the format above
2. Click the "Choose File" button and select your CSV file
3. Click the "Start Import" button
4. The system will validate your CSV file and begin the import process
5. You'll see real-time progress updates as the import proceeds
6. Once complete, you'll see a summary of the import results

## Important Notes

- The import process runs in the background, so you can navigate away from the page after starting the import
- If a school with the same name and county already exists, it will be updated rather than duplicated
- States must already exist in the system (the import will not create new states)
- The import will create new regions, markets, sub-regions, and counties as needed
- The import process is transactional, so if an error occurs during the import of a particular row, only that row will be affected

## Troubleshooting

If you encounter errors during the import process:

1. Check that your CSV file follows the required format
2. Ensure that all state codes in your CSV file are valid and exist in the system
3. Check for any special characters or formatting issues in your CSV file
4. For large imports, consider splitting your CSV file into smaller batches

## Command Line Usage

For advanced users or for testing purposes, you can also run the import from the command line:

```bash
php artisan app:test-school-import /path/to/your/file.csv
```

This command will validate the CSV file and prompt you to confirm before proceeding with the import.

## Sample CSV File

A sample CSV file is available at `storage/app/public/sample_schools.csv` that you can use as a template for your imports. 
