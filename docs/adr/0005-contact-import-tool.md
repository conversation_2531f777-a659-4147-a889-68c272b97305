# ADR 0005: Nominee Import Tool

## Status

Accepted

## Context

The Positive Athlete platform needs a way to import nominees (positive_athlete and positive_coach types) from external sources. These nominees come from various nomination processes and need to be integrated into the system for further processing and management.

The primary source of these nominees is CSV files exported from nomination forms and other external systems. The import process needs to handle data validation, mapping to the internal data model, and proper error handling.

## Decision

We will implement a nominee import tool with the following components:

1. **Filament Admin Page**: A dedicated page in the admin panel for uploading and managing nominee imports.
2. **Livewire Component**: A component to handle file uploads and initiate the import process.
3. **Import Service**: A service to handle the business logic of parsing CSV files and mapping data to the contact model.
4. **Background Job**: A queued job to process imports asynchronously to prevent timeouts for large files.
5. **Repository Pattern**: Use the existing ContactRepository for database operations.

### Technical Implementation Details

#### Data Flow

1. Admin uploads a CSV file through the Filament admin page
2. The Livewire component validates the file structure
3. If valid, a background job is dispatched to process the file
4. The import service processes the CSV rows and maps them to contact data
5. The contact repository creates or updates contacts in the database
6. Notifications are sent to the admin upon completion or failure

#### CSV Structure

The CSV file should include the following columns:
- Nominated Date
- Nominees Name
- State
- County
- +A Market
- +A Region
- High School Name
- Type
- Grade
- Gender
- Sport #1
- Sport #2 (optional)
- Sport #3 (optional)
- Nomination
- Nominee Email
- Nominee Phone Number
- Nominator Full Name
- Nominator Email
- Nominator Phone Number
- Relationship to Nominee

#### Data Mapping

The CSV data will be mapped to the Contact model with the following structure:
- Basic contact fields: name, email, phone_number, type, status
- Metadata JSON field: containing all additional information from the CSV

#### Nominee Types

The import tool supports two types of nominees:
- **Positive Athletes** (type: positive_athlete)
- **Positive Coaches** (type: positive_coach)

The admin can select the nominee type during the import process, and the system will set the appropriate type for all imported contacts.

#### Error Handling

- CSV structure validation before processing
- Row-by-row error handling to prevent entire import failure
- Detailed error logging and reporting
- Admin notifications for import status

## Consequences

### Positive

- Admins can easily import nominees from external sources
- Asynchronous processing prevents timeouts for large files
- Detailed error reporting helps identify and fix data issues
- Flexible metadata structure accommodates various data points
- Existing contacts can be updated with new information
- Support for different nominee types provides flexibility

### Negative

- Complex CSV files may require additional validation
- Large imports may consume significant server resources
- Duplicate detection relies primarily on email addresses
- Manual intervention may be needed for complex data issues

### Mitigations

- Implement rate limiting for imports
- Add more sophisticated duplicate detection
- Consider implementing a preview feature before final import
- Add support for additional file formats in the future

## Implementation Plan

1. Create the Contact model and migration (if not already existing)
2. Implement the ContactRepository with CRUD operations
3. Develop the NomineeImportService for CSV processing
4. Create the ImportNomineesJob for background processing
5. Build the Filament admin page and Livewire component
6. Add comprehensive error handling and notifications
7. Write tests for all components
8. Document the import process for administrators

## Future Considerations

In the future, we plan to implement separate import tools for:
1. **Athletic Directors**: With a different CSV schema and specific business logic
2. **General Contacts**: For miscellaneous contact types that won't become users

Each import tool will have its own Filament page, Livewire component, and service logic tailored to the specific requirements of that contact type.

## References

- [Laravel Queue Documentation](https://laravel.com/docs/10.x/queues)
- [Filament Admin Panel](https://filamentphp.com/)
- [Laravel Livewire](https://laravel-livewire.com/)
- [CSV Processing with League/CSV](https://csv.thephpleague.com/) 
