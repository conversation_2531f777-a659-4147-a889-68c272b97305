@component('mail::message')
@component('mail::header', ['url' => config('app.url')])
{{ config('app.name') }}
@endcomponent

# Parent Account Setup

Hello **{{ $data['first_name'] }} {{ $data['last_name'] }}**!

Your student has been nominated as a Positive Athlete, and we'd like to invite you to create a parent account to view their progress and achievements.

As a parent, you'll be able to:
- View your student's Positive Athlete profile
- Monitor their progress through our character development program
- Access exclusive parent resources and updates
- Receive important notifications about opportunities and achievements

@component('mail::button', ['url' => $registrationUrl])
Set Up Your Account
@endcomponent

@include('emails.partials.mobile-code')

This invite link will expire in 30 days. If you need a new invite after that time, please contact support.

Thanks,<br>
{{ config('app.name') }}

<small>If you received this invitation in error, please ignore this email.</small>
@endcomponent
