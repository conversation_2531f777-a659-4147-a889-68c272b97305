@component('mail::message')
{{-- Logo --}}
@component('mail::header', ['url' => config('app.url')])
{{ config('app.name') }}
@endcomponent

{{-- Body --}}
# Reset Your Password

Hello!

You are receiving this email because we received a password reset request for your account.

@component('mail::panel')
Click the button below to reset your password. This link will expire in {{ config('auth.passwords.users.expire') }} minutes.
@endcomponent

@component('mail::button', ['url' => $url, 'align' => 'left'])
Reset Password
@endcomponent

{{-- Thanks section wrapped in its own table --}}
<table width="100%" cellpadding="0" cellspacing="0" role="presentation" style="margin:0;">
<tr>
<td>
<p style="margin: 0; padding: 0;">
Thanks,<br>
{{ config('app.name') }}
</p>
</td>
</tr>
</table>

@slot('subcopy')
If you did not request a password reset, no further action is required.<br><br>
If you're having trouble clicking the button, copy and paste this URL into your browser:<br>
<a href="{{ $url }}" style="color: #002855; text-decoration: none;">{{ $url }}</a>
@endslot

@slot('footer')
© {{ date('Y') }} {{ config('app.name') }}. All rights reserved.
@endslot
@endcomponent
