<div class="flex items-center gap-2">
    <div class="flex-shrink-0 h-10 w-10 relative">
        @if(isset($user) && $user->getFirstMediaUrl('avatar'))
            <img class="h-10 w-10 rounded-full object-cover" src="{{ $user->getFirstMediaUrl('avatar') }}" alt="{{ $name }}">
        @else
            <div class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                <span class="text-gray-500 font-medium">{{ isset($user) && isset($user->first_name) ? substr($user->first_name, 0, 1) : '?' }}</span>
            </div>
        @endif
    </div>
    <div class="ml-4">
        <span class="text-sm font-medium text-gray-900">{{ $name }}</span>
    </div>
</div>
