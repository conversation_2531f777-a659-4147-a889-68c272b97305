import preset from "../../../../vendor/filament/filament/tailwind.config.preset";

export default {
    presets: [preset],
    content: [
        "./app/Filament/Admin/**/*.php",
        "./resources/views/filament/admin/**/*.blade.php",
        "./vendor/filament/**/*.blade.php",
        "./resources/views/livewire/**/*.blade.php",
        "./app/Livewire/**/*.php",
        './resources/views/**/*.blade.php',
        // './vendor/awcodes/filament-tiptap-editor/resources/**/*.blade.php',
        // './vendor/awcodes/filament-tiptap-editor/src/**/*.php',
        // './vendor/awcodes/filament-tiptap-editor/src/Enums/*.php',
        // './vendor/awcodes/filament-tiptap-editor/src/Extensions/**/*.php',
        // './vendor/awcodes/filament-tiptap-editor/resources/views/**/*.blade.php',
        // './vendor/awcodes/filament-tiptap-editor/resources/js/**/*.js',
        // './vendor/awcodes/filament-tiptap-editor/resources/css/**/*.css',
    ],
    safelist: [
        // Dynamic colors for winner verification status - using existing color palette
        'bg-warning-100',
        'text-warning-800',
        'bg-warning-500',
        'text-warning-600',
        'accent-warning-600',
        'focus:ring-warning-500',
        'bg-gray-100',
        'text-gray-800',
        'bg-gray-300',
        'bg-gray-500',
        'text-gray-600',
        'accent-gray-600',
        'focus:ring-gray-500',
        'bg-blue-100',
        'text-blue-800',
        'bg-blue-500',
        'text-blue-600',
        'accent-blue-600',
        'focus:ring-blue-500',
        'bg-green-100',
        'text-green-800',
        'bg-green-500',
        'text-green-600',
        'accent-green-600',
        'focus:ring-green-500',
    ],
    theme: {
        extend: {
            spacing: {
                40: "40px",
                56: "56px",
                72: "72px",
                80: "80px",
                96: "96px",
                120: "120px",
                160: "160px",
                200: "200px",
                240: "240px",
                280: "280px",
                320: "320px",
                360: "360px",
                400: "400px",
            },
            gridTemplateColumns: {
                12: "repeat(12, minmax(0, 1fr))",
            },
            gridColumn: {
                "span-8": "span 8 / span 8",
                "span-4": "span 4 / span 4",
            },
            height: {
                40: "40px",
                56: "56px",
                72: "72px",
                80: "80px",
                96: "96px",
                120: "120px",
                160: "160px",
                200: "200px",
                240: "240px",
                280: "280px",
                320: "320px",
                360: "360px",
                400: "400px",
            },
            colors: {
                primary: {
                    50: "#f0f9ff",
                    100: "#e0f2fe",
                    200: "#bae6fd",
                    300: "#7dd3fc",
                    400: "#38bdf8",
                    500: "#0ea5e9",
                    600: "#0284c7",
                    700: "#0369a1",
                    800: "#075985",
                    900: "#0c4a6e",
                    950: "#082f49",
                },
                brand: {
                    blue: "#002855",
                    red: "#ce0d2f",
                    white: "#FFFFFF",
                    grey: "#C3C3C3",
                },
                text: {
                    primary: "#383838",
                    secondary: "#777D80",
                    "primary-reverse": "#FFFFFF",
                    "secondary-reverse": "#D7DCDE",
                    red: "#ce0d2f",
                    blue: "#002855",
                },
                surface: {
                    primary: "#FFFFFF",
                    secondary: "#F6F6F6",
                    tertiary: "#EDEFF0",
                },
                stroke: {
                    weak: "#EDEFF0",
                    strong: "#A5ABAD",
                },
                utility: {
                    success: "#249B00",
                    "success-light": "#F9FFF7",
                    alert: "#D59500",
                    danger: "#920038",
                    "danger-light": "#FFF7F7",
                },
                warning: {
                    50: "#FFF9E6",
                    100: "#FFF3CC",
                    200: "#FFE799",
                    300: "#FFDB66",
                    400: "#FFCF33",
                    500: "#D59500", // Our alert/warning color
                    600: "#CC8A00",
                    700: "#B37A00",
                    800: "#996A00",
                    900: "#805A00",
                    950: "#664800",
                },
                info: {
                    50: "#F0F9FF",
                    100: "#E0F2FE",
                    200: "#BAE6FD",
                    300: "#7DD3FC",
                    400: "#38BDF8",
                    500: "#0EA5E9",
                    600: "#0284C7",
                    700: "#0369A1",
                    800: "#075985",
                    900: "#0C4A6E",
                    950: "#082F49",
                },
                success: {
                    50: "#F9FFF7",
                    100: "#E6FFE0",
                    200: "#CCFFC1",
                    300: "#B3FFA3",
                    400: "#99FF84",
                    500: "#249B00", // Our success color
                    600: "#1F8A00",
                    700: "#1B7A00",
                    800: "#176A00",
                    900: "#135A00",
                    950: "#0F4A00",
                    DEFAULT: "#249B00",
                    light: "#F9FFF7",
                },
                danger: {
                    50: "#FFF7F7",
                    100: "#FFE0E0",
                    200: "#FFC1C1",
                    300: "#FFA3A3",
                    400: "#FF8484",
                    500: "#920038", // Our danger color
                    600: "#800033",
                    700: "#6E002D",
                    800: "#5C0026",
                    900: "#4A001F",
                    950: "#380019",
                },
                gray: {
                    50: "#F9FAFB",
                    100: "#F3F4F6",
                    200: "#E5E7EB",
                    300: "#D1D5DB",
                    400: "#9CA3AF",
                    500: "#6B7280",
                    600: "#4B5563",
                    700: "#374151",
                    800: "#1F2937",
                    900: "#111827",
                    950: "#030712",
                },
                grey: {
                    1: '#F6F6F6',
                    2: '#EDEFF0',
                    3: '#D7DCDE',
                    4: '#A5ABAD',
                    5: '#777D80',
                    6: '#414547',
                    7: '#383838',
                    8: '#060F11',
                },
            },
            textColor: ({ theme }) => ({
                ...theme('colors'),
                brand: theme('colors.brand'),
                success: theme('colors.success'),
                danger: theme('colors.danger'),
                warning: theme('colors.warning'),
                info: theme('colors.info'),
            }),
            borderColor: ({ theme }) => ({
                ...theme('colors'),
                brand: theme('colors.brand'),
                success: theme('colors.success'),
                danger: theme('colors.danger'),
                warning: theme('colors.warning'),
                info: theme('colors.info'),
            }),
            backgroundColor: ({ theme }) => ({
                ...theme('colors'),
                brand: theme('colors.brand'),
                success: theme('colors.success'),
                danger: theme('colors.danger'),
                warning: theme('colors.warning'),
                info: theme('colors.info'),
            }),
            fontFamily: {
                sans: ["Figtree", "system-ui", "sans-serif"],
            },
            fontSize: {
                "heading-2xl": [
                    "32px",
                    { lineHeight: "1.3", letterSpacing: "-0.01em" },
                ],
                "heading-xl": [
                    "28px",
                    { lineHeight: "1.3", letterSpacing: "-0.01em" },
                ],
                "heading-lg": [
                    "24px",
                    { lineHeight: "1.3", letterSpacing: "-0.01em" },
                ],
                "heading-md": [
                    "20px",
                    { lineHeight: "1.3", letterSpacing: "-0.01em" },
                ],
                "heading-sm": [
                    "16px",
                    { lineHeight: "1.3", letterSpacing: "-0.01em" },
                ],
                "heading-xs": [
                    "14px",
                    { lineHeight: "1.3", letterSpacing: "-0.01em" },
                ],
                'display-xl': ['45px', { lineHeight: '1.2', letterSpacing: '-0.02em' }],
                'display-lg': ['36px', { lineHeight: '1.2', letterSpacing: '-0.02em' }],
                'display-md': ['30px', { lineHeight: '1.2', letterSpacing: '-0.02em' }],
                'display-sm': ['24px', { lineHeight: '1.2', letterSpacing: '-0.02em' }],
                'display-xs': ['20px', { lineHeight: '1.2', letterSpacing: '-0.02em' }],
                'text-2xl': ['24px', { lineHeight: '1.5', letterSpacing: '0' }],
                'text-xl': ['20px', { lineHeight: '1.5', letterSpacing: '0' }],
                'text-lg': ['18px', { lineHeight: '1.5', letterSpacing: '0' }],
                'text-md': ['16px', { lineHeight: '1.5', letterSpacing: '0' }],
                'text-sm': ['14px', { lineHeight: '1.5', letterSpacing: '0' }],
                'text-xs': ['12px', { lineHeight: '1.5', letterSpacing: '0' }],
                'label-xl': ['16px', { lineHeight: '1.2', letterSpacing: '0.02em', fontWeight: '600' }],
                'label-lg': ['14px', { lineHeight: '1.2', letterSpacing: '0.02em', fontWeight: '600' }],
                'label-md': ['12px', { lineHeight: '1.2', letterSpacing: '0.02em', fontWeight: '600' }],
                'label-sm': ['11px', { lineHeight: '1.2', letterSpacing: '0.02em', fontWeight: '600' }],
                'label-xs': ['10px', { lineHeight: '1.2', letterSpacing: '0.02em', fontWeight: '600' }],
                'caption-lg': ['14px', { lineHeight: '1.4', letterSpacing: '0.01em' }],
                'caption-md': ['12px', { lineHeight: '1.4', letterSpacing: '0.01em' }],
                'caption-sm': ['11px', { lineHeight: '1.4', letterSpacing: '0.01em' }],
            },
            fontWeight: {
                regular: '400',
                medium: '500',
                semibold: '600',
                bold: '700',
                heavy: '800',
            },
            boxShadow: {
                'avatar-outer': '0px 4px 4px rgba(255, 255, 255, 1), 0px 10px 16px rgba(0, 0, 0, 0.05), 0px -3px 4px rgba(0, 0, 0, 0.1), inset 0px 4px 6px rgba(0,0,0, 0.1)',
                'card': '0px 8px 20px 0px rgba(0, 0, 0, 0.04)',
                'neumorphism-inset-small': '0px -3px 4px 0px rgba(0,0,0,0.10), 0px 10px 16px 0px rgba(0,0,0,0.05), 0px 4px 4px 0px rgba(255,255,255,1.00), inset 0px 4px 6px 0px rgba(0,0,0,0.10)',
                'search-input': '0px 4px 4px rgb(255 255 255), 0px 10px 16px rgb(0 0 0 / 0.05), 0px -3px 4px rgb(0 0 0 / 0.1), inset 0px 4px 6px rgb(0 0 0 / 0.1)',
            },
        },
    },
};
