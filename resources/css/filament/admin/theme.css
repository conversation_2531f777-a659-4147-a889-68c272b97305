@tailwind base;
@tailwind components;
@tailwind utilities;

@config './tailwind.config.js';

@layer components {
    /* Radio Inputs */
    .fi-input-wrp {
        @apply shadow-sm rounded-lg;
    }

    .fi-input {
        @apply border-stroke-weak focus:border-brand-blue focus:ring-brand-blue/20;
    }

    /* Checkbox Inputs */
    input[type="checkbox"].fi-checkbox-input {
        @apply text-brand-blue border-gray-300;
    }

    input[type="checkbox"].fi-checkbox-input:focus {
        @apply ring-brand-blue/20 border-brand-blue;
    }

    input[type="checkbox"].fi-checkbox-input:checked {
        @apply bg-brand-blue border-brand-blue;
    }

    input[type="checkbox"].fi-checkbox-input:checked:focus {
        @apply ring-brand-blue/20;
    }

    input.fi-radio-input:checked {
        @apply text-brand-blue border-brand-blue ring-brand-blue/20;
    }

    input.fi-radio-input:focus {
        @apply ring-brand-blue/20;
    }

    /* Navigation */
    .fi-sidebar {
        @apply bg-surface-secondary border-none;
    }

    .fi-sidebar-item {
        @apply text-text-secondary hover:text-text-primary hover:bg-surface-tertiary rounded-lg;
    }

    .fi-sidebar-item-active {
        @apply bg-surface-tertiary text-text-primary font-medium;
    }

    /* Main Navigation Active State */
    .fi-sidebar-nav-item-active {
        @apply text-brand-blue;
    }

    .fi-sidebar-item-active a.fi-sidebar-item-button,
    .fi-sidebar-item-active .fi-sidebar-item-label,
    .fi-sidebar-item-active .fi-sidebar-item-icon {
        @apply text-brand-blue;
    }

    .fi-sidebar-nav-item-active svg {
        @apply text-brand-blue;
    }

    /* Text Colors */
    .text-brand-blue {
        color: #002855;
    }

    .text-brand-red {
        color: #ce0d2f;
    }

    /* Buttons */
    .fi-btn {
        @apply shadow-none font-medium;
    }

    .fi-btn-primary {
        @apply bg-brand-blue text-white hover:bg-brand-blue/90;
    }

    /* Tables */
    .fi-ta {
        @apply bg-surface-primary rounded-lg border border-stroke-weak;
    }

    .fi-ta-header {
        @apply bg-surface-secondary border-b border-stroke-weak;
    }

    .fi-ta-header-cell {
        @apply text-text-secondary font-medium;
    }

    .fi-ta-cell {
        @apply text-text-primary;
    }

    /* Cards */
    .fi-card {
        @apply bg-surface-primary border border-stroke-weak rounded-lg;
    }

    /* Badges */
    .fi-badge {
        @apply inline-flex items-center justify-center whitespace-nowrap rounded-md px-2 py-0.5 text-xs font-medium ring-1 ring-inset;
    }

    .fi-badge-success {
        @apply bg-success-50 text-success-700 ring-success-600/20;
    }

    .fi-badge-warning {
        @apply bg-warning-50 text-warning-700 ring-warning-600/20;
    }

    .fi-badge-danger {
        @apply bg-danger-50 text-danger-700 ring-danger-600/20;
    }

    .fi-badge-info {
        @apply bg-info-50 text-info-700 ring-info-600/20;
    }

    .fi-badge-gray {
        background-color: #f9fafb; /* Tailwind gray-50 */
        color: #374151; /* Tailwind gray-700 */
        ring: 1px solid rgba(75, 85, 99, 0.2); /* Tailwind ring-gray-600/20 */
    }

    /* Typography */
    .fi-header {
        @apply text-text-primary;
    }

    .fi-label {
        @apply text-text-secondary font-medium;
    }

    /* Modal */
    .fi-modal {
        @apply rounded-xl overflow-hidden;
    }

    .fi-modal-header {
        @apply rounded-t-xl;
    }

    /* Target the specific modal content */
    .create-module-modal .fi-modal-content,
    .create-course-modal .fi-modal-content {
        background-color: #f9fafb; /* Tailwind gray-50 */
        border-radius: 0.75rem;
    }

    .create-category-modal .fi-modal-content {
        background-color: white;
        border-radius: 0.75rem;
    }

    /* Dynamic Filters Component Styling */
    /* Filter groups styling - only target our specific repeater */
    .filter-groups-repeater .fi-repeater-item {
        background-color: #f9fafb; /* Tailwind gray-50 */
        border-radius: 0.5rem;
        padding: 0.5rem; /* Change from 1.5rem to match gap-2 */
        margin-bottom: 1rem;
        box-shadow: none;
        border: 0;
    }

    /* Add a specific selector for the filter group background - scoped to our repeater */
    .filter-groups-repeater div.bg-white[wire\:key*="repeater-item"] {
        background-color: #f9fafb !important; /* Tailwind gray-50 */
    }

    /* Target Filament's repeater item styling - scoped to our repeater */
    .filter-groups-repeater .fi-fo-repeater-item {
        border: none !important;
        box-shadow: none !important;
        background-color: #f9fafb !important;
    }

    /* Remove border from repeater container - scoped to our repeater */
    .filter-groups-repeater .fi-fo-repeater,
    .filter-groups-repeater .fi-fo-repeater > ul,
    .filter-groups-repeater .fi-fo-repeater-item-container,
    .filter-groups-repeater [data-repeater-container="true"] {
        border: none !important;
        background-color: transparent !important;
        box-shadow: none !important;
    }

    /* Conjunction buttons between groups */
    .filter-groups-repeater .conjunction-select {
        @apply my-2;
    }

    /* Grid layout for filter conditions */
    .filter-groups-repeater .filter-conditions-grid {
        width: 100%;
        gap: 1.5rem; /* Match Tailwind's gap-2 (8px) */
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(0, 1fr));
    }

    /* Delete button styling */
    .filter-groups-repeater .fi-ac-icon.fi-ac-icon-btn {
        @apply rounded-full p-1;
    }

    /* Style the delete button to match design */
    .filter-groups-repeater .conditions-repeater .fi-fo-repeater-item-header .fi-icon-btn {
        @apply text-gray-500 hover:text-gray-700 bg-transparent hover:bg-gray-200 transition-colors duration-200;
    }

    /* Fix grid spacing */
    .filter-groups-repeater .fi-fo-grid {
        @apply gap-2;
    }

    /* Add filter button styling */
    .filter-groups-repeater .fi-repeater-add-action {
        @apply mt-3;
    }

    /* Remove borders from select inputs */
    .filter-groups-repeater .filter-conditions-grid select,
    .filter-groups-repeater .filter-conditions-grid .fi-select-input div[aria-haspopup="listbox"] {
        border: none;
        background-color: white;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        border-radius: 0.375rem;
    }

    /* CRITICAL: This is the main styling for the repeater items to use flex */
    .filter-groups-repeater .conditions-repeater li.fi-fo-repeater-item {
        display: flex !important;
        flex-direction: row !important;
        align-items: center !important;
    }

    /* Adjust padding for filter item content */
    .filter-groups-repeater .conditions-repeater .fi-fo-repeater-item-content {
        padding: 0 !important;
    }

    /* Header with delete button should be at the end */
    .filter-groups-repeater .conditions-repeater li.fi-fo-repeater-item .fi-fo-repeater-item-header {
        order: 2 !important;
        padding-left: 0 !important;
    }

    /* Make delete button align properly */
    .filter-groups-repeater .conditions-repeater
        li.fi-fo-repeater-item
        .fi-fo-repeater-item-header
        .fi-icon-btn {
        margin: 0 !important;
    }

    /* Clean up header styling */
    .filter-groups-repeater .conditions-repeater li.fi-fo-repeater-item .fi-fo-repeater-item-header ul {
        margin: 0 !important;
    }

    /* Ensure button gets proper hover state */
    .filter-groups-repeater .conditions-repeater .fi-icon-btn:hover {
        @apply bg-gray-100;
    }

    /* Vertical alignment for select inputs in filter conditions */
    .filter-groups-repeater .conditions-repeater .filter-conditions-grid .fi-select-input {
        @apply my-0;
    }

    /* Align all form inputs vertically */
    .filter-groups-repeater .conditions-repeater .filter-conditions-grid > div {
        @apply flex items-center;
    }

    /* Fix vertical alignment of all select elements */
    .filter-groups-repeater .filter-conditions-grid select,
    .filter-groups-repeater .filter-conditions-grid .fi-select-input div {
        @apply my-0;
    }

    /* Ensure proper height for the grid */
    .filter-groups-repeater .custom-filter-grid {
        min-height: 36px;
        margin-bottom: 0;
    }

    /* Style the X delete icons to match design */
    .filter-groups-repeater .conditions-repeater .fi-fo-repeater-item-header .fi-icon-btn svg {
        @apply h-5 w-5 text-gray-500;
    }

    /* Remove extra padding from repeater items */
    .filter-groups-repeater .fi-fo-repeater-item {
        padding: 0 !important;
    }

    /* Adjust spacing for the filter group container */
    .filter-groups-repeater .fi-fo-repeater,
    .filter-groups-repeater [data-repeater-container="true"] {
        gap: 0.5rem !important;
    }

    /* Remove grid gaps for text input ancestors */
    .filter-groups-repeater .pa-text-input {
        margin: 0 !important;
    }

    /* Remove vertical gaps from parent grids of text input */
    .filter-groups-repeater .pa-text-input:is(input) {
        margin: 0 !important;
    }

    /* Target parent grid containers and remove their gaps */
    .filter-groups-repeater .pa-text-input:is(input):where(
        .grid,
        .gap-y-2,
        [class*="gap-"]
    )::parent {
        gap: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Ensure the input container itself has no gaps */
    .filter-groups-repeater .fi-input-wrp:has(.pa-text-input) {
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Target the specific grid structure shown in the screenshot */
    .filter-groups-repeater .grid.gap-y-2:has(.pa-text-input),
    .filter-groups-repeater .grid.auto-cols-fr:has(.pa-text-input) {
        gap: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Target the specific grid containing the text input */
    .filter-groups-repeater .grid:not(.items-start):has(.pa-text-input) {
        gap: 0 1.5rem !important;
    }

    /* Remove any remaining gaps from the input's container */
    .filter-groups-repeater .fi-input-wrp.pa-text-input {
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Ensure the text input itself has no margins */
    .filter-groups-repeater input.pa-text-input {
        margin: 0 !important;
    }

    /* Add consistent horizontal spacing to all grid elements */
    .filter-groups-repeater .filter-conditions-grid > div {
        margin-right: 0.5rem;
    }

    .filter-groups-repeater .filter-conditions-grid > div:last-child {
        margin-right: 0;
    }

    .filter-groups-repeater button.fi-ac-btn-action {
        margin-top: 1.5rem;
    }

    .filter-groups-repeater > ul > .items-start {
        gap: 2rem !important;
    }
}
