<?php

namespace App\Support\MediaLibrary;

use Spatie\MediaLibrary\Support\UrlGenerator\DefaultUrlGenerator;

class CustomUrlGenerator extends DefaultUrlGenerator
{
    public function getUrl(): string
    {
        $url = parent::getUrl();
        $request = request();
        $path = parse_url($url, PHP_URL_PATH);

        // Always use our own detection logic
        $isAdmin = $this->detectAdminRequest($request);

        // Use app.url for admin requests, api_url for client requests
        // This applies to all environments
        if ($isAdmin) {
            return config('app.url') . $path;
        }

        // This is a client/API request (NextJS or Flutter), use API_URL
        return config('app.api_url') . $path;
    }

    /**
     * Detect if the request is coming from the admin interface
     */
    protected function detectAdminRequest($request): bool
    {
        // Check if this is a direct admin request based on URL
        $isAdmin = $request->is('admin/*') || $request->is('filament/*');

        // If not an admin URL, check headers for Livewire or AJAX requests
        if (!$isAdmin) {
            // Get referer and parse it
            $referer = $request->header('Referer');
            $refererPath = $referer ? parse_url($referer, PHP_URL_PATH) : null;

            // Check if this is a Livewire or AJAX request from admin area
            if (($request->hasHeader('X-Livewire') || $request->ajax()) && $refererPath) {
                $isAdmin = str_starts_with($refererPath, '/admin') || str_starts_with($refererPath, '/filament');
            }
        }

        return $isAdmin;
    }
}
