<?php

namespace App\Contracts;

/**
 * Interface for components that provide exportable columns
 */
interface HasExportableColumnsInterface
{
    /**
     * Get columns available for export
     *
     * @return array<string, string> Column key => Label mapping
     */
    public function getExportableColumns(): array;

    /**
     * Get default columns that should be pre-selected for export
     *
     * @return array<string> Array of column keys
     */
    public function getDefaultExportColumns(): array;

    /**
     * Identify which columns contain date values for filtering
     *
     * @return array<string> Array of date column keys
     */
    public function getExportableDateColumns(): array;

    /**
     * Get column types for exportable columns
     *
     * @return array<string, string> Column key => type mapping
     */
    public function getExportableColumnTypes(): array;

    /**
     * Handle CSV export request
     *
     * @param array $data Export form data containing columns and filters
     * @return void
     */
    public function handleExportCsv(array $data): void;
}
