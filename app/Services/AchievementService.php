<?php

namespace App\Services;

use App\Models\User;

class AchievementService
{
    public function createFromOnboarding(User $user, array $data): void
    {
        foreach ($data['items'] as $item) {
            $user->achievements()->create([
                'name' => $item['name'],
                'date' => $item['date_range'],
                'description' => $item['description'],
                'order' => $item['order'],
            ]);
        }
    }
}
