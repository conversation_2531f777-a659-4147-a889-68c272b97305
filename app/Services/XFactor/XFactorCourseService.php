<?php

namespace App\Services\XFactor;

use App\Data\XFactor\CourseBrowseRequest;
use App\Data\XFactor\CourseDetailResponse;
use App\Data\XFactor\CourseResponse;
use App\Data\XFactor\ModuleResponse;
use App\Data\XFactor\SearchCoursesResponse;
use App\Data\XFactor\TopicCoursesResponse;
use App\Enums\ModuleType;
use App\Models\User;
use App\Repositories\XFactorCourseRepository;
use Illuminate\Pagination\LengthAwarePaginator;

class XFactorCourseService
{
    public function __construct(
        private XFactorCourseRepository $repository,
        private XFactorExamService $examService
    ) {}

    /**
     * Calculate course progress for a user
     */
    private function calculateCourseProgress($course, User $user): array
    {
        // Ensure modules are loaded
        if (!$course->relationLoaded('modules')) {
            $course->load(['modules' => function($query) {
                $query->where('published', true);
            }]);
        }

        // Initialize with default empty values
        $moduleData = collect();
        $completedModulesCount = 0;
        $totalModules = 0;
        $progress = 0;

        // Only process if there are published modules
        if ($course->modules->isNotEmpty()) {
            $moduleData = $course->modules
                ->sortBy('pivot.order')
                ->map(fn($module) => ModuleResponse::fromModel($module, $user))
                ->values();

            $completedModulesCount = $moduleData->whereNotNull('completed_at')->count();
            $totalModules = $moduleData->count();
            $progress = $totalModules > 0 ? round(($completedModulesCount / $totalModules) * 100) : 0;
        }

        return [
            'moduleData' => $moduleData,
            'completedModulesCount' => $completedModulesCount,
            'totalModules' => $totalModules,
            'progress' => $progress,
        ];
    }

    /**
     * Get featured courses for the user
     */
    public function getFeaturedCourses(User $user, int $limit = 5): array
    {
        $courses = $this->repository->getFeaturedCourses($user, $limit);

        return $courses->map(function ($course) use ($user) {
            // Check if the user has started this course
            $hasStarted = $course->users->isNotEmpty();

            // Calculate course progress
            $progress = $this->calculateCourseProgress($course, $user);

            return new CourseResponse(
                id: $course->id,
                title: $course->title,
                description: $course->description,
                presenter: $course->presenter,
                coverImageUrl: $course->getFirstMediaUrl('cover', 'full') ?: null,
                coverImageThumbUrl: $course->getFirstMediaUrl('cover', 'thumb') ?: null,
                isCompleted: $hasStarted && !is_null($course->pivot?->completed_at),
                createdAt: $course->created_at,
                progress: $progress['progress'],
                modulesCompleted: $progress['completedModulesCount'],
                totalModules: $progress['totalModules'],
                averageScore: $hasStarted ? $course->getAverageScoreForUser($user) : 0,
                topics: $course->topics->pluck('name')->toArray(),
                lastAccessedAt: $course->pivot?->updated_at,
                totalRuntimeMinutes: $course->total_runtime_minutes,
            );
        })->toArray();
    }

    /**
     * Get courses grouped by topics
     */
    public function getTopicCourses(
        User $user,
        ?string $topic = null,
        int $page = 1,
        int $perPage = 5,
        int $coursesPerTopic = 5
    ): array|TopicCoursesResponse {
        if ($topic) {
            // Get paginated courses for a specific topic
            $courses = $this->repository->getCoursesByTopic($user, $topic, $page, $perPage);
            $totalCount = $this->repository->getTopicCourseCount($topic);

            return new TopicCoursesResponse(
                topic: $topic,
                courses: $courses->map(fn($course) => new CourseResponse(
                    id: $course->id,
                    title: $course->title,
                    description: $course->description,
                    presenter: $course->presenter,
                    coverImageUrl: $course->getFirstMediaUrl('cover', 'full') ?: null,
                    coverImageThumbUrl: $course->getFirstMediaUrl('cover', 'thumb') ?: null,
                    isCompleted: !is_null($course->completed_at),
                    createdAt: $course->created_at,
                    progress: $course->progress ?? 0,
                    modulesCompleted: $course->modules()->whereHas('users', function ($query) use ($user) {
                        $query->where('user_id', $user->id)->whereNotNull('completed_at');
                    })->count(),
                    totalModules: $course->modules()->where('published', true)->count(),
                    averageScore: $course->pivot?->average_score ?? 0,
                    topics: $course->topics->pluck('name')->toArray(),
                    lastAccessedAt: $course->pivot?->updated_at,
                ))->toArray(),
                hasMore: $courses->hasMorePages(),
                totalCount: $totalCount,
                page: $page,
                perPage: $perPage
            );
        }

        // Get courses grouped by topics for the browse view
        $topics = $this->repository->getAllTopics();
        return $topics->map(function ($topic) use ($user, $coursesPerTopic) {
            $courses = $this->repository->getCoursesByTopic($user, $topic->name, 1, $coursesPerTopic);
            $totalCount = $this->repository->getTopicCourseCount($topic->name);

            return new TopicCoursesResponse(
                topic: $topic->name,
                courses: $courses->map(fn($course) => new CourseResponse(
                    id: $course->id,
                    title: $course->title,
                    description: $course->description,
                    presenter: $course->presenter,
                    coverImageUrl: $course->getFirstMediaUrl('cover', 'full') ?: null,
                    coverImageThumbUrl: $course->getFirstMediaUrl('cover', 'thumb') ?: null,
                    isCompleted: !is_null($course->completed_at),
                    createdAt: $course->created_at,
                    progress: $course->progress ?? 0,
                    modulesCompleted: $course->modules()->whereHas('users', function ($query) use ($user) {
                        $query->where('user_id', $user->id)->whereNotNull('completed_at');
                    })->count(),
                    totalModules: $course->modules()->where('published', true)->count(),
                    averageScore: $course->pivot?->average_score ?? 0,
                    topics: $course->topics->pluck('name')->toArray(),
                    lastAccessedAt: $course->pivot?->updated_at,
                ))->toArray(),
                hasMore: $courses->hasMorePages(),
                totalCount: $totalCount,
                page: 1,
                perPage: $coursesPerTopic
            );
        })->toArray();
    }

    /**
     * Search courses with filters
     */
    public function searchCourses(User $user, CourseBrowseRequest $request): SearchCoursesResponse
    {
        $courses = $this->repository->searchCourses(
            user: $user,
            query: $request->search,
            topics: $request->topics,
            sort: $request->sort,
            status: $request->status,
            page: $request->page,
            perPage: $request->perPage
        );

        return new SearchCoursesResponse(
            courses: $courses->map(fn($course) => new CourseResponse(
                id: $course->id,
                title: $course->title,
                description: $course->description,
                presenter: $course->presenter,
                coverImageUrl: $course->getFirstMediaUrl('cover', 'full') ?: null,
                coverImageThumbUrl: $course->getFirstMediaUrl('cover', 'thumb') ?: null,
                isCompleted: !is_null($course->completed_at),
                createdAt: $course->created_at,
                progress: $course->progress ?? 0,
                modulesCompleted: $course->modules()->whereHas('users', function ($query) use ($user) {
                    $query->where('user_id', $user->id)->whereNotNull('completed_at');
                })->count(),
                totalModules: $course->modules()->where('published', true)->count(),
                averageScore: $course->pivot?->average_score ?? 0,
                topics: $course->topics->pluck('name')->toArray(),
                lastAccessedAt: $course->pivot?->updated_at,
                totalRuntimeMinutes: $course->total_runtime_minutes,
            ))->toArray(),
            totalCount: $courses->total()
        );
    }

    /**
     * Get detailed course information including modules and final exam
     */
    public function getCourseDetail(User $user, string $courseId): CourseDetailResponse
    {
        $course = $this->repository->getCourseWithDetails($user, $courseId);
        $progress = $this->calculateCourseProgress($course, $user);


        // Process exam modules separately
        $examModules = $course->modules
            ->where('type', ModuleType::Exam)
            ->values()
            ->map(function ($module) use ($user) {
                return $this->examService->getExamByModule($module->id, $user->id);
            })
            ->filter()
            ->values();

        return new CourseDetailResponse(
            id: $course->id,
            title: $course->title,
            description: $course->description,
            presenter: $course->presenter,
            coverImageUrl: $course->getFirstMediaUrl('cover', 'full') ?: null,
            coverImageThumbUrl: $course->getFirstMediaUrl('cover', 'thumb') ?: null,
            isCompleted: !is_null($course->pivot?->completed_at),
            createdAt: $course->created_at,
            progress: $progress['progress'],
            modulesCompleted: $progress['completedModulesCount'],
            totalModules: $progress['totalModules'],
            averageScore: $course->getAverageScoreForUser($user),
            topics: $course->topics->pluck('name')->toArray(),
            lastAccessedAt: $course->pivot?->updated_at,
            totalRuntimeMinutes: $course->total_runtime_minutes,
            modules: $progress['moduleData']->toArray(),
            exams: $examModules->toArray(),
            completedAt: $course->pivot?->completed_at,
        );
    }
}
