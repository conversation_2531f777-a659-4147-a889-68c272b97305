<?php

namespace App\Services\XFactor;

use App\Data\XFactor\Exam\ExamData;
use App\Data\XFactor\Exam\TestAttemptData;
use App\Enums\TestStatus;
use App\Models\TestAttempt;
use App\Models\User;
use App\Repositories\XFactorExamRepository;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\HttpException;

class XFactorExamService
{
    public function __construct(
        private readonly XFactorExamRepository $examRepository,
    ) {}

    public function getExamByModule(int $moduleId, int $userId): ExamData
    {
        $exam = $this->examRepository->getExamByModuleId($moduleId, $userId);

        if (!$exam) {
            throw new HttpException(404, 'Exam not found for this module.');
        }

        return ExamData::fromModel($exam, $userId);
    }

    public function startExamAttempt(int $moduleId, int $userId): TestAttemptData
    {
        return DB::transaction(function () use ($moduleId, $userId) {
            /** @var \App\Models\Test $exam */
            $exam = $this->examRepository->getExamByModuleId($moduleId);
            $user = User::query()->findOrFail($userId);

            if (!$exam) {
                throw new HttpException(404, 'Exam not found for this module.');
            }

            // Check for existing valid attempt
            $existingAttempt = $this->examRepository->findValidAttempt($exam->id, $userId);
            if ($existingAttempt) {
                return TestAttemptData::fromModel($existingAttempt);
            }

            // Check if user has exceeded attempt limit (exams typically allow 1 attempt)
            $attempts = $exam->userAttempts($user)->count();
            if ($attempts >= ($exam->attempts_allowed ?? 3)) {
                throw new HttpException(403, 'You have exceeded the maximum number of attempts for this exam.');
            }

            // Create new attempt with proper end time based on test time limit
            $attempt = $this->examRepository->createExamAttempt($exam->id, $userId, $exam->time_limit);

            return TestAttemptData::fromModel($attempt);
        });
    }

    /**
     * Mark an attempt as expired if it has passed its end time
     */
    public function markAttemptExpired(int $attemptId): void
    {
        $attempt = TestAttempt::query()->findOrFail($attemptId);

        if ($attempt->status !== 'complete' && $attempt->ends_at->isPast()) {
            $attempt->update([
                'status' => 'expired',
                'completed_at' => now(),
            ]);
        }
    }

    public function submitExam(int $moduleId, int $userId, array $responses, TestStatus $status): TestAttemptData
    {
        return DB::transaction(function () use ($moduleId, $userId, $responses, $status) {
            $attempt = $this->examRepository->findActiveAttempt($moduleId, $userId);

            if (!$attempt) {
                throw new HttpException(404, 'No active exam attempt found.');
            }

            if ($attempt->ends_at->isPast()) {
                $status = TestStatus::Expired;
            }

            // Create responses and calculate score for multiple choice questions
            $attempt = $this->examRepository->submitExamResponses($attempt->id, $responses);

            // Update attempt status
            $attempt = $this->examRepository->updateAttemptStatus($attempt->id, $status);

            return TestAttemptData::fromModel($attempt);
        });
    }
}
