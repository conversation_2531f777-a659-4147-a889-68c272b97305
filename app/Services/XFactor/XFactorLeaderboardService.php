<?php

namespace App\Services\XFactor;

use App\Data\XFactor\LeaderboardRequest;
use App\Data\XFactor\LeaderboardResponse;
use App\Data\XFactor\LeaderboardUserData;
use App\Models\Region;
use App\Models\State;
use App\Models\User;
use App\Repositories\XFactorLeaderboardRepository;
use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;

class XFactorLeaderboardService
{
    public function __construct(
        private readonly XFactorLeaderboardRepository $leaderboardRepository,
    ) {}

    /**
     * Get the X-Factor leaderboard with filters
     *
     * @param LeaderboardRequest $request The leaderboard request DTO
     * @param User|null $currentUser The currently authenticated user
     * @return LeaderboardResponse
     */
    public function getLeaderboard(LeaderboardRequest $request, ?User $currentUser = null): LeaderboardResponse
    {
        // Calculate offset and limit for pagination
        $offset = ($request->page - 1) * $request->per_page;
        $limit = $request->per_page;

        // Get the region name
        $regionName = 'National'; // Default to National
        if ($request->region) {
            $region = Region::query()->find($request->region);
            if ($region) {
                $regionName = $region->name;
            }
        }

        // Get the state name if specified
        $stateName = null;
        if ($request->state) {
            $state = State::query()->where('code', $request->state)->first();
            if ($state) {
                $stateName = $state->name;
            }
        }

        // Format academic year for display
        $academicYear = $request->academic_year;

        // Get the leaderboard data
        $leaderboardData = $this->leaderboardRepository->getLeaderboard(
            $request->region,
            $request->state,
            $request->graduation_year,
            $request->start_date,
            $request->end_date,
            $limit,
            $offset
        );

        // Get total users for pagination
        $totalUsers = $this->leaderboardRepository->getTotalUsers(
            $request->region,
            $request->state,
            $request->graduation_year,
            $request->start_date,
            $request->end_date
        );

        // Create paginator instance
        $paginator = new LengthAwarePaginator(
            $leaderboardData,
            $totalUsers,
            $limit,
            $request->page
        );

        // Get current user's rank and details if provided
        $currentUserRank = null;
        $currentUserData = null;

        if ($currentUser) {
            $currentUserRank = $this->leaderboardRepository->getUserRank(
                $currentUser->id,
                $request->region,
                $request->state,
                $request->graduation_year,
                $request->start_date,
                $request->end_date
            );

            $userDetails = $this->leaderboardRepository->getUserDetails(
                $currentUser->id,
                $request->start_date,
                $request->end_date
            );

            if ($userDetails) {
                // Ensure we pass the full user model for sports data
                $currentUserData = LeaderboardUserData::fromObject($userDetails, $currentUser);
                // Make sure to set the rank
                if ($currentUserRank) {
                    $currentUserData->rank = $currentUserRank;
                }
            }
        }

        // Create and return the response DTO
        return LeaderboardResponse::fromPaginator(
            $paginator,
            $currentUserData,
            $currentUserRank,
            $regionName,
            $stateName,
            $request->graduation_year,
            $academicYear,
            $request->start_date,
            $request->end_date,
            $request->all_time,
            $currentUser
        );
    }
}
