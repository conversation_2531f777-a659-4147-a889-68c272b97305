<?php

namespace App\Services\XFactor;

use App\Data\XFactor\ModuleBrowseRequest;
use App\Data\XFactor\ModuleResponse;
use App\Data\XFactor\TopicModulesResponse;
use App\Models\Topic;
use App\Models\User;
use App\Repositories\XFactorModuleRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class XFactorModuleService
{
    public function __construct(
        private readonly XFactorModuleRepository $moduleRepository,
    ) {}

    public function getModulesByTopic(Topic $topic, ?User $user = null): TopicModulesResponse
    {
        $result = $this->moduleRepository->getModulesByTopic($topic, $user);

        return TopicModulesResponse::fromModel($topic, $result['modules']->all());
    }

    public function searchModules(ModuleBrowseRequest $request, ?User $user = null): LengthAwarePaginator
    {
        $paginator = $this->moduleRepository->searchModules($request, $user);

        return $paginator->through(fn ($module) => ModuleResponse::fromModel(
            $module,
            $user
        ));
    }

    /**
     * Get all modules grouped by topics
     *
     * @param User $user The authenticated user
     * @return Collection<TopicModulesResponse>
     */
    public function getAllModulesByTopics(User $user): Collection
    {
        $topics = $this->moduleRepository->getAllModulesByTopics($user);

        return $topics->map(fn ($topic) => TopicModulesResponse::fromModel($topic, $user));
    }
}
