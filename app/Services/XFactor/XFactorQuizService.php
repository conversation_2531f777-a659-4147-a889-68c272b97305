<?php

namespace App\Services\XFactor;

use App\Data\XFactor\Quiz\QuizData;
use App\Data\XFactor\Quiz\QuestionResponseData;
use App\Data\XFactor\Quiz\TestAttemptData;
use App\Repositories\XFactorQuizRepository;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\HttpException;

class XFactorQuizService
{
    public function __construct(
        private readonly XFactorQuizRepository $quizRepository,
    ) {}

    public function getQuizByModule(string $moduleId, ?string $userId = null): QuizData
    {
        $quiz = $this->quizRepository->getQuizByModuleId($moduleId, $userId);

        if (!$quiz) {
            throw new HttpException(404, 'Quiz not found for this module.');
        }

        return QuizData::fromModel($quiz, $userId);
    }

    public function startQuizAttempt(string $moduleId, string $userId): TestAttemptData
    {
        return DB::transaction(function () use ($moduleId, $userId) {
            $quiz = $this->quizRepository->getQuizByModuleId($moduleId);

            if (!$quiz) {
                throw new HttpException(404, 'Quiz not found for this module.');
            }

            // Check for existing valid attempt
            $existingAttempt = $this->quizRepository->findValidAttempt($quiz->id, $userId);
            if ($existingAttempt) {
                return TestAttemptData::fromModel($existingAttempt);
            }

            // Check if user has exceeded attempt limit
            $attempts = $this->quizRepository->getUserAttempts($quiz->id, $userId);
            if ($attempts >= ($quiz->attempts_allowed ?? 3)) {
                throw new HttpException(403, 'You have exceeded the maximum number of attempts for this quiz.');
            }

            $attempt = $this->quizRepository->createQuizAttempt($quiz->id, $userId);

            return TestAttemptData::fromModel($attempt);
        });
    }

    public function submitQuestionResponse(
        string $attemptId,
        string $questionId,
        string $userId,
        string $response
    ): QuestionResponseData {
        return DB::transaction(function () use ($attemptId, $questionId, $userId, $response) {
            $attempt = $this->quizRepository->getTestAttempt($attemptId);

            if (!$attempt) {
                throw new HttpException(404, 'Test attempt not found.');
            }

            if ($attempt->completed_at) {
                throw new HttpException(403, 'Cannot submit response for a completed test.');
            }

            if ($attempt->user_id !== $userId) {
                throw new HttpException(403, 'You do not have permission to submit responses for this test attempt.');
            }

            $questionResponse = $this->quizRepository->createQuestionResponse(
                $attemptId,
                $questionId,
                $userId,
                $response
            );

            return QuestionResponseData::fromModel($questionResponse);
        });
    }

    public function completeQuizAttempt(int $attemptId, array $responses): TestAttemptData
    {
        return DB::transaction(function () use ($attemptId, $responses) {
            $attempt = $this->quizRepository->completeQuizAttempt($attemptId, $responses);

            return TestAttemptData::fromModel($attempt);
        });
    }
}
