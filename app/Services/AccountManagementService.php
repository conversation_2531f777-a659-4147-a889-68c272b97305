<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class AccountManagementService
{
    /**
     * Update a user's profile information.
     *
     * @param User $user
     * @param array $data
     * @return User
     */
    public function updateUserProfile(User $user, array $data): User
    {
        // Log the incoming data for debugging
        Log::info('Updating user profile', [
            'user_id' => $user->id,
            'data' => $data
        ]);

        // Only update fields that are explicitly present in the data array
        // This prevents nulling out fields that weren't included in the request
        foreach ($data as $key => $value) {
            // Skip null values for required fields
            if ($value === null && in_array($key, ['email', 'first_name', 'last_name', 'profile_type'])) {
                Log::warning("Attempted to set required field '{$key}' to null, skipping", [
                    'user_id' => $user->id
                ]);
                continue;
            }

            // Only set the attribute if it's actually present in the request
            // This is different from checking if it's null - we only want to update
            // fields that were explicitly included in the request
            if (array_key_exists($key, $data)) {
                $user->{$key} = $value;
            }
        }

        $user->save();

        Log::info('User profile updated', ['user_id' => $user->id, 'fields_updated' => array_keys($data)]);

        return $user;
    }

    /**
     * Update a user's address information.
     *
     * @param User $user
     * @param array $data
     * @return User
     */
    public function updateAddress(User $user, array $data): User
    {
        // Log the incoming data for debugging
        Log::info('Updating user address', [
            'user_id' => $user->id,
            'data' => $data
        ]);

        // Only update fields that are explicitly present in the data array
        // This prevents nulling out fields that weren't included in the request
        foreach ($data as $key => $value) {
            // Only set the attribute if it's actually present in the request
            if (array_key_exists($key, $data)) {
                $user->{$key} = $value;
            }
        }

        $user->save();

        Log::info('User address updated', ['user_id' => $user->id, 'fields_updated' => array_keys($data)]);

        return $user;
    }

    /**
     * Update a user's password.
     *
     * @param User $user
     * @param string $currentPassword
     * @param string $newPassword
     * @return bool
     * @throws ValidationException
     */
    public function updatePassword(User $user, string $currentPassword, string $newPassword): bool
    {
        // Verify current password
        if (!Hash::check($currentPassword, $user->password)) {
            throw ValidationException::withMessages([
                'current_password' => ['The current password is incorrect.'],
            ]);
        }

        $user->password = Hash::make($newPassword);
        $user->save();

        Log::info('User password updated', ['user_id' => $user->id]);

        return true;
    }

    /**
     * Delete a user account and all related data.
     *
     * @param User $user
     * @param string $password Password confirmation for security
     * @return bool
     * @throws ValidationException
     */
    public function deleteAccount(User $user, string $password): bool
    {
        // Verify password for security
        if (!Hash::check($password, $user->password)) {
            throw ValidationException::withMessages([
                'password' => ['The password is incorrect.'],
            ]);
        }

        // Start a transaction to ensure all related data is deleted properly
        DB::beginTransaction();

        try {
            // Log the deletion for audit purposes
            Log::info('User account deletion initiated', [
                'user_id' => $user->id,
                'email' => $user->email,
                'profile_type' => $user->profile_type,
            ]);

            // Delete the user
            $user->delete();

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to delete user account', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }
}
