<?php

namespace App\Services\ContentModeration;

use Exception;
use Google\Cloud\Language\V2\Document;
use Google\Cloud\Language\V2\Document\Type;
use Google\Cloud\Language\V2\Client\LanguageServiceClient;
use Google\Cloud\Language\V2\ModerateTextRequest;
use Illuminate\Support\Facades\Log;

class GoogleNaturalLanguageService
{
    /**
     * @var string
     */
    protected $credentialsPath;

    /**
     * Create a new service instance.
     */
    public function __construct()
    {
        $this->credentialsPath = storage_path('app/google-credentials/google-language-credentials.json');
    }

    /**
     * Moderate text content using Google Natural Language API.
     *
     * @param string $content
     * @return array
     */
    public function moderateContent(string $content): array
    {
        try {
            // Check if credentials file exists
            if (!file_exists($this->credentialsPath)) {
                throw new Exception('Google API credentials file not found');
            }

            // Create the Language client with explicit credentials
            // This avoids manipulating environment variables
            $languageClient = new LanguageServiceClient([
                'credentials' => $this->credentialsPath
            ]);

            // Create a document object
            $document = new Document([
                'content' => $content,
                'type' => Type::PLAIN_TEXT,
            ]);

            // Create a ModerateTextRequest object
            $request = new ModerateTextRequest([
                'document' => $document
            ]);

            // Call the moderateText method with the request
            $response = $languageClient->moderateText($request);

            // Get moderation categories
            $moderationCategories = $response->getModerationCategories();

            // Convert response to array for storage
            $result = [
                'moderationCategories' => [],
                'languageCode' => $response->getLanguageCode(),
                'languageSupported' => $response->getLanguageSupported()
            ];

            foreach ($moderationCategories as $category) {
                $result['moderationCategories'][] = [
                    'name' => $category->getName(),
                    'confidence' => $category->getConfidence(),
                    'severity' => $category->getSeverity()
                ];
            }

            // Determine if content should be flagged
            $isFlagged = $this->shouldFlagContent($result);

            // Close the client
            $languageClient->close();

            return [
                'is_flagged' => $isFlagged,
                'moderation_result' => $result
            ];
        } catch (Exception $e) {
            Log::error('Content moderation error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return a safe default in case of error
            return [
                'is_flagged' => false,
                'moderation_result' => null,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Determine if content should be flagged based on moderation results.
     *
     * @param array|null $result
     * @return bool
     */
    protected function shouldFlagContent(?array $result): bool
    {
        // If no result or no moderation categories, don't flag
        if (!$result || !isset($result['moderationCategories']) || empty($result['moderationCategories'])) {
            return false;
        }

        // Define threshold for flagging content based on category
        $thresholds = [
            // High-risk categories (lower threshold)
            'Sexual' => 0.7,
            'Toxic' => 0.7,
            'Insult' => 0.7,
            'Profanity' => 0.8,
            'Derogatory' => 0.7,
            'Violent' => 0.7,
            'Death, Harm & Tragedy' => 0.8,
            'Firearms & Weapons' => 0.8,
            'Public Safety' => 0.8,
            'Illicit Drugs' => 0.8,

            // Medium-risk categories (higher threshold)
            'Health' => 0.9,
            'War & Conflict' => 0.9,

            // Low-risk categories (very high threshold or not flagged)
            'Religion & Belief' => 0.95, // Only flag extremely high confidence
            'Politics' => 0.95,         // Only flag extremely high confidence
            'Finance' => 0.95,          // Only flag extremely high confidence
            'Legal' => 0.95             // Only flag extremely high confidence
        ];

        // Check each moderation category against thresholds
        foreach ($result['moderationCategories'] as $category) {
            $categoryName = $category['name'] ?? '';
            $confidence = $category['confidence'] ?? 0;
            $severity = $category['severity'] ?? 0;

            // If category has a defined threshold and confidence exceeds it, flag the content
            if (isset($thresholds[$categoryName]) && $confidence >= $thresholds[$categoryName]) {
                Log::info('Content flagged', [
                    'category' => $categoryName,
                    'confidence' => $confidence,
                    'threshold' => $thresholds[$categoryName],
                    'severity' => $severity
                ]);
                return true;
            }
        }

        return false;
    }
}
