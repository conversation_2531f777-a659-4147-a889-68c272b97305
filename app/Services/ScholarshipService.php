<?php

namespace App\Services;

use App\Models\Scholarship;
use App\Models\User;
use App\Models\Winner;
use App\Repositories\ScholarshipRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use App\Enums\WinnerVerificationState;

class ScholarshipService
{
    protected ScholarshipRepository $scholarshipRepository;

    /**
     * Create a new service instance.
     *
     * @param ScholarshipRepository $scholarshipRepository
     */
    public function __construct(ScholarshipRepository $scholarshipRepository)
    {
        $this->scholarshipRepository = $scholarshipRepository;
    }

    /**
     * Get a paginated list of scholarships with optional filtering.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getScholarships(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        return $this->scholarshipRepository->getScholarships($filters, $perPage);
    }

    /**
     * Get a scholarship by ID.
     *
     * @param int $id
     * @return Scholarship
     */
    public function getScholarshipById(int $id): Scholarship
    {
        return $this->scholarshipRepository->findById($id);
    }

    /**
     * Create a new scholarship with validation.
     *
     * @param array $data
     * @return Scholarship
     * @throws ValidationException
     */
    public function createScholarship(array $data): Scholarship
    {
        // Validate data
        $this->validateScholarshipData($data);

        // Check for duplicate
        if ($this->scholarshipRepository->isDuplicate($data['name'], $data['year'])) {
            throw ValidationException::withMessages([
                'name' => ['A scholarship with this name already exists for the specified year.'],
            ]);
        }

        // Create scholarship
        $scholarship = $this->scholarshipRepository->create($data);

        // Handle tags if present
        if (isset($data['tags']) && is_array($data['tags'])) {
            $this->syncScholarshipTags($scholarship, $data['tags']);
        }

        return $scholarship;
    }

    /**
     * Update an existing scholarship with validation.
     *
     * @param int $id
     * @param array $data
     * @return Scholarship
     * @throws ValidationException
     */
    public function updateScholarship(int $id, array $data): Scholarship
    {
        // Get scholarship
        $scholarship = $this->scholarshipRepository->findById($id);

        // Validate data
        $this->validateScholarshipData($data);

        // Check for duplicate
        if (
            isset($data['name']) &&
            isset($data['year']) &&
            $this->scholarshipRepository->isDuplicate($data['name'], $data['year'], $id)
        ) {
            throw ValidationException::withMessages([
                'name' => ['A scholarship with this name already exists for the specified year.'],
            ]);
        }

        // Update scholarship
        $scholarship = $this->scholarshipRepository->update($scholarship, $data);

        // Handle tags if present
        if (isset($data['tags']) && is_array($data['tags'])) {
            $this->syncScholarshipTags($scholarship, $data['tags']);
        }

        return $scholarship;
    }

    /**
     * Delete a scholarship.
     *
     * @param int $id
     * @return bool
     */
    public function deleteScholarship(int $id): bool
    {
        $scholarship = $this->scholarshipRepository->findById($id);
        return $this->scholarshipRepository->delete($scholarship);
    }

    /**
     * Activate a scholarship.
     *
     * @param int $id
     * @return Scholarship
     */
    public function activateScholarship(int $id): Scholarship
    {
        $scholarship = $this->scholarshipRepository->findById($id);
        return $this->scholarshipRepository->activate($scholarship);
    }

    /**
     * Deactivate a scholarship.
     *
     * @param int $id
     * @return Scholarship
     */
    public function deactivateScholarship(int $id): Scholarship
    {
        $scholarship = $this->scholarshipRepository->findById($id);
        return $this->scholarshipRepository->deactivate($scholarship);
    }

    /**
     * Duplicate an existing scholarship.
     *
     * @param Scholarship $scholarship
     * @return Scholarship
     * @throws ValidationException
     */
    public function duplicateScholarship(Scholarship $scholarship): Scholarship
    {
        // Create a copy of the scholarship data
        $scholarshipData = $scholarship->toArray();

        // Generate a unique name for the copy
        $baseName = "Copy of " . $scholarshipData['name'];
        $copyName = $baseName;
        $counter = 1;

        // Keep checking and incrementing counter until we find a non-duplicate name
        while ($this->scholarshipRepository->isDuplicate($copyName, $scholarshipData['year'])) {
            $counter++;
            $copyName = $baseName . " ({$counter})";
        }

        // Set the unique name
        $scholarshipData['name'] = $copyName;

        // Remove fields that shouldn't be duplicated
        unset($scholarshipData['id']);
        unset($scholarshipData['created_at']);
        unset($scholarshipData['updated_at']);
        unset($scholarshipData['deleted_at']);

        // Create new scholarship using existing create method
        return $this->createScholarship($scholarshipData);
    }

    /**
     * Calculate the default scholarship year based on the current date
     * - If before June 3rd: Use current year
     * - If after June 2nd: Use next year
     *
     * @return int The appropriate default year for a new scholarship
     */
    public function getDefaultScholarshipYear(): int
    {
        $currentDate = now();
        $cutoffDate = \Carbon\Carbon::create($currentDate->year, 6, 3, 0, 0, 0);

        if ($currentDate->lt($cutoffDate)) {
            // Before June 3rd - use current year
            return $currentDate->year;
        } else {
            // June 3rd or later - use next year
            return $currentDate->year + 1;
        }
    }

    /**
     * Get scholarships for a specific user.
     *
     * @param User $user
     * @param array $filters
     * @return Collection
     */
    public function getScholarshipsForUser(User $user, array $filters = []): Collection
    {
        return $this->scholarshipRepository->getScholarshipsForUser($user, $filters);
    }

    /**
     * Get scholarships by geographic criteria.
     *
     * @param array $geoFilters
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getScholarshipsByGeography(array $geoFilters, array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $allFilters = array_merge($filters, $geoFilters);
        return $this->scholarshipRepository->getScholarships($allFilters, $perPage);
    }

    /**
     * Add a winner to a scholarship, respecting the winner limit.
     *
     * @param int $scholarshipId
     * @param int $userId
     * @param array $winnerData
     * @return Winner
     * @throws \Exception
     */
    public function addWinner(int $scholarshipId, int $userId, array $winnerData = []): Winner
    {
        $scholarship = $this->getScholarshipById($scholarshipId);
        $user = User::findOrFail($userId);

        // Check if user is already a winner for this scholarship
        if ($scholarship->winners()->where('user_id', $userId)->exists()) {
            throw ValidationException::withMessages([
                'user_id' => ['This user is already a winner for this scholarship.'],
            ]);
        }

        // Check if adding another winner would exceed the limit
        if ($scholarship->winners()->count() >= $scholarship->limit) {
            throw ValidationException::withMessages([
                'limit' => ['Cannot add more winners. The scholarship has reached its winner limit.'],
            ]);
        }

        // Set default values
        $winnerData = array_merge([
            'user_id' => $userId,
            'scholarship_id' => $scholarshipId,
            'year' => $scholarship->year,
            'is_finalist' => true,
            'is_winner' => true,
            'verification_state' => WinnerVerificationState::PENDING_VERIFICATION->value,
            'tshirt_size' => 'M', // Default value, typically updated later by the winner
        ], $winnerData);

        // Create the winner entry
        return DB::transaction(function () use ($winnerData) {
            return Winner::create($winnerData);
        });
    }

    /**
     * Remove a winner from a scholarship.
     *
     * @param int $scholarshipId
     * @param int $winnerId
     * @return bool
     * @throws \Exception
     */
    public function removeWinner(int $scholarshipId, int $winnerId): bool
    {
        $scholarship = $this->getScholarshipById($scholarshipId);

        // Check if the winner belongs to this scholarship
        $winner = $scholarship->winners()->where('id', $winnerId)->first();

        if (!$winner) {
            throw ValidationException::withMessages([
                'winner_id' => ['This winner does not belong to the specified scholarship.'],
            ]);
        }

        return DB::transaction(function () use ($winner) {
            return $winner->delete();
        });
    }

    /**
     * Sync scholarship tags.
     *
     * @param Scholarship $scholarship
     * @param array $tagNames
     * @return void
     */
    protected function syncScholarshipTags(Scholarship $scholarship, array $tagNames): void
    {
        // Create tags that don't exist and get all tag IDs
        $tagIds = [];

        foreach ($tagNames as $tagName) {
            $tag = \App\Models\Tag::firstOrCreate(['name' => $tagName]);
            $tagIds[] = $tag->id;
        }

        // Sync tags
        $scholarship->tags()->sync($tagIds);
    }

    /**
     * Validate scholarship data.
     *
     * @param array $data
     * @return void
     * @throws ValidationException
     */
    protected function validateScholarshipData(array $data): void
    {
        $validator = Validator::make($data, [
            'name' => 'required|string|max:255',
            'year' => 'required|integer|min:2000|max:' . (date('Y') + 10),
            'details' => 'nullable|string',
            'notes' => 'nullable|string',
            'limit' => 'nullable|integer|min:1|max:100',
            'is_active' => 'boolean',
            'region_id' => 'required|exists:regions,id',
            'market_id' => 'required|exists:markets,id',
            'state_id' => 'required|exists:states,code',
            'subregion_id' => 'nullable|exists:sub_regions,id',
            'tags' => 'nullable|array',
            'tags.*' => 'nullable|string|max:50',
        ]);

        if ($validator->fails()) {
            throw ValidationException::withMessages($validator->errors()->toArray());
        }
    }
}
