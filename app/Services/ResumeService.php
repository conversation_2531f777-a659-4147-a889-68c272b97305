<?php

namespace App\Services;

use App\Data\Resume\ResumeData;
use App\Data\Resume\ResumeSectionData;
use App\Models\Resume;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Spatie\LaravelData\DataCollection;
use Illuminate\Http\UploadedFile;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class ResumeService
{
    /**
     * Get the authenticated user's resume.
     */
    public function getUserResume(): ?Resume
    {
        return Resume::query()
            ->where('user_id', Auth::id())
            ->with('sections')
            ->first();
    }

    /**
     * Create a new resume from DTO, or return existing one.
     */
    public function create(ResumeData $data): Resume
    {
        // First try to get existing resume
        $resume = Resume::query()
            ->where('user_id', Auth::id())
            ->first();

        // If resume exists, return it
        if ($resume) {
            return $resume->load('sections');
        }

        // Create new resume if none exists
        $resume = Resume::query()->create([
            'user_id' => Auth::id(),
            'name' => $data->name,
        ]);

        $this->syncSections($resume, $data->sections);

        // Only copy profile avatar if this is a new resume
        if ($resume->wasRecentlyCreated) {
            $this->copyProfileAvatar($resume);
        }

        return $resume->fresh(['sections']);
    }

    /**
     * Update a resume and sync its sections.
     */
    public function update(Resume $resume, ResumeData $data): Resume
    {
        $resume->update([
            'name' => $data->name,
        ]);

        $this->syncSections($resume, $data->sections);

        return $resume->fresh(['sections']);
    }

    /**
     * Delete a resume and its sections.
     */
    public function delete(Resume $resume): bool
    {
        return $resume->delete();
    }

    /**
     * Sync resume sections from DTO collection.
     */
    protected function syncSections(Resume $resume, DataCollection $sections): void
    {
        ray($sections);
        $resume->sections()->delete();

        /** @var ResumeSectionData $section */
        foreach ($sections as $section) {
            $resume->sections()->create([
                'section_type' => $section->section_type,
                'is_enabled' => $section->is_enabled,
                'content' => $section->content,
            ]);
        }
    }

    /**
     * Copy the user's profile avatar to their resume if they don't have one.
     */
    protected function copyProfileAvatar(Resume $resume): void
    {
        // Skip if resume already has an avatar
        if ($resume->getFirstMedia('avatar')) {
            return;
        }

        // Get user's profile avatar
        $user = $resume->user;
        $profileAvatar = $user->getFirstMedia('avatar');

        // Skip if user doesn't have a profile avatar
        if (!$profileAvatar) {
            return;
        }

        // Copy the profile avatar to the resume
        $profileAvatar->copy($resume, 'avatar');
    }

    /**
     * Reset the resume's avatar.
     */
    public function resetAvatar(Resume $resume): void
    {
        $resume->clearMediaCollection('avatar');
        $this->copyProfileAvatar($resume);
    }

    /**
     * Update the resume's avatar with a new file.
     *
     * @param  Resume  $resume The resume model instance.
     * @param  UploadedFile|string  $file The uploaded avatar file or base64 string.
     * @return Media The Spatie Media model instance.
     */
    public function updateAvatar(Resume $resume, $file): Media
    {
        // Clear existing avatar first
        $resume->clearMediaCollection('avatar');

        // Add the new avatar
        if (is_string($file) && strpos($file, 'data:image/') === 0) {
            // Handle base64 encoded image
            return $resume->addMediaFromBase64($file)
                ->toMediaCollection('avatar');
        } else {
            // Handle uploaded file
            return $resume->addMedia($file)
                ->toMediaCollection('avatar');
        }
    }

    /**
     * Store the uploaded PDF resume.
     *
     * @param  Resume  $resume The resume model instance.
     * @param  UploadedFile  $file The uploaded PDF file.
     * @return Media The Spatie Media model instance.
     */
    public function storeResumePdf(Resume $resume, UploadedFile $file): Media
    {
        // The 'resume_pdf' collection is configured as singleFile(),
        // so this will automatically replace any existing file in that collection.
        return $resume->addMedia($file)
            ->toMediaCollection('resume_pdf');
    }

    /**
     * Upload PDF for a user's resume, creating the resume if it doesn't exist.
     *
     * @param  UploadedFile  $file The uploaded PDF file.
     * @return array An array containing the resume model and media item.
     */
    public function uploadResumePdf(UploadedFile $file): array
    {
        // Get or create the user's resume
        $resume = $this->getUserResume();

        if (!$resume) {
            // Create a basic resume if it doesn't exist
            $resumeData = new ResumeData(
                id: null,
                name: Auth::user()->name . "'s Resume",
                sections: new DataCollection(ResumeSectionData::class, [])
            );

            $resume = $this->create($resumeData);
        }

        // Store the PDF
        $mediaItem = $this->storeResumePdf($resume, $file);

        return [
            'resume' => $resume,
            'media' => $mediaItem
        ];
    }
}
