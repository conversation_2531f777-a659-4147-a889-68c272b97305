<?php

namespace App\Services;

use App\Enums\ProfileType;
use App\Enums\UiRegion;
use App\Models\Advertisement;
use App\Models\AdvertisementProfileType;
use App\Models\AdvertisementUiRegion;
use App\Models\User;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AdvertisementService
{
    /**
     * Create a new advertisement.
     *
     * @param array $data Advertisement data
     * @param array|null $mediaItems Media items to attach
     * @return Advertisement
     */
    public function createAdvertisement(array $data, ?array $mediaItems = null): Advertisement
    {
        try {
            DB::beginTransaction();

            // Create the advertisement
            $advertisement = Advertisement::query()->create([
                'name' => $data['name'],
                'user_id' => $data['user_id'],
                'is_listed' => $data['is_listed'] ?? true,
                'copy' => $data['copy'] ?? null,
                'cta_text' => $data['cta_text'] ?? null,
                'cta_url' => $data['cta_url'] ?? null,
                'impressions' => 0,
                'clicks' => 0,
            ]);

            // Add profile types if provided
            if (!empty($data['profile_types'])) {
                $this->syncProfileTypes($advertisement, $data['profile_types']);
            }

            // Add UI regions if provided
            if (!empty($data['ui_regions'])) {
                $this->syncUiRegions($advertisement, $data['ui_regions']);
            }

            // Add geographic filters if provided
            if (!empty($data['regions'])) {
                $advertisement->regions()->attach($data['regions']);
            }

            if (!empty($data['markets'])) {
                $advertisement->markets()->attach($data['markets']);
            }

            if (!empty($data['sub_regions'])) {
                $advertisement->subRegions()->attach($data['sub_regions']);
            }

            if (!empty($data['counties'])) {
                $advertisement->counties()->attach($data['counties']);
            }

            if (!empty($data['states'])) {
                $advertisement->states()->attach($data['states']);
            }

            // Handle media uploads if provided
            if ($mediaItems && !empty($mediaItems['logo'])) {
                $advertisement->addMedia($mediaItems['logo'])
                    ->toMediaCollection('logo');
            }

            if ($mediaItems && !empty($mediaItems['background'])) {
                $advertisement->addMedia($mediaItems['background'])
                    ->toMediaCollection('background');
            }

            DB::commit();
            return $advertisement;
        } catch (\Throwable $e) {
            DB::rollBack();
            Log::error('Failed to create advertisement', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);
            throw new Exception('Failed to create advertisement: ' . $e->getMessage());
        }
    }

    /**
     * Update an existing advertisement.
     *
     * @param int $id Advertisement ID
     * @param array $data Advertisement data
     * @param array|null $mediaItems Media items to attach
     * @return Advertisement
     */
    public function updateAdvertisement(int $id, array $data, ?array $mediaItems = null): Advertisement
    {
        try {
            DB::beginTransaction();

            $advertisement = Advertisement::query()->findOrFail($id);

            // Update basic information
            $advertisement->update([
                'name' => $data['name'] ?? $advertisement->name,
                'is_listed' => $data['is_listed'] ?? $advertisement->is_listed,
                'copy' => $data['copy'] ?? $advertisement->copy,
                'cta_text' => $data['cta_text'] ?? $advertisement->cta_text,
                'cta_url' => $data['cta_url'] ?? $advertisement->cta_url,
            ]);

            // Update profile types if provided
            if (isset($data['profile_types'])) {
                $this->syncProfileTypes($advertisement, $data['profile_types']);
            }

            // Update UI regions if provided
            if (isset($data['ui_regions'])) {
                $this->syncUiRegions($advertisement, $data['ui_regions']);
            }

            // Update geographic filters if provided
            if (isset($data['regions'])) {
                $advertisement->regions()->sync($data['regions']);
            }

            if (isset($data['markets'])) {
                $advertisement->markets()->sync($data['markets']);
            }

            if (isset($data['sub_regions'])) {
                $advertisement->subRegions()->sync($data['sub_regions']);
            }

            if (isset($data['counties'])) {
                $advertisement->counties()->sync($data['counties']);
            }

            if (isset($data['states'])) {
                $advertisement->states()->sync($data['states']);
            }

            // Handle media uploads if provided
            if ($mediaItems && !empty($mediaItems['logo'])) {
                // Remove old logo first
                $advertisement->clearMediaCollection('logo');
                // Add new logo
                $advertisement->addMedia($mediaItems['logo'])
                    ->toMediaCollection('logo');
            }

            if ($mediaItems && !empty($mediaItems['background'])) {
                // Remove old background first
                $advertisement->clearMediaCollection('background');
                // Add new background
                $advertisement->addMedia($mediaItems['background'])
                    ->toMediaCollection('background');
            }

            DB::commit();
            return $advertisement;
        } catch (\Throwable $e) {
            DB::rollBack();
            Log::error('Failed to update advertisement', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'advertisement_id' => $id,
                'data' => $data
            ]);
            throw new Exception('Failed to update advertisement: ' . $e->getMessage());
        }
    }

    /**
     * Delete an advertisement.
     *
     * @param int $id Advertisement ID
     * @return bool
     */
    public function deleteAdvertisement(int $id): bool
    {
        try {
            $advertisement = Advertisement::query()->findOrFail($id);
            return $advertisement->delete();
        } catch (\Throwable $e) {
            Log::error('Failed to delete advertisement', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'advertisement_id' => $id
            ]);
            return false;
        }
    }

    /**
     * Get all advertisements, optionally paginated.
     *
     * @param int $perPage
     * @param array $filters
     * @return LengthAwarePaginator
     */
    public function getAdvertisements(int $perPage = 15, array $filters = []): LengthAwarePaginator
    {
        $query = Advertisement::query()
            ->with(['user', 'media']);

        // Apply filters if provided
        if (!empty($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (isset($filters['is_listed'])) {
            $query->where('is_listed', $filters['is_listed']);
        }

        if (!empty($filters['profile_type'])) {
            $query->forProfileType(ProfileType::from($filters['profile_type']));
        }

        if (!empty($filters['ui_region'])) {
            $query->forUiRegion(UiRegion::from($filters['ui_region']));
        }

        // Geographic filtering
        if (!empty($filters['region_id'])) {
            $query->whereHas('regions', function ($q) use ($filters) {
                $q->where('region_id', $filters['region_id']);
            });
        }

        if (!empty($filters['market_id'])) {
            $query->whereHas('markets', function ($q) use ($filters) {
                $q->where('market_id', $filters['market_id']);
            });
        }

        if (!empty($filters['sub_region_id'])) {
            $query->whereHas('subRegions', function ($q) use ($filters) {
                $q->where('sub_region_id', $filters['sub_region_id']);
            });
        }

        if (!empty($filters['county_id'])) {
            $query->whereHas('counties', function ($q) use ($filters) {
                $q->where('county_id', $filters['county_id']);
            });
        }

        if (!empty($filters['state_id'])) {
            $query->whereHas('states', function ($q) use ($filters) {
                $q->where('state_id', $filters['state_id']);
            });
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Get a single advertisement by ID.
     *
     * @param int $id
     * @return Advertisement|null
     */
    public function getAdvertisement(int $id): ?Advertisement
    {
        return Advertisement::query()
            ->with([
                'user',
                'media',
                'regions',
                'markets',
                'subRegions',
                'counties',
                'states'
            ])
            ->find($id);
    }

    /**
     * Record an impression for an advertisement.
     *
     * @param int $id Advertisement ID
     * @return bool
     */
    public function recordImpression(int $id): bool
    {
        try {
            $advertisement = Advertisement::query()->findOrFail($id);
            $this->recordImpressionDirectly($advertisement);
            return true;
        } catch (\Throwable $e) {
            Log::error('Failed to record advertisement impression', [
                'error' => $e->getMessage(),
                'advertisement_id' => $id
            ]);
            return false;
        }
    }

    /**
     * Record a click for an advertisement.
     *
     * @param int $id Advertisement ID
     * @return bool
     */
    public function recordClick(int $id): bool
    {
        try {
            $advertisement = Advertisement::query()->findOrFail($id);
            $this->recordClickDirectly($advertisement);
            return true;
        } catch (\Throwable $e) {
            Log::error('Failed to record advertisement click', [
                'error' => $e->getMessage(),
                'advertisement_id' => $id
            ]);
            return false;
        }
    }

    /**
     * Get advertisements that should be shown to a specific user.
     *
     * @param User $user
     * @param UiRegion $uiRegion
     * @param int $limit
     * @return Collection
     */
    public function getAdvertisementsForUser(User $user, UiRegion $uiRegion, int $limit = 3): Collection
    {
        // Get the user's geographic data first
        $isPositiveAthlete = $user->profile_type === ProfileType::POSITIVE_ATHLETE;
        $hasSchool = $isPositiveAthlete && $user->school_id;
        $geoData = $this->getUserGeographicData($user, $hasSchool);

        // Start with base query for active advertisements in the requested UI region
        $query = Advertisement::query()
            ->with(['media'])
            ->where('is_listed', true)
            ->whereHas('uiRegions', function ($q) use ($uiRegion) {
                $q->where('ui_region', $uiRegion->value);
            });

        // Filter by user's profile type if available
        if ($user->profile_type) {
            // Get the profile type enum from the user model if valid
            $profileType = $user->profile_type instanceof ProfileType
                ? $user->profile_type
                : ProfileType::tryFrom((string)$user->profile_type);

            if ($profileType) {
                $query->whereHas('profileTypes', function ($q) use ($profileType) {
                    $q->where('profile_type', $profileType->value);
                });
            }
        }

        // Geographic targeting - using WHERE clauses instead of UNION
        $query->where(function ($q) use ($geoData) {
            // Include ads with no geo targeting (always shown)
            $q->whereDoesntHave('regions')
              ->whereDoesntHave('markets')
              ->whereDoesntHave('subRegions')
              ->whereDoesntHave('states')
              ->whereDoesntHave('counties');

            // OR ads that match the user's state
            if (!empty($geoData['state_code'])) {
                $q->orWhereHas('states', function ($sq) use ($geoData) {
                    $sq->where('state_code', $geoData['state_code']);
                });
            }

            // OR ads that match the user's region
            if (!empty($geoData['region_id'])) {
                $q->orWhereHas('regions', function ($sq) use ($geoData) {
                    $sq->where('region_id', $geoData['region_id']);
                });
            }

            // OR ads that match the user's market
            if (!empty($geoData['market_id'])) {
                $q->orWhereHas('markets', function ($sq) use ($geoData) {
                    $sq->where('market_id', $geoData['market_id']);
                });
            }

            // OR ads that match the user's sub-region
            if (!empty($geoData['sub_region_id'])) {
                $q->orWhereHas('subRegions', function ($sq) use ($geoData) {
                    $sq->where('sub_region_id', $geoData['sub_region_id']);
                });
            }
        });

        // Order and limit results at the database level
        return $query->orderBy('created_at', 'desc')->limit($limit)->get();
    }

    /**
     * Get user's geographic data, prioritizing school data for positive athletes.
     *
     * @param User $user
     * @param bool $hasSchool
     * @return array
     */
    private function getUserGeographicData(User $user, bool $hasSchool): array
    {
        $geoData = [
            'region_id' => null,
            'market_id' => null,
            'sub_region_id' => null,
            'state_code' => null,
        ];

        if ($hasSchool) {
            // Load the school relationship if not already loaded
            if (!$user->relationLoaded('school')) {
                $user->load(['school', 'school.region', 'school.county']);
            }

            $school = $user->school;

            if ($school) {
                // Get region from school
                $geoData['region_id'] = $school->region_id;

                // Get state from school's county if available
                if ($school->county_id && !$school->relationLoaded('county')) {
                    $school->load('county');
                }

                if ($school->county) {
                    $geoData['state_code'] = $school->county->state_code;

                    // Get market and sub-region data from county if available
                    $geoData['market_id'] = $school->county->market_id;
                    $geoData['sub_region_id'] = $school->county->sub_region_id;
                }
            }
        } else {
            // Use user's direct geographic data
            $geoData['region_id'] = $user->region_id;
            $geoData['state_code'] = $user->state_code;

            // Load county relationship if user has county_id to get market and sub-region
            if ($user->county_id && !$user->relationLoaded('county')) {
                $user->load('county');
            }

            // Get market and sub-region from county if available
            if ($user->county) {
                $geoData['market_id'] = $user->county->market_id;
                $geoData['sub_region_id'] = $user->county->sub_region_id;
            }
        }

        return $geoData;
    }

    /**
     * Check if user's geographic data matches advertisement's targeting.
     *
     * @param Advertisement $advertisement
     * @param array $geoData
     * @return bool
     */
    private function matchesGeographicTargeting(Advertisement $advertisement, array $geoData): bool
    {
        // Check state targeting
        if ($advertisement->states()->count() > 0) {
            // If ad targets specific states, user must be in one of them
            if (!$geoData['state_code']) {
                return false;
            }

            return $advertisement->states()
                ->where('state_code', $geoData['state_code'])
                ->exists();
        }

        // Check sub-region targeting
        if ($advertisement->subRegions()->count() > 0) {
            // If ad targets specific sub-regions, user must be in one of them
            if (!$geoData['sub_region_id']) {
                return false;
            }

            return $advertisement->subRegions()
                ->where('sub_region_id', $geoData['sub_region_id'])
                ->exists();
        }

        // Check market targeting
        if ($advertisement->markets()->count() > 0) {
            // If ad targets specific markets, user must be in one of them
            if (!$geoData['market_id']) {
                return false;
            }

            return $advertisement->markets()
                ->where('market_id', $geoData['market_id'])
                ->exists();
        }

        // Check region targeting (broadest level)
        if ($advertisement->regions()->count() > 0) {
            // If ad targets specific regions, user must be in one of them
            if (!$geoData['region_id']) {
                return false;
            }

            return $advertisement->regions()
                ->where('region_id', $geoData['region_id'])
                ->exists();
        }

        // If we got here, there's geographic targeting but user doesn't match any of it
        return false;
    }

    /**
     * Sync profile types for an advertisement.
     *
     * @param Advertisement $advertisement
     * @param array $profileTypes
     * @return void
     */
    public function syncProfileTypes(Advertisement $advertisement, array $profileTypes): void
    {
        // First, delete all existing profile types
        AdvertisementProfileType::query()
            ->where('advertisement_id', $advertisement->id)
            ->delete();

        // Add new profile types
        foreach ($profileTypes as $type) {
            if ($type instanceof ProfileType) {
                $this->addProfileType($advertisement, $type);
            } else {
                try {
                    // Handle string values by converting to enum
                    $profileType = is_string($type) ? ProfileType::from($type) : $type;
                    $this->addProfileType($advertisement, $profileType);
                } catch (\Exception $e) {
                    Log::warning("Invalid profile type: {$type}", [
                        'advertisement_id' => $advertisement->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }
    }

    /**
     * Sync UI regions for an advertisement.
     *
     * @param Advertisement $advertisement
     * @param array $uiRegions
     * @return void
     */
    public function syncUiRegions(Advertisement $advertisement, array $uiRegions): void
    {
        // First, delete all existing UI regions
        AdvertisementUiRegion::query()
            ->where('advertisement_id', $advertisement->id)
            ->delete();

        // Add new UI regions
        foreach ($uiRegions as $region) {
            if ($region instanceof UiRegion) {
                $this->addUiRegion($advertisement, $region);
            } else {
                try {
                    // Handle string values by converting to enum
                    $uiRegion = is_string($region) ? UiRegion::from($region) : $region;
                    $this->addUiRegion($advertisement, $uiRegion);
                } catch (\Exception $e) {
                    Log::warning("Invalid UI region: {$region}", [
                        'advertisement_id' => $advertisement->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }
    }

    /**
     * Determine if an advertisement should be shown to a user.
     *
     * @param Advertisement $advertisement
     * @param User $user
     * @return bool
     */
    public function shouldShowToUser(Advertisement $advertisement, User $user): bool
    {
        // Not active, don't show
        if (!$advertisement->is_listed) {
            Log::debug('Advertisement not listed', ['ad_id' => $advertisement->id]);
            return false;
        }

        // Check profile type targeting
        $profileTypeCount = $advertisement->profileTypes()->count();
        if ($profileTypeCount > 0 && !$advertisement->profileTypes()->where('profile_type', $user->profile_type)->exists()) {
            Log::debug('Profile type mismatch', [
                'ad_id' => $advertisement->id,
                'ad_profile_types' => $advertisement->profileTypes()->pluck('profile_type')->toArray(),
                'user_profile_type' => $user->profile_type
            ]);
            return false;
        }

        // Geographic targeting logic
        $hasGeographicTargeting =
            $advertisement->regions()->count() > 0 ||
            $advertisement->markets()->count() > 0 ||
            $advertisement->subRegions()->count() > 0 ||
            $advertisement->states()->count() > 0 ||
            $advertisement->counties()->count() > 0;

        if ($hasGeographicTargeting) {
            $matchesGeographic = false;

            // For positive athletes, we want to check school relationship first
            $isPositiveAthlete = $user->profile_type === ProfileType::POSITIVE_ATHLETE;

            // If this is a positive athlete and they have a school, use the school's geographic data
            if ($isPositiveAthlete && $user->school_id) {
                // Load the school relationship if not already loaded
                if (!$user->relationLoaded('school')) {
                    $user->load(['school', 'school.region', 'school.county']);
                }

                $school = $user->school;

                Log::debug('School loaded for user', [
                    'user_id' => $user->id,
                    'school_id' => $user->school_id,
                    'school_loaded' => !is_null($school),
                    'school_region_id' => $school->region_id ?? null,
                    'school_county_id' => $school->county_id ?? null,
                ]);

                if ($school) {
                    // Check if ad has region targeting and if school's region matches
                    if ($advertisement->regions()->count() > 0) {
                        Log::debug('Checking region targeting', [
                            'ad_id' => $advertisement->id,
                            'ad_regions' => $advertisement->regions()->pluck('region_id')->toArray(),
                            'school_region_id' => $school->region_id,
                            'matches' => $advertisement->regions()->where('region_id', $school->region_id)->exists()
                        ]);
                    }

                    // Check if the school matches any of the geographic targets
                    if (
                        // Region targeting - check school's region
                        ($advertisement->regions()->count() > 0 && $school->region_id &&
                        $advertisement->regions()->where('region_id', $school->region_id)->exists()) ||

                        // County targeting - check school's county
                        ($advertisement->counties()->count() > 0 && $school->county_id &&
                        $advertisement->counties()->where('county_id', $school->county_id)->exists()) ||

                        // State targeting - check school's county's state
                        ($advertisement->states()->count() > 0 && $school->county && $school->county->state_code &&
                        $advertisement->states()->where('state_code', $school->county->state_code)->exists()) ||

                        // Market targeting - check school's county's market
                        ($advertisement->markets()->count() > 0 && $school->county && $school->county->market_id &&
                        $advertisement->markets()->where('market_id', $school->county->market_id)->exists()) ||

                        // SubRegion targeting - check school's county's sub_region
                        ($advertisement->subRegions()->count() > 0 && $school->county && $school->county->sub_region_id &&
                        $advertisement->subRegions()->where('sub_region_id', $school->county->sub_region_id)->exists())
                    ) {
                        $matchesGeographic = true;
                        Log::debug('User matched ad through school geographic data', [
                            'user_id' => $user->id,
                            'ad_id' => $advertisement->id
                        ]);
                    } else {
                        Log::debug('User did not match ad through school data', [
                            'user_id' => $user->id,
                            'ad_id' => $advertisement->id
                        ]);
                    }
                }
            }

            // If not a positive athlete or no school match was found, fall back to user's direct geographic attributes
            if (!$matchesGeographic) {
                // Debugging for user's direct attributes
                Log::debug('Checking user direct geographic attributes', [
                    'user_id' => $user->id,
                    'user_region_id' => $user->region_id,
                    'user_county_id' => $user->county_id,
                    'user_state_code' => $user->state_code
                ]);

                if (
                    // State targeting
                    ($advertisement->states()->count() > 0 && $user->state_code &&
                    $advertisement->states()->where('state_code', $user->state_code)->exists()) ||

                    // County targeting
                    ($advertisement->counties()->count() > 0 && $user->county_id &&
                    $advertisement->counties()->where('county_id', $user->county_id)->exists()) ||

                    // Region targeting
                    ($advertisement->regions()->count() > 0 && $user->region_id &&
                    $advertisement->regions()->where('region_id', $user->region_id)->exists()) ||

                    // Market targeting
                    ($advertisement->markets()->count() > 0 && $user->county && $user->county->market_id &&
                    $advertisement->markets()->where('market_id', $user->county->market_id)->exists()) ||

                    // SubRegion targeting
                    ($advertisement->subRegions()->count() > 0 && $user->county && $user->county->sub_region_id &&
                    $advertisement->subRegions()->where('sub_region_id', $user->county->sub_region_id)->exists())
                ) {
                    $matchesGeographic = true;
                    Log::debug('User matched ad through direct geographic data', [
                        'user_id' => $user->id,
                        'ad_id' => $advertisement->id
                    ]);
                } else {
                    Log::debug('User did not match ad through direct attributes', [
                        'user_id' => $user->id,
                        'ad_id' => $advertisement->id
                    ]);
                }
            }

            if (!$matchesGeographic) {
                Log::debug('No geographic match, ad will not be shown', [
                    'user_id' => $user->id,
                    'ad_id' => $advertisement->id
                ]);
                return false;
            }
        }

        Log::debug('Ad will be shown to user', [
            'user_id' => $user->id,
            'ad_id' => $advertisement->id
        ]);
        return true;
    }

    /**
     * Get all available profile types for targeting.
     *
     * @return array
     */
    public function getAvailableProfileTypes(): array
    {
        return [
            ProfileType::POSITIVE_ATHLETE,
            ProfileType::POSITIVE_COACH,
            ProfileType::ATHLETICS_DIRECTOR,
            ProfileType::SPONSOR,
            ProfileType::PARENT,
            ProfileType::COLLEGE_ATHLETE,
            ProfileType::PROFESSIONAL,
        ];
    }

    /**
     * Get all available UI regions for advertisement placement.
     *
     * @return array
     */
    public function getAvailableUiRegions(): array
    {
        return UiRegion::cases();
    }

    /**
     * Add a profile type to an advertisement's targeting.
     *
     * @param Advertisement $advertisement
     * @param ProfileType $profileType
     * @return void
     */
    public function addProfileType(Advertisement $advertisement, ProfileType $profileType): void
    {
        AdvertisementProfileType::create([
            'advertisement_id' => $advertisement->id,
            'profile_type' => $profileType->value,
        ]);
    }

    /**
     * Remove a profile type from an advertisement's targeting.
     *
     * @param Advertisement $advertisement
     * @param ProfileType $profileType
     * @return void
     */
    public function removeProfileType(Advertisement $advertisement, ProfileType $profileType): void
    {
        $advertisement->profileTypes()
            ->where('profile_type', $profileType->value)
            ->delete();
    }

    /**
     * Add a UI region to an advertisement.
     *
     * @param Advertisement $advertisement
     * @param UiRegion $uiRegion
     * @return void
     */
    public function addUiRegion(Advertisement $advertisement, UiRegion $uiRegion): void
    {
        AdvertisementUiRegion::create([
            'advertisement_id' => $advertisement->id,
            'ui_region' => $uiRegion->value,
        ]);
    }

    /**
     * Remove a UI region from an advertisement.
     *
     * @param Advertisement $advertisement
     * @param UiRegion $uiRegion
     * @return void
     */
    public function removeUiRegion(Advertisement $advertisement, UiRegion $uiRegion): void
    {
        $advertisement->uiRegions()
            ->where('ui_region', $uiRegion->value)
            ->delete();
    }

    /**
     * Record an impression for an advertisement.
     *
     * @param Advertisement $advertisement
     * @return void
     */
    public function recordImpressionDirectly(Advertisement $advertisement): void
    {
        $advertisement->increment('impressions');
    }

    /**
     * Record a click for an advertisement.
     *
     * @param Advertisement $advertisement
     * @return void
     */
    public function recordClickDirectly(Advertisement $advertisement): void
    {
        $advertisement->increment('clicks');
    }

    /**
     * Get the click-through rate for an advertisement.
     *
     * @param Advertisement $advertisement
     * @return float
     */
    public function getClickThroughRate(Advertisement $advertisement): float
    {
        if ($advertisement->impressions === 0) {
            return 0.0;
        }

        return round(($advertisement->clicks / $advertisement->impressions) * 100, 2);
    }

    // Temporary diagnostic method to help debug the shouldShowToUser issue
    public function diagnoseRegionMatch(Advertisement $advertisement, User $user): array
    {
        $diagnostics = [
            'ad_id' => $advertisement->id,
            'user_id' => $user->id,
            'ad_is_listed' => $advertisement->is_listed,
            'user_profile_type' => $user->profile_type,
            'ad_profile_types' => $advertisement->profileTypes()->pluck('profile_type')->toArray(),
            'ad_regions' => $advertisement->regions()->pluck('region_id')->toArray(),
            'user_region_id' => $user->region_id,
            'is_athlete' => $user->profile_type === ProfileType::POSITIVE_ATHLETE,
            'has_school' => (bool) $user->school_id
        ];

        if ($user->school_id) {
            if (!$user->relationLoaded('school')) {
                $user->load(['school', 'school.region', 'school.county']);
            }

            $school = $user->school;
            $diagnostics['school_found'] = (bool) $school;

            if ($school) {
                $diagnostics['school_id'] = $school->id;
                $diagnostics['school_region_id'] = $school->region_id;

                if ($advertisement->regions()->count() > 0) {
                    $regionsMatch = $advertisement->regions()
                        ->where('region_id', $school->region_id)
                        ->exists();
                    $diagnostics['regions_match'] = $regionsMatch;
                }
            }
        }

        return $diagnostics;
    }
}
