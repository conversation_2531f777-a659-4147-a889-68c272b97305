<?php

namespace App\Services;

use App\Data\School\SchoolNomineePaginatedData;
use App\Models\User;
use App\Repositories\SchoolNomineeRepository;
use Illuminate\Auth\Access\AuthorizationException;

class SchoolNomineeService
{
    protected $repository;

    public function __construct(SchoolNomineeRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Get nominees for the athletics director's school
     *
     * @param User $user The authenticated athletics director
     * @param array $filters
     * @return SchoolNomineePaginatedData
     * @throws AuthorizationException
     */
    public function getNominees(User $user, array $filters = []): SchoolNomineePaginatedData
    {
        // Get the school ID associated with the athletics director
        $schoolId = $this->getSchoolIdForAthleticsDirector($user);

        // Get nominees from the repository
        $nominees = $this->repository->getNomineesForSchool($schoolId, $filters);

        // Create and return the DTO
        return SchoolNomineePaginatedData::fromPaginator($nominees);
    }

    /**
     * Get the school ID associated with the athletics director
     *
     * @param User $user
     * @return int
     * @throws AuthorizationException
     */
    private function getSchoolIdForAthleticsDirector(User $user): int
    {
        // Check if the user has a school_id directly on their record
        if (!$user->school_id) {
            throw new AuthorizationException('Athletics director has no associated school');
        }

        return $user->school_id;
    }
}
