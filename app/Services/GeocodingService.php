<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GeocodingService
{
    /**
     * Base URL for the OpenStreetMap Nominatim API
     */
    protected string $baseUrl = 'https://nominatim.openstreetmap.org/search';

    /**
     * Geocode a location by city and state code
     *
     * @param string $city The city name
     * @param string $stateCode The state code (e.g., NY, CA)
     * @return array|null Array with latitude and longitude or null if geocoding failed
     */
    public function geocodeLocation(string $city, string $stateCode): ?array
    {
        try {
            // Add a delay to respect Nominatim usage policy (1 request per second)
            $response = Http::withHeaders([
                'User-Agent' => 'PositiveAthlete/1.0 (<EMAIL>)'
            ])->get($this->baseUrl, [
                'city' => $city,
                'state' => $stateCode,
                'country' => 'USA',
                'format' => 'json',
                'limit' => 1,
            ]);

            if ($response->successful()) {
                $data = $response->json();

                if (!empty($data)) {
                    $result = $data[0];

                    Log::info("Geocoded location: {$city}, {$stateCode}");

                    return [
                        'latitude' => (float) $result['lat'],
                        'longitude' => (float) $result['lon'],
                    ];
                }

                Log::info("No match found for location: {$city}, {$stateCode}");
                return null;
            }

            Log::warning("API error when geocoding {$city}, {$stateCode}: " . $response->status());
            return null;
        } catch (\Exception $e) {
            Log::error("Error geocoding location {$city}, {$stateCode}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Batch geocode multiple locations
     *
     * @param array $locations Array of [city, state_code] pairs
     * @return array
     */
    public function batchGeocode(array $locations): array
    {
        $results = [];

        foreach ($locations as $location) {
            // Add a small delay to avoid rate limiting
            usleep(500000); // 500ms

            $result = $this->geocodeLocation($location['city'], $location['state_code']);

            if ($result) {
                $results[] = [
                    'city' => $location['city'],
                    'state_code' => $location['state_code'],
                    'latitude' => $result['latitude'],
                    'longitude' => $result['longitude']
                ];
            }
        }

        return $results;
    }
}
