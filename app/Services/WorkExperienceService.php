<?php

namespace App\Services;

use App\Models\User;

class WorkExperienceService
{
    public function createFromOnboarding(User $user, array $data): void
    {
        if (!isset($data['items'])) {
            return;
        }

        foreach ($data['items'] as $item) {
            $user->workExperiences()->create([
                'name' => $item['name'],
                'date' => $item['date_range'],
                'description' => $item['description'],
                'order' => $item['order'],
            ]);
        }
    }
}
