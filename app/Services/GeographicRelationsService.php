<?php

namespace App\Services;

use App\Models\County;
use App\Models\Market;
use App\Models\Region;
use App\Models\State;
use App\Models\SubRegion;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class GeographicRelationsService
{
    /**
     * Cache timeout in seconds (default: 1 hour)
     */
    protected int $cacheTimeout = 3600;

    /**
     * Get all regions
     */
    public function getRegions(): Collection
    {
        return Cache::remember('geo:regions', $this->cacheTimeout, function () {
            return Region::query()->orderBy('name')->get();
        });
    }

    /**
     * Get markets, optionally filtered by region
     */
    public function getMarkets(?int $regionId = null): Collection
    {
        $cacheKey = 'geo:markets' . ($regionId ? ":region:{$regionId}" : '');

        return Cache::remember($cacheKey, $this->cacheTimeout, function () use ($regionId) {
            return Market::query()
                ->when($regionId, function (Builder $query) use ($regionId) {
                    $query->where('region_id', $regionId);
                })
                ->orderBy('name')
                ->get();
        });
    }

    /**
     * Get states, optionally filtered by market
     */
    public function getStates(?int $marketId = null): Collection
    {
        $cacheKey = 'geo:states' . ($marketId ? ":market:{$marketId}" : '');

        return Cache::remember($cacheKey, $this->cacheTimeout, function () use ($marketId) {
            // If filtering by market, we need to get the counties in the market first,
            // then get their unique state codes
            if ($marketId) {
                // Get all counties in the market
                $counties = County::query()
                    ->where('market_id', $marketId)
                    ->distinct()
                    ->pluck('state_code');

                // Then get the states for these counties
                return State::query()
                    ->whereIn('code', $counties)
                    ->orderBy('name')
                    ->get();
            }

            // If no market filter, return all states
            return State::query()
                ->orderBy('name')
                ->get();
        });
    }

    /**
     * Get sub-regions, optionally filtered by market
     */
    public function getSubRegions(?int $marketId = null): Collection
    {
        $cacheKey = 'geo:subregions' . ($marketId ? ":market:{$marketId}" : '');

        return Cache::remember($cacheKey, $this->cacheTimeout, function () use ($marketId) {
            return SubRegion::query()
                ->when($marketId, function (Builder $query) use ($marketId) {
                    $query->where('market_id', $marketId);
                })
                ->orderBy('name')
                ->get();
        });
    }

    /**
     * Get counties, optionally filtered by state code and/or sub-region ID
     */
    public function getCounties(?string $stateCode = null, ?int $subRegionId = null): Collection
    {
        $cacheKey = 'geo:counties';
        $cacheKey .= $stateCode ? ":state:{$stateCode}" : '';
        $cacheKey .= $subRegionId ? ":subregion:{$subRegionId}" : '';

        return Cache::remember($cacheKey, $this->cacheTimeout, function () use ($stateCode, $subRegionId) {
            return County::query()
                ->when($stateCode, function (Builder $query) use ($stateCode) {
                    $query->where('state_code', $stateCode);
                })
                ->when($subRegionId, function (Builder $query) use ($subRegionId) {
                    $query->where('sub_region_id', $subRegionId);
                })
                ->orderBy('name')
                ->get();
        });
    }

    /**
     * Get counties filtered by market ID
     */
    public function getCountiesByMarket(?int $marketId = null): Collection
    {
        $cacheKey = 'geo:counties' . ($marketId ? ":market:{$marketId}" : '');

        return Cache::remember($cacheKey, $this->cacheTimeout, function () use ($marketId) {
            return County::query()
                ->when($marketId, function (Builder $query) use ($marketId) {
                    $query->where('market_id', $marketId);
                })
                ->orderBy('name')
                ->get();
        });
    }

    /**
     * Get markets filtered by state code
     */
    public function getMarketsByState(?string $stateCode = null): Collection
    {
        if ($stateCode) {
            // Get counties for the state
            $counties = County::query()
                ->where('state_code', $stateCode)
                ->get();

            // Extract unique market IDs, ensuring we only include non-null values
            $marketIds = $counties->pluck('market_id')
                ->unique()
                ->filter(function ($id) {
                    return !is_null($id);
                })
                ->values()
                ->toArray();

            // Then get markets for these counties
            return Market::query()
                ->whereIn('id', $marketIds)
                ->orderBy('name')
                ->get();
        }

        // If no state filter, return all markets
        return Market::query()
            ->orderBy('name')
            ->get();
    }

    /**
     * Check if a geographic combination is valid
     */
    public function isValidCombination(
        ?array $regionIds = null,
        ?array $marketIds = null,
        ?array $stateCodes = null,
        ?array $subRegionIds = null,
        ?array $countyIds = null
    ): bool {
        // If nothing selected, that's a valid state
        if (
            empty($regionIds) &&
            empty($marketIds) &&
            empty($stateCodes) &&
            empty($subRegionIds) &&
            empty($countyIds)
        ) {
            return true;
        }

        // Check if markets belong to selected regions
        if (!empty($regionIds) && !empty($marketIds)) {
            $validMarketIds = Market::query()
                ->whereIn('region_id', $regionIds)
                ->pluck('id')
                ->toArray();

            if (count(array_diff($marketIds, $validMarketIds)) > 0) {
                return false;
            }
        }

        // Check if sub-regions belong to selected markets
        if (!empty($marketIds) && !empty($subRegionIds)) {
            $validSubRegionIds = SubRegion::query()
                ->whereIn('market_id', $marketIds)
                ->pluck('id')
                ->toArray();

            if (count(array_diff($subRegionIds, $validSubRegionIds)) > 0) {
                return false;
            }
        }

        // Check if counties belong to selected sub-regions
        if (!empty($subRegionIds) && !empty($countyIds)) {
            $validCountyIds = County::query()
                ->whereIn('sub_region_id', $subRegionIds)
                ->pluck('id')
                ->toArray();

            if (count(array_diff($countyIds, $validCountyIds)) > 0) {
                return false;
            }
        }

        // Check if counties belong to selected states
        if (!empty($stateCodes) && !empty($countyIds)) {
            $validCountyIds = County::query()
                ->whereIn('state_code', $stateCodes)
                ->pluck('id')
                ->toArray();

            if (count(array_diff($countyIds, $validCountyIds)) > 0) {
                return false;
            }
        }

        return true;
    }

    /**
     * Clear all geographic caches
     * Note: getMarketsByState() no longer uses caching
     */
    public function clearCache(): void
    {
        Cache::forget('geo:regions');

        // Use pattern to clear all market caches for other methods
        $marketKeys = Cache::get('cache_keys:geo:markets', []);
        foreach ($marketKeys as $key) {
            // Skip markets by state cache keys as that method no longer uses caching
            if (!str_contains($key, ':state:')) {
                Cache::forget($key);
            }
        }

        // Same for other entities
        $stateKeys = Cache::get('cache_keys:geo:states', []);
        foreach ($stateKeys as $key) {
            Cache::forget($key);
        }

        $subregionKeys = Cache::get('cache_keys:geo:subregions', []);
        foreach ($subregionKeys as $key) {
            Cache::forget($key);
        }

        $countyKeys = Cache::get('cache_keys:geo:counties', []);
        foreach ($countyKeys as $key) {
            Cache::forget($key);
        }

        // Clear the keys tracking lists
        Cache::forget('cache_keys:geo:markets');
        Cache::forget('cache_keys:geo:states');
        Cache::forget('cache_keys:geo:subregions');
        Cache::forget('cache_keys:geo:counties');
    }

    /**
     * Get the full geographic path for a given entity
     */
    public function getGeographicPath($entity): array
    {
        $path = [];

        if ($entity->region_id ?? null) {
            $path['region'] = Region::find($entity->region_id);
        }

        if ($entity->market_id ?? null) {
            $path['market'] = Market::find($entity->market_id);

            // If region not in path yet, add it
            if (!isset($path['region']) && $path['market']) {
                $path['region'] = $path['market']->region;
            }
        }

        if ($entity->sub_region_id ?? null) {
            $path['subRegion'] = SubRegion::find($entity->sub_region_id);

            // If market not in path yet, add it
            if (!isset($path['market']) && $path['subRegion']) {
                $path['market'] = $path['subRegion']->market;

                // If region not in path yet, add it
                if (!isset($path['region']) && $path['market']) {
                    $path['region'] = $path['market']->region;
                }
            }
        }

        if ($entity->county_id ?? null) {
            $path['county'] = County::find($entity->county_id);

            // Fill in missing hierarchy
            if ($path['county']) {
                if (!isset($path['subRegion']) && $path['county']->sub_region_id) {
                    $path['subRegion'] = $path['county']->subRegion;
                }

                if (!isset($path['market'])) {
                    $path['market'] = $path['county']->market;
                }

                if (!isset($path['region']) && $path['market']) {
                    $path['region'] = $path['market']->region;
                }
            }
        }

        if ($entity->state_code ?? null) {
            $path['state'] = State::where('code', $entity->state_code)->first();
        }

        return $path;
    }
}
