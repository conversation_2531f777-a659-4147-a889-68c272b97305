<?php

namespace App\Services;

use App\Enums\OpportunityStatus;
use App\Models\Opportunity;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OpportunityRecommendationService
{
    /**
     * Get recommended opportunities based on user's interests
     *
     * @param User $user
     * @param int $limit Default: 4
     * @return Collection
     */
    public function getRecommendedOpportunities(User $user, int $limit = 4): Collection
    {
        // Use caching to improve performance
        $cacheKey = "user:{$user->id}:recommended_opportunities:{$limit}";

        return Cache::remember($cacheKey, now()->addHour(), function () use ($user, $limit) {
            // Get user interests
            $userInterestIds = $user->interests()->pluck('interests.id')->toArray();

            // Log interests for debugging
            Log::debug("User interests: " . json_encode($userInterestIds));

            // If user has interests, find opportunities matching those interests
            $opportunities = new Collection();

            if (!empty($userInterestIds)) {
                // Start with opportunities matching interests
                $matchingOpportunities = $this->getOpportunitiesMatchingInterests($userInterestIds, $limit);
                Log::debug("Found " . $matchingOpportunities->count() . " matching opportunities");
                $opportunities = $opportunities->merge($matchingOpportunities);
            }

            // If we don't have enough opportunities, supplement with recent ones
            if ($opportunities->count() < $limit) {
                $existingIds = $opportunities->pluck('id');
                $additionalCount = $limit - $opportunities->count();

                $additionalOpportunities = $this->getRecentOpportunities($additionalCount, $existingIds);
                Log::debug("Added " . $additionalOpportunities->count() . " additional recent opportunities");
                $opportunities = $opportunities->merge($additionalOpportunities);
            }

            return $opportunities->load(['organization', 'interests']);
        });
    }

    /**
     * Get opportunities matching the given interest IDs
     *
     * @param array $interestIds
     * @param int $limit
     * @return Collection
     */
    private function getOpportunitiesMatchingInterests(array $interestIds, int $limit): Collection
    {
        // Get active opportunities with matching interests, ordered by most matches
        return Opportunity::with(['organization', 'interests'])
            ->where('status', '=', 'listed')
            ->where('admin_disabled', '=', false)
            ->whereHas('interests', function ($query) use ($interestIds) {
                $query->whereIn('interests.id', $interestIds);
            })
            ->select('opportunities.*')
            ->addSelect([
                DB::raw('(
                    SELECT COUNT(*)
                    FROM interest_opportunity
                    WHERE interest_opportunity.opportunity_id = opportunities.id
                    AND interest_opportunity.interest_id IN (' . implode(',', $interestIds) . ')
                ) AS interest_match_count')
            ])
            ->orderByDesc('interest_match_count')
            ->orderByDesc('is_featured')
            ->orderByDesc('created_at')
            ->limit($limit)
            ->get();
    }

    /**
     * Get recent opportunities
     *
     * @param int $limit
     * @param Collection|array|null $excludeIds
     * @return Collection
     */
    private function getRecentOpportunities(int $limit, $excludeIds = null): Collection
    {
        $query = Opportunity::with(['organization', 'interests'])
            ->where('status', '=', 'listed')
            ->where('admin_disabled', '=', false)
            ->orderByDesc('is_featured')
            ->orderByDesc('created_at');

        if ($excludeIds && !empty($excludeIds)) {
            $query->whereNotIn('id', $excludeIds);
        }

        return $query->limit($limit)->get();
    }
}
