<?php

namespace App\Services;

use App\Data\Opportunity\BookmarkStatusData;
use App\Data\Opportunity\BookmarkedIdsData;
use App\Models\Opportunity;
use App\Models\User;

class OpportunityService
{
    /**
     * Get a single opportunity by ID with its relationships
     *
     * @param int $id
     * @return Opportunity
     */
    public function show(int $id): Opportunity
    {
        return Opportunity::with(['organization', 'industries', 'locationCoordinate'])->findOrFail($id);
    }

    /**
     * Toggle bookmark status for an opportunity
     *
     * @param User $user
     * @param Opportunity $opportunity
     * @return BookmarkStatusData
     */
    public function toggleBookmark(User $user, Opportunity $opportunity): BookmarkStatusData
    {
        $isBookmarked = $user->bookmarkedOpportunities()->where('opportunity_id', $opportunity->id)->exists();

        if ($isBookmarked) {
            $user->bookmarkedOpportunities()->detach($opportunity->id);
            $isBookmarked = false;
        } else {
            $user->bookmarkedOpportunities()->attach($opportunity->id);
            $isBookmarked = true;
        }

        return BookmarkStatusData::fromBookmarkStatus($isBookmarked, $opportunity->id);
    }

    /**
     * Get all bookmarked opportunity IDs for a user
     *
     * @param User $user
     * @return BookmarkedIdsData
     */
    public function getBookmarkedOpportunityIds(User $user): BookmarkedIdsData
    {
        $ids = $user->bookmarkedOpportunities()->pluck('opportunities.id')->toArray();
        return BookmarkedIdsData::fromIds($ids);
    }
}
