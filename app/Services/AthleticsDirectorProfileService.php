<?php

namespace App\Services;

use App\Models\User;
use App\Models\Contact;
use App\Models\AthleticsDirectorProfile;
use Illuminate\Support\Facades\DB;
use App\Events\AthleticsDirectorVerified;

class AthleticsDirectorProfileService
{
    /**
     * Create an athletics director profile for a user
     */
    public function createForUser(User $user): AthleticsDirectorProfile
    {
        return DB::transaction(function () use ($user) {
            return AthleticsDirectorProfile::create([
                'user_id' => $user->id,
                'school_id' => $user->school_id,
                'is_verified' => false,
            ]);
        });
    }

    /**
     * Create an athletics director profile for a contact
     */
    public function createForContact(Contact $contact): AthleticsDirectorProfile
    {
        return DB::transaction(function () use ($contact) {
            return AthleticsDirectorProfile::create([
                'contact_id' => $contact->id,
                'school_id' => $contact->school_id,
                'is_verified' => false,
            ]);
        });
    }

    /**
     * Convert a contact-based profile to a user-based profile
     */
    public function convertContactToUser(Contact $contact, User $user): void
    {
        DB::transaction(function () use ($contact, $user) {
            $profile = AthleticsDirectorProfile::where('contact_id', $contact->id)
                ->firstOrFail();

            $profile->update([
                'user_id' => $user->id,
                'contact_id' => null,
                'school_id' => $user->school_id,
            ]);
        });
    }

    /**
     * Verify an athletics director profile
     */
    public function verify(AthleticsDirectorProfile $profile, User $verifier): void
    {
        DB::transaction(function () use ($profile, $verifier) {
            // First, unverify any existing AD profiles for this school
            AthleticsDirectorProfile::query()
                ->where('school_id', $profile->school_id)
                ->where('id', '!=', $profile->id)
                ->update([
                    'is_verified' => false,
                    'verified_at' => null,
                    'verified_by' => null
                ]);

            // Now verify the incoming profile
            $profile->verify($verifier);

            // Dispatch verification event
            event(new AthleticsDirectorVerified($profile));
        });
    }

    /**
     * Get all pending verification profiles
     */
    public function getPendingVerification()
    {
        return AthleticsDirectorProfile::query()
            ->where('is_verified', false)
            ->with(['user', 'contact'])
            ->get();
    }

    /**
     * Restore a previously rejected athletics director profile
     */
    public function restore(AthleticsDirectorProfile $profile): void
    {
        DB::transaction(function () use ($profile) {
            $profile->rejected_at = null;
            $profile->rejected_by = null;
            $profile->save();

            $profile->restore();
        });
    }

    /**
     * Reject and soft delete an athletics director profile
     */
    public function reject(AthleticsDirectorProfile $profile, User $rejector): void
    {
        DB::transaction(function () use ($profile, $rejector) {
            $profile->reject($rejector);

            $profile->delete();
        });
    }
}
