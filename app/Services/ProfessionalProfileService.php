<?php

namespace App\Services;

use App\Data\Profile\ProfessionalDetailsData;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class ProfessionalProfileService
{
    /**
     * Get professional profile details
     */
    public function getDetails(User $user): ProfessionalDetailsData
    {
        return ProfessionalDetailsData::fromModel($user->load('interests'));
    }

    /**
     * Update professional profile details
     */
    public function updateDetails(User $user, ProfessionalDetailsData $data): ProfessionalDetailsData
    {
        DB::beginTransaction();

        try {
            // Update user direct fields
            $user->update($data->toUserArray());

            // Preserve existing metadata and merge with new metadata
            $existingMetadata = $user->metadata ?? [];
            $newMetadata = $data->getMetadata();
            $metadata = array_merge($existingMetadata, $newMetadata);

            $user->metadata = $metadata;
            $user->save();

            // Sync interests to pivot table (user_interests)
            $user->interests()->sync($data->interests);

            DB::commit();

            return ProfessionalDetailsData::fromModel($user->load('interests'));
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
