<?php

namespace App\Services;

use App\Data\Profile\CollegeAthleteDetailsData;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class CollegeAthleteProfileService
{
    /**
     * Get college athlete profile details
     */
    public function getDetails(User $user): CollegeAthleteDetailsData
    {
        return CollegeAthleteDetailsData::fromModel($user->load('interests'));
    }

    /**
     * Update college athlete profile details
     */
    public function updateDetails(User $user, CollegeAthleteDetailsData $data): CollegeAthleteDetailsData
    {
        DB::beginTransaction();

        try {
            // Update user direct fields
            $user->update($data->toUserArray());

            // Preserve existing metadata and merge with new metadata
            $existingMetadata = $user->metadata ?? [];
            $newMetadata = $data->getMetadata();
            $metadata = array_merge($existingMetadata, $newMetadata);

            $user->metadata = $metadata;
            $user->save();

            // Sync career interests to pivot table (user_interests) if provided
            if (!empty($data->career_interests)) {
                $user->interests()->sync($data->career_interests);
            }

            DB::commit();

            return CollegeAthleteDetailsData::fromModel($user->load('interests'));
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
