<?php

namespace App\Services;

use App\Models\Award;
use App\Models\Tag;
use App\Models\User;
use App\Models\Winner;
use App\Repositories\AwardRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use App\Enums\WinnerVerificationState;

class AwardService
{
    protected AwardRepository $awardRepository;

    /**
     * Create a new service instance.
     *
     * @param AwardRepository $awardRepository
     */
    public function __construct(AwardRepository $awardRepository)
    {
        $this->awardRepository = $awardRepository;
    }

    /**
     * Get a paginated list of awards with optional filtering.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAwards(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        return $this->awardRepository->getAwards($filters, $perPage);
    }

    /**
     * Get an award by ID.
     *
     * @param int $id
     * @return Award
     */
    public function getAwardById(int $id): Award
    {
        return $this->awardRepository->findById($id);
    }

    /**
     * Create a new award with validation.
     *
     * @param array $data
     * @return Award
     * @throws ValidationException
     */
    public function createAward(array $data): Award
    {
        // Validate data
        $this->validateAwardData($data);

        // Extract geographical data for duplicate check
        $geoData = $this->extractGeographicalData($data);

        // Check for duplicate
        if ($this->awardRepository->isDuplicate(
            $data['name'],
            $data['year'],
            $data['type'],
            $geoData
        )) {
            throw ValidationException::withMessages([
                'name' => ['An award with this name already exists for the specified year.'],
            ]);
        }

        // Create award
        $award = $this->awardRepository->create($data);

        // Handle tags if present
        if (isset($data['tags']) && is_array($data['tags'])) {
            $this->syncAwardTags($award, $data['tags']);
        }

        return $award;
    }

    /**
     * Update an existing award with validation.
     *
     * @param int $id
     * @param array $data
     * @return Award
     * @throws ValidationException
     */
    public function updateAward(int $id, array $data): Award
    {
        // Get award
        $award = $this->awardRepository->findById($id);

        // Validate data
        $this->validateAwardData($data);

        // Extract geographical data for duplicate check
        $geoData = $this->extractGeographicalData($data);

        // Check for duplicate
        if (
            isset($data['name']) &&
            isset($data['year']) &&
            isset($data['type']) &&
            $this->awardRepository->isDuplicate(
                $data['name'],
                $data['year'],
                $data['type'],
                $geoData,
                $id
            )
        ) {
            throw ValidationException::withMessages([
                'name' => ['An award with this name already exists for the specified year.'],
            ]);
        }

        // Update award
        $award = $this->awardRepository->update($award, $data);

        // Handle tags if present
        if (isset($data['tags']) && is_array($data['tags'])) {
            $this->syncAwardTags($award, $data['tags']);
        }

        return $award;
    }

    /**
     * Delete an award.
     *
     * @param int $id
     * @return bool
     */
    public function deleteAward(int $id): bool
    {
        $award = $this->awardRepository->findById($id);
        return $this->awardRepository->delete($award);
    }

    /**
     * Activate an award.
     *
     * @param int $id
     * @return Award
     */
    public function activateAward(int $id): Award
    {
        $award = $this->awardRepository->findById($id);
        return $this->awardRepository->activate($award);
    }

    /**
     * Deactivate an award.
     *
     * @param int $id
     * @return Award
     */
    public function deactivateAward(int $id): Award
    {
        $award = $this->awardRepository->findById($id);
        return $this->awardRepository->deactivate($award);
    }

    /**
     * Duplicate an existing award.
     *
     * @param Award $award
     * @return Award
     * @throws ValidationException
     */
    public function duplicateAward(Award $award): Award
    {
        // Create a copy of the award data
        $awardData = $award->toArray();

        // Generate a unique name for the copy
        $baseName = "Copy of " . $awardData['name'];
        $copyName = $baseName;
        $counter = 1;

        // Extract geographical data for duplicate check
        $geoData = $this->extractGeographicalData($awardData);

        // Keep checking and incrementing counter until we find a non-duplicate name
        while ($this->awardRepository->isDuplicate(
            $copyName,
            $awardData['year'],
            $awardData['type'],
            $geoData
        )) {
            $counter++;
            $copyName = $baseName . " ({$counter})";
        }

        // Set the unique name
        $awardData['name'] = $copyName;

        // Remove fields that shouldn't be duplicated
        unset($awardData['id']);
        unset($awardData['created_at']);
        unset($awardData['updated_at']);
        unset($awardData['deleted_at']);

        // Create new award using existing create method
        return $this->createAward($awardData);
    }

    /**
     * Calculate the default award year based on the current date
     * - If before June 3rd: Use current year
     * - If after June 2nd: Use next year
     *
     * @return int The appropriate default year for a new award
     */
    public function getDefaultAwardYear(): int
    {
        $currentDate = now();
        $cutoffDate = \Carbon\Carbon::create($currentDate->year, 6, 3, 0, 0, 0);

        if ($currentDate->lt($cutoffDate)) {
            // Before June 3rd - use current year
            return $currentDate->year;
        } else {
            // June 3rd or later - use next year
            return $currentDate->year + 1;
        }
    }

    /**
     * Get awards for a specific user.
     *
     * @param User $user
     * @param array $filters
     * @return Collection
     */
    public function getAwardsForUser(User $user, array $filters = []): Collection
    {
        return $this->awardRepository->getAwardsForUser($user, $filters);
    }

    /**
     * Get awards by geographic criteria.
     *
     * @param array $geoFilters
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAwardsByGeography(array $geoFilters, array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $allFilters = array_merge($filters, $geoFilters);
        return $this->awardRepository->getAwards($allFilters, $perPage);
    }

    /**
     * Add a winner to an award.
     *
     * @param int $awardId
     * @param int $userId
     * @param array $winnerData
     * @param string|null $awardType Optional award type
     * @return Winner
     * @throws \Exception
     */
    public function addWinner(int $awardId, int $userId, array $winnerData = [], ?string $awardType = null): Winner
    {
        $award = $this->getAwardById($awardId);

        // Create winner record
        $winner = new Winner();
        $winner->user_id = $userId;
        $winner->award_id = $awardId;
        $winner->year = $award->year;
        $winner->is_winner = $winnerData['is_winner'] ?? true;
        $winner->is_finalist = $winnerData['is_finalist'] ?? true;
        $winner->verification_state = $winnerData['verification_state'] ?? WinnerVerificationState::PENDING_VERIFICATION->value;
        $winner->tshirt_size = $winnerData['tshirt_size'] ?? 'M';

        DB::beginTransaction();
        try {
            $winner->save();

            // If an award type is specified, update the award's type
            if ($awardType && $award) {
                $award->type = $awardType;
                $award->save();
            }

            DB::commit();
            return $winner;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Remove a winner from an award.
     *
     * @param int $awardId
     * @param int $winnerId
     * @return bool
     */
    public function removeWinner(int $awardId, int $winnerId): bool
    {
        $award = $this->getAwardById($awardId);
        $winner = $award->winners()->where('id', $winnerId)->first();

        if (!$winner) {
            throw new \Exception('Winner not found for this award.');
        }

        DB::beginTransaction();
        try {
            $result = $winner->delete();
            DB::commit();
            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Extract geographical data from award data for duplicate checking.
     *
     * @param array $data
     * @return array
     */
    protected function extractGeographicalData(array $data): array
    {
        $geoData = [];

        if (isset($data['region_id'])) {
            $geoData['region_id'] = $data['region_id'];
        }

        if (isset($data['market_id'])) {
            $geoData['market_id'] = $data['market_id'];
        }

        if (isset($data['subregion_id'])) {
            $geoData['subregion_id'] = $data['subregion_id'];
        }

        return $geoData;
    }

    /**
     * Sync tags for an award based on an array of tag names.
     *
     * @param Award $award
     * @param array $tagNames
     * @return void
     */
    protected function syncAwardTags(Award $award, array $tagNames): void
    {
        $tagIds = [];
        foreach ($tagNames as $tagName) {
            $tag = Tag::firstOrCreate(['name' => $tagName]);
            $tagIds[] = $tag->id;
        }

        $award->tags()->sync($tagIds);
    }

    /**
     * Validate award data.
     *
     * @param array $data
     * @throws ValidationException
     */
    protected function validateAwardData(array $data): void
    {
        $rules = [
            'name' => 'sometimes|required|string|max:255',
            'details' => 'sometimes|nullable|string',
            'notes' => 'sometimes|nullable|string',
            'type' => 'sometimes|required|string|in:regional,market,subregional',
            'region_id' => 'sometimes|required|exists:regions,id',
            'market_id' => 'sometimes|required|exists:markets,id',
            'state_id' => 'sometimes|required|exists:states,code',
            'subregion_id' => 'sometimes|nullable|exists:sub_regions,id',
            'year' => 'sometimes|required|integer|min:2023',
            'is_active' => 'sometimes|boolean',
            'tags' => 'sometimes|array',
            'tags.*' => 'string|max:50',
        ];

        Validator::make($data, $rules)->validate();
    }
}
