<?php

namespace App\Services\Organization;

use App\Data\Sponsor\OrganizationSearchResultDTO;
use App\Models\Organization;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class OrganizationSearchService
{
    /**
     * Search for organizations by name using the indexed search_vector column
     *
     * @param string $query The search query
     * @param int $limit Maximum number of results to return
     * @return Collection<OrganizationSearchResultDTO>
     */
    public function search(string $query, int $limit = 10): Collection
    {
        // Sanitize and prepare the query for plainto_tsquery
        $searchQuery = trim($query);

        // Use the indexed search_vector column with plainto_tsquery
        // Using 'simple' config as defined in the migration
        $organizations = Organization::query()
            ->whereRaw("search_vector @@ plainto_tsquery('simple', ?)", [$searchQuery])
            ->orderByRaw("ts_rank(search_vector, plainto_tsquery('simple', ?)) DESC", [$searchQuery]) // Rank results
            // Optionally include similarity sort as a secondary factor or fallback
            // ->orderByRaw('similarity(name, ?) DESC', [$searchQuery])
            ->limit($limit)
            ->get();

        // Fallback or broaden search if no FTS results (optional)
        // if ($organizations->isEmpty() && !empty($searchQuery)) {
        //     $organizations = Organization::query()
        //         ->where('name', 'ilike', "%{$searchQuery}%")
        //         ->orderByRaw('similarity(name, ?) DESC', [$searchQuery])
        //         ->limit($limit)
        //         ->get();
        // }

        return $organizations->map(fn (Organization $organization) => OrganizationSearchResultDTO::fromOrganization($organization));
    }

    /**
     * Check if an organization with a similar name already exists
     *
     * @param string $name The name to check
     * @param float $threshold The similarity threshold (0-1, higher is more similar)
     * @return Organization|null
     */
    public function findSimilarOrganization(string $name, float $threshold = 0.7): ?Organization
    {
        try {
            // Try to use similarity function if available
            return Organization::query()
                ->whereRaw('similarity(name, ?) > ?', [$name, $threshold])
                ->orderByRaw('similarity(name, ?) DESC', [$name])
                ->first();
        } catch (\Exception $e) {
            // Fallback to basic ILIKE if similarity function is not available
            return $this->findSimilarOrganizationFallback($name);
        }
    }

    /**
     * Fallback method to find similar organizations without using the similarity function
     * Uses standard Eloquent methods for string comparison
     *
     * @param string $name The organization name to check
     * @return Organization|null
     */
    protected function findSimilarOrganizationFallback(string $name): ?Organization
    {
        try {
            // Normalize the name for comparison
            $normalizedName = trim($name);

            // Try exact match first - using query builder safely
            $exactMatch = Organization::query()
                ->where('name', '=', $normalizedName)
                ->first();

            if ($exactMatch) {
                return $exactMatch;
            }

            // Then try close matches with case-insensitive comparison
            return Organization::query()
                ->where(function($query) use ($normalizedName) {
                    // Exact match (case insensitive)
                    $query->where('name', 'ilike', $normalizedName)
                        // Starts with pattern
                        ->orWhere('name', 'ilike', $normalizedName . '%')
                        // Contains pattern
                        ->orWhere('name', 'ilike', '%' . $normalizedName . '%');
                })
                ->first();
        } catch (\Exception $e) {
            // Log the error and return null if any part of the search fails
            \Illuminate\Support\Facades\Log::warning('Error in organization similarity search fallback', [
                'error' => $e->getMessage(),
                'name' => $name
            ]);
            return null;
        }
    }
}
