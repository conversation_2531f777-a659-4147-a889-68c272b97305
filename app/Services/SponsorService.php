<?php

namespace App\Services;

use App\Data\Sponsor\SponsorOrganizationData;
use App\Models\User;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class SponsorService
{
    /**
     * Get the active organization for the sponsor user
     *
     * @param User $user The sponsor user
     * @return SponsorOrganizationData
     * @throws ModelNotFoundException If no active organization is found
     */
    public function getActiveOrganizationForSponsor(User $user): SponsorOrganizationData
    {
        $organization = $user->organizations()
            ->withPivot(['role'])
            ->wherePivotNull('deactivated_at')
            ->firstOrFail();

        $role = $organization->pivot->role ?? null;
        return SponsorOrganizationData::fromOrganization($organization, $role);
    }

    /**
     * Get a specific organization for a sponsor user
     *
     * @param User $user The sponsor user
     * @param int $organizationId The organization ID
     * @return SponsorOrganizationData
     * @throws ModelNotFoundException If the organization is not found
     */
    public function getOrganizationForSponsor(User $user, int $organizationId): SponsorOrganizationData
    {
        $organization = $user->organizations()
            ->withPivot(['role'])
            ->wherePivotNull('deactivated_at')
            ->findOrFail($organizationId);

        $role = $organization->pivot->role ?? null;
        return SponsorOrganizationData::fromOrganization($organization, $role);
    }
}
