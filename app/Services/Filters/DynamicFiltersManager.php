<?php

namespace App\Services\Filters;

use Illuminate\Database\Eloquent\Builder;
use App\Models\FilterView;
use Illuminate\Support\Facades\Log;

class DynamicFiltersManager
{
    /**
     * @var DynamicQueryBuilder
     */
    protected $queryBuilder;

    /**
     * DynamicFiltersManager constructor.
     *
     * @param DynamicQueryBuilder $queryBuilder
     */
    public function __construct(DynamicQueryBuilder $queryBuilder)
    {
        $this->queryBuilder = $queryBuilder;
    }

    /**
     * Apply a filter view to the query.
     */
    public function applyFilterView(Builder $query, string $filterViewId): Builder
    {
        try {
            // Find the filter view
            $filterView = FilterView::query()->find($filterViewId);
            if (!$filterView) {
                Log::warning('Filter view not found', ['id' => $filterViewId]);
                return $query;
            }

            // Apply the filter definition
            $filterDefinition = $filterView->filter_definition;

            if (empty($filterDefinition)) {
                Log::warning('Filter definition is empty');
                return $query;
            }

            // Apply the filters
            $result = $this->queryBuilder->applyFilters($query, $filterDefinition);

            return $result;
        } catch (\Exception $e) {
            Log::error('Error applying filter view', [
                'id' => $filterViewId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $query;
        }
    }
}
