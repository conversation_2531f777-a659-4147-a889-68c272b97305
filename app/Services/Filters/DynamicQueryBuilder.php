<?php

namespace App\Services\Filters;

use App\Filters\Filter;
use App\Filters\Filterable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class DynamicQueryBuilder
{
    /**
     * Reformat the filter definition for compatibility
     *
     * This is needed to handle edge cases in filter definitions.
     *
     * @param array $filterGroups
     * @return array
     */
    protected function reformatFilterDefinition(array $filterGroups): array
    {
        // Always ensure we have a numerically indexed structure
        // If filter definition has the structure {conditions: [...], group_conjunctions: [...]}
        // Instead of {0: {conditions: [...]}, 1: {conditions: [...]}, group_conjunctions: [...]}
        if (isset($filterGroups['conditions']) && !isset($filterGroups[0]) && !isset($filterGroups['0'])) {
            // Create a proper grouped format with string keys
            $filterGroups = [
                '0' => [
                    'conditions' => $filterGroups['conditions'],
                    'conjunction' => $filterGroups['conjunction'] ?? 'and'
                ],
                'group_conjunctions' => $filterGroups['group_conjunctions'] ?? []
            ];
        }

        return $filterGroups;
    }

    /**
     * Apply filters to a query
     *
     * @param Builder $query
     * @param array $filterGroups
     * @return Builder
     */
    public function applyFilters(Builder $query, array $filterGroups): Builder
    {
        // Extract existing constraints from the original query
        $existingWheres = collect($query->getQuery()->wheres ?? []);
        $profileTypeConstraint = $existingWheres->first(function($where) {
            return ($where['column'] ?? '') === 'profile_type';
        });

        // Use clone to ensure we don't modify the original query
        $queryClone = clone $query;

        // We need to keep track of the original "from" component to handle edge cases
        $originalFrom = $queryClone->getQuery()->from;

        // Use reformatFilterDefinition to ensure consistent structure
        $filterGroups = $this->reformatFilterDefinition($filterGroups);

        // Extract group conjunctions (and/or between groups)
        $groupConjunctions = $filterGroups['group_conjunctions'] ?? [];
        unset($filterGroups['group_conjunctions']);

        // Create the filtered query
        $filteredQuery = $this->applyFilterGroups($queryClone, $filterGroups, $groupConjunctions);

        // Ensure the original query's table is maintained
        $filteredQuery->from($originalFrom);

        // Re-apply the profile_type constraint if it existed in the original query
        if ($profileTypeConstraint) {
            $operator = $profileTypeConstraint['operator'];
            $value = $profileTypeConstraint['value'];

            // Check if the filtered query already has this constraint
            $newQueryWheres = collect($filteredQuery->getQuery()->wheres ?? []);
            $hasProfileTypeConstraint = $newQueryWheres->contains(function($where) {
                return ($where['column'] ?? '') === 'profile_type';
            });

            if (!$hasProfileTypeConstraint) {
                $filteredQuery->where('profile_type', $operator, $value);
            }
        }

        return $filteredQuery;
    }

    /**
     * Extract group conjunctions from the filter definition
     *
     * @param array $filterGroups
     * @return array
     */
    protected function extractGroupConjunctions(array &$filterGroups): array
    {
        $groupConjunctions = $filterGroups['group_conjunctions'] ?? [];
        unset($filterGroups['group_conjunctions']);
        return $groupConjunctions;
    }

    /**
     * Apply filter groups to a query
     *
     * @param Builder $query
     * @param array $filterGroups
     * @param array $groupConjunctions
     * @return Builder
     */
    protected function applyFilterGroups(Builder $query, array $filterGroups, array $groupConjunctions): Builder
    {
        return $query->where(function (Builder $outerQuery) use ($filterGroups, $groupConjunctions) {
            $this->processFilterGroups($outerQuery, $filterGroups, $groupConjunctions);
        });
    }

    /**
     * Process each filter group
     *
     * @param Builder $outerQuery
     * @param array $filterGroups
     * @param array $groupConjunctions
     * @return void
     */
    protected function processFilterGroups(Builder $outerQuery, array $filterGroups, array $groupConjunctions): void
    {
        // If we have no groups, return early
        if (empty($filterGroups)) {
            return;
        }

        // Start with a where closure for the first group
        $outerQuery->where(function ($q) use ($filterGroups, $groupConjunctions) {
            // Process each filter group
            foreach ($filterGroups as $groupIndex => $group) {
                // Skip the group conjunctions entry
                if ($groupIndex === 'group_conjunctions') {
                    continue;
                }

                // Get the conjunction with the previous group (if any)
                $groupConjunction = null;
                if ($groupIndex > 0 && isset($groupConjunctions[$groupIndex - 1])) {
                    $groupConjunction = $groupConjunctions[$groupIndex - 1];
                }

                // Apply the group with the appropriate conjunction
                $method = $this->determineGroupConjunctionMethod($groupIndex, $groupConjunctions);
                $this->processGroupConditions($q, $method, $group);
            }
        });
    }

    /**
     * Check if a filter group is valid
     *
     * @param int|string $groupIndex
     * @param array $group
     * @return bool
     */
    protected function isValidFilterGroup($groupIndex, array $group): bool
    {
        // Skip non-numeric keys except for '0', '1', etc. which are valid string representations of numeric indices
        if ($groupIndex === 'group_conjunctions' || ($groupIndex !== '0' && !is_numeric($groupIndex))) {
            return false;
        }

        // Group must have conditions
        return !empty($group['conditions'] ?? []);
    }

    /**
     * Determine the method to use for group conjunction (where or orWhere)
     *
     * @param int $groupIndex
     * @param array $groupConjunctions
     * @return string
     */
    protected function determineGroupConjunctionMethod(int $groupIndex, array $groupConjunctions): string
    {
        if ($groupIndex === 0) {
            return 'where';
        }

        $groupConjunction = $groupConjunctions[$groupIndex - 1] ?? 'and';
        return strtolower($groupConjunction) === 'or' ? 'orWhere' : 'where';
    }

    /**
     * Process conditions within a group
     *
     * @param Builder $outerQuery
     * @param string $method
     * @param array $group
     * @return void
     */
    protected function processGroupConditions(Builder $outerQuery, string $method, array $group): void
    {
        $outerQuery->$method(function (Builder $subQuery) use ($group) {
            if (count($group['conditions']) === 1) {
                $this->applySingleCondition($subQuery, $group['conditions'][0]);
                return;
            }

            $this->applyMultipleConditions($subQuery, $group['conditions']);
        });
    }

    /**
     * Apply a single condition
     *
     * @param Builder $query
     * @param array $condition
     * @return void
     */
    protected function applySingleCondition(Builder $query, array $condition): void
    {
        $this->applyWhereCondition($query, 'where', $condition);
    }

    /**
     * Apply multiple conditions with proper conjunctions
     *
     * @param Builder $query
     * @param array $conditions
     * @return void
     */
    protected function applyMultipleConditions(Builder $query, array $conditions): void
    {
        $query->where(function (Builder $nestedQuery) use ($conditions) {
            // Apply first condition directly - always starts with WHERE
            $this->applyWhereCondition($nestedQuery, 'where', $conditions[0]);

            // Group remaining conditions by their conjunction type
            $groupedConditions = $this->groupConditionsByConjunction(array_slice($conditions, 1));

            // Process all AND conditions first (if any)
            $this->applyAndConditions($nestedQuery, $groupedConditions['and'] ?? []);

            // Process all OR conditions next (if any)
            $this->applyOrConditions($nestedQuery, $groupedConditions['or'] ?? []);
        });
    }

    /**
     * Group conditions by their conjunction
     *
     * @param array $conditions
     * @return array
     */
    protected function groupConditionsByConjunction(array $conditions): array
    {
        $grouped = [];

        foreach ($conditions as $i => $condition) {
            $conjunction = strtolower($condition['conjunction'] ?? 'and');

            if (!isset($grouped[$conjunction])) {
                $grouped[$conjunction] = [];
            }

            $grouped[$conjunction][] = [
                'index' => $i + 1, // +1 because we're processing from the second condition
                'condition' => $condition
            ];
        }

        return $grouped;
    }

    /**
     * Apply all AND conditions
     *
     * @param Builder $query
     * @param array $andConditions
     * @return void
     */
    protected function applyAndConditions(Builder $query, array $andConditions): void
    {
        foreach ($andConditions as $item) {
            $condition = $item['condition'];
            $cleanCondition = $this->removeConjunction($condition);
            $this->applyWhereCondition($query, 'where', $cleanCondition);
        }
    }

    /**
     * Apply OR conditions to the query
     *
     * @param Builder $query
     * @param array $orConditions
     * @return void
     */
    protected function applyOrConditions(Builder $query, array $orConditions): void
    {
        foreach ($orConditions as $item) {
            $condition = $item['condition'];
            $cleanCondition = $this->removeConjunction($condition);

            $field = $cleanCondition['field'];
            $operator = $cleanCondition['operator'];
            $value = $cleanCondition['value'];

            $this->applyOrConditionByOperator($query, $field, $operator, $value, $cleanCondition);
        }
    }

    /**
     * Apply an OR condition based on the operator
     *
     * @param Builder $query
     * @param string $field
     * @param string $operator
     * @param mixed $value
     * @param array $cleanCondition
     * @return void
     */
    protected function applyOrConditionByOperator(Builder $query, string $field, string $operator, $value, array $cleanCondition): void
    {
        // Check if this is a relationship field (contains a dot)
        if (strpos($field, '.') !== false) {
            // For relationship fields, use the standard applyWhereCondition with 'orWhere'
            $this->applyWhereCondition($query, 'orWhere', $cleanCondition);
            return;
        }

        // For direct fields (no relationships)
        switch ($operator) {
            case '=':
                $this->applyOrEqualCondition($query, $field, $value);
                break;
            case 'contains':
                $this->applyOrContainsCondition($query, $field, $value);
                break;
            case 'starts_with':
                $this->applyOrStartsWithCondition($query, $field, $value);
                break;
            default:
                // For other operators, use the standard applyWhereCondition
                $this->applyWhereCondition($query, 'orWhere', $cleanCondition);
                break;
        }
    }

    /**
     * Apply an OR equal condition
     *
     * @param Builder $query
     * @param string $field
     * @param mixed $value
     * @return void
     */
    protected function applyOrEqualCondition(Builder $query, string $field, $value): void
    {
        // Double-check for relationships (this should be caught earlier, but for defense in depth)
        if (strpos($field, '.') !== false) {
            $query->orWhere(function (Builder $subQuery) use ($field, $value) {
                $parts = explode('.', $field);
                $relationPath = $parts;
                $targetField = array_pop($relationPath);
                $this->applyNestedRelationshipCondition($subQuery, $relationPath, $targetField, '=', $value);
            });
            return;
        }

        if ($this->shouldUseCaseInsensitiveComparison($field, $value)) {
            $query->orWhereRaw("LOWER({$field}) = LOWER(?)", [$value]);
        } else {
            $query->orWhere($field, '=', $value);
        }
    }

    /**
     * Apply an OR contains condition
     *
     * @param Builder $query
     * @param string $field
     * @param mixed $value
     * @return void
     */
    protected function applyOrContainsCondition(Builder $query, string $field, $value): void
    {
        // Double-check for relationships (this should be caught earlier, but for defense in depth)
        if (strpos($field, '.') !== false) {
            $query->orWhere(function (Builder $subQuery) use ($field, $value) {
                $parts = explode('.', $field);
                $relationPath = $parts;
                $targetField = array_pop($relationPath);
                $this->applyNestedRelationshipCondition($subQuery, $relationPath, $targetField, 'contains', $value);
            });
            return;
        }

        if ($this->shouldUseCaseInsensitiveComparison($field, $value)) {
            $query->orWhereRaw("{$field} ILIKE ?", ["%{$value}%"]);
        } else {
            $query->orWhere($field, 'like', "%{$value}%");
        }
    }

    /**
     * Apply an OR starts_with condition
     *
     * @param Builder $query
     * @param string $field
     * @param mixed $value
     * @return void
     */
    protected function applyOrStartsWithCondition(Builder $query, string $field, $value): void
    {
        // Double-check for relationships (this should be caught earlier, but for defense in depth)
        if (strpos($field, '.') !== false) {
            $query->orWhere(function (Builder $subQuery) use ($field, $value) {
                $parts = explode('.', $field);
                $relationPath = $parts;
                $targetField = array_pop($relationPath);
                $this->applyNestedRelationshipCondition($subQuery, $relationPath, $targetField, 'starts_with', $value);
            });
            return;
        }

        if ($this->shouldUseCaseInsensitiveComparison($field, $value)) {
            $query->orWhereRaw("{$field} ILIKE ?", ["{$value}%"]);
        } else {
            $query->orWhere($field, 'like', "{$value}%");
        }
    }

    /**
     * Check if case-insensitive comparison should be used
     *
     * @param string $field
     * @param mixed $value
     * @return bool
     */
    protected function shouldUseCaseInsensitiveComparison(string $field, $value): bool
    {
        return DB::connection()->getDriverName() === 'pgsql'
            && !$this->isNumericField($field);
    }

    /**
     * Remove conjunction from a condition
     *
     * @param array $condition
     * @return array
     */
    protected function removeConjunction(array $condition): array
    {
        $cleanCondition = $condition;
        unset($cleanCondition['conjunction']);
        return $cleanCondition;
    }

    /**
     * Apply a where condition to the query
     *
     * @param Builder $query
     * @param string $whereMethod
     * @param array $condition
     * @return void
     */
    protected function applyWhereCondition(Builder $query, string $whereMethod, array $condition): void
    {
        $field = $condition['field'];
        $operator = $condition['operator'];
        $value = $condition['value'];

        // Check if this is a relationship field (contains a dot)
        if (strpos($field, '.') !== false) {
            $parts = explode('.', $field);
            $relationPath = $parts;
            $targetField = array_pop($relationPath);

            $query->$whereMethod(function (Builder $subQuery) use ($relationPath, $targetField, $operator, $value) {
                // Handle nested relationships (county.state.code)
                $this->applyNestedRelationshipCondition($subQuery, $relationPath, $targetField, $operator, $value);
            });
        } else {
            $this->applyDirectWhereCondition($query, $whereMethod, $field, $operator, $value);
        }
    }

    /**
     * Apply a condition for nested relationships
     *
     * @param Builder $query
     * @param array $relationPath
     * @param string $targetField
     * @param string $operator
     * @param mixed $value
     * @return void
     */
    protected function applyNestedRelationshipCondition(Builder $query, array $relationPath, string $targetField, string $operator, mixed $value): void
    {
        $relation = array_shift($relationPath);

        if (empty($relationPath)) {
            // Simple relationship (no nesting)
            $query->whereHas($relation, function (Builder $subQuery) use ($targetField, $operator, $value) {
                $this->applyDirectWhereCondition($subQuery, 'where', $targetField, $operator, $value);
            });
        } else {
            // Nested relationship (county.state.code) - handle recursively
            $query->whereHas($relation, function (Builder $subQuery) use ($relationPath, $targetField, $operator, $value) {
                $this->applyNestedRelationshipCondition($subQuery, $relationPath, $targetField, $operator, $value);
            });
        }
    }

    /**
     * Check if a field is of numeric type
     *
     * @param string $field
     * @return bool
     */
    protected function isNumericField(string $field): bool
    {
        // Common patterns for numeric fields
        if (in_array($field, ['graduation_year', 'id', 'gpa', 'height_in_inches', 'weight', 'class_rank'])) {
            return true;
        }

        // Fields ending with common numeric suffixes
        if (Str::endsWith($field, ['_id', '_count', '_year', '_rating', '_score', '_amount', '_price', '_number'])) {
            return true;
        }

        return false;
    }

    /**
     * Apply a direct where condition to a query
     *
     * @param Builder $query
     * @param string $method
     * @param string $field
     * @param string $operator
     * @param mixed $value
     * @return void
     */
    protected function applyDirectWhereCondition(Builder $query, string $method, string $field, string $operator, mixed $value): void
    {
        // Handle null operators
        if (in_array($operator, ['null', 'not_null'])) {
            $query->{$operator === 'null' ? 'whereNull' : 'whereNotNull'}($field);
            return;
        }

        // Determine if we're using PostgreSQL for case-insensitive comparisons
        $isPostgres = DB::connection()->getDriverName() === 'pgsql';
        $isNumeric = $this->isNumericField($field) || is_numeric($value);

        // Choose method based on PostgreSQL vs MySQL and operator
        switch ($operator) {
            case 'contains':
                if ($isPostgres && !$isNumeric) {
                    $rawField = $field;
                    $rawMethod = $method === 'where' ? 'whereRaw' : 'orWhereRaw';
                    $query->$rawMethod("$rawField ILIKE ?", ["%{$value}%"]);
                } else {
                    $query->$method($field, 'like', "%{$value}%");
                }
                break;
            case 'starts_with':
                if ($isPostgres && !$isNumeric) {
                    $rawField = $field;
                    $rawMethod = $method === 'where' ? 'whereRaw' : 'orWhereRaw';
                    $query->$rawMethod("$rawField ILIKE ?", ["{$value}%"]);
                } else {
                    $query->$method($field, 'like', "{$value}%");
                }
                break;
            case 'ends_with':
                if ($isPostgres && !$isNumeric) {
                    $rawField = $field;
                    $rawMethod = $method === 'where' ? 'whereRaw' : 'orWhereRaw';
                    $query->$rawMethod("$rawField ILIKE ?", ["%{$value}"]);
                } else {
                    $query->$method($field, 'like', "%{$value}");
                }
                break;
            case 'in':
                if ($method === 'where') {
                    $query->whereIn($field, is_array($value) ? $value : [$value]);
                } else {
                    $query->orWhereIn($field, is_array($value) ? $value : [$value]);
                }
                break;
            case 'not_in':
                if ($method === 'where') {
                    $query->whereNotIn($field, is_array($value) ? $value : [$value]);
                } else {
                    $query->orWhereNotIn($field, is_array($value) ? $value : [$value]);
                }
                break;
            case 'between':
                if (is_array($value) && count($value) === 2) {
                    if ($method === 'where') {
                        $query->whereBetween($field, [$value[0], $value[1]]);
                    } else {
                        $query->orWhereBetween($field, [$value[0], $value[1]]);
                    }
                }
                break;
            default:
                $query->$method($field, $operator, $this->prepareValue($field, $operator, $value));
                break;
        }
    }

    /**
     * Apply a where clause with the correct operator
     *
     * @param Builder $query
     * @param string $field
     * @param string $operator
     * @param mixed $value
     * @return void
     */
    protected function applyWhereClause(Builder $query, string $field, string $operator, mixed $value): void
    {
        $this->applyDirectWhereCondition($query, 'where', $field, $operator, $value);
    }

    /**
     * Prepare value for database query based on expected type
     *
     * @param string $field
     * @param string $operator
     * @param mixed $value
     * @return mixed
     */
    protected function prepareValue(string $field, string $operator, mixed $value): mixed
    {
        // Handle date values
        if ($value && Str::endsWith($field, ['_at', '_date', 'date']) && !is_object($value)) {
            try {
                return Carbon::parse($value);
            } catch (\Exception $e) {
                // If not a valid date, return as is
                return $value;
            }
        }

        // Handle boolean conversions
        if (is_string($value) && in_array(strtolower($value), ['true', 'false', '1', '0', 'yes', 'no'])) {
            return filter_var($value, FILTER_VALIDATE_BOOLEAN);
        }

        return $value;
    }

    /**
     * Build a dynamic query using the model's filter definitions
     *
     * @param string $modelClass Model class that implements Filterable
     * @param array $filterGroups
     * @return Builder
     */
    public function buildQueryFromModel(string $modelClass, array $filterGroups): Builder
    {
        if (!in_array(Filterable::class, class_implements($modelClass))) {
            throw new \InvalidArgumentException("Model {$modelClass} must implement Filterable interface");
        }

        $query = $modelClass::query();
        return $this->applyFilters($query, $filterGroups);
    }

    /**
     * Apply filters and execute query
     *
     * @param Builder $query
     * @param array $filterGroups
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getFilteredResults(Builder $query, array $filterGroups): \Illuminate\Database\Eloquent\Collection
    {
        // Apply the filters
        $query = $this->applyFilters($query, $filterGroups);

        // Execute the query
        $result = $query->get();

        return $result;
    }

    /**
     * Process a single filter condition
     */
    protected function processCondition(Builder $query, string $method, array $condition): void
    {
        // Skip malformed conditions
        if (!isset($condition['field']) || !isset($condition['operator'])) {
            return;
        }

        $field = $condition['field'];
        $operator = $condition['operator'];
        $value = $condition['value'] ?? null;

        // Handle null conditions specially (in_array check is faster than regex for small fixed sets)
        if (in_array($operator, ['null', 'not_null'])) {
        }
    }
}
