<?php

namespace App\Services;

use App\Data\Resume\ResumeSectionData;
use App\Data\Resume\UpdateResumeSectionData;
use App\Data\Resume\WorkExperienceData;
use App\Data\Resume\ProfileSectionData;
use App\Data\Resume\AcademicData;
use App\Models\ResumeSection;

class ResumeSectionService
{
    /**
     * Create a new resume section.
     */
    public function create(ResumeSectionData $data): ResumeSection
    {
        return ResumeSection::query()->create($data->toArray());
    }

    /**
     * Update an existing resume section.
     */
    public function update(ResumeSection $section, UpdateResumeSectionData $data): ResumeSection
    {
        $section->update([
            'content' => $data->content,
            'is_enabled' => $data->is_enabled ?? $section->is_enabled,
        ]);

        return $section->fresh();
    }

    /**
     * Delete a resume section.
     */
    public function delete(ResumeSection $section): bool
    {
        return $section->delete();
    }

    /**
     * Transform content based on section type using appropriate DTO.
     */
    protected function transformContent(string $type, mixed $content): array
    {
        return match($type) {
            'work' => WorkExperienceData::from($content)->toArray(),
            'profile' => ProfileSectionData::from($content)->toArray(),
            'academic' => AcademicData::from($content)->toArray(),
            default => $content,
        };
    }
}

