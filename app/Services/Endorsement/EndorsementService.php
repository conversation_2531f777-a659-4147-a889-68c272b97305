<?php

namespace App\Services\Endorsement;

use App\Data\Endorsement\EndorsementData;
use App\Data\Endorsement\EndorsementSummaryData;
use App\Data\Endorsement\PublicEndorsementRequest;
use App\Data\Endorsement\PublicEndorsementResponse;
use App\Data\Endorsement\UserEndorsementData;
use App\Models\Endorsement;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Spatie\LaravelData\DataCollection;

class EndorsementService
{
    /**
     * Get all endorsement categories
     */
    public function getAllEndorsements(): DataCollection
    {
        $endorsements = Endorsement::query()->get();

        $endorsementData = $endorsements->map(function (Endorsement $endorsement) {
            return EndorsementData::fromEndorsement($endorsement);
        });

        return new DataCollection(EndorsementData::class, $endorsementData->all());
    }

    /**
     * Get endorsements for a user, grouped by category with counts
     */
    public function getUserEndorsements(User $user): DataCollection
    {
        // Get all endorsements with their pivot data for this user
        $userEndorsements = $user->endorsements()
            ->withPivot(['endorser_id', 'relation'])
            ->get();

        // Group endorsements by category
        $groupedEndorsements = $userEndorsements->groupBy('id');

        // Create summary data for each endorsement category
        $summaryData = [];
        foreach ($groupedEndorsements as $endorsementId => $endorsements) {
            $endorsement = $endorsements->first();

            // Create UserEndorsementData for each endorser
            $endorsers = $endorsements->map(function ($item) {
                return UserEndorsementData::fromPivot($item->pivot);
            });

            // Create summary data for this category
            $summaryData[] = new EndorsementSummaryData(
                id: $endorsementId,
                name: $endorsement->name,
                icon: $endorsement->icon,
                count: $endorsers->count(),
                endorsers: new DataCollection(UserEndorsementData::class, $endorsers->all()),
            );
        }

        // Sort by count (descending)
        usort($summaryData, function ($a, $b) {
            return $b->count <=> $a->count;
        });

        return new DataCollection(EndorsementSummaryData::class, $summaryData);
    }

    /**
     * Create a new endorsement for a user
     */
    public function createEndorsement(User $user, int $endorsementId, int $endorserId, string $relation): bool
    {
        try {
            // Check if the endorsement already exists
            $exists = DB::table('user_endorsement')
                ->where('user_id', $user->id)
                ->where('endorsement_id', $endorsementId)
                ->where('endorser_id', $endorserId)
                ->exists();

            if ($exists) {
                return false;
            }

            // Create the endorsement
            $user->endorsements()->attach($endorsementId, [
                'endorser_id' => $endorserId,
                'relation' => $relation,
            ]);

            return true;
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error creating endorsement: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Handle public endorsements from potentially unauthenticated users
     */
    public function handlePublicEndorsements(PublicEndorsementRequest $request): PublicEndorsementResponse
    {
        try {
            // Find the user to endorse
            $user = User::query()->find($request->userId);
            if (!$user) {
                return new PublicEndorsementResponse(
                    success: false,
                    message: 'User not found'
                );
            }

            // Get endorser ID (if authenticated)
            $endorserId = Auth::id();

            // Store endorsements
            $successCount = 0;

            DB::beginTransaction();

            foreach ($request->endorsements as $endorsementId) {
                // Verify the endorsement ID exists
                if (!Endorsement::query()->where('id', $endorsementId)->exists()) {
                    Log::warning("Endorsement ID {$endorsementId} does not exist");
                    continue;
                }

                // Check if this endorsement already exists
                $exists = DB::table('user_endorsement')
                    ->where('user_id', $user->id)
                    ->where('endorsement_id', $endorsementId)
                    ->where(function ($query) use ($endorserId) {
                        if ($endorserId) {
                            $query->where('endorser_id', $endorserId);
                        } else {
                            $query->whereNull('endorser_id');
                        }
                    })
                    ->exists();

                if ($exists) {
                    Log::info("Endorsement already exists: user_id={$user->id}, endorsement_id={$endorsementId}, endorser_id=" . ($endorserId ?? 'null'));
                    continue;
                }

                // Create the endorsement
                $user->endorsements()->attach($endorsementId, [
                    'endorser_id' => $endorserId,
                    'relation' => $request->relation,
                ]);

                $successCount++;
            }

            DB::commit();

            if ($successCount === 0) {
                return new PublicEndorsementResponse(
                    success: false,
                    message: 'No new endorsements were added',
                    endorsementCount: 0
                );
            }

            return new PublicEndorsementResponse(
                success: true,
                message: 'Endorsements submitted successfully',
                endorsementCount: $successCount
            );
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating public endorsements: ' . $e->getMessage());
            return new PublicEndorsementResponse(
                success: false,
                message: 'Failed to submit endorsements'
            );
        }
    }

    /**
     * Remove an endorsement from a user
     */
    public function removeEndorsement(User $user, int $endorsementId, int $endorserId): bool
    {
        try {
            // Remove the endorsement
            $user->endorsements()->wherePivot('endorsement_id', $endorsementId)
                ->wherePivot('endorser_id', $endorserId)
                ->detach();

            return true;
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error removing endorsement: ' . $e->getMessage());
            return false;
        }
    }
}
