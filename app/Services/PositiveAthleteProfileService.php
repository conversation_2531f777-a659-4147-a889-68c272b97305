<?php

namespace App\Services;

use App\Data\Profile\ProfileDetailsData;
use App\Data\Profile\CommunityInvolvementData;
use App\Data\Profile\WorkExperienceData;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use App\Data\Profile\Sports\SportsData;
use App\Data\Profile\Sports\SportResponse;
use App\Models\Sport;

class PositiveAthleteProfileService
{
    /**
     * Get user's profile details
     */
    public function getDetails(User $user): ProfileDetailsData
    {
        return ProfileDetailsData::fromModel($user->load(['school', 'interests']));
    }

    /**
     * Update user's profile details
     */
    public function updateDetails(User $user, ProfileDetailsData $data): ProfileDetailsData
    {
        DB::beginTransaction();

        try {
            $user->update($data->toArray());

            // Sync interests if provided
            if (isset($data->interest_ids)) {
                $user->interests()->sync($data->interest_ids);
            }

            DB::commit();

            return ProfileDetailsData::fromModel($user->load('interests'));
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get user's community involvements
     */
    public function getInvolvements(User $user): array
    {
        return CommunityInvolvementData::fromCollection(
            $user->communityInvolvements()->orderBy('order')->get()
        );
    }

    /**
     * Update user's community involvements
     */
    public function updateInvolvements(User $user, array $involvements): array
    {
        DB::beginTransaction();

        try {
            // Delete existing involvements
            $user->communityInvolvements()->delete();

            // Create new involvements with order
            foreach ($involvements as $index => $involvement) {
                $user->communityInvolvements()->create([
                    'title' => $involvement->title,
                    'date_range' => $involvement->date_range,
                    'description' => $involvement->description,
                    'order' => $index,
                ]);
            }

            DB::commit();

            return $this->getInvolvements($user);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get user's work experiences
     */
    public function getWorkExperiences(User $user): array
    {
        return WorkExperienceData::fromCollection(
            $user->workExperiences()->orderBy('order')->get()
        );
    }

    /**
     * Update user's work experiences
     */
    public function updateWorkExperiences(User $user, array $experiences): array
    {
        DB::beginTransaction();

        try {
            // Delete existing experiences
            $user->workExperiences()->delete();

            // Create new experiences with order
            foreach ($experiences as $index => $experience) {
                $user->workExperiences()->create([
                    'name' => $experience->name,
                    'date' => $experience->date,
                    'description' => $experience->description,
                    'order' => $index,
                ]);
            }

            DB::commit();

            return $this->getWorkExperiences($user);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get user's sports (both platform and custom)
     */
    public function getSports(User $user): SportsData
    {
        // Fetch platform sports with pivot data
        $platformSports = $user->sports()
            ->get()
            ->map(fn ($sport) => [
                'sport' => SportResponse::fromModel($sport),
                'order' => $sport->pivot->order,
            ]);

        // Fetch custom sports
        $customSports = $user->customSports()
            ->get()
            ->map(fn ($sport) => [
                'sport' => SportResponse::fromModel($sport, true),
                'order' => $sport->order,
            ]);

        // Combine and sort by order
        $sports = collect([...$platformSports, ...$customSports])
            ->sortBy('order')
            ->map(fn ($item) => $item['sport'])
            ->values()
            ->all();

        return SportsData::fromArray($sports);
    }

    /**
     * Update user's sports
     */
    public function updateSports(User $user, SportsData $data): SportsData
    {
        DB::beginTransaction();

        try {
            // Clear existing sports
            $user->sports()->detach();
            $user->customSports()->delete();

            // Add new sports
            foreach ($data->sports as $sport) {
                if ($sport->isCustom) {
                    if (!$sport->customName) {
                        throw new \InvalidArgumentException('Custom sports require a name');
                    }

                    $user->customSports()->create([
                        'name' => $sport->customName,
                        'order' => $sport->order,
                    ]);
                } else {
                    if (!$sport->id) {
                        throw new \InvalidArgumentException('Platform sports require an ID');
                    }

                    $user->sports()->attach($sport->id, [
                        'order' => $sport->order,
                    ]);
                }
            }

            DB::commit();

            return $this->getSports($user);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Search for sports (both platform and user's custom sports)
     */
    public function searchSports(User $user, string $query): array
    {
        // Search platform sports with case-insensitive query
        $platformSports = Sport::query()
            ->where('name', 'ilike', "%{$query}%")
            ->get()
            ->map(fn ($sport) => SportResponse::fromModel($sport));

        // Search user's custom sports with case-insensitive query
        $customSports = $user->customSports()
            ->where('name', 'ilike', "%{$query}%")
            ->get()
            ->map(fn ($sport) => SportResponse::fromModel($sport, true));

        return [
            'platform' => $platformSports,
            'custom' => $customSports,
        ];
    }
}
