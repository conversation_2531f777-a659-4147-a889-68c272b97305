<?php

namespace App\Services;

use App\Data\School\SchoolData;
use App\Http\Requests\School\SearchSchoolRequest;
use App\Models\School;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class SchoolService
{
    /**
     * Search for schools with optional filters
     *
     * @param SearchSchoolRequest $request
     * @return array<SchoolData>
     */
    public function searchSchools(SearchSchoolRequest $request): array
    {
        try {
            // If no query parameters were provided, just return empty results
            if (!$request->query && !$request->county_id && !$request->state_code) {
                return [];
            }

            $query = School::query()
                ->with(['county.state']);

            // Apply filters
            $query = $this->applyFilters($query, $request);

            // Try full-text search if query is provided
            if ($request->query) {
                $fullTextResults = (clone $query)->search($request->query)->limit(10)->get();

                // If full-text search returns results, use them
                if ($fullTextResults->isNotEmpty()) {
                    return SchoolData::collection($fullTextResults);
                }

                // Fall back to ILIKE search if full-text search returns no results
                $query->where('name', 'ilike', "%{$request->query}%");
            }

            $schools = $query->limit(10)->get();
            return SchoolData::collection($schools);
        } catch (\Exception $e) {
            Log::error('Error searching schools: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all(),
            ]);

            return [];
        }
    }

    /**
     * Apply county and state filters to the query
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param SearchSchoolRequest $request
     * @return \Illuminate\Database\Eloquent\Builder
     */
    private function applyFilters($query, SearchSchoolRequest $request)
    {
        // Filter by county if provided
        if ($request->county_id) {
            $query->where('county_id', $request->county_id);
        }

        // Filter by state if provided
        if ($request->state_code) {
            $query->whereHas('county', function ($q) use ($request) {
                $q->where('state_code', $request->state_code);
            });
        }

        return $query;
    }

    /**
     * Get a school by ID
     *
     * @param int $id
     * @return SchoolData|null
     */
    public function getSchoolById(int $id): ?SchoolData
    {
        $school = School::query()->with(['county.state'])->find($id);

        if (!$school) {
            return null;
        }

        return SchoolData::fromModel($school);
    }
}
