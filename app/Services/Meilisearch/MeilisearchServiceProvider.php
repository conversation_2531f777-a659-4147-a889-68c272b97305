<?php

namespace App\Services\Meilisearch;

use App\Services\Meilisearch\Facades\SearchCache;
use Illuminate\Cache\RedisStore;
use Illuminate\Support\ServiceProvider;

class MeilisearchServiceProvider extends ServiceProvider {
	public function register(): void {
		$this->app->singleton( MeilisearchClient::class, function () {
			return new MeilisearchClient();
		} );

		$this->app->singleton( SearchCache::class, function ( $app ) {
			$redisInstance = $app->get( 'redis' );

			return new RedisStore( $redisInstance, '', 'search' );
		} );
	}
}
