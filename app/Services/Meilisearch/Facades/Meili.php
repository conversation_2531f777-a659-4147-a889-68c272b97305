<?php

namespace App\Services\Meilisearch\Facades;

use App\Services\Meilisearch\MeilisearchClient;
use Illuminate\Support\Facades\Facade;

/**
 * Class Meili
 * @package App\Services\Meilisearch
 * @returns MeilisearchClient
 *
 * @see MeilisearchClient
 * @mixin MeilisearchClient
 */
class Meili extends Facade {
	public static function getFacadeAccessor(): string {
		return MeilisearchClient::class;
	}
}
