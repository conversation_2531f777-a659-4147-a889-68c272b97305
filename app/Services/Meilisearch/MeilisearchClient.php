<?php

namespace App\Services\Meilisearch;

use <PERSON><PERSON>\Scout\Searchable;
use <PERSON><PERSON>ear<PERSON>\Client;
use Mei<PERSON>earch\Endpoints\Indexes;

class MeilisearchClient {
	public function __construct(
		public Client|null $client = null,
	) {
		$this->client = new Client( config( 'scout.meilisearch.host' ), config( 'scout.meilisearch.key' ) );
	}

	public function client(): ?Client {
		return $this->client;
	}

	public function for( string $class ): ?Indexes {
		if ( ! in_array( Searchable::class, class_uses( $class ) ) ) {
			return null;
		}

		return $this->index( app( $class )->searchableAs() );
	}

	public function index( string $indexName ): Indexes {
		return $this->client->index( $indexName );
	}
}
