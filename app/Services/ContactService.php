<?php

namespace App\Services;

use App\Models\Contact;
use App\Models\User;
use App\Enums\ContactStatus;
use App\Enums\ContactType;
use App\Repositories\ContactRepository;
use Illuminate\Support\Facades\DB;

class ContactService
{
    public function __construct(
        protected ContactRepository $contactRepository,
        protected AthleticsDirectorProfileService $adProfileService
    ) {}

    /**
     * Create a new contact
     */
    public function create(array $data): Contact
    {
        return DB::transaction(function () use ($data) {
            $data['status'] = $data['status'] ?? ContactStatus::ACTIVE;
            $data['type'] = $data['type'] ?? ContactType::GENERAL;

            return $this->contactRepository->create($data);
        });
    }

    /**
     * Update an existing contact
     */
    public function update(Contact $contact, array $data): Contact
    {
        return DB::transaction(function () use ($contact, $data) {
            return $this->contactRepository->update($contact, $data);
        });
    }

    /**
     * Create a new athletics director contact
     */
    public function createAthleticsDirectorContact(array $data): Contact
    {
        return DB::transaction(function () use ($data) {
            // Create contact with AD type
            $contact = $this->contactRepository->create([
                ...$data,
                'type' => ContactType::ATHLETICS_DIRECTOR,
                'status' => ContactStatus::ACTIVE,
            ]);

            // Create AD profile
            $this->adProfileService->createForContact($contact);

            return $contact;
        });
    }

    /**
     * Update an athletics director contact
     */
    public function updateAthleticsDirectorContact(Contact $contact, array $data): Contact
    {
        return DB::transaction(function () use ($contact, $data) {
            // Update contact
            $contact = $this->contactRepository->update($contact, [
                ...$data,
                'type' => ContactType::ATHLETICS_DIRECTOR,
            ]);

            // Ensure AD profile exists
            if (!$contact->athleticsDirectorProfile) {
                $this->adProfileService->createForContact($contact);
            }

            return $contact;
        });
    }

    /**
     * Convert a contact to a user
     */
    public function convertToUser(Contact $contact, array $userData): User
    {
        return DB::transaction(function () use ($contact, $userData) {
            // Create user based on contact type
            $user = match($contact->type) {
                ContactType::ATHLETICS_DIRECTOR => app(UserService::class)->createAthleticsDirector([
                    ...$userData,
                    'first_name' => $contact->first_name,
                    'last_name' => $contact->last_name,
                    'email' => $contact->email,
                ]),
                default => throw new \InvalidArgumentException("Unsupported contact type for conversion: {$contact->type}")
            };

            // Update contact with user reference if needed
            // This might be needed for tracking or auditing purposes
            $contact->update([
                'metadata' => [
                    ...$contact->metadata ?? [],
                    'converted_to_user_id' => $user->id,
                    'converted_at' => now(),
                ],
            ]);

            return $user;
        });
    }
}
