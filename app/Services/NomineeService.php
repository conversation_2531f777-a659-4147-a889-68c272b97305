<?php

namespace App\Services;

use App\Models\Nomination;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;

class NomineeService
{
    /**
     * Generate a case-insensitive composite key for a nominee
     */
    public static function generateNomineeKey(string $email, string $firstName, string $lastName): string
    {
        return strtolower($email) . '|' . strtolower($firstName) . '|' . strtolower($lastName);
    }

    /**
     * Find all nominations for a specific nominee using the composite key
     */
    public static function findNominationsForNominee(string $email, string $firstName, string $lastName): Collection
    {
        return Nomination::query()
            ->whereRaw('LOWER(email) = ?', [strtolower($email)])
            ->whereRaw('LOWER(first_name) = ?', [strtolower($firstName)])
            ->whereRaw('LOWER(last_name) = ?', [strtolower($lastName)])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Check if a nominee already exists (case-insensitive)
     */
    public static function nomineeExists(string $email, string $firstName, string $lastName): bool
    {
        return Nomination::query()
            ->whereRaw('LOWER(email) = ?', [strtolower($email)])
            ->whereRaw('LOWER(first_name) = ?', [strtolower($firstName)])
            ->whereRaw('LOWER(last_name) = ?', [strtolower($lastName)])
            ->exists();
    }

    /**
     * Get aggregated nominee data using the PostgreSQL view
     * Falls back to subquery if view doesn't exist
     */
    public static function getNomineeAggregates(int $limit = 25, int $offset = 0): Collection
    {
        try {
            // Try using the nominees_view first
            return DB::table('nominees_view')
                ->orderBy('latest_nomination_date', 'desc')
                ->limit($limit)
                ->offset($offset)
                ->get();
        } catch (\Exception $e) {
            // Fallback to subquery if view doesn't exist
            return self::getNomineeAggregatesViaSubquery($limit, $offset);
        }
    }

    /**
     * Fallback method using subquery aggregation
     */
    private static function getNomineeAggregatesViaSubquery(int $limit = 25, int $offset = 0): Collection
    {
        return DB::table('nominations')
            ->select([
                DB::raw('CONCAT(LOWER(email), \'|\', LOWER(first_name), \'|\', LOWER(last_name)) as nominee_key'),
                'email',
                'first_name',
                'last_name',
                DB::raw('COUNT(*) as nomination_count'),
                DB::raw('MAX(CASE WHEN user_id IS NOT NULL THEN 1 ELSE 0 END) as is_linked_to_user'),
                DB::raw('MAX(created_at) as latest_nomination_date'),
                DB::raw('STRING_AGG(DISTINCT status, \',\') as statuses'),
                DB::raw('STRING_AGG(DISTINCT sport, \',\') as sports'),
                DB::raw('STRING_AGG(DISTINCT school_name, \',\') as school_names'),
            ])
            ->whereNotNull('email')
            ->whereNotNull('first_name')
            ->whereNotNull('last_name')
            ->groupBy(['email', 'first_name', 'last_name'])
            ->orderBy('latest_nomination_date', 'desc')
            ->limit($limit)
            ->offset($offset)
            ->get();
    }

    /**
     * Attempt to link a nominee to an existing user account
     */
    public static function linkNomineeToUser(string $email, string $firstName, string $lastName): ?User
    {
        // Try to find a user with matching email (case-insensitive)
        $user = User::query()
            ->whereRaw('LOWER(email) = ?', [strtolower($email)])
            ->first();

        if ($user) {
            // Update all nominations for this nominee to link to the user
            $nominations = self::findNominationsForNominee($email, $firstName, $lastName);

            foreach ($nominations as $nomination) {
                if (!$nomination->user_id) {
                    $nomination->update(['user_id' => $user->id]);
                }
            }

            return $user;
        }

        return null;
    }

    /**
     * Get duplicate nominees (same person with multiple slightly different entries)
     * This helps identify data quality issues
     */
    public static function findPotentialDuplicates(): Collection
    {
        // Find nominees with same email but different names
        $emailDuplicates = DB::table('nominations')
            ->select([
                'email',
                DB::raw('ARRAY_AGG(DISTINCT CONCAT(first_name, \' \', last_name)) as names'),
                DB::raw('COUNT(DISTINCT CONCAT(LOWER(first_name), \'|\', LOWER(last_name))) as name_variations'),
            ])
            ->whereNotNull('email')
            ->groupBy('email')
            ->having(DB::raw('COUNT(DISTINCT CONCAT(LOWER(first_name), \'|\', LOWER(last_name)))'), '>', 1)
            ->get();

        // Find nominees with same name but different emails (less reliable)
        $nameDuplicates = DB::table('nominations')
            ->select([
                DB::raw('CONCAT(first_name, \' \', last_name) as full_name'),
                DB::raw('ARRAY_AGG(DISTINCT email) as emails'),
                DB::raw('COUNT(DISTINCT LOWER(email)) as email_variations'),
            ])
            ->whereNotNull('first_name')
            ->whereNotNull('last_name')
            ->groupBy(['first_name', 'last_name'])
            ->having(DB::raw('COUNT(DISTINCT LOWER(email))'), '>', 1)
            ->get();

        return collect([
            'email_duplicates' => $emailDuplicates,
            'name_duplicates' => $nameDuplicates,
        ]);
    }

    /**
     * Get nominee statistics
     */
    public static function getNomineeStatistics(): array
    {
        $stats = DB::table('nominations')
            ->selectRaw('
                COUNT(DISTINCT CONCAT(LOWER(email), \'|\', LOWER(first_name), \'|\', LOWER(last_name))) as total_unique_nominees,
                COUNT(*) as total_nominations,
                COUNT(DISTINCT CASE WHEN user_id IS NOT NULL THEN CONCAT(LOWER(email), \'|\', LOWER(first_name), \'|\', LOWER(last_name)) END) as linked_nominees,
                AVG(CASE WHEN user_id IS NOT NULL THEN 1.0 ELSE 0.0 END) * 100 as link_percentage
            ')
            ->first();

        return [
            'total_unique_nominees' => $stats->total_unique_nominees ?? 0,
            'total_nominations' => $stats->total_nominations ?? 0,
            'linked_nominees' => $stats->linked_nominees ?? 0,
            'unlinked_nominees' => ($stats->total_unique_nominees ?? 0) - ($stats->linked_nominees ?? 0),
            'link_percentage' => round($stats->link_percentage ?? 0, 2),
            'avg_nominations_per_nominee' => $stats->total_nominations && $stats->total_unique_nominees
                ? round($stats->total_nominations / $stats->total_unique_nominees, 2)
                : 0,
        ];
    }

    /**
     * Bulk link nominees to users based on email matching using mass update operations
     * This method efficiently handles large datasets without N+1 query issues
     */
    public static function bulkLinkNomineesToUsers(): array
    {
        // Use a single mass update query with a JOIN to link nominations to users
        // This eliminates the N+1 problem by doing everything in one operation
        $linkedCount = DB::table('nominations')
            ->join('users', function ($join) {
                $join->whereRaw('LOWER(nominations.email) = LOWER(users.email)');
            })
            ->whereNull('nominations.user_id')
            ->whereNotNull('nominations.email')
            ->whereNotNull('nominations.first_name')
            ->whereNotNull('nominations.last_name')
            ->update(['nominations.user_id' => DB::raw('users.id')]);

        // Count total unlinked nominees after the update
        $remainingUnlinked = DB::table('nominations')
            ->whereNull('user_id')
            ->whereNotNull('email')
            ->whereNotNull('first_name')
            ->whereNotNull('last_name')
            ->count();

        // Calculate statistics
        $totalNominations = DB::table('nominations')
            ->whereNotNull('email')
            ->whereNotNull('first_name')
            ->whereNotNull('last_name')
            ->count();

        $totalLinked = DB::table('nominations')
            ->whereNotNull('user_id')
            ->whereNotNull('email')
            ->whereNotNull('first_name')
            ->whereNotNull('last_name')
            ->count();

        return [
            'linked' => $linkedCount,
            'already_linked' => $totalLinked - $linkedCount,
            'no_user_found' => $remainingUnlinked,
            'total_processed' => $totalNominations,
        ];
    }
}
