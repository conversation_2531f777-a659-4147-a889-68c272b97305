<?php

namespace App\Services;

use App\Enums\ProfileType;
use App\Models\Message;
use App\Repositories\MessageRepository;
use App\Services\ContentModeration\GoogleNaturalLanguageService;
use Exception;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Log;

class MessageService
{
    /**
     * @var MessageRepository
     */
    protected $messageRepository;

    /**
     * @var NetworkingService
     */
    protected $networkingService;

    /**
     * @var GoogleNaturalLanguageService
     */
    protected $contentModerationService;

    /**
     * Create a new service instance.
     *
     * @param MessageRepository $messageRepository
     * @param NetworkingService $networkingService
     * @param GoogleNaturalLanguageService $contentModerationService
     */
    public function __construct(
        MessageRepository $messageRepository,
        NetworkingService $networkingService,
        GoogleNaturalLanguageService $contentModerationService
    ) {
        $this->messageRepository = $messageRepository;
        $this->networkingService = $networkingService;
        $this->contentModerationService = $contentModerationService;
    }

    /**
     * Send a message.
     *
     * @param int $senderId
     * @param int $recipientId
     * @param string|null $content
     * @param array|null $media
     * @return Message
     * @throws Exception
     */
    public function sendMessage(int $senderId, int $recipientId, ?string $content, ?array $media = null): Message
    {
        // Check if either user has blocked the other
        if ($this->networkingService->isUserBlocked($senderId, $recipientId)) {
            throw new Exception('Cannot send message due to blocking restrictions');
        }

        // Verify users can connect based on profile types (includes sponsor exception)
        if (!$this->networkingService->canConnect($senderId, $recipientId)) {
            throw new Exception('Cannot send message due to connection restrictions');
        }

        // Get existing connection if any
        $connection = $this->networkingService->getConnection($senderId, $recipientId);

        // Check if the connection was rejected - if so, can't message
        if ($connection && $connection->status === \App\Enums\ConnectionStatus::REJECTED->value) {
            throw new Exception('Cannot send message due to rejected connection');
        }

        // Check if users are connected
        $isConnected = $this->networkingService->areUsersConnected($senderId, $recipientId);
        $isPending = $this->networkingService->isConnectionPending($senderId, $recipientId);

        // If not connected and not pending, create a connection request
        if (!$isConnected && !$isPending) {
            $this->networkingService->createConnectionRequest($senderId, $recipientId);
        }

        // If there's a pending connection, apply message restrictions
        if ($isPending) {
            if (!$connection) {
                $connection = $this->networkingService->getConnection($senderId, $recipientId);
            }

            // Case 1: If sender is NOT the requester, they can't send messages until connection is accepted
            if ($connection && $connection->requester_id !== $senderId) {
                throw new Exception('Cannot send message until connection request is accepted');
            }

            // Case 2: If sender IS the requester, check if they've already sent a message
            if ($connection && $connection->requester_id === $senderId) {
                $existingMessageCount = $this->messageRepository->countMessagesBetweenUsers($senderId, $recipientId);

                if ($existingMessageCount > 0) {
                    throw new Exception('Cannot send more messages until connection request is accepted');
                }
            }
        }

        // Prepare message data
        $messageData = [
            'sender_id' => $senderId,
            'recipient_id' => $recipientId,
            'content' => $content,
            'is_flagged' => false,
            'moderation_result' => null
        ];

        // Moderate content if not empty
        if (!empty($content)) {
            try {
                $moderationResult = $this->contentModerationService->moderateContent($content);
                $messageData['is_flagged'] = $moderationResult['is_flagged'];
                $messageData['moderation_result'] = $moderationResult['moderation_result'];

                // Log moderation result
                Log::info('Message content moderation', [
                    'sender_id' => $senderId,
                    'recipient_id' => $recipientId,
                    'is_flagged' => $moderationResult['is_flagged']
                ]);
            } catch (Exception $e) {
                // Log error but continue with message creation
                Log::error('Content moderation failed', [
                    'error' => $e->getMessage(),
                    'sender_id' => $senderId,
                    'recipient_id' => $recipientId
                ]);
            }
        }

        return $this->messageRepository->createMessage($messageData, $media);
    }

    /**
     * Edit an existing message.
     *
     * @param int $messageId
     * @param int $userId
     * @param string $content
     * @param array|null $media
     * @return Message|null
     * @throws Exception
     */
    public function editMessage(int $messageId, int $userId, string $content, ?array $media = null): ?Message
    {
        // Prepare message data
        $messageData = [
            'content' => $content,
            'edited_at' => now(),
            'is_flagged' => false,
            'moderation_result' => null
        ];

        // Moderate content if not empty
        if (!empty($content)) {
            try {
                $moderationResult = $this->contentModerationService->moderateContent($content);
                $messageData['is_flagged'] = $moderationResult['is_flagged'];
                $messageData['moderation_result'] = $moderationResult['moderation_result'];

                // Log moderation result
                Log::info('Edited message content moderation', [
                    'user_id' => $userId,
                    'message_id' => $messageId,
                    'is_flagged' => $moderationResult['is_flagged']
                ]);
            } catch (Exception $e) {
                // Log error but continue with message update
                Log::error('Content moderation failed for edited message', [
                    'error' => $e->getMessage(),
                    'user_id' => $userId,
                    'message_id' => $messageId
                ]);
            }
        }

        // Update the message
        $message = $this->messageRepository->updateMessage($messageId, $userId, $messageData);

        // If message was not found or not owned by the user
        if (!$message) {
            throw new Exception('Message not found or you do not have permission to edit it');
        }

        // Handle media update if needed (not implemented in this initial version)
        // This would need to be implemented if we want to support updating media attachments

        return $message;
    }

    /**
     * Resolve the appropriate user ID to use for message operations
     * If the user is a parent, returns their child's ID
     * Otherwise returns the original user ID
     *
     * @param int $userId
     * @return int The user ID to use (either the same or the child's ID)
     */
    public function resolveEffectiveUserId(int $userId): int
    {
        // Get the user with their profile type
        $user = \App\Models\User::query()->with('children')->find($userId);

        // If not a parent or not found, return the original ID
        if (!$user || !$user->isParent()) {
            return $userId;
        }

        // Get the first high school student child (if any)
        $highSchoolChild = $user->children()
            ->where('life_stage', \App\Enums\LifeStage::HIGH_SCHOOL_STUDENT)
            ->first();

        // Return the child's ID if found, otherwise return the original ID
        return $highSchoolChild ? $highSchoolChild->id : $userId;
    }

    /**
     * Get conversation between two users.
     *
     * @param int $userId
     * @param int $otherUserId
     * @param int $perPage
     * @return LengthAwarePaginator
     * @throws Exception
     */
    public function getConversation(int $userId, int $otherUserId, int $perPage = 15): LengthAwarePaginator
    {
        // For parent users, resolve to their child's ID for viewing conversations
        $effectiveUserId = $this->resolveEffectiveUserId($userId);

        // Check if either user has blocked the other
        if ($this->networkingService->isUserBlocked($effectiveUserId, $otherUserId)) {
            throw new Exception('Cannot view conversation due to blocking restrictions');
        }

        // Get conversation with organization data for Sponsor users
        return $this->messageRepository->getConversation($effectiveUserId, $otherUserId, $perPage);
    }

    /**
     * Get all conversations for a user.
     *
     * @param int $userId
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getConversations(int $userId, int $perPage = 15): LengthAwarePaginator
    {
        try {
            // Get the user with profile type to check if they are a sponsor or parent
            $user = \App\Models\User::query()->with('activeOrganization', 'children')->find($userId);

            // For parent users, resolve the effective user ID (their child)
            $effectiveUserId = $userId;
            if ($user && $user->isParent()) {
                $highSchoolChild = $user->children()
                    ->where('life_stage', \App\Enums\LifeStage::HIGH_SCHOOL_STUDENT)
                    ->first();

                if ($highSchoolChild) {
                    $effectiveUserId = $highSchoolChild->id;
                }
            }

            $isSponsor = $user && $user->isSponsor();
            $organizationId = $isSponsor && $user->activeOrganization->count() > 0 ? $user->activeOrganization->first()->id : null;

            // Use a match expression to determine which conversations to retrieve
            $conversations = match (true) {
                $isSponsor && $organizationId !== null => $this->messageRepository->getSponsorConversations($userId, $organizationId, $perPage),
                default => $this->messageRepository->getConversations($effectiveUserId, $perPage),
            };

            // If there are no conversations, return the empty paginator
            if (count($conversations->items()) === 0) {
                return $conversations;
            }

            // Filter out conversations with blocked users
            $items = collect($conversations->items())->filter(function ($conversation) {
                // Check if the connection_blocked_at property exists and is not null
                $isBlocked = property_exists($conversation, 'connection_blocked_at') && $conversation->connection_blocked_at !== null;
                return !$isBlocked;
            });

            // If all conversations were filtered out, return an empty paginator
            if ($items->count() === 0) {
                return new LengthAwarePaginator(
                    [],
                    $conversations->total(),
                    $conversations->perPage(),
                    $conversations->currentPage(),
                    ['path' => request()->url(), 'query' => request()->query()]
                );
            }

            return new LengthAwarePaginator(
                $items,
                $conversations->total(),
                $conversations->perPage(),
                $conversations->currentPage(),
                ['path' => request()->url(), 'query' => request()->query()]
            );
        } catch (\Throwable $e) {
            Log::error('MessageService::getConversations - Error', [
                'userId' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return an empty paginator in case of error
            return new LengthAwarePaginator(
                [],
                0,
                $perPage,
                1,
                ['path' => request()->url(), 'query' => request()->query()]
            );
        }
    }

    /**
     * Mark a message as read.
     *
     * @param int $messageId
     * @param int $userId
     * @return bool
     */
    public function markAsRead(int $messageId, int $userId): bool
    {
        return $this->messageRepository->markAsRead($messageId, $userId);
    }

    /**
     * Delete a message.
     *
     * @param int $messageId
     * @param int $userId
     * @return bool
     */
    public function deleteMessage(int $messageId, int $userId): bool
    {
        $message = Message::find($messageId);

        if (!$message) {
            return false;
        }

        if ($message->sender_id !== $userId && $message->recipient_id !== $userId) {
            return false;
        }

        if ($message->sender_id === $userId) {
            return $this->messageRepository->deleteMessageForSender($messageId, $userId);
        } else {
            return $this->messageRepository->deleteMessageForRecipient($messageId, $userId);
        }
    }

    /**
     * Pin a conversation.
     *
     * @param int $otherUserId
     * @param int $userId
     * @return bool
     */
    public function pinConversation(int $otherUserId, int $userId): bool
    {
        return $this->messageRepository->pinConversation($otherUserId, $userId);
    }

    /**
     * Unpin a conversation.
     *
     * @param int $otherUserId
     * @param int $userId
     * @return bool
     */
    public function unpinConversation(int $otherUserId, int $userId): bool
    {
        return $this->messageRepository->unpinConversation($otherUserId, $userId);
    }

    /**
     * Find or create a conversation between two users.
     *
     * @param int $userId
     * @param int $otherUserId
     * @return object
     */
    public function findOrCreateConversation(int $userId, int $otherUserId): object
    {
        // Check if a conversation already exists
        $existingConversation = $this->messageRepository->getConversationBetweenUsers($userId, $otherUserId);

        if ($existingConversation) {
            return (object) [
                'id' => $otherUserId,
                'exists' => true
            ];
        }

        // Create a new conversation (in our case, just return the other user's ID)
        return (object) [
            'id' => $otherUserId,
            'exists' => false
        ];
    }

    /**
     * Check if a colleague is in the same organization as the current user.
     *
     * @param int $userId Current user ID (sponsor)
     * @param int $colleagueId Colleague user ID to check
     * @return bool True if in same organization, false otherwise
     */
    public function isColleagueInSameOrganization(int $userId, int $colleagueId): bool
    {
        // Get the current user's organization
        $user = \App\Models\User::query()->with('activeOrganization')->find($userId);

        if (!$user || !$user->activeOrganization || $user->activeOrganization->isEmpty()) {
            return false;
        }

        $userOrgId = $user->activeOrganization->first()->id;

        // Get the colleague's organization
        $colleague = \App\Models\User::query()->with('activeOrganization')->find($colleagueId);

        if (!$colleague || !$colleague->activeOrganization || $colleague->activeOrganization->isEmpty()) {
            return false;
        }

        $colleagueOrgId = $colleague->activeOrganization->first()->id;

        // Check if they belong to the same organization
        return $userOrgId === $colleagueOrgId;
    }

    /**
     * Get conversation between a colleague and another user (organizational view).
     *
     * @param int $viewerId Current user ID (sponsor viewing the conversation)
     * @param int $colleagueId Colleague user ID
     * @param int $otherUserId Other user ID in the conversation
     * @param int $perPage Number of messages per page
     * @return LengthAwarePaginator
     * @throws Exception
     */
    public function getColleagueConversation(int $viewerId, int $colleagueId, int $otherUserId, int $perPage = 15): LengthAwarePaginator
    {
        // Verify the viewer is a sponsor
        $viewer = \App\Models\User::query()->find($viewerId);
        if (!$viewer || $viewer->profile_type !== \App\Enums\ProfileType::SPONSOR) {
            throw new Exception('You are not authorized to view this conversation');
        }

        // Verify the colleague is in the same organization
        if (!$this->isColleagueInSameOrganization($viewerId, $colleagueId)) {
            throw new Exception('You can only view conversations of colleagues in your organization');
        }

        // Check if the colleague has blocked the other user or vice versa
        if ($this->networkingService->isUserBlocked($colleagueId, $otherUserId)) {
            throw new Exception('Cannot view conversation due to blocking restrictions');
        }

        // Get conversation - reuse existing repository method but pass colleague ID
        return $this->messageRepository->getConversation($colleagueId, $otherUserId, $perPage);
    }
}
