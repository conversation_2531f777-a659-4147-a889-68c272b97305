<?php

namespace App\Services;

use App\Data\SystemInvites\TeamStudentInviteData;
use App\Enums\TeamUserType;
use App\Models\Team;
use App\Models\User;
use App\Services\Invite\SystemInviteService;
use Illuminate\Support\Facades\DB;

class TeamService
{
    public function __construct(
        private readonly SystemInviteService $systemInviteService
    ) {}

    public function createTeam(array $data, User $coach): Team
    {
        return DB::transaction(function() use ($data, $coach) {
            $team = Team::query()->create([
                'name' => $data['name'],
                'sport_id' => $data['sport_id'],
                'school_id' => $data['school_id']
            ]);

            // Attach coach
            $team->users()->attach($coach->id, [
                'type' => TeamUserType::Coach->value
            ]);

            return $team;
        });
    }

    public function inviteStudents(Team $team, array $students): void
    {
        foreach ($students as $student) {
            // Create team invite record first
            $teamInvite = $team->invites()->create([
                'name' => "{$student['first_name']} {$student['last_name']}",
                'email' => $student['email'],
                'phone' => $student['phone'] ?? null,
                'groups' => isset($student['group']) ? [$student['group']] : []
            ]);

            // Create system invite for onboarding
            $inviteData = new TeamStudentInviteData(
                first_name: $student['first_name'],
                last_name: $student['last_name'],
                email: $student['email'],
                team_name: $team->name,
                school_name: $team->school->name
            );

            $this->systemInviteService->createForTeamStudent($inviteData);
        }
    }
}
