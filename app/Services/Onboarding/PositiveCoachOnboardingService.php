<?php

namespace App\Services\Onboarding;

use App\Data\Onboarding\InvolvementDTO;
use App\Data\Onboarding\PositiveCoach\DetailsDTO;
use App\Data\Onboarding\PositiveCoach\TeamSuccessesDTO;
use App\Data\Onboarding\SportsDTO;
use App\Data\Onboarding\StoryDTO;
use App\Data\Onboarding\AccountInfoWithAddressDTO;
use App\Enums\ProfileType;
use App\Enums\RegistrationMethod;
use App\Models\Onboarding;
use App\Models\SystemInvite;
use App\Models\User;
use App\States\Onboarding\States\AccountInfo;
use App\States\Onboarding\States\CommunityInvolvement;
use App\States\Onboarding\States\Details;
use App\States\Onboarding\States\Sports;
use App\States\Onboarding\States\Story;
use App\States\Onboarding\States\TeamSuccesses;
use Illuminate\Support\Facades\DB;

class PositiveCoachOnboardingService
{
    /**
     * Handle the submission of personal details during onboarding
     *
     * @param SystemInvite $invite The system invite
     * @param DetailsDTO $data The details data
     * @return void
     */
    public function submitDetails(SystemInvite $invite, DetailsDTO $data): void
    {
        $onboarding = $invite->onboarding;

        // Convert the DTO to array and handle the profile photo separately
        $detailsData = $data->toArray();

        // Remove the profile_photo from the array data since we'll store it separately
        if (isset($detailsData['profile_photo'])) {
            unset($detailsData['profile_photo']);
        }

        $onboarding->updateStepData(Details::identifier(), $detailsData);

        // Handle profile photo upload if present
        if ($data->profile_photo) {
            // Clear any existing avatar
            $invite->clearMediaCollection('avatar');

            // Add the new avatar
            $invite->addMedia($data->profile_photo)
                ->withResponsiveImages()
                ->toMediaCollection('avatar');
        }
    }

    /**
     * Handle the submission of account information during onboarding
     *
     * @param SystemInvite $invite The system invite
     * @param AccountInfoWithAddressDTO $data The account info data
     * @return void
     */
    public function submitAccountInfo(SystemInvite $invite, AccountInfoWithAddressDTO $data): void
    {
        $onboarding = $invite->onboarding;
        $onboarding->updateStepData(AccountInfo::identifier(), $data->toArray());
    }

    /**
     * Handle the submission of sports selection during onboarding
     *
     * @param SystemInvite $invite The system invite
     * @param SportsDTO $data The sports data
     * @return void
     */
    public function submitSports(SystemInvite $invite, SportsDTO $data): void
    {
        $onboarding = $invite->onboarding;
        $onboarding->updateStepData(Sports::identifier(), $data->toArray());
    }

    /**
     * Handle the submission of community involvement information during onboarding
     *
     * @param SystemInvite $invite The system invite
     * @param InvolvementDTO $data The involvement data
     * @return void
     */
    public function submitCommunityInvolvement(SystemInvite $invite, InvolvementDTO $data): void
    {
        $onboarding = $invite->onboarding;
        $onboarding->updateStepData(CommunityInvolvement::identifier(), $data->toArray());
    }

    /**
     * Handle the submission of team successes information during onboarding
     *
     * @param SystemInvite $invite The system invite
     * @param TeamSuccessesDTO $data The team successes data
     * @return void
     */
    public function submitTeamSuccesses(SystemInvite $invite, TeamSuccessesDTO $data): void
    {
        $onboarding = $invite->onboarding;
        $onboarding->updateStepData(TeamSuccesses::identifier(), $data->toArray());
    }

    /**
     * Handle the submission of personal story during onboarding
     *
     * @param SystemInvite $invite The system invite
     * @param StoryDTO $data The story data
     * @return void
     */
    public function submitStory(SystemInvite $invite, StoryDTO $data): void
    {
        $onboarding = $invite->onboarding;
        $onboarding->updateStepData(Story::identifier(), $data->toArray());
    }

    /**
     * Complete the onboarding process and create a user account
     *
     * @param SystemInvite $invite The system invite
     * @param AccountInfoWithAddressDTO $data The account info data
     * @return User|null The created user or null if the user already exists
     */
    public function completeOnboarding(SystemInvite $invite, AccountInfoWithAddressDTO $data): ?User
    {
        $onboarding = $invite->onboarding;

        // Check if user already exists
        $existingUser = User::query()->where('email', $data->email)->first();
        if ($existingUser) {
            return null;
        }

        // Begin database transaction
        DB::beginTransaction();

        try {
            // Update the onboarding data with the final account info
            $onboarding->updateStepData('account_info', $data->toArray());

            // Determine public_profile setting - default to false unless explicitly set to true
            $dataArray = $data->toArray();
            $publicProfile = isset($dataArray['public_profile']) && $dataArray['public_profile'] === true;

            // Create a clean user data array with only the fields that exist in the users table
            $userData = [
                // Required fields
                'first_name' => $data->first_name,
                'last_name' => $data->last_name,
                'email' => $data->email,
                'password' => $data->password,
                'profile_type' => ProfileType::POSITIVE_COACH->value,
                'public_profile' => $publicProfile,

                // Optional fields mapped to their database column names
                'phone' => $data->phone ?? null,
                'street_address_1' => $data->street_address ?? null,
                'street_address_2' => $data->unit ?? null,
                'city' => $data->city ?? null,
                'state_code' => $data->state ?? null,
                'zip' => $data->zip_code ?? null
            ];

            // Create the user directly with the clean data
            $user = User::query()->create($userData);

            // Mark the user as verified
            $this->markUserAsVerified($user);

            // Transfer details from onboarding
            $this->transferDetailsFromOnboarding($user, $onboarding);

            // Transfer avatar from system invite to user
            $this->transferAvatarToUser($invite, $user);

            // Link the nomination to the created user
            $this->linkNominationToUser($invite, $user);

            // Mark the invite as completed
            $invite->markAsCompleted();

            // Commit the transaction
            DB::commit();

            return $user;
        } catch (\Exception $e) {
            // Rollback the transaction on error
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Mark the user as verified via invite
     *
     * @param User $user The user to mark as verified
     * @return void
     */
    private function markUserAsVerified(User $user): void
    {
        $user->forceFill([
            'email_verified_at' => now(),
            'registration_method' => RegistrationMethod::SystemInvite,
        ])->save();
    }

    /**
     * Transfer avatar from system invite to user
     *
     * @param SystemInvite $invite The system invite
     * @param User $user The user
     * @return void
     */
    private function transferAvatarToUser(SystemInvite $invite, User $user): void
    {
        $avatar = $invite->getFirstMedia('avatar');

        if ($avatar) {
            // Clear any existing avatar for the user
            $user->clearMediaCollection('avatar');

            // Copy the avatar to the user
            $avatar->copy($user, 'avatar');
        }
    }

    /**
     * Link the nomination to the created user
     *
     * @param SystemInvite $invite The system invite
     * @param User $user The created user
     * @return void
     */
    private function linkNominationToUser(SystemInvite $invite, User $user): void
    {
        // Update the associated nomination to link it to the created user
        if ($invite->nomination) {
            $invite->nomination->update(['user_id' => $user->id]);
        }
    }

    /**
     * Transfer details from onboarding to user
     *
     * @param User $user The user
     * @param Onboarding $onboarding The onboarding data
     * @return void
     */
    private function transferDetailsFromOnboarding(User $user, Onboarding $onboarding): void
    {
        // Get details from the onboarding steps
        $detailsData = $onboarding->getStepData(Details::identifier());
        $sportsData = $onboarding->getStepData(Sports::identifier());
        $storyData = $onboarding->getStepData(Story::identifier());
        $teamSuccessesData = $onboarding->getStepData(TeamSuccesses::identifier());
        $communityInvolvementsData = $onboarding->getStepData(CommunityInvolvement::identifier());

        // Update user profile with details data if available
        if ($detailsData) {
            $user->update([
                'state_code' => $detailsData['state'] ?? null,
                'county_id' => $detailsData['county'] ?? null,
                'school_name' => $detailsData['high_school'] ?? null,
                'school_id' => $detailsData['school_id'] ?? null,
                // Social media fields
                'twitter' => $detailsData['twitter'] ?? null,
                'instagram' => $detailsData['instagram'] ?? null,
                'facebook' => $detailsData['facebook'] ?? null,
                'hudl' => $detailsData['hudl'] ?? null,
                'custom_link' => $detailsData['custom_link'] ?? null,
            ]);
        }

        // Update user story if available
        if ($storyData) {
            $user->update([
                'content' => $storyData['content'] ?? null,
            ]);
        }

        // Add sports if available
        if ($sportsData && isset($sportsData['sports']) && is_array($sportsData['sports'])) {
            $sportSync = [];

            // Handle sports data format (array of objects with id, name, order, is_custom)
            foreach ($sportsData['sports'] as $sport) {
                if (!($sport['is_custom'] ?? false)) {
                    $sportSync[$sport['id']] = ['order' => $sport['order'] ?? 0];
                }
            }

            if (!empty($sportSync)) {
                $user->sports()->sync($sportSync);
            }
        }

        // Add community involvements if available
        if ($communityInvolvementsData && isset($communityInvolvementsData['items']) && is_array($communityInvolvementsData['items'])) {
            foreach ($communityInvolvementsData['items'] as $item) {
                $user->communityInvolvements()->create([
                    'title' => $item['name'] ?? null,
                    'date_range' => $item['date_range'] ?? null,
                    'description' => $item['description'] ?? null,
                    'order' => $item['order'] ?? 0,
                ]);
            }
        }

        // Add team successes as work experiences if available
        if ($teamSuccessesData) {
            // Check for different possible data formats
            if (isset($teamSuccessesData['successes']) && is_array($teamSuccessesData['successes'])) {
                // If using the successes array format
                foreach ($teamSuccessesData['successes'] as $index => $success) {
                    $user->workExperiences()->create([
                        'name' => $success['name'] ?? $success['title'] ?? 'Team Success ' . ($index + 1),
                        'date' => $success['date_range'] ?? $success['date'] ?? null,
                        'description' => $success['description'] ?? null,
                        'order' => $success['order'] ?? $index,
                    ]);
                }
            } elseif (isset($teamSuccessesData['items']) && is_array($teamSuccessesData['items'])) {
                // If using the items array format (like involvement)
                foreach ($teamSuccessesData['items'] as $item) {
                    $user->workExperiences()->create([
                        'name' => $item['name'] ?? null,
                        'date' => $item['date_range'] ?? null,
                        'description' => $item['description'] ?? null,
                        'order' => $item['order'] ?? 0,
                    ]);
                }
            }

            // Store additional info if present
            if (isset($teamSuccessesData['additional_info'])) {
                $user->update([
                    'additional_info' => $teamSuccessesData['additional_info'],
                ]);
            }
        }
    }
}
