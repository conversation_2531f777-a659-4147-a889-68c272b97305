<?php

namespace App\Services\Onboarding;

use App\Data\Onboarding\Alumni\AccountInfoDTO;
use App\Data\Onboarding\Alumni\LifeStageSelectionDTO;
use App\Data\Onboarding\Alumni\CollegeDetailsDTO;
use App\Data\Onboarding\Alumni\ProfessionalDetailsDTO;
use App\Enums\LifeStage;
use App\Enums\ProfileType;
use App\Enums\RegistrationMethod;
use App\Models\SystemInvite;
use App\Models\User;
use App\States\Onboarding\States\AccountInfo;
use App\States\Onboarding\States\Completed;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class AlumniOnboardingService
{
    /**
     * Store the life stage selection and determine the intended profile type
     *
     * @param SystemInvite $invite The system invite
     * @param LifeStageSelectionDTO $data The life stage selection data
     * @return void
     */
    public function storeLifeStageSelection(SystemInvite $invite, LifeStageSelectionDTO $data): void
    {
        $onboarding = $invite->onboarding;
        $currentData = $onboarding->data ?? [];

        $currentData['life_stage'] = $data->life_stage;
        $currentData['intended_profile_type'] = $data->intended_profile_type;
        $currentData['current_step'] = 'account_info';

        $onboarding->update(['data' => $currentData]);
    }

    /**
     * Store account information for alumni onboarding
     *
     * @param SystemInvite $invite The system invite
     * @param AccountInfoDTO $data The account info data
     * @return void
     */
    public function storeAccountInfo(SystemInvite $invite, AccountInfoDTO $data): void
    {
        /** @var \App\Models\Onboarding $onboarding */
        $onboarding = $invite->onboarding;

        // Create a copy of the data to modify
        $accountData = $data->toArray();

        // Remove password confirmation as we don't need to store it
        if (isset($accountData['password_confirmation'])) {
            unset($accountData['password_confirmation']);
        }

        $onboarding->updateStepData(AccountInfo::identifier(), $accountData);

        // Set next step based on intended profile type
        $currentData = $onboarding->data;
        $intendedProfileType = $currentData['intended_profile_type'] ?? null;

        // Determine the next step based on intended profile type
        $nextStep = $this->determineNextStepFromProfileType($intendedProfileType);

        $currentData['current_step'] = $nextStep;
        $onboarding->update(['data' => $currentData]);
    }

    /**
     * Store college details for alumni with college experience
     *
     * @param SystemInvite $invite The system invite
     * @param CollegeDetailsDTO $data The college details data
     * @return void
     */
    public function storeCollegeDetails(SystemInvite $invite, CollegeDetailsDTO $data): void
    {
        $onboarding = $invite->onboarding;
        $onboardingData = $data->toArray();

        // Handle profile photo if present (remove from toArray data as we handle it separately)
        if (isset($onboardingData['profile_photo'])) {
            unset($onboardingData['profile_photo']);
        }

        $onboarding->updateStepData('college_details', $onboardingData);

        // Handle profile photo upload if present
        if ($data->profile_photo) {
            // Clear any existing avatar
            $invite->clearMediaCollection('avatar');

            // Add the new avatar
            $invite->addMedia($data->profile_photo)
                ->withResponsiveImages()
                ->toMediaCollection('avatar');
        }

        // Set next step to completed
        $currentData = $onboarding->data;
        $currentData['current_step'] = 'completed';
        $onboarding->update(['data' => $currentData]);
    }

    /**
     * Store professional details for alumni in professional settings
     *
     * @param SystemInvite $invite The system invite
     * @param ProfessionalDetailsDTO $data The professional details data
     * @return void
     */
    public function storeProfessionalDetails(SystemInvite $invite, ProfessionalDetailsDTO $data): void
    {
        $onboarding = $invite->onboarding;
        $onboardingData = $data->toArray();

        // Handle profile photo if present (remove from toArray data as we handle it separately)
        if (isset($onboardingData['profile_photo'])) {
            unset($onboardingData['profile_photo']);
        }

        $onboarding->updateStepData('professional_details', $onboardingData);

        // Handle profile photo upload if present
        if ($data->profile_photo) {
            // Clear any existing avatar
            $invite->clearMediaCollection('avatar');

            // Add the new avatar
            $invite->addMedia($data->profile_photo)
                ->withResponsiveImages()
                ->toMediaCollection('avatar');
        }

        // Set next step to completed
        $currentData = $onboarding->data;
        $currentData['current_step'] = 'completed';
        $onboarding->update(['data' => $currentData]);
    }

    /**
     * Complete the onboarding process and create the appropriate user
     *
     * @param SystemInvite $invite The system invite
     * @return User|null The created user or null if user already exists
     * @throws \Exception If user creation fails
     */
    public function completeOnboarding(SystemInvite $invite): ?User
    {
        $onboarding = $invite->onboarding;
        $onboardingData = $onboarding->data;

        // Get account info data
        $accountData = $onboarding->getStepData(AccountInfo::identifier());

        if (!$accountData) {
            throw new \Exception("Account information is missing");
        }

        // Check if user already exists
        $existingUser = User::query()->where('email', $accountData['email'])->first();
        if ($existingUser) {
            return null;
        }

        // Begin database transaction
        DB::beginTransaction();

        try {
            // Determine profile type based on life stage
            $lifeStage = $onboardingData['life_stage'] ?? null;
            $profileType = $this->determineProfileType($lifeStage);

            // Determine public_profile setting - default to false unless explicitly set to true
            $publicProfile = isset($accountData['public_profile']) && $accountData['public_profile'] === true;

            // Create a clean user data array with only the fields that exist in the users table
            $userData = [
                'first_name' => $accountData['first_name'],
                'last_name' => $accountData['last_name'],
                'email' => $accountData['email'],
                'password' => $accountData['password'] ?? Hash::make(Str::random(12)),
                'profile_type' => $profileType->value,
                'registration_method' => RegistrationMethod::SystemInvite->value,
                'public_profile' => $publicProfile,

                // Optional fields mapped to their database column names
                'phone' => $accountData['phone'] ?? null,
                'street_address_1' => $accountData['street_address'] ?? null,
                'street_address_2' => $accountData['unit'] ?? null,
                'city' => $accountData['city'] ?? null,
                'state_code' => $accountData['state'] ?? null,
                'zip' => $accountData['zip_code'] ?? null,
                'onboarding_completed_at' => now(),
            ];

            // Create user
            $user = User::query()->create($userData);

            // Set user metadata based on profile type
            $this->setUserMetadata($user, $onboarding, $profileType);

            // Transfer avatar from system invite to user
            $this->transferAvatarToUser($invite, $user);

            // Mark invite as completed
            $invite->markAsCompleted();

            // Complete the onboarding process
            $onboarding->state->transitionTo(Completed::class);

            DB::commit();

            return $user;
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Failed to complete alumni onboarding', [
                'error' => $e->getMessage(),
                'invite_id' => $invite->id,
                'email' => $accountData['email'] ?? null,
            ]);

            throw $e;
        }
    }

    /**
     * Transfer avatar from system invite to user
     *
     * @param SystemInvite $invite The system invite
     * @param User $user The user
     * @return void
     */
    private function transferAvatarToUser(SystemInvite $invite, User $user): void
    {
        $avatar = $invite->getFirstMedia('avatar');

        if ($avatar) {
            // Clear any existing avatar for the user
            $user->clearMediaCollection('avatar');

            // Copy the avatar to the user
            $avatar->copy($user, 'avatar');
        }
    }

    /**
     * Get prefill data for the current step
     *
     * @param SystemInvite $invite The system invite
     * @return array The prefill data
     */
    public function getPrefillData(SystemInvite $invite): array
    {
        $inviteData = $invite->invite_data?->toArray() ?? [];
        $onboardingData = $invite->onboarding->data ?? [];

        return array_merge($inviteData, $onboardingData);
    }

    /**
     * Determine the next step based on the selected life stage
     *
     * @param string|null $lifeStage The selected life stage
     * @return string The next step identifier
     * @deprecated Use determineNextStepFromProfileType instead
     */
    private function determineNextStep(?string $lifeStage): string
    {
        if (!$lifeStage) {
            return 'account_info';
        }

        return match ($lifeStage) {
            LifeStage::COLLEGE_STUDENT->value,
            LifeStage::COLLEGE_GRADUATE->value => 'college_details',
            LifeStage::PROFESSIONAL->value => 'professional_details',
            LifeStage::GAP_YEAR->value,
            LifeStage::HIGH_SCHOOL_STUDENT->value,
            LifeStage::HIGH_SCHOOL_GRADUATE->value => 'college_details', // treat as college_details for onboarding
            default => 'college_details',
        };
    }

    /**
     * Determine the next step based on the intended profile type
     *
     * @param string|null $profileType The intended profile type
     * @return string The next step identifier
     */
    private function determineNextStepFromProfileType(?string $profileType): string
    {
        if (!$profileType) {
            return 'college_details'; // Default to college details
        }

        return match ($profileType) {
            ProfileType::COLLEGE_ATHLETE->value => 'college_details',
            ProfileType::PROFESSIONAL->value => 'professional_details',
            default => 'college_details', // Default to college details
        };
    }

    /**
     * Determine the profile type based on the selected life stage
     *
     * @param string|null $lifeStage The selected life stage
     * @return ProfileType The determined profile type
     */
    private function determineProfileType(?string $lifeStage): ProfileType
    {
        // Mapping rules:
        // - High school student => Positive Athlete
        // - Gap year, High school graduate, Professional => Professional (not all go to college)
        // - College student, College graduate => College Athlete
        // - Default => Alumni
        if (!$lifeStage) {
            return ProfileType::ALUMNI; // Default to alumni profile type if no life stage
        }

        return match ($lifeStage) {
            LifeStage::HIGH_SCHOOL_STUDENT->value => ProfileType::POSITIVE_ATHLETE,
            LifeStage::GAP_YEAR->value, LifeStage::HIGH_SCHOOL_GRADUATE->value, LifeStage::PROFESSIONAL->value => ProfileType::PROFESSIONAL,
            LifeStage::COLLEGE_STUDENT->value, LifeStage::COLLEGE_GRADUATE->value => ProfileType::COLLEGE_ATHLETE,
            default => ProfileType::ALUMNI, // Default to alumni profile type
        };
    }

    /**
     * Set user metadata based on the profile type and onboarding data
     *
     * @param User $user The user to update
     * @param \App\Models\Onboarding $onboarding The onboarding record
     * @param ProfileType $profileType The determined profile type
     * @return void
     */
    private function setUserMetadata(User $user, \App\Models\Onboarding $onboarding, ProfileType $profileType): void
    {
        $metadata = [];
        $lifeStage = $onboarding->data['life_stage'] ?? null;

        // Add life stage to metadata
        if ($lifeStage) {
            // Store life_stage directly on the user model since it has this column
            $user->life_stage = $lifeStage;
        }

        // Add profile-specific data
        if ($profileType === ProfileType::COLLEGE_ATHLETE) {
            $collegeData = $onboarding->getStepData('college_details') ?? [];

            if (!empty($collegeData)) {
                // Update state_code from college details if provided (may be different from account info)
                if (isset($collegeData['state'])) {
                    $user->state_code = $collegeData['state'];
                }

                // Store college-specific fields in user table (standard fields)
                if (isset($collegeData['gender'])) {
                    $user->gender = $collegeData['gender'];
                }
                if (isset($collegeData['height'])) {
                    $user->height_in_inches = $this->convertHeightToInches($collegeData['height']);
                }
                if (isset($collegeData['weight'])) {
                    $user->weight = $collegeData['weight'];
                }

                // Set social media fields directly on the user model
                if (!empty($collegeData['twitter'])) $user->twitter = $collegeData['twitter'];
                if (!empty($collegeData['instagram'])) $user->instagram = $collegeData['instagram'];
                if (!empty($collegeData['facebook'])) $user->facebook = $collegeData['facebook'];
                if (!empty($collegeData['hudl'])) $user->hudl = $collegeData['hudl'];
                if (!empty($collegeData['custom_link'])) $user->custom_link = $collegeData['custom_link'];

                // Store college-specific fields in metadata (as specified)
                $metadata['college'] = $collegeData['college'] ?? null;
                $metadata['graduation_year'] = $collegeData['graduation_year'] ?? null;
                $metadata['gpa'] = $collegeData['gpa'] ?? null;

                // Store career interests in metadata for reference (also synced to pivot table below)
                $metadata['career_interests'] = $collegeData['career_interests'] ?? [];

                // Construct social_links from individual fields for backward compatibility
                $socialLinks = [];
                if (!empty($collegeData['twitter'])) $socialLinks['twitter'] = $collegeData['twitter'];
                if (!empty($collegeData['instagram'])) $socialLinks['instagram'] = $collegeData['instagram'];
                if (!empty($collegeData['facebook'])) $socialLinks['facebook'] = $collegeData['facebook'];
                if (!empty($collegeData['hudl'])) $socialLinks['hudl'] = $collegeData['hudl'];
                if (!empty($collegeData['custom_link'])) $socialLinks['custom_link'] = $collegeData['custom_link'];

                $metadata['social_links'] = $socialLinks;
            }
        } elseif ($profileType === ProfileType::PROFESSIONAL) {
            $professionalData = $onboarding->getStepData('professional_details') ?? [];

            if (!empty($professionalData)) {
                // Update state_code from professional details if provided
                if (isset($professionalData['state'])) {
                    $user->state_code = $professionalData['state'];
                }

                // Set social media fields directly on the user model
                if (!empty($professionalData['twitter'])) $user->twitter = $professionalData['twitter'];
                if (!empty($professionalData['instagram'])) $user->instagram = $professionalData['instagram'];
                if (!empty($professionalData['facebook'])) $user->facebook = $professionalData['facebook'];
                if (!empty($professionalData['hudl'])) $user->hudl = $professionalData['hudl'];
                if (!empty($professionalData['custom_link'])) $user->custom_link = $professionalData['custom_link'];

                // Store metadata
                $metadata['employer'] = $professionalData['employer'] ?? null;
                $metadata['job_title'] = $professionalData['job_title'] ?? null;
                $metadata['interests'] = $professionalData['interests'] ?? [];

                // Construct social_links from individual fields
                $socialLinks = [];
                if (!empty($professionalData['twitter'])) $socialLinks['twitter'] = $professionalData['twitter'];
                if (!empty($professionalData['instagram'])) $socialLinks['instagram'] = $professionalData['instagram'];
                if (!empty($professionalData['facebook'])) $socialLinks['facebook'] = $professionalData['facebook'];
                if (!empty($professionalData['hudl'])) $socialLinks['hudl'] = $professionalData['hudl'];
                if (!empty($professionalData['custom_link'])) $socialLinks['custom_link'] = $professionalData['custom_link'];

                $metadata['social_links'] = $socialLinks;
            }
        }

        // Store metadata if we have any
        if (!empty($metadata)) {
            $user->metadata = $metadata;
        }

        $user->save();

        // After saving the user, sync their interests if provided
        if ($profileType === ProfileType::COLLEGE_ATHLETE && isset($collegeData['career_interests']) && is_array($collegeData['career_interests'])) {
            $this->syncUserInterests($user, $collegeData['career_interests']);
        } elseif ($profileType === ProfileType::PROFESSIONAL && isset($professionalData['interests']) && is_array($professionalData['interests'])) {
            $this->syncUserInterests($user, $professionalData['interests']);
        }
    }

    /**
     * Sync interests for a user
     *
     * @param User $user The user to sync interests for
     * @param array $interestIds List of interest IDs
     * @return void
     */
    private function syncUserInterests(User $user, array $interestIds): void
    {
        if (empty($interestIds)) {
            return;
        }

        // Sync interests with the user
        $user->interests()->sync($interestIds);
    }

    /**
     * Convert height from string format (e.g. "5'11") to inches
     *
     * @param string|null $height
     * @return int|null
     */
    private function convertHeightToInches(?string $height): ?int
    {
        if (!$height) {
            return null;
        }

        // Handle common formats like "5'11", "5-11", "5 11"
        $height = str_replace(["'", "-", " "], ":", $height);
        $parts = explode(":", $height);

        if (count($parts) === 2) {
            $feet = (int) $parts[0];
            $inches = (int) $parts[1];
            return ($feet * 12) + $inches;
        }

        // If it's already in inches
        if (is_numeric($height)) {
            return (int) $height;
        }

        return null;
    }
}

