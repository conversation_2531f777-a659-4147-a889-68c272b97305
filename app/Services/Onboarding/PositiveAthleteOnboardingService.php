<?php

namespace App\Services\Onboarding;

use App\Data\Onboarding\AccountInfoWithAddressDTO;
use App\Data\Onboarding\DetailsDTO;
use App\Enums\ProfileType;
use App\Enums\RegistrationMethod;
use App\Models\Onboarding;
use App\Models\SystemInvite;
use App\Models\User;
use App\States\Onboarding\States\Details;
use App\States\Onboarding\States\School as SchoolState;
use App\States\Onboarding\States\Sports;
use App\States\Onboarding\States\Story;
use App\States\Onboarding\States\WorkExperience;
use Illuminate\Support\Facades\DB;

class PositiveAthleteOnboardingService
{
    /**
     * Handle the submission of personal details during onboarding
     *
     * @param SystemInvite $invite The system invite
     * @param DetailsDTO $data The details data
     * @return void
     */
    public function submitDetails(SystemInvite $invite, DetailsDTO $data): void
    {
        $onboarding = $invite->onboarding;

        // Convert the DTO to array and handle the profile photo separately
        $detailsData = $data->toArray();

        // Remove the profile_photo from the array data since we'll store it separately
        if (isset($detailsData['profile_photo'])) {
            unset($detailsData['profile_photo']);
        }

        $onboarding->updateStepData(Details::identifier(), $detailsData);

        // Handle profile photo upload if present
        if ($data->profile_photo) {
            // Clear any existing avatar
            $invite->clearMediaCollection('avatar');

            // Add the new avatar
            $invite->addMedia($data->profile_photo)
                ->withResponsiveImages()
                ->toMediaCollection('avatar');
        }
    }

    /**
     * Complete the onboarding process and create a user account
     *
     * @param SystemInvite $invite The system invite
     * @param AccountInfoWithAddressDTO $data The account info data
     * @return User|null The created user or null if the user already exists
     */
    public function completeOnboarding(SystemInvite $invite, AccountInfoWithAddressDTO $data): ?User
    {
        $onboarding = $invite->onboarding;

        // Check if user already exists
        $existingUser = User::query()->where('email', $data->email)->first();
        if ($existingUser) {
            return null;
        }

        // Begin database transaction
        DB::beginTransaction();

        try {
            // Update the onboarding data with the final account info
            $onboarding->updateStepData('account_info', $data->toArray());

            // Determine public_profile setting - default to false unless explicitly set to true
            $dataArray = $data->toArray();
            $publicProfile = isset($dataArray['public_profile']) && $dataArray['public_profile'] === true;

            // Create a clean user data array with only the fields that exist in the users table
            $userData = [
                // Required fields
                'first_name' => $data->first_name,
                'last_name' => $data->last_name,
                'email' => $data->email,
                'password' => $data->password,
                'profile_type' => ProfileType::POSITIVE_ATHLETE->value,
                'public_profile' => $publicProfile,

                // Optional fields mapped to their database column names
                'phone' => $data->phone ?? null,
                'street_address_1' => $data->street_address ?? null,
                'street_address_2' => $data->unit ?? null,
                'city' => $data->city ?? null,
                'state_code' => $data->state ?? null,
                'zip' => $data->zip_code ?? null
            ];

            // Create the user directly with the clean data
            $user = User::query()->create($userData);

            // Mark the user as verified
            $this->markUserAsVerified($user);

            // Transfer details from onboarding
            $this->transferDetailsFromOnboarding($user, $onboarding);

            // Transfer avatar from system invite to user
            $this->transferAvatarToUser($invite, $user);

            // Link the nomination to the created user
            $this->linkNominationToUser($invite, $user);

            // Mark the invite as completed
            $invite->markAsCompleted();

            // Commit the transaction
            DB::commit();

            return $user;
        } catch (\Exception $e) {
            // Rollback the transaction on error
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Mark the user as verified via invite
     *
     * @param User $user The user to mark as verified
     * @return void
     */
    private function markUserAsVerified(User $user): void
    {
        $user->forceFill([
            'email_verified_at' => now(),
            'registration_method' => RegistrationMethod::SystemInvite,
        ])->save();
    }

    /**
     * Transfer avatar from system invite to user
     *
     * @param SystemInvite $invite The system invite
     * @param User $user The user
     * @return void
     */
    private function transferAvatarToUser(SystemInvite $invite, User $user): void
    {
        $avatar = $invite->getFirstMedia('avatar');

        if ($avatar) {
            // Clear any existing avatar for the user
            $user->clearMediaCollection('avatar');

            // Copy the avatar to the user
            $avatar->copy($user, 'avatar');
        }
    }

    /**
     * Link the nomination to the created user
     *
     * @param SystemInvite $invite The system invite
     * @param User $user The created user
     * @return void
     */
    private function linkNominationToUser(SystemInvite $invite, User $user): void
    {
        // Update the associated nomination to link it to the created user
        if ($invite->nomination) {
            $invite->nomination->update(['user_id' => $user->id]);
        }
    }

    /**
     * Transfer details from onboarding to user
     *
     * @param User $user The user
     * @param Onboarding $onboarding The onboarding data
     * @return void
     */
    private function transferDetailsFromOnboarding(User $user, Onboarding $onboarding): void
    {
        // Get details from the onboarding steps
        $detailsData = $onboarding->getStepData(Details::identifier());
        $sportsData = $onboarding->getStepData(Sports::identifier());
        $storyData = $onboarding->getStepData(Story::identifier());
        $workExperienceData = $onboarding->getStepData(WorkExperience::identifier());
        $communityInvolvementsData = $onboarding->getStepData(SchoolState::identifier()); // This is community involvements data, not school data

        // Update user profile with details data if available
        if ($detailsData) {
            $user->update([
                'state_code' => $detailsData['state'] ?? null,
                'county_id' => $detailsData['county'] ?? null,
                'school_id' => $detailsData['school_id'] ?? null,
                'graduation_year' => $detailsData['graduation_year'] ?? null,
                'gpa' => $detailsData['current_gpa'] ?? null,
                'class_rank' => $detailsData['current_class_rank'] ?? null,
                'gender' => $detailsData['gender'] ?? null,
                'height_in_inches' => $this->convertHeightToInches($detailsData['height'] ?? null),
                'weight' => $detailsData['weight'] ?? null,
                // Social links - updated to use individual fields
                'twitter' => $detailsData['twitter'] ?? null,
                'instagram' => $detailsData['instagram'] ?? null,
                'facebook' => $detailsData['facebook'] ?? null,
                'hudl' => $detailsData['hudl'] ?? null,
                'custom_link' => $detailsData['custom_link'] ?? null,
            ]);

            // Add interests
            // Prefer 'interests' (array of IDs) over 'career_interests' (also array of IDs, maintained for backward compatibility)
            if (isset($detailsData['interests']) && is_array($detailsData['interests']) && !empty($detailsData['interests'])) {
                // Use the new interests field
                $user->interests()->sync($detailsData['interests']);
            } elseif (isset($detailsData['career_interests']) && is_array($detailsData['career_interests'])) {
                // Fall back to the legacy career_interests field (still expects IDs despite its name)
                $user->interests()->sync($detailsData['career_interests']);
            }
        }

        // Update user story if available
        if ($storyData) {
            $user->update([
                'content' => $storyData['content'] ?? null,
            ]);
        }

        // Add sports if available
        if ($sportsData && isset($sportsData['sports']) && is_array($sportsData['sports'])) {
            $sportSync = [];

            // Handle new sports data format (array of objects with id, name, order, is_custom)
            foreach ($sportsData['sports'] as $sport) {
                if (!($sport['is_custom'] ?? false)) {
                    $sportSync[$sport['id']] = ['order' => $sport['order'] ?? 0];
                }
            }

            if (!empty($sportSync)) {
                $user->sports()->sync($sportSync);
            }
        }

        // Add community involvements if available
        if ($communityInvolvementsData && isset($communityInvolvementsData['items']) && is_array($communityInvolvementsData['items'])) {
            foreach ($communityInvolvementsData['items'] as $item) {
                $user->communityInvolvements()->create([
                    'title' => $item['name'] ?? null,
                    'date_range' => $item['date_range'] ?? null,
                    'description' => $item['description'] ?? null,
                    'order' => $item['order'] ?? 0,
                ]);
            }
        }

        // Add work experiences if available
        if ($workExperienceData && isset($workExperienceData['items']) && is_array($workExperienceData['items'])) {
            foreach ($workExperienceData['items'] as $item) {
                $user->workExperiences()->create([
                    'name' => $item['name'] ?? null,
                    'date' => $item['date_range'] ?? null,
                    'description' => $item['description'] ?? null,
                    'order' => $item['order'] ?? 0,
                ]);
            }
        }
    }

    /**
     * Convert height from string format (e.g. "5'11") to inches
     *
     * @param string|null $height
     * @return int|null
     */
    private function convertHeightToInches(?string $height): ?int
    {
        if (!$height) {
            return null;
        }

        // Handle common formats like "5'11", "5-11", "5 11"
        $height = str_replace(["'", "-", " "], ":", $height);
        $parts = explode(":", $height);

        if (count($parts) === 2) {
            $feet = (int) $parts[0];
            $inches = (int) $parts[1];
            return ($feet * 12) + $inches;
        }

        // If it's already in inches
        if (is_numeric($height)) {
            return (int) $height;
        }

        return null;
    }
}
