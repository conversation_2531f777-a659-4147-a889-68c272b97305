<?php

namespace App\Services\Onboarding;

use App\Data\Parent\ParentAccountInfoDTO;
use App\Enums\ProfileType;
use App\Models\Onboarding;
use App\Models\User;
use App\States\Onboarding\States\AccountInfo;
use App\States\Onboarding\States\Completed;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class ParentOnboardingService
{
    /**
     * Save account information for the parent onboarding
     */
    public function saveAccountInfo(Onboarding $onboarding, ParentAccountInfoDTO $data): Onboarding
    {
        // The DTO already uses snake_case property names
        $onboarding->updateStepData(AccountInfo::identifier(), $data->toArray());

        // Don't transition to Completed state yet - let the controller handle this
        // so we can check for existing users first

        return $onboarding;
    }

    /**
     * Complete the onboarding process for a parent
     *
     * @throws \Exception If user creation fails
     */
    public function completeOnboarding(Onboarding $onboarding): ?User
    {
        // Get data from onboarding steps
        $accountData = $onboarding->getStepData(AccountInfo::identifier());

        if (!$accountData) {
            throw new \Exception("Account information is missing");
        }

        // Check if a user with this email already exists
        $existingUser = User::query()->where('email', $accountData['email'])->first();
        if ($existingUser) {
            // Return null to indicate existing user instead of throwing an exception
            return null;
        }

        // Create the user - using snake_case field names that match our normalized data
        $user = User::query()->create([
            'email' => $accountData['email'],
            'password' => $accountData['password'],
            'first_name' => $accountData['first_name'],
            'last_name' => $accountData['last_name'],
            'phone' => $accountData['phone'],
            'profile_type' => ProfileType::PARENT,
            'registration_method' => \App\Enums\RegistrationMethod::SystemInvite,
        ]);

        // Get the athlete ID from the onboarding data and create parent-athlete association
        $this->linkParentToAthlete($user, $onboarding);

        return $user;
    }

    /**
     * Link parent to the athlete referred to in the onboarding process
     *
     * @param User $parent The newly created parent user
     * @param Onboarding $onboarding The onboarding record with athlete data
     * @return void
     */
    private function linkParentToAthlete(User $parent, Onboarding $onboarding): void
    {
        try {
            // Get the full onboarding data which should contain athlete_user_id
            $onboardingData = $onboarding->data;

            // Extract athlete ID from the onboarding data structure
            $athleteId = null;
            if (is_array($onboardingData) && isset($onboardingData['athlete_user_id'])) {
                $athleteId = $onboardingData['athlete_user_id'];
            } elseif (is_object($onboardingData) && isset($onboardingData->athlete_user_id)) {
                $athleteId = $onboardingData->athlete_user_id;
            }

            if (!$athleteId) {
                Log::warning('No athlete_user_id found in onboarding data', [
                    'onboarding_id' => $onboarding->id,
                    'parent_id' => $parent->id,
                    'onboarding_data' => $onboardingData,
                ]);
                return;
            }

            // Verify the athlete exists
            $athlete = User::query()->find($athleteId);
            if (!$athlete) {
                Log::warning('Athlete not found with provided ID', [
                    'athlete_id' => $athleteId,
                    'parent_id' => $parent->id,
                    'onboarding_id' => $onboarding->id,
                ]);
                return;
            }

            // Check if athlete already has a parent assigned
            if ($athlete->parent_id) {
                Log::warning('Athlete already has a parent assigned', [
                    'athlete_id' => $athleteId,
                    'existing_parent_id' => $athlete->parent_id,
                    'new_parent_id' => $parent->id,
                    'onboarding_id' => $onboarding->id,
                ]);
                return;
            }

            // Update the athlete's parent_id
            $athlete->update(['parent_id' => $parent->id]);

            Log::info('Successfully linked parent to athlete', [
                'parent_id' => $parent->id,
                'athlete_id' => $athleteId,
                'parent_email' => $parent->email,
                'athlete_email' => $athlete->email,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to link parent to athlete', [
                'error' => $e->getMessage(),
                'parent_id' => $parent->id,
                'onboarding_id' => $onboarding->id,
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }
}
