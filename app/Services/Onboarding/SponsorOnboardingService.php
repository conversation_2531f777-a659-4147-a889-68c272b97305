<?php

namespace App\Services\Onboarding;

use App\Data\Onboarding\Sponsor\AccountInfoDTO;
use App\Data\Onboarding\Sponsor\OrganizationInfoDTO;
use App\Enums\ProfileType;
use App\Models\Onboarding;
use App\Models\Organization;
use App\Models\User;
use App\Services\Organization\OrganizationSearchService;
use App\States\Onboarding\States\AccountInfo;
use App\States\Onboarding\States\Completed;
use App\States\Onboarding\States\OrganizationInfo;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class SponsorOnboardingService
{
    public function __construct(
        private readonly OrganizationSearchService $organizationSearchService
    ) {
    }

    /**
     * Save account information for the sponsor onboarding
     */
    public function saveAccountInfo(Onboarding $onboarding, AccountInfoDTO $data): Onboarding
    {
        // Convert camelCase DTO properties to snake_case for compatibility with database fields
        $normalizedData = [
            'first_name' => $data->firstName,
            'last_name' => $data->lastName,
            'email' => $data->email,
            'phone' => $data->phone,
            'password' => $data->password,
        ];

        // Store the normalized data
        $onboarding->updateStepData(AccountInfo::identifier(), $normalizedData);

        // Don't transition to Completed state yet - let the controller handle this
        // so we can check for existing users first

        return $onboarding;
    }

    /**
     * Save organization information for the sponsor onboarding
     */
    public function saveOrganizationInfo(Onboarding $onboarding, OrganizationInfoDTO $data): Onboarding
    {
        // Store everything except the file upload which is handled separately in the controller
        $onboarding->updateStepData(OrganizationInfo::identifier(), $data->except('organizationLogo')->toArray());
        return $onboarding;
    }

    /**
     * Handle the logo upload for the organization info step
     *
     * @param Onboarding $onboarding The onboarding record
     * @param \Illuminate\Http\UploadedFile|null $logoFile The uploaded logo file
     * @return void
     */
    public function handleLogoUpload(Onboarding $onboarding, ?\Illuminate\Http\UploadedFile $logoFile): void
    {
        if (!$logoFile) {
            return;
        }

        try {
            // Remove any existing logos first
            $onboarding->clearMediaCollection('temp_logos');

            // Add the new logo
            $onboarding->addMedia($logoFile)
                ->usingFileName(uniqid() . '.' . $logoFile->extension())
                ->toMediaCollection('temp_logos');
        } catch (\Exception $e) {
            Log::error('Failed to upload organization logo', [
                'error' => $e->getMessage(),
                'onboarding_id' => $onboarding->id
            ]);
        }
    }

    /**
     * Complete the onboarding process for a sponsor
     *
     * @throws \Exception If user creation or organization association fails
     */
    public function completeOnboarding(Onboarding $onboarding): ?User
    {
        // Get data from onboarding steps
        $accountData = $onboarding->getStepData(AccountInfo::identifier());

        // Get organization ID directly from onboarding data
        $organizationId = $onboarding->data['organization_id'] ?? null;

        if (!$accountData) {
            throw new \Exception("Account information is missing");
        }

        if (!$organizationId) {
            throw new \Exception("Organization ID is missing from onboarding data");
        }

        // Check if a user with this email already exists
        $existingUser = User::query()->where('email', $accountData['email'])->first();
        if ($existingUser) {
            // Return null to indicate existing user instead of throwing an exception
            return null;
        }

        // Try to get the organization by ID
        $organization = Organization::query()->find($organizationId);
        if (!$organization) {
            throw new \Exception("Could not find the organization with ID: {$organizationId}");
        }

        // Create the user - using snake_case field names that match our normalized data
        $user = User::query()->create([
            'email' => $accountData['email'],
            'password' => $accountData['password'],
            'first_name' => $accountData['first_name'],
            'last_name' => $accountData['last_name'],
            'phone' => $accountData['phone'],
            'profile_type' => ProfileType::SPONSOR,
            'onboarding_completed_at' => now(),
        ]);

        // Associate the user with the organization
        $user->organizations()->attach($organization->id, [
            'role' => 'member', // Default to member role
            'metadata' => json_encode([
                'joined_at' => now()->toDateTimeString(),
                'source' => 'onboarding',
                'is_founding_member' => false,
            ]),
        ]);

        return $user;
    }

    /**
     * Transfer the logo from onboarding to the user's organization
     *
     * @param Onboarding $onboarding The onboarding model with logo
     * @param User $user The user to get the organization from
     * @return bool True if logo was successfully transferred, false otherwise
     */
    public function transferLogoToOrganization(Onboarding $onboarding, User $user): bool
    {
        try {
            if (!$onboarding->hasMedia('temp_logos') || !$user->organizations()->exists()) {
                return false;
            }

            $organization = $user->organizations()->first();
            if (!$organization) {
                return false;
            }

            $logo = $onboarding->getFirstMedia('temp_logos');
            if (!$logo) {
                return false;
            }

            $logoPath = $logo->getPath();

            // Add the logo to the organization
            $organization->addMedia($logoPath)
                ->usingName($logo->name)
                ->usingFileName($logo->file_name)
                ->toMediaCollection('logo');

            // Clear the temporary logo after successful transfer
            $onboarding->clearMediaCollection('temp_logos');

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to transfer logo from onboarding to organization', [
                'error' => $e->getMessage(),
                'onboarding_id' => $onboarding->id,
            ]);
            return false;
        }
    }

}
