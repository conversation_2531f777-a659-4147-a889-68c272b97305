<?php

namespace App\Services\Onboarding;

use App\Data\Onboarding\AthleticsDirector\DetailsDTO;
use App\Enums\ProfileType;
use App\Enums\RegistrationMethod;
use App\Models\AthleticsDirectorProfile;
use App\Models\SystemInvite;
use App\Models\User;
use App\States\Onboarding\States\AccountInfo;
use App\States\Onboarding\States\Bio;
use App\States\Onboarding\States\CommunityInvolvement;
use App\States\Onboarding\States\Details;
use App\States\Onboarding\States\SchoolSuccesses;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AthleticsDirectorOnboardingService
{
    /**
     * Process the details submission step, including handling the profile photo upload
     *
     * @param SystemInvite $invite
     * @param DetailsDTO $data
     * @return array
     */
    public function processDetails(SystemInvite $invite, DetailsDTO $data): array
    {
        $stepData = $data->toArray();

        // Remove the profile_photo from the array data since we'll store it separately
        if (isset($stepData['profile_photo'])) {
            unset($stepData['profile_photo']);
        }

        // Handle profile photo upload if present
        if ($data->profile_photo) {
            // Clear any existing avatar
            $invite->clearMediaCollection('avatar');

            // Add the new avatar
            $invite->addMedia($data->profile_photo)
                ->withResponsiveImages()
                ->toMediaCollection('avatar');
        }

        return $stepData;
    }

    /**
     * Complete the onboarding process and create a user account
     *
     * @param SystemInvite $invite The system invite
     * @return User The created user
     * @throws \Exception
     */
    public function completeOnboarding(SystemInvite $invite): User
    {
        $onboarding = $invite->onboarding;

        // Retrieve step data
        $accountInfoData = $onboarding->getStepData(AccountInfo::identifier());
        $detailsData = $onboarding->getStepData(Details::identifier());
        $bioData = $onboarding->getStepData(Bio::identifier());
        $communityInvolvementData = $onboarding->getStepData(CommunityInvolvement::identifier());
        $schoolSuccessesData = $onboarding->getStepData(SchoolSuccesses::identifier());

        // Check if user already exists
        $existingUser = User::query()->where('email', $accountInfoData['email'])->first();
        if ($existingUser) {
            return $existingUser;
        }

        // Begin database transaction
        return DB::transaction(function () use ($invite, $accountInfoData, $detailsData, $bioData, $communityInvolvementData, $schoolSuccessesData) {
            // Create user data
            $userData = [
                // Required fields from account info
                'first_name' => $accountInfoData['first_name'],
                'last_name' => $accountInfoData['last_name'],
                'email' => $accountInfoData['email'],
                'notification_email' => $accountInfoData['notification_email'] ?? null,
                'password' => $accountInfoData['password'],
                'phone' => $accountInfoData['phone'] ?? null,
                'profile_type' => ProfileType::ATHLETICS_DIRECTOR->value,
                'email_verified_at' => now(),
                'registration_method' => RegistrationMethod::SystemInvite->value,
            ];

            // Add details data if available
            if ($detailsData) {
                $userData = array_merge($userData, [
                    'state_code' => $detailsData['state'] ?? null,
                    'county' => $detailsData['county'] ?? null,
                    'school_id' => $detailsData['school_id'] ?? null,
                    'title' => $detailsData['title'] ?? null,
                    // Social media links
                    'twitter' => $detailsData['twitter'] ?? null,
                    'instagram' => $detailsData['instagram'] ?? null,
                    'facebook' => $detailsData['facebook'] ?? null,
                    'hudl' => $detailsData['hudl'] ?? null,
                    'custom_link' => $detailsData['custom_link'] ?? null,
                ]);

                // Denormalize county_id and region_id from the school
                if (!empty($detailsData['school_id'])) {
                    $school = \App\Models\School::find($detailsData['school_id']);
                    if ($school) {
                        $userData['county_id'] = $school->county_id;
                        $userData['region_id'] = $school->region_id;
                    }
                }
            }

            // Add bio if available
            if ($bioData && isset($bioData['content'])) {
                $userData['content'] = $bioData['content'];
            }

            // Create the user
            $user = User::query()->create($userData);

            // Assign the athletics director role
            $user->assignRole('user');

            // Create athletics director profile if school_id is provided
            if (!empty($userData['school_id'])) {
                AthleticsDirectorProfile::create([
                    'user_id' => $user->id,
                    'school_id' => $userData['school_id'],
                    'is_verified' => true, // Auto-verify since they're invited by admins
                    'verified_at' => now(),
                    'verified_by' => null, // Could be set to the admin who created the invite if available
                ]);
            }

            // Transfer avatar from system invite to user
            $this->transferAvatarToUser($invite, $user);

            // Process community involvement data if available
            if ($communityInvolvementData && isset($communityInvolvementData['items']) && count($communityInvolvementData['items']) > 0) {
                foreach ($communityInvolvementData['items'] as $item) {
                    $user->communityInvolvements()->create([
                        'title' => $item['name'],
                        'date_range' => $item['date_range'],
                        'description' => $item['description'],
                        'order' => $item['order'] ?? 0,
                    ]);
                }
            }

            // Process school successes data if available
            if ($schoolSuccessesData && isset($schoolSuccessesData['successes']) && count($schoolSuccessesData['successes']) > 0) {
                foreach ($schoolSuccessesData['successes'] as $success) {
                    $user->workExperiences()->create([
                        'name' => $success['name'],
                        'date' => $success['date_range'],
                        'description' => $success['description'],
                        'order' => $success['order'] ?? 0,
                    ]);
                }
            }

            return $user;
        });
    }

    /**
     * Transfer avatar from system invite to user
     *
     * @param SystemInvite $invite The system invite
     * @param User $user The user
     * @return void
     */
    private function transferAvatarToUser(SystemInvite $invite, User $user): void
    {
        $avatar = $invite->getFirstMedia('avatar');

        if ($avatar) {
            // Clear any existing avatar for the user
            $user->clearMediaCollection('avatar');

            // Copy the avatar to the user
            $avatar->copy($user, 'avatar');
        }
    }
}
