<?php

namespace App\Services;

use App\Models\User;

class SportService
{
    public function attachUserSports(User $user, array $data): void
    {
        // Handle system sports
        $systemSports = collect($data['sports'])
            ->filter(fn ($sport) => !$sport['is_custom'])
            ->mapWithKeys(fn ($sport) => [
                $sport['id'] => ['order' => $sport['order']]
            ]);

        $user->sports()->attach($systemSports);

        // Handle custom sports
        $customSports = collect($data['sports'])
            ->filter(fn ($sport) => $sport['is_custom']);

        foreach ($customSports as $sport) {
            $user->customSports()->create([
                'name' => $sport['name'],
                'order' => $sport['order']
            ]);
        }
    }
}
