<?php

namespace App\Services\Nomination;

use App\Data\Nomination\NominationData;
use App\Data\Nomination\UserNominationData;
use App\Enums\NominationType;
use App\Models\Nomination;
use App\Models\User;
use App\Services\Invite\SystemInviteService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Spatie\LaravelData\DataCollection;

class NominationService
{
    public function __construct(
        private readonly SystemInviteService $systemInviteService
    ) {}

    public function handleSingleNomination(NominationData $data, bool $createSystemInvite = true): Nomination
    {
        return DB::transaction(function () use ($data, $createSystemInvite) {
            $nomination = $this->createNomination($data);

            if ($createSystemInvite) {
                match ($data->type) {
                    NominationType::ATHLETE => $this->systemInviteService->createForPositiveAthlete($nomination),
                    NominationType::COACH => $this->systemInviteService->createForPositiveCoach($nomination),
                };
            }

            return $nomination;
        });
    }

    public function handleBulkNomination(array $nominations, bool $createSystemInvite = true): Collection
    {
        return DB::transaction(function () use ($nominations, $createSystemInvite) {
            return collect($nominations)->map(function (NominationData $data) use ($createSystemInvite) {
                $nomination = $this->createNomination($data);

                if ($createSystemInvite) {
                    match ($data->type) {
                        NominationType::ATHLETE => $this->systemInviteService->createForPositiveAthlete($nomination),
                        NominationType::COACH => $this->systemInviteService->createForPositiveCoach($nomination),
                    };
                }

                return $nomination;
            });
        });
    }

    /**
     * Get nominations for a user
     */
    public function getUserNominations(User $user): DataCollection
    {
        // Get nominations for the user by email
        $nominations = Nomination::query()
            ->where('email', $user->email)
            ->orderBy('created_at', 'desc')
            ->get();

        // Transform nominations to UserNominationData
        $userNominations = $nominations->map(function (Nomination $nomination) {
            return UserNominationData::fromNomination($nomination);
        });

        return new DataCollection(UserNominationData::class, $userNominations->all());
    }

    private function createNomination(NominationData $data): Nomination
    {
        $nomination = Nomination::query()->create([
            'email' => $data->email,
            'first_name' => $data->first_name,
            'last_name' => $data->last_name,
            'nominator_email' => $data->nominator_email,
            'nominator_first_name' => $data->nominator_first_name,
            'nominator_last_name' => $data->nominator_last_name,
            'school_name' => $data->school_name,
            'school_id' => $data->school_id,
            'sport' => $data->sport,
            'relationship' => $data->relationship,
            'note' => $data->note,
            'type' => $data->type->value,

            // Location Information
            'state_code' => $data->state_code,
            'county' => $data->county,
            'location_resolution_notes' => $data->location_resolution_notes,

            // Enhanced Contact Information
            'nominee_phone' => $data->nominee_phone,
            'nominator_phone' => $data->nominator_phone,

            // Multiple Sports Support
            'sport_2' => $data->sport_2,
            'sport_3' => $data->sport_3,
            'other_sport' => $data->other_sport,

            // Demographic & Academic Information
            'gender' => $data->gender,
            'grade' => $data->grade,

            // Parent/Guardian Information
            'parent_guardian_first_name' => $data->parent_guardian_first_name,
            'parent_guardian_last_name' => $data->parent_guardian_last_name,
            'parent_guardian_email' => $data->parent_guardian_email,
            'parent_guardian_phone' => $data->parent_guardian_phone,

            // Social Media & Digital Presence
            'instagram_handle' => $data->instagram_handle,
            'twitter_handle' => $data->twitter_handle,

            // Marketing & Attribution Data
            'how_did_you_hear' => $data->how_did_you_hear,
            'referral_source_name' => $data->referral_source_name,

            // Processing Workflow Enhancement
            'processing_status' => $data->processing_status ?? 'received',

            // JotForm Integration Metadata
            'jotform_submission_id' => $data->jotform_submission_id,
            'jotform_form_id' => $data->jotform_form_id,
        ]);

        return $nomination;
    }
}
