<?php

namespace App\Services;

use Illuminate\Support\Collection;
use App\Data\Nomination\NominationData;
use App\Enums\NominationType;
use App\Models\Nomination;

/**
 * JotForm Field Mapping Service
 *
 * Maps JotForm webhook data to our nomination schema by removing
 * field ID prefixes and using descriptive field suffixes.
 */
class JotFormFieldMappingService
{
    /**
     * Field mapping configuration
     * Maps JotForm field suffixes to our nomination field names
     */
    protected array $fieldMap = [
        // Nominee Information
        'nominatedPerson' => 'nominee_name',
        'nomineeEmail' => 'nominee_email',
        'contactPhone148' => 'nominee_phone',
        'gender' => 'nominee_gender',
        'myNominee' => 'nominee_type',
        'gradeif73' => 'nominee_grade',

        // Location - handled by resolveLocationData()
        'state88' => 'state',
        'county' => 'county',

        // School
        'nameOf84' => 'school_name',
        'otherSchool' => 'other_school',

        // Sports
        'sport179' => 'sport_1',
        'sport280' => 'sport_2',
        'sport381' => 'sport_3',
        'enterName82' => 'other_sport',

        // Social Media
        'instagramHandle' => 'instagram_handle',
        'twitterxHandle' => 'twitter_handle',

        // Parent/Guardian
        'fullName60' => 'parent_name',
        'parentguardianEmail' => 'parent_email',
        'parentguardianPhone' => 'parent_phone',

        // Nominator
        'fullName51' => 'nominator_name',
        'nominatorEmail' => 'nominator_email',
        'nominatorPhone149' => 'nominator_phone',
        'relationshipTo92' => 'relationship_to_nominee',

        // Nomination Details
        'whyIs' => 'nomination_reason',
        'howDid' => 'how_heard_about_us',
        'nameOf' => 'referral_source_name',
    ];

    /**
     * State abbreviation to county field suffix mapping
     */
    protected array $stateCountyFields = [
        'AK' => 'akCounty',
        'AL' => 'alCounty',
        'AR' => 'arCounty',
        'AZ' => 'azCounty',
        'CA' => 'caCounty',
        'CO' => 'coCounty',
        'CT' => 'ctCounty',
        'DC' => 'dcCounty',
        'DE' => 'deCounty',
        'FL' => 'flCounty',
        'GA' => 'gaCounty',
        'HI' => 'hiCounty',
        'IA' => 'iaCounty',
        'ID' => 'idCounty',
        'IL' => 'ilCounty',
        'IN' => 'inCounty',
        'KS' => 'ksCounty',
        'KY' => 'kyCounty',
        'LA' => 'laCounty',
        'MA' => 'maCounty',
        'MD' => 'mdCounty',
        'ME' => 'meCounty',
        'MI' => 'miCounty',
        'MN' => 'mnCounty',
        'MO' => 'moCounty',
        'MS' => 'msCounty',
        'MT' => 'mtCounty',
        'NC' => 'ncCounty',
        'ND' => 'ndCounty',
        'NE' => 'neCounty',
        'NH' => 'nhCounty',
        'NJ' => 'njCounty',
        'NM' => 'nmCounty',
        'NV' => 'nvCounty',
        'NY' => 'nyCounty',
        'OH' => 'ohCounty',
        'OK' => 'okCounty',
        'OR' => 'orCounty',
        'PA' => 'paCounty',
        'RI' => 'riCounty',
        'SC' => 'scCounty',
        'SD' => 'sdCounty',
        'TN' => 'tnCounty',
        'TX' => 'txCounty',
        'UT' => 'utCounty',
        'VA' => 'vaCounty',
        'VT' => 'vtCounty',
        'WA' => 'waCounty',
        'WI' => 'wiCounty',
        'WV' => 'wvCounty',
        'WY' => 'wyCounty',
    ];

    /**
     * Transform JotForm webhook data to our nomination structure
     *
     * @param array $webhookData
     * @return array
     */
    public function transformWebhookData(array $webhookData): array
    {
        // Extract form data from rawRequest
        $rawRequest = json_decode($webhookData['rawRequest'] ?? '{}', true);

        // Remove prefixes and map fields
        $mappedData = $this->mapFields($rawRequest);

        // Resolve location data with smart county detection
        $locationData = $this->resolveLocationData($rawRequest);
        $mappedData = array_merge($mappedData, $locationData);

        // Add metadata
        $mappedData['jotform_submission_id'] = $webhookData['submissionID'] ?? null;
        $mappedData['jotform_form_id'] = $webhookData['formID'] ?? null;
        $mappedData['submitted_at'] = now();

        return $mappedData;
    }

    /**
     * Map JotForm fields by removing prefixes and using field mapping
     *
     * @param array $rawFormData
     * @return array
     */
    protected function mapFields(array $rawFormData): array
    {
        $mapped = [];

        foreach ($rawFormData as $jotformField => $value) {
            // Remove prefix (everything before and including underscore)
            $fieldSuffix = $this->removeFieldPrefix($jotformField);

            // Skip non-form fields
            if ($this->isMetadataField($fieldSuffix)) {
                continue;
            }

            // Map to our schema if mapping exists
            if (isset($this->fieldMap[$fieldSuffix])) {
                $ourField = $this->fieldMap[$fieldSuffix];
                $mapped[$ourField] = $this->transformFieldValue($fieldSuffix, $value);
            }
        }

        return $mapped;
    }

    /**
     * Remove JotForm field prefix (e.g., "q90_nominatedPerson" -> "nominatedPerson")
     *
     * @param string $field
     * @return string
     */
    protected function removeFieldPrefix(string $field): string
    {
        // Remove everything up to and including the first underscore
        if (strpos($field, '_') !== false) {
            return substr($field, strpos($field, '_') + 1);
        }

        return $field;
    }

    /**
     * Check if field is metadata (not actual form data)
     *
     * @param string $field
     * @return bool
     */
    protected function isMetadataField(string $field): bool
    {
        $metadataFields = [
            'slug', 'jsExecutionTracker', 'submitSource', 'buildDate',
            'uploadServerUrl', 'eventObserver', 'event_id', 'timeToSubmit',
            'validatedNewRequiredFieldIDs', 'path'
        ];

        return in_array($field, $metadataFields);
    }

    /**
     * Transform field values based on field type
     *
     * @param string $fieldSuffix
     * @param mixed $value
     * @return mixed
     */
    protected function transformFieldValue(string $fieldSuffix, $value)
    {
        // Handle name fields (combine first/last)
        if (in_array($fieldSuffix, ['nominatedPerson', 'fullName60', 'fullName51']) && is_array($value)) {
            return trim(($value['first'] ?? '') . ' ' . ($value['last'] ?? ''));
        }

        // Handle phone fields (extract full number or construct from parts)
        if (stripos($fieldSuffix, 'Phone') !== false && is_array($value)) {
            // First, try to get the full phone number
            if (!empty($value['full'])) {
                return $value['full'];
            }

            // If 'full' key is missing, construct from available parts
            $phoneParts = [];

            // Check for area code
            if (!empty($value['area'])) {
                $phoneParts[] = $value['area'];
            }

            // Check for phone number
            if (!empty($value['phone'])) {
                $phoneParts[] = $value['phone'];
            }

            // Check for other common phone field parts
            if (!empty($value['number'])) {
                $phoneParts[] = $value['number'];
            }

            // If we have parts, join them with appropriate formatting
            if (!empty($phoneParts)) {
                // Format as (area) phone if we have both parts
                if (count($phoneParts) >= 2 && !empty($value['area']) && !empty($value['phone'])) {
                    return '(' . $value['area'] . ') ' . $value['phone'];
                }
                // Otherwise, just join with spaces
                return implode(' ', $phoneParts);
            }

            // If no usable phone data, return null
            return null;
        }

        // Handle empty strings
        if (is_string($value) && trim($value) === '') {
            return null;
        }

        return $value;
    }

    /**
     * Test the mapping with sample data
     *
     * @param array $sampleRawRequest
     * @return array
     */
    public function testMapping(array $sampleRawRequest): array
    {
        $result = [
            'original_fields' => [],
            'mapped_fields' => [],
            'unmapped_fields' => []
        ];

        foreach ($sampleRawRequest as $jotformField => $value) {
            $fieldSuffix = $this->removeFieldPrefix($jotformField);

            if ($this->isMetadataField($fieldSuffix)) {
                continue;
            }

            $result['original_fields'][$jotformField] = $fieldSuffix;

            if (isset($this->fieldMap[$fieldSuffix])) {
                $ourField = $this->fieldMap[$fieldSuffix];
                $transformedValue = $this->transformFieldValue($fieldSuffix, $value);
                $result['mapped_fields'][$ourField] = $transformedValue;
            } else {
                $result['unmapped_fields'][$fieldSuffix] = $value;
            }
        }

        return $result;
    }

    /**
     * Smart location data resolution
     * Handles state-specific county fields and validates consistency
     *
     * @param array $rawFormData
     * @return array
     */
    protected function resolveLocationData(array $rawFormData): array
    {
        $result = [
            'state' => null,
            'county' => null,
            'location_resolution_notes' => []
        ];

        // Extract state
        $state = null;
        foreach ($rawFormData as $field => $value) {
            $suffix = $this->removeFieldPrefix($field);
            if ($suffix === 'state88' && !empty($value)) {
                $state = trim($value);
                break;
            }
        }

        if (!$state) {
            $result['location_resolution_notes'][] = 'No state found in form data';
            return $result;
        }

        $result['state'] = $state;

        // Find county using multiple methods
        $countyMethods = [
            'state_specific' => $this->getStateSpecificCounty($rawFormData, $state),
            'general_county' => $this->getGeneralCounty($rawFormData),
        ];

        // Remove empty results
        $countyMethods = array_filter($countyMethods, fn($county) => !empty($county));

        if (empty($countyMethods)) {
            $result['location_resolution_notes'][] = 'No county data found';
            return $result;
        }

        // Check for consistency
        $uniqueCounties = array_unique(array_values($countyMethods));

        if (count($uniqueCounties) === 1) {
            // All methods agree
            $result['county'] = $uniqueCounties[0];
            $result['location_resolution_notes'][] = 'County resolved consistently: ' . implode(', ', array_keys($countyMethods));
        } else {
            // Inconsistent data - prioritize state-specific field
            if (isset($countyMethods['state_specific'])) {
                $result['county'] = $countyMethods['state_specific'];
                $result['location_resolution_notes'][] = 'Used state-specific county field due to inconsistency';
                $result['location_resolution_notes'][] = 'Conflicting values: ' . json_encode($countyMethods);
            } else {
                $result['county'] = $countyMethods['general_county'];
                $result['location_resolution_notes'][] = 'Used general county field';
            }
        }

        return $result;
    }

    /**
     * Get county from state-specific field
     *
     * @param array $rawFormData
     * @param string $state
     * @return string|null
     */
    protected function getStateSpecificCounty(array $rawFormData, string $state): ?string
    {
        if (!isset($this->stateCountyFields[$state])) {
            return null;
        }

        $countyFieldSuffix = $this->stateCountyFields[$state];

        foreach ($rawFormData as $field => $value) {
            $suffix = $this->removeFieldPrefix($field);
            if ($suffix === $countyFieldSuffix && !empty(trim($value))) {
                return trim($value);
            }
        }

        return null;
    }

    /**
     * Get county from general county field
     *
     * @param array $rawFormData
     * @return string|null
     */
    protected function getGeneralCounty(array $rawFormData): ?string
    {
        foreach ($rawFormData as $field => $value) {
            $suffix = $this->removeFieldPrefix($field);
            if ($suffix === 'county' && !empty(trim($value))) {
                return trim($value);
            }
        }

        return null;
    }

    /**
     * Extract and transform JotForm webhook data to NominationData format
     *
     * @param array $webhookData
     * @return array Structured data ready for NominationData::from()
     */
    public function extractNominationData(array $webhookData): array
    {
        // Extract form data from rawRequest
        $rawRequest = json_decode($webhookData['rawRequest'] ?? '{}', true);

        if (empty($rawRequest)) {
            throw new \InvalidArgumentException('Invalid or missing rawRequest data in webhook');
        }

        // Transform and map fields
        $mappedData = $this->mapFields($rawRequest);

        // Resolve location data
        $locationData = $this->resolveLocationData($rawRequest);
        $mappedData = array_merge($mappedData, $locationData);

        // Extract nominee name components
        $nomineeName = $this->extractNameComponents($rawRequest, 'nominatedPerson');

        // Extract nominator name components
        $nominatorName = $this->extractNameComponents($rawRequest, 'fullName51');

        // Map to NominationData structure
        $nominationData = [
            // Nominee information
            'email' => $mappedData['nominee_email'] ?? null,
            'first_name' => $nomineeName['first'] ?? null,
            'last_name' => $nomineeName['last'] ?? null,

            // Nominator information
            'nominator_email' => $mappedData['nominator_email'] ?? null,
            'nominator_first_name' => $nominatorName['first'] ?? null,
            'nominator_last_name' => $nominatorName['last'] ?? null,

            // School information
            'school_name' => $this->resolveSchoolName($mappedData),
            'school_id' => null, // Will be resolved later if needed

            // Sport information
            'sport' => $this->resolvePrimarySport($mappedData),
            'sport_2' => $mappedData['sport_2'] ?? null,
            'sport_3' => $mappedData['sport_3'] ?? null,
            'other_sport' => $mappedData['other_sport'] ?? null,

            // Basic nomination metadata
            'relationship' => $mappedData['relationship_to_nominee'] ?? null,
            'note' => $mappedData['nomination_reason'] ?? null,
            'type' => $this->resolveNominationType($mappedData['nominee_type'] ?? null),

            // Location Information
            'state_code' => $locationData['state'] ?? null,
            'county' => $locationData['county'] ?? null,
            'location_resolution_notes' => $locationData['location_resolution_notes'] ?? null,

            // Enhanced Contact Information
            'nominee_phone' => $mappedData['nominee_phone'] ?? null,
            'nominator_phone' => $mappedData['nominator_phone'] ?? null,

            // Demographic & Academic Information
            'gender' => $mappedData['nominee_gender'] ?? null,
            'grade' => $mappedData['nominee_grade'] ?? null,

            // Parent/Guardian Information
            'parent_guardian_first_name' => $this->extractParentName($mappedData)['first'] ?? null,
            'parent_guardian_last_name' => $this->extractParentName($mappedData)['last'] ?? null,
            'parent_guardian_email' => $mappedData['parent_email'] ?? null,
            'parent_guardian_phone' => $mappedData['parent_phone'] ?? null,

            // Social Media & Digital Presence
            'instagram_handle' => $mappedData['instagram_handle'] ?? null,
            'twitter_handle' => $mappedData['twitter_handle'] ?? null,

            // Marketing & Attribution Data
            'how_did_you_hear' => $mappedData['how_heard_about_us'] ?? null,
            'referral_source_name' => $mappedData['referral_source_name'] ?? null,

            // Processing Workflow Enhancement
            'processing_status' => 'received', // Always start as received

            // Metadata for tracking
            'jotform_submission_id' => $webhookData['submissionID'] ?? null,
            'jotform_form_id' => $webhookData['formID'] ?? null,
        ];

        // Clean up null values and validate required fields
        return $this->validateAndCleanNominationData($nominationData);
    }

    /**
     * Extract name components from JotForm name field
     *
     * @param array $rawRequest
     * @param string $fieldSuffix
     * @return array
     */
    protected function extractNameComponents(array $rawRequest, string $fieldSuffix): array
    {
        foreach ($rawRequest as $field => $value) {
            $suffix = $this->removeFieldPrefix($field);
            if ($suffix === $fieldSuffix && is_array($value)) {
                return [
                    'first' => trim($value['first'] ?? ''),
                    'last' => trim($value['last'] ?? '')
                ];
            }
        }

        return ['first' => null, 'last' => null];
    }

    /**
     * Extract parent/guardian name from mapped data
     *
     * @param array $mappedData
     * @return array
     */
    protected function extractParentName(array $mappedData): array
    {
        $parentName = $mappedData['parent_name'] ?? null;

        if (empty($parentName)) {
            return ['first' => null, 'last' => null];
        }

        // If it's already an array with first/last, return it
        if (is_array($parentName)) {
            return [
                'first' => trim($parentName['first'] ?? ''),
                'last' => trim($parentName['last'] ?? '')
            ];
        }

        // If it's a string, try to split it
        $parts = explode(' ', trim($parentName), 2);
        return [
            'first' => $parts[0] ?? null,
            'last' => $parts[1] ?? null
        ];
    }

    /**
     * Resolve school name from multiple possible fields
     *
     * @param array $mappedData
     * @return string|null
     */
    protected function resolveSchoolName(array $mappedData): ?string
    {
        // Priority: school_name, then other_school
        $schoolName = $mappedData['school_name'] ?? null;

        if (empty($schoolName)) {
            $schoolName = $mappedData['other_school'] ?? null;
        }

        return !empty(trim($schoolName)) ? trim($schoolName) : null;
    }

    /**
     * Resolve primary sport from multiple sport fields
     *
     * @param array $mappedData
     * @return string|null
     */
    protected function resolvePrimarySport(array $mappedData): ?string
    {
        // Priority: sport_1, sport_2, sport_3, other_sport
        $sports = [
            $mappedData['sport_1'] ?? null,
            $mappedData['sport_2'] ?? null,
            $mappedData['sport_3'] ?? null,
            $mappedData['other_sport'] ?? null,
        ];

        foreach ($sports as $sport) {
            if (!empty($sport) && trim($sport) !== '') {
                return trim($sport);
            }
        }

        return null;
    }

    /**
     * Resolve nomination type from form data
     *
     * @param string|null $nomineeType
     * @return NominationType
     */
    protected function resolveNominationType(?string $nomineeType): NominationType
    {
        if (empty($nomineeType)) {
            return NominationType::ATHLETE; // Default assumption
        }

        $type = strtolower(trim($nomineeType));

        return match ($type) {
            'coach' => NominationType::COACH,
            'athlete' => NominationType::ATHLETE,
            default => NominationType::ATHLETE, // Default fallback
        };
    }

    /**
     * Validate and clean nomination data before creation
     *
     * @param array $nominationData
     * @return array
     * @throws \InvalidArgumentException
     */
    protected function validateAndCleanNominationData(array $nominationData): array
    {
        // Required fields validation
        $requiredFields = [
            'email' => 'Nominee email',
            'first_name' => 'Nominee first name',
            'last_name' => 'Nominee last name',
            'nominator_email' => 'Nominator email',
            'nominator_first_name' => 'Nominator first name',
            'nominator_last_name' => 'Nominator last name',
            'school_name' => 'School name',
            'sport' => 'Sport',
            'relationship' => 'Relationship to nominee',
        ];

        $missingFields = [];

        foreach ($requiredFields as $field => $label) {
            if (empty($nominationData[$field])) {
                $missingFields[] = $label;
            }
        }

        if (!empty($missingFields)) {
            throw new \InvalidArgumentException(
                'Missing required nomination fields: ' . implode(', ', $missingFields)
            );
        }

        // Email validation
        if (!filter_var($nominationData['email'], FILTER_VALIDATE_EMAIL)) {
            throw new \InvalidArgumentException('Invalid nominee email address');
        }

        if (!filter_var($nominationData['nominator_email'], FILTER_VALIDATE_EMAIL)) {
            throw new \InvalidArgumentException('Invalid nominator email address');
        }

        // Remove null values to avoid issues with data validation
        return array_filter($nominationData, fn($value) => $value !== null);
    }

    /**
     * Check if a nomination already exists for this nominee/nominator combination
     *
     * @param array $nominationData
     * @return Nomination|null Returns existing nomination if found, null otherwise
     */
    public function checkForDuplicateNomination(array $nominationData): ?Nomination
    {
        return Nomination::query()
            ->where('email', $nominationData['email'])
            ->where('nominator_email', $nominationData['nominator_email'])
            ->first();
    }
}
