<?php

namespace App\Services;

use App\Enums\ProfileType;
use App\Models\User;
use App\Repositories\ParentAccountRepository;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class PositiveAthleteAccountService
{
    public function __construct(
        private readonly ParentAccountRepository $parentRepository
    ) {}

    /**
     * Get all parent accounts linked to an athlete.
     *
     * @param User $athlete The athlete user
     * @return \Illuminate\Support\Collection The collection of parent users and pending invites
     */
    public function getParentsForAthlete(User $athlete): \Illuminate\Support\Collection
    {
        $this->validateAthleteUser($athlete);
        return $this->parentRepository->getForAthlete($athlete->id);
    }

    /**
     * Link a parent account to an athlete.
     * Either links an existing parent user or creates a system invite.
     *
     * @param User $athlete The athlete user
     * @param array $parentData The parent data (first_name, last_name, email, phone)
     * @return mixed The parent user or invite representation
     */
    public function linkParentToAthlete(User $athlete, array $parentData)
    {
        $this->validateAthlete<PERSON>ser($athlete);
        return $this->parentRepository->linkParentToAthlete($athlete, $parentData);
    }

    /**
     * Unlink a parent from an athlete by email.
     * Handles both linked parents and pending invites.
     *
     * @param User $athlete The athlete user
     * @param string $parentEmail The parent email
     * @return bool Whether the unlink was successful
     */
    public function unlinkParent(User $athlete, string $parentEmail): bool
    {
        $this->validateAthleteUser($athlete);

        $result = $this->parentRepository->unlinkParent($athlete->id, $parentEmail);

        if ($result) {
            Log::info('Parent account unlinked from athlete', [
                'athlete_id' => $athlete->id,
                'parent_email' => $parentEmail,
            ]);
        } else {
            Log::warning('Failed to unlink parent account from athlete', [
                'athlete_id' => $athlete->id,
                'parent_email' => $parentEmail,
            ]);
        }

        return $result;
    }

    /**
     * Validate that the user is an athlete.
     *
     * @param User $user The user to validate
     * @throws ValidationException If the user is not an athlete
     */
    private function validateAthleteUser(User $user): void
    {
        if ($user->profile_type !== ProfileType::POSITIVE_ATHLETE) {
            throw ValidationException::withMessages([
                'user' => ['Only athlete accounts can manage parent connections.'],
            ]);
        }
    }
}
