<?php

namespace App\Services;

use App\Data\Sponsor\CreateOpportunityRequest;
use App\Data\Sponsor\OpportunityData;
use App\Data\Sponsor\UpdateOpportunityRequest;
use App\Models\Opportunity;
use App\Models\User;
use App\Repositories\OpportunityRepository;
use App\Validators\CreateOpportunityRequestValidator;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Support\Facades\DB;
use Spatie\LaravelData\DataCollection;

class SponsorOpportunityService
{
    public function __construct(
        private readonly OpportunityRepository $opportunityRepository,
    ) {}

    /**
     * Get opportunities for a sponsor user, filtered to their organization(s).
     *
     * @param User $user
     * @param string|null $search
     * @param string|null $status
     * @param array|null $industries
     * @param int $perPage
     * @return DataCollection
     */
    public function getOpportunitiesForSponsor(
        User $user,
        ?string $search = null,
        ?string $status = null,
        ?array $industries = null,
        int $perPage = 15
    ): DataCollection {
        $organizationIds = $this->getUserOrganizations($user);

        if (empty($organizationIds)) {
            return new DataCollection(OpportunityData::class, []);
        }

        // Get opportunities for the user's first active organization for now
        // Could be expanded to get from all organizations if needed
        $opportunities = $this->opportunityRepository->getOpportunitiesForOrganization(
            $organizationIds[0],
            $search,
            $status,
            $industries,
            $perPage
        );

        $items = collect($opportunities->items())->map(fn($opportunity) => OpportunityData::fromModel($opportunity));

        return new DataCollection(OpportunityData::class, $items);
    }

    /**
     * Get a specific opportunity for a sponsor user.
     *
     * @param int $id
     * @param User $user
     * @return OpportunityData
     * @throws AuthorizationException
     */
    public function getOpportunity(int $id, User $user): OpportunityData
    {
        $opportunity = $this->opportunityRepository->findById($id);

        if (!$opportunity) {
            throw new \Illuminate\Database\Eloquent\ModelNotFoundException();
        }

        // Ensure relationships are loaded
        $opportunity->load(['organization', 'industries', 'locationCoordinate']);

        // Authorization is now handled by middleware

        return OpportunityData::fromModel($opportunity);
    }

    /**
     * Create a new opportunity for a sponsor user.
     *
     * @param CreateOpportunityRequest $request
     * @param User $user
     * @return OpportunityData
     */
    public function createOpportunity(CreateOpportunityRequest $request, User $user): OpportunityData
    {
        // Validate organization access
        app(CreateOpportunityRequestValidator::class, ['user' => $user])->validate($request);

        // Log location fields for debugging
        \Illuminate\Support\Facades\Log::info('Creating opportunity with location data', [
            'location' => $request->location,
            'location_coordinate_id' => $request->location_coordinate_id
        ]);

        return DB::transaction(function () use ($request, $user) {
            $data = [
                'title' => $request->title,
                'description' => $request->description,
                'details' => $request->details,
                'organization_id' => $request->organizationId,
                'term' => $request->term,
                'type' => $request->type,
                'subtype' => $request->subtype,
                'city' => $request->city,
                'state_code' => $request->stateCode,
                'location_type' => $request->locationType,
                'location_coordinate_id' => $request->location_coordinate_id ?: $request->location, // Use location as fallback
                'apply_url' => $request->applyUrl,
                'qualifications' => $request->qualifications,
                'responsibilities' => $request->responsibilities,
                'benefits' => $request->benefits,
                'visible_start_date' => $request->visibleStartDate,
                'visible_end_date' => $request->visibleEndDate,
                'status' => $request->status,
                'user_id' => $user->id, // Set the current user as the owner
                'is_featured' => $request->isFeatured ?? false, // Set is_featured with default value
                'preferred_graduation_year_start' => $request->preferredGraduationYearStart,
                'preferred_graduation_year_end' => $request->preferredGraduationYearEnd,
                'preferred_states' => $request->preferredStates,
            ];

            $opportunity = $this->opportunityRepository->create($data);

            // Attach industries if provided
            if (!empty($request->industryIds)) {
                $this->opportunityRepository->attachIndustries($opportunity, $request->industryIds);
            }

            // Attach interests if provided
            if (!empty($request->interestIds)) {
                $this->opportunityRepository->attachInterests($opportunity, $request->interestIds);
            }

            // Reload the opportunity with industries
            $opportunity->load(['industries', 'interests', 'organization', 'locationCoordinate']);

            return OpportunityData::fromModel($opportunity);
        });
    }

    /**
     * Update an existing opportunity.
     *
     * @param int $id
     * @param UpdateOpportunityRequest $request
     * @param User $user
     * @return OpportunityData
     * @throws AuthorizationException
     */
    public function updateOpportunity(int $id, UpdateOpportunityRequest $request, User $user): OpportunityData
    {
        $opportunity = $this->opportunityRepository->findById($id);

        if (!$opportunity) {
            throw new \Illuminate\Database\Eloquent\ModelNotFoundException();
        }

        // Authorization is now handled by middleware

        return DB::transaction(function () use ($opportunity, $request) {
            $data = [
                'title' => $request->title,
                'description' => $request->description,
                'details' => $request->details,
                'term' => $request->term,
                'type' => $request->type,
                'subtype' => $request->subtype,
                'city' => $request->city,
                'state_code' => $request->stateCode,
                'location_type' => $request->locationType,
                'location_coordinate_id' => $request->location_coordinate_id ?: $request->location, // Use location as fallback
                'apply_url' => $request->applyUrl,
                'qualifications' => $request->qualifications,
                'responsibilities' => $request->responsibilities,
                'benefits' => $request->benefits,
                'visible_start_date' => $request->visibleStartDate,
                'visible_end_date' => $request->visibleEndDate,
                'status' => $request->status,
                'is_featured' => $request->isFeatured ?? false,
                'preferred_graduation_year_start' => $request->preferredGraduationYearStart,
                'preferred_graduation_year_end' => $request->preferredGraduationYearEnd,
                'preferred_states' => $request->preferredStates,
            ];

            $opportunity = $this->opportunityRepository->update($opportunity, $data);

            // Attach industries if provided
            if (!empty($request->industryIds)) {
                $this->opportunityRepository->attachIndustries($opportunity, $request->industryIds);
            }

            // Attach interests if provided
            if (!empty($request->interestIds)) {
                $this->opportunityRepository->attachInterests($opportunity, $request->interestIds);
            }

            // Reload the opportunity with industries
            $opportunity->load(['industries', 'interests', 'organization', 'locationCoordinate']);

            return OpportunityData::fromModel($opportunity);
        });
    }

    /**
     * Delete an opportunity.
     *
     * @param int $id
     * @param User $user
     * @return bool
     * @throws AuthorizationException
     */
    public function deleteOpportunity(int $id, User $user): bool
    {
        $opportunity = $this->opportunityRepository->findById($id);

        if (!$opportunity) {
            throw new \Illuminate\Database\Eloquent\ModelNotFoundException();
        }

        // Authorization for access is now handled by middleware,
        // but we still need to check if the user can delete this specific opportunity
        $this->validateDeletePermission($user, $opportunity);

        return $this->opportunityRepository->delete($opportunity);
    }

    /**
     * Duplicate an opportunity.
     *
     * @param int $id
     * @param User $user
     * @return OpportunityData
     * @throws AuthorizationException
     */
    public function duplicateOpportunity(int $id, User $user): OpportunityData
    {
        $opportunity = $this->opportunityRepository->findById($id);

        if (!$opportunity) {
            throw new \Illuminate\Database\Eloquent\ModelNotFoundException();
        }

        // Authorization is now handled by middleware

        return DB::transaction(function () use ($opportunity, $user) {
            $duplicated = $this->opportunityRepository->duplicate($opportunity);

            // Set the current user as the owner of the duplicated opportunity
            $duplicated->update(['user_id' => $user->id]);

            // Reload the opportunity with industries
            $duplicated->load(['industries', 'organization']);

            return OpportunityData::fromModel($duplicated);
        });
    }

    /**
     * Toggle the status of an opportunity.
     *
     * @param int $id
     * @param User $user
     * @return OpportunityData
     * @throws AuthorizationException
     */
    public function toggleOpportunityStatus(int $id, User $user): OpportunityData
    {
        $opportunity = $this->opportunityRepository->findById($id);

        if (!$opportunity) {
            throw new \Illuminate\Database\Eloquent\ModelNotFoundException();
        }

        // Authorization is now handled by middleware

        $opportunity = $this->opportunityRepository->toggleStatus($opportunity);

        return OpportunityData::fromModel($opportunity);
    }

    /**
     * Get the organization IDs for a sponsor user.
     *
     * @param User $user
     * @return array
     */
    private function getUserOrganizations(User $user): array
    {
        return $user->organizations()
            ->wherePivotNull('deactivated_at')
            ->pluck('organizations.id')
            ->toArray();
    }

    /**
     * Validate that a user has permission to delete an opportunity.
     *
     * @param User $user
     * @param Opportunity $opportunity
     * @return void
     * @throws AuthorizationException
     */
    private function validateDeletePermission(User $user, Opportunity $opportunity): void
    {
        // If opportunity has a user_id set, only allow deletion by that user
        if ($opportunity->user_id && $opportunity->user_id !== $user->id) {
            throw new AuthorizationException("Only the creator can delete this opportunity.");
        }
    }
}
