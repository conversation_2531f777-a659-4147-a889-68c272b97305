<?php

namespace App\Services;

use App\Data\Organization\UpdateOrganizationData;
use App\Exceptions\OrganizationNotAssociatedException;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\OrganizationRepository;
use Illuminate\Support\Facades\Log;
use Spatie\LaravelData\Optional;

class OrganizationService
{
    public function __construct(
        private readonly OrganizationRepository $organizationRepository
    ) {
    }

    /**
     * Get the organization associated with a sponsor user
     *
     * @throws OrganizationNotAssociatedException
     */
    public function getSponsorOrganization(User $user): Organization
    {
        $organization = $user->activeOrganization->first();

        if (!$organization) {
            throw new OrganizationNotAssociatedException();
        }

        return $organization;
    }

    /**
     * Update the organization associated with a sponsor user
     *
     * @throws OrganizationNotAssociatedException
     */
    public function updateSponsorOrganization(User $user, UpdateOrganizationData $data): Organization
    {
        $organization = $user->activeOrganization->first();

        if (!$organization) {
            throw new OrganizationNotAssociatedException();
        }

        // Only allow logo updates - handle logo management using Spatie Media Library
        if ($data->organization_logo !== null && $data->organization_logo instanceof \Illuminate\Http\UploadedFile) {
            // Replace logo with new file (will automatically replace any existing file in the collection)
            $organization->addMedia($data->organization_logo)
                ->toMediaCollection('logo');
        }
        // Handle logo removal (if explicitly set to null)
        else if ($data->organization_logo === null) {
            // Clear the logo collection
            $organization->clearMediaCollection('logo');
        }

        return $organization;
    }
}
