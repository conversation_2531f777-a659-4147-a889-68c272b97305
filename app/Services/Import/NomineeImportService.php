<?php

namespace App\Services\Import;

use App\Models\County;
use App\Models\Market;
use App\Models\Region;
use App\Models\School;
use App\Models\State;
use App\Models\SubRegion;
use App\Repositories\ContactRepository;
use App\Services\Import\Tracking\ImportTrackingService;
use App\Traits\SanitizesImportData;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use League\Csv\Reader;
use League\Csv\Statement;
use App\Data\Nomination\NominationData;
use App\Enums\NominationType;
use App\Services\Nomination\NominationService;

class NomineeImportService
{
    use SanitizesImportData;

    /**
     * Required columns in the CSV file
     */
    protected array $requiredColumns = [
        'Nominated Date',
        'Nominees Name',
        'State',
        'County',
        '+A Market',
        '+A Region',
        'High School Name',
        'Type',
        'Grade',
        'Gender',
        'Sport #1',
        'Nomination',
        'Nominee Email',
        'Nominee Phone Number',
        'Nominator Full Name',
        'Nominator Email',
        'Nominator Phone Number',
        'Relationship to Nominee'
    ];

    /**
     * Optional columns in the CSV file
     */
    protected array $optionalColumns = [
        'Sport #2',
        'Sport #3'
    ];

    /**
     * The contact repository instance.
     */
    protected ContactRepository $contactRepository;

    /**
     * The nomination service instance.
     */
    protected NominationService $nominationService;

    /**
     * The import tracking service instance.
     */
    protected ImportTrackingService $importTrackingService;

    /**
     * Create a new service instance.
     */
    public function __construct(
        ContactRepository $contactRepository,
        NominationService $nominationService,
        ImportTrackingService $importTrackingService
    ) {
        $this->contactRepository = $contactRepository;
        $this->nominationService = $nominationService;
        $this->importTrackingService = $importTrackingService;
    }

    /**
     * Import contacts from a CSV file
     *
     * @param string $filePath
     * @return array
     */
    public function importFromCsv(string $filePath): array
    {
        Log::info("Starting contact import from CSV: {$this->sanitizeForLogging($filePath)}");

        $results = [
            'total' => 0,
            'created' => 0,
            'updated' => 0,
            'skipped' => 0,
            'nominations_created' => 0,
            'nominations_failed' => 0,
            'errors' => [],
        ];

        try {
            // Extract file name from path
            $fileName = basename($filePath);

            // Create import record
            $import = $this->importTrackingService->createImport(
                'nominees',
                $filePath,
                $fileName,
                ['source' => 'admin_upload']
            );

            // Read the CSV file
            $csv = Reader::createFromPath(Storage::path($filePath), 'r');
            $csv->setHeaderOffset(0);

            // Set UTF-8 encoding for the CSV reader
            $csv->addStreamFilter('convert.iconv.ISO-8859-1/UTF-8//TRANSLIT');

            $records = Statement::create()->process($csv);
            $totalRows = $records->count();
            $results['total'] = $totalRows;

            // Mark import as started
            $this->importTrackingService->startImport($import, $totalRows);

            foreach ($records as $index => $record) {
                try {
                    // Sanitize record data to ensure valid UTF-8
                    $sanitizedRecord = $this->sanitizeRecordData($record);

                    $contactData = $this->mapCsvRowToContactData($sanitizedRecord);

                    // Check if contact already exists by email
                    $existingContact = null;
                    if (!empty($contactData['email'])) {
                        $existingContact = $this->contactRepository->findByEmail($contactData['email']);
                    }

                    if ($existingContact) {
                        // Update existing contact
                        $contact = $this->contactRepository->update($existingContact, $contactData);
                        $results['updated']++;
                        $contactStatus = 'updated';
                        $contactId = $contact->id;
                    } else {
                        // Create new contact
                        $contact = $this->contactRepository->create($contactData);
                        $results['created']++;
                        $contactStatus = 'created';
                        $contactId = $contact->id;
                    }

                    // Record the contact outcome
                    $this->importTrackingService->recordOutcome(
                        $import,
                        $index + 1,
                        $contactStatus,
                        $sanitizedRecord,
                        ['contact_id' => $contactId],
                        'App\\Models\\Contact',
                        $contactId,
                        null
                    );

                    // Create nomination
                    try {
                        // Extract name parts
                        $fullName = $record['Nominees Name'] ?? '';
                        $nameParts = explode(' ', $fullName, 2);
                        $firstName = $nameParts[0] ?? '';
                        $lastName = $nameParts[1] ?? '';

                        // Extract nominator name parts
                        $nominatorFullName = $record['Nominator Full Name'] ?? '';
                        $nominatorNameParts = explode(' ', $nominatorFullName, 2);
                        $nominatorFirstName = $nominatorNameParts[0] ?? '';
                        $nominatorLastName = $nominatorNameParts[1] ?? '';

                        // Get or create school
                        $schoolName = $record['High School Name'] ?? '';
                        $stateCode = $record['State'] ?? '';
                        $state = $this->getOrCreateState($stateCode);
                        $regionName = $record['+A Region'] ?? '';
                        $region = $this->getOrCreateRegion($regionName);
                        $marketName = $record['+A Market'] ?? '';
                        $market = $this->getOrCreateMarket($marketName, $region->id);
                        $countyName = $record['County'] ?? '';
                        $county = $this->getOrCreateCounty($countyName, $market->id, null, $stateCode);
                        $school = $this->getOrCreateSchool($schoolName, $county->id, $region->id);

                        // Determine nomination type
                        $csvType = $record['Type'] ?? null;
                        $nominationType = match (strtolower($csvType ?? '')) {
                            'coach' => NominationType::COACH,
                            default => NominationType::ATHLETE,
                        };

                        // Create nomination data
                        $nominationData = new NominationData(
                            email: $record['Nominee Email'] ?? '',
                            first_name: $firstName,
                            last_name: $lastName,
                            nominator_email: $record['Nominator Email'] ?? '',
                            nominator_first_name: $nominatorFirstName,
                            nominator_last_name: $nominatorLastName,
                            school_name: $schoolName,
                            sport: $record['Sport #1'] ?? '',
                            relationship: $record['Relationship to Nominee'] ?? '',
                            type: $nominationType,
                            note: $record['Nomination'] ?? null,
                            school_id: $school->id
                        );

                        // Create the nomination
                        $nomination = $this->nominationService->handleSingleNomination($nominationData);
                        $results['nominations_created']++;

                        // Record the nomination outcome
                        $this->importTrackingService->recordOutcome(
                            $import,
                            $index + 1,
                            'created',
                            $sanitizedRecord,
                            ['nomination_id' => $nomination->id],
                            'App\\Models\\Nomination',
                            $nomination->id,
                            null
                        );
                    } catch (\Exception $e) {
                        Log::error("Error creating nomination: " . $e->getMessage(), [
                            'record' => $this->sanitizeResultsForLogging($record),
                            'exception' => $e,
                        ]);
                        $results['nominations_failed']++;

                        // Record the nomination failure
                        $this->importTrackingService->recordOutcome(
                            $import,
                            $index + 1,
                            'failed',
                            $sanitizedRecord,
                            null,
                            'App\\Models\\Nomination',
                            null,
                            ['error' => $e->getMessage()]
                        );
                    }
                } catch (\Exception $e) {
                    Log::error("Error processing contact record: " . $e->getMessage(), [
                        'record' => $this->sanitizeResultsForLogging($record),
                        'exception' => $e,
                    ]);

                    // Record the failure
                    $this->importTrackingService->recordOutcome(
                        $import,
                        $index + 1,
                        'failed',
                        $record,
                        null,
                        null,
                        null,
                        ['error' => $e->getMessage()]
                    );

                    $results['errors'][] = "Row " . ($results['created'] + $results['updated'] + $results['skipped'] + 1) .
                        ": " . $e->getMessage();
                    $results['skipped']++;
                }
            }

            // Mark import as completed
            $this->importTrackingService->completeImport($import);

            Log::info("Completed contact import", $this->sanitizeResultsForLogging($results));
            return $results;
        } catch (\Exception $e) {
            Log::error("Failed to import contacts from CSV: " . $e->getMessage(), [
                'file_path' => $this->sanitizeForLogging($filePath),
                'exception' => $e,
            ]);

            // If we have an import record, mark it as failed
            if (isset($import)) {
                $this->importTrackingService->failImport($import, $e->getMessage());
            }

            $results['errors'][] = "Failed to process CSV file: " . $e->getMessage();
            return $results;
        }
    }

    /**
     * Map a CSV row to contact data
     *
     * @param array $row
     * @return array
     */
    protected function mapCsvRowToContactData(array $row): array
    {
        // Validate required fields
        $requiredFields = ['Nominees Name', 'Nominee Email'];
        foreach ($requiredFields as $field) {
            if (empty($row[$field])) {
                throw new \InvalidArgumentException("Missing required field: {$field}");
            }
        }

        // Extract name parts
        $fullName = $row['Nominees Name'] ?? '';
        $nameParts = explode(' ', $fullName, 2);
        $firstName = $nameParts[0] ?? '';
        $lastName = $nameParts[1] ?? '';

        // Map CSV columns to contact fields
        $contactData = [
            'first_name' => $firstName,
            'last_name' => $lastName,
            'email' => $row['Nominee Email'] ?? null,
            'phone' => $row['Nominee Phone Number'] ?? null,
            'type' => 'positive_athlete', // Default type for this import
            'status' => 'active',
            'metadata' => [
                'state' => $row['State'] ?? null,
                'county' => $row['County'] ?? null,
                'market' => $row['+A Market'] ?? null,
                'region' => $row['+A Region'] ?? null,
                'high_school' => $row['High School Name'] ?? null,
                'contact_type' => $row['Type'] ?? null,
                'grade' => $row['Grade'] ?? null,
                'gender' => $row['Gender'] ?? null,
                'sports' => $this->extractSports($row),
                'import_source' => 'csv_import',
                'import_date' => now()->toDateTimeString(),
            ],
        ];

        return $contactData;
    }

    /**
     * Extract sports from CSV row
     *
     * @param array $row
     * @return array
     */
    protected function extractSports(array $row): array
    {
        $sports = [];

        if (!empty($row['Sport #1'])) {
            $sports[] = $row['Sport #1'];
        }

        if (!empty($row['Sport #2'])) {
            $sports[] = $row['Sport #2'];
        }

        if (!empty($row['Sport #3'])) {
            $sports[] = $row['Sport #3'];
        }

        return $sports;
    }

    /**
     * Validate the structure of the CSV file
     */
    public function validateCsvStructure(string $filePath): array
    {
        // Log the validation attempt
        Log::info('Validating CSV structure', ['file_path' => $this->sanitizeForLogging($filePath)]);

        // Determine if we're dealing with a relative path or full path
        $isFullPath = str_starts_with($filePath, '/');
        $relativePath = $isFullPath ? str_replace(Storage::disk('local')->path(''), '', $filePath) : $filePath;
        $fullPath = $isFullPath ? $filePath : Storage::disk('local')->path($relativePath);

        // Log the path information for debugging
        Log::info('CSV path details', [
            'original_path' => $filePath,
            'is_full_path' => $isFullPath,
            'relative_path' => $relativePath,
            'full_path' => $fullPath,
            'exists_relative' => Storage::disk('local')->exists($relativePath),
            'exists_full' => file_exists($fullPath),
        ]);

        // Check if the file exists
        if (!file_exists($fullPath)) {
            Log::error('CSV file not found at full path', ['full_path' => $fullPath]);
            return [
                'valid' => false,
                'errors' => ['CSV file not found at full path.'],
            ];
        }

        try {
            $handle = fopen($fullPath, 'r');
            if (!$handle) {
                Log::error('Could not open CSV file', ['full_path' => $fullPath]);
                return [
                    'valid' => false,
                    'errors' => ['Could not open CSV file.'],
                ];
            }

            // Read the header row
            $header = fgetcsv($handle);
            if (!$header) {
                fclose($handle);
                Log::error('CSV file is empty or has invalid format', ['file_path' => $filePath]);
                return [
                    'valid' => false,
                    'errors' => ['CSV file is empty or has invalid format.'],
                ];
            }

            // Log the header for debugging
            Log::info('CSV header', ['header' => $header]);

            // Check for required columns
            $missingColumns = [];
            foreach ($this->requiredColumns as $requiredColumn) {
                if (!in_array($requiredColumn, $header)) {
                    $missingColumns[] = $requiredColumn;
                }
            }

            if (!empty($missingColumns)) {
                fclose($handle);
                Log::error('Missing required columns', [
                    'file_path' => $filePath,
                    'missing_columns' => $missingColumns,
                ]);
                return [
                    'valid' => false,
                    'errors' => ['Missing required columns: ' . implode(', ', $missingColumns)],
                ];
            }

            fclose($handle);
            return [
                'valid' => true,
                'errors' => [],
            ];
        } catch (\Exception $e) {
            Log::error('Error validating CSV structure', [
                'file_path' => $filePath,
                'error' => $e->getMessage(),
            ]);
            return [
                'valid' => false,
                'errors' => ['Error validating CSV structure: ' . $e->getMessage()],
            ];
        }
    }

    /**
     * Process the CSV file
     */
    public function processCsvFile(string $filePath): array
    {
        $results = [
            'total' => 0,
            'created' => 0,
            'updated' => 0,
            'failed' => 0,
            'nominations_created' => 0,
            'nominations_failed' => 0,
            'errors' => [],
        ];

        try {
            // Determine if we're dealing with a relative path or full path
            $isFullPath = str_starts_with($filePath, '/');
            $relativePath = $isFullPath ? str_replace(Storage::disk('local')->path(''), '', $filePath) : $filePath;

            // Log the path information for debugging
            Log::info('Processing CSV file', [
                'original_path' => $this->sanitizeForLogging($filePath),
                'is_full_path' => $isFullPath,
                'relative_path' => $this->sanitizeForLogging($relativePath),
                'exists_relative' => Storage::disk('local')->exists($relativePath),
                'full_path' => $this->sanitizeForLogging(Storage::disk('local')->path($relativePath)),
            ]);

            // Validate the CSV structure using the appropriate path
            $validationResult = $this->validateCsvStructure($isFullPath ? $filePath : Storage::disk('local')->path($relativePath));
            if (!$validationResult['valid']) {
                return [
                    'success' => false,
                    'errors' => $validationResult['errors'],
                ];
            }

            // Check if the file exists in the storage
            if (!Storage::disk('local')->exists($relativePath)) {
                Log::error('CSV file not found in storage during processing', [
                    'relative_path' => $relativePath,
                    'full_path' => $isFullPath ? $filePath : Storage::disk('local')->path($relativePath),
                ]);
                return [
                    'success' => false,
                    'errors' => ['CSV file not found in storage during processing.'],
                ];
            }

            // Get the full path to the file
            $fullPath = Storage::disk('local')->path($relativePath);
            $fileName = basename($fullPath);

            // Create import record
            $import = $this->importTrackingService->createImport(
                'nominees',
                $fullPath,
                $fileName,
                ['source' => 'admin_upload']
            );

            // Use League/CSV to read the file
            $csv = Reader::createFromPath($fullPath, 'r');
            $csv->setHeaderOffset(0); // First row contains headers

            // Set UTF-8 encoding for the CSV reader
            $csv->addStreamFilter('convert.iconv.ISO-8859-1/UTF-8//TRANSLIT');

            // Create a statement to process the CSV
            $records = Statement::create()->process($csv);

            // Log the total number of records
            $totalRows = count($records);
            Log::info("Processing {$totalRows} records from CSV");
            $results['total'] = $totalRows;

            // Mark import as started
            $this->importTrackingService->startImport($import, $totalRows);

            // Process each row within a transaction
            DB::beginTransaction();
            try {
                foreach ($records as $offset => $record) {
                    // Process the row without detailed logging
                    try {
                        // Sanitize record data to ensure valid UTF-8
                        $sanitizedRecord = $this->sanitizeRecordData($record);

                        // Process the row
                        $processResult = $this->processRow($sanitizedRecord);

                        // Record the outcome in the import tracking system
                        $status = $processResult['success'] ? ($processResult['action'] === 'created' ? 'created' : 'updated') : 'failed';
                        $errors = $processResult['success'] ? null : ['error' => $processResult['error']];
                        $modelType = $processResult['success'] ? 'App\\Models\\Contact' : null;
                        $modelId = $processResult['success'] && isset($processResult['contact']) ? $processResult['contact']->id : null;

                        $this->importTrackingService->recordOutcome(
                            $import,
                            $offset + 1, // Row number (1-indexed)
                            $status,
                            $sanitizedRecord,
                            $processResult['success'] ? $processResult : null,
                            $modelType,
                            $modelId,
                            $errors
                        );

                        if ($processResult['success']) {
                            if ($processResult['action'] === 'created') {
                                $results['created']++;
                            } else {
                                $results['updated']++;
                            }

                            // Check if a nomination was created
                            if (isset($processResult['nomination_created']) && $processResult['nomination_created']) {
                                $results['nominations_created']++;

                                // Record the nomination outcome if available
                                if (isset($processResult['nomination'])) {
                                    $this->importTrackingService->recordOutcome(
                                        $import,
                                        $offset + 1,
                                        'created',
                                        $sanitizedRecord,
                                        ['nomination_id' => $processResult['nomination']->id],
                                        'App\\Models\\Nomination',
                                        $processResult['nomination']->id,
                                        null
                                    );
                                }
                            }
                        } else {
                            $results['failed']++;
                            $results['errors'][] = "Row " . ($offset + 1) . ": " . $processResult['error'];

                            // Log the error for debugging
                            Log::error("Error processing row " . ($offset + 1), [
                                'error' => $processResult['error']
                            ]);
                        }
                    } catch (\Exception $e) {
                        $results['failed']++;
                        $results['errors'][] = "Row " . ($offset + 1) . ": " . $e->getMessage();

                        // Record the exception in the import tracking system
                        $this->importTrackingService->recordOutcome(
                            $import,
                            $offset + 1,
                            'failed',
                            $record,
                            null,
                            null,
                            null,
                            ['exception' => $e->getMessage()]
                        );

                        // Log the exception for debugging
                        Log::error("Exception processing row " . ($offset + 1), [
                            'error' => $this->sanitizeForLogging($e->getMessage())
                        ]);
                    }
                }

                // Commit the transaction if all rows were processed
                DB::commit();

                // Mark import as completed
                $this->importTrackingService->completeImport($import);

                Log::info("CSV processing completed", [
                    'total' => $results['total'],
                    'created' => $results['created'],
                    'updated' => $results['updated'],
                    'nominations_created' => $results['nominations_created'],
                    'nominations_failed' => $results['nominations_failed'],
                    'failed' => $results['failed']
                ]);

                return [
                    'success' => true,
                    'results' => $results,
                ];
            } catch (\Exception $e) {
                // Roll back the transaction if an error occurred
                DB::rollBack();

                // Mark import as failed
                $this->importTrackingService->failImport($import, $e->getMessage());

                Log::error("Failed to process CSV", [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('Error processing CSV file', [
                'file_path' => $this->sanitizeForLogging($filePath),
                'error' => $this->sanitizeForLogging($e->getMessage()),
                'trace' => $e->getTraceAsString()
            ]);

            // If we have an import record, mark it as failed
            if (isset($import)) {
                $this->importTrackingService->failImport($import, $e->getMessage());
            }

            return [
                'success' => false,
                'errors' => ['Error processing CSV file: ' . $e->getMessage()],
            ];
        }
    }

    /**
     * Process a row from the CSV file
     */
    public function processRow(array $row): array
    {
        try {
            // Validate required fields
            $requiredFields = ['Nominees Name', 'State'];
            foreach ($requiredFields as $field) {
                if (empty($row[$field])) {
                    throw new \InvalidArgumentException("Missing required field: {$field}");
                }
            }

            // Extract name parts
            $fullName = $row['Nominees Name'] ?? '';
            $nameParts = explode(' ', $fullName, 2);
            $firstName = $nameParts[0] ?? '';
            $lastName = $nameParts[1] ?? '';

            // Get or create state
            $stateCode = $row['State'] ?? '';
            $state = $this->getOrCreateState($stateCode);

            // Get or create region
            $regionName = $row['+A Region'] ?? '';
            $region = $this->getOrCreateRegion($regionName);

            // Get or create market
            $marketName = $row['+A Market'] ?? '';
            $market = $this->getOrCreateMarket($marketName, $region->id);

            // Get or create county
            $countyName = $row['County'] ?? '';
            $county = $this->getOrCreateCounty($countyName, $market->id, null, $stateCode);

            // Get or create school
            $schoolName = $row['High School Name'] ?? '';
            $school = $this->getOrCreateSchool($schoolName, $county->id, $region->id);

            // Process sports
            $sports = [];
            if (!empty($row['Sport #1'])) {
                $sports[] = $row['Sport #1'];
            }
            if (!empty($row['Sport #2'])) {
                $sports[] = $row['Sport #2'];
            }
            if (!empty($row['Sport #3'])) {
                $sports[] = $row['Sport #3'];
            }

            // Determine the contact type from the CSV's Type field
            $csvType = $row['Type'] ?? null;
            $contactType = \App\Enums\ContactType::fromCsvType($csvType)->value;

            // Create contact data - without nomination-related fields
            $contactData = [
                'first_name' => $firstName,
                'last_name' => $lastName,
                'email' => $row['Nominee Email'] ?? null,
                'phone' => $row['Nominee Phone Number'] ?? null,
                'type' => $contactType,
                'status' => 'active',
                'region_id' => $region->id,
                'market_id' => $market->id,
                'county_id' => $county->id,
                'state_id' => $state->id,
                'school_id' => $school->id,
                'gender' => $row['Gender'] ?? null,
                'graduation_year' => $row['Grade'] ?? null,
                'sports' => $sports,
                'metadata' => [
                    'imported_at' => now()->toDateTimeString(),
                    'original_data' => $row,
                ],
            ];

            // Create or update the contact
            $contact = null;
            if (!empty($contactData['email'])) {
                $contact = $this->contactRepository->findByEmail($contactData['email']);
            }

            if ($contact) {
                $contact = $this->contactRepository->update($contact, $contactData);
                $action = 'updated';
            } else {
                $contact = $this->contactRepository->create($contactData);
                $action = 'created';
            }

            // Process nomination date
            $nominatedAt = null;
            if (!empty($row['Nominated Date'])) {
                try {
                    $nominatedAt = Carbon::createFromFormat('n/j/Y', $row['Nominated Date']);
                } catch (\Exception $e) {
                    // Try alternative date formats
                    try {
                        $nominatedAt = Carbon::parse($row['Nominated Date']);
                    } catch (\Exception $e2) {
                        // Silently continue if date can't be parsed
                    }
                }
            }

            // Extract nominator name parts
            $nominatorFullName = $row['Nominator Full Name'] ?? '';
            $nominatorNameParts = explode(' ', $nominatorFullName, 2);
            $nominatorFirstName = $nominatorNameParts[0] ?? '';
            $nominatorLastName = $nominatorNameParts[1] ?? '';

            // Determine nomination type
            $nominationType = match (strtolower($csvType ?? '')) {
                'coach' => NominationType::COACH,
                default => NominationType::ATHLETE,
            };

            // Create nomination data
            $nominationData = new NominationData(
                email: $row['Nominee Email'] ?? '',
                first_name: $firstName,
                last_name: $lastName,
                nominator_email: $row['Nominator Email'] ?? '',
                nominator_first_name: $nominatorFirstName,
                nominator_last_name: $nominatorLastName,
                school_name: $schoolName,
                sport: $row['Sport #1'] ?? '',
                relationship: $row['Relationship to Nominee'] ?? '',
                type: $nominationType,
                note: $row['Nomination'] ?? null,
                school_id: $school->id,
                
                // Add these fields to align with JotForm data structure
                state_code: $stateCode,
                county: $countyName,
                nominee_phone: $row['Nominee Phone Number'] ?? null,
                nominator_phone: $row['Nominator Phone Number'] ?? null,
                sport_2: $row['Sport #2'] ?? null,
                sport_3: $row['Sport #3'] ?? null,
                gender: $row['Gender'] ?? null,
                grade: $row['Grade'] ?? null,
                
                // Processing metadata
                processing_status: 'validated',
                processed_at: now(),
                
                // Import source tracking
                how_did_you_hear: 'CSV Import',
                location_resolution_notes: ['source' => 'csv_import', 'imported_at' => now()->toDateTimeString()]
            );

            // Create the nomination without creating system invites
            $nominationCreated = false;
            try {
                $nomination = $this->nominationService->handleSingleNomination($nominationData, false);
                $nominationCreated = true;
            } catch (\Exception $e) {
                Log::error('Error creating nomination', [
                    'error' => $e->getMessage()
                ]);
                // Continue with the import even if nomination creation fails
            }

            return [
                'success' => true,
                'contact' => $contact,
                'action' => $action,
                'nomination_created' => $nominationCreated,
            ];
        } catch (\Exception $e) {
            Log::error('Error processing row', [
                'error' => $e->getMessage()
            ]);
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get or create a state
     */
    protected function getOrCreateState(string $stateCode): ?State
    {
        return State::query()->firstOrCreate(['code' => $stateCode]);
    }

    /**
     * Get or create a region
     */
    protected function getOrCreateRegion(string $name): Region
    {
        return Region::query()->firstOrCreate(
            ['name' => $name],
            ['slug' => Str::slug($name)]
        );
    }

    /**
     * Get or create a market
     */
    protected function getOrCreateMarket(string $name, string $regionId): Market
    {
        return Market::query()->firstOrCreate(
            ['name' => $name, 'region_id' => $regionId],
            ['slug' => Str::slug($name)]
        );
    }

    /**
     * Get or create a sub-region
     */
    protected function getOrCreateSubRegion(string $name, string $marketId): SubRegion
    {
        return SubRegion::query()->firstOrCreate(
            ['name' => $name, 'market_id' => $marketId],
            ['slug' => Str::slug($name)]
        );
    }

    /**
     * Get or create a county
     */
    protected function getOrCreateCounty(string $name, string $marketId, ?string $subRegionId, string $stateCode): County
    {
        // Standardize the name format: trim and convert to title case
        $name = trim($name);
        $standardizedName = Str::title($name);

        return County::query()->firstOrCreate(
            ['name' => $standardizedName, 'state_code' => $stateCode],
            [
                'market_id' => $marketId,
                'sub_region_id' => $subRegionId,
                'slug' => Str::slug($standardizedName),
            ]
        );
    }

    /**
     * Get or create a school
     */
    protected function getOrCreateSchool(string $name, string $countyId, string $regionId): School
    {
        return School::query()->firstOrCreate(
            ['name' => $name, 'county_id' => $countyId],
            ['region_id' => $regionId]
        );
    }
}
