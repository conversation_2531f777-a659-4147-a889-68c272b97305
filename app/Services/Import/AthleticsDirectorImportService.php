<?php

namespace App\Services\Import;

use App\Models\County;
use App\Models\Market;
use App\Models\Region;
use App\Models\School;
use App\Models\State;
use App\Models\SubRegion;
use App\Repositories\ContactRepository;
use App\Services\Import\Tracking\ImportTrackingService;
use App\Traits\SanitizesImportData;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use League\Csv\Reader;
use League\Csv\Statement;
use App\Services\ContactService;

class AthleticsDirectorImportService
{
    use SanitizesImportData;

    /**
     * Required columns in the CSV file
     */
    protected array $requiredColumns = [
        'First Name',
        'Last Name',
        'Title',
        'Account Name',
        'State',
        'County',
        'Email',
        'Phone'
    ];

    /**
     * Optional columns in the CSV file
     */
    protected array $optionalColumns = [
        'Shipping Address',
        'City',
        'Zip Code',
        'Region',
        'Market'
    ];

    /**
     * The contact repository instance.
     */
    protected ContactRepository $contactRepository;

    /**
     * The import tracking service instance.
     */
    protected ImportTrackingService $importTrackingService;

    /**
     * The contact service instance.
     */
    protected ContactService $contactService;

    /**
     * Create a new service instance.
     */
    public function __construct(
        ContactRepository $contactRepository,
        ImportTrackingService $importTrackingService,
        ContactService $contactService
    ) {
        $this->contactRepository = $contactRepository;
        $this->importTrackingService = $importTrackingService;
        $this->contactService = $contactService;
    }

    /**
     * Validate the CSV structure
     *
     * @param string $filePath
     * @return array
     */
    public function validateCsvStructure(string $filePath): array
    {
        $result = [
            'valid' => true,
            'errors' => [],
        ];

        try {
            // Check if file exists
            if (!file_exists($filePath)) {
                $result['valid'] = false;
                $result['errors'][] = "File not found: {$filePath}";
                return $result;
            }

            // Read the CSV file
            $csv = Reader::createFromPath($filePath, 'r');
            $csv->setHeaderOffset(0);

            // Get the header row
            $headers = $csv->getHeader();

            // Check for required columns
            $missingColumns = [];
            foreach ($this->requiredColumns as $requiredColumn) {
                if (!in_array($requiredColumn, $headers)) {
                    $missingColumns[] = $requiredColumn;
                }
            }

            if (!empty($missingColumns)) {
                $result['valid'] = false;
                $result['errors'][] = "Missing required columns: " . implode(', ', $missingColumns);
            }

            return $result;
        } catch (\Exception $e) {
            $result['valid'] = false;
            $result['errors'][] = "Error validating CSV structure: " . $e->getMessage();
            return $result;
        }
    }

    /**
     * Process the CSV file
     *
     * @param string $filePath
     * @return array
     */
    public function processCsvFile(string $filePath): array
    {
        $result = [
            'success' => true,
            'errors' => [],
            'results' => [
                'total' => 0,
                'created' => 0,
                'updated' => 0,
                'failed' => 0,
                'schools_created' => 0,
                'schools_matched' => 0,
            ],
        ];

        try {
            // Extract file name from path
            $fileName = basename($filePath);

            // Create import record
            $import = $this->importTrackingService->createImport(
                'athletics_directors',
                $filePath,
                $fileName,
                ['source' => 'admin_upload']
            );

            // Read the CSV file
            $csv = Reader::createFromPath(Storage::disk('local')->path($filePath), 'r');
            $csv->setHeaderOffset(0);

            // Set UTF-8 encoding for the CSV reader
            $csv->addStreamFilter('convert.iconv.ISO-8859-1/UTF-8//TRANSLIT');

            $records = Statement::create()->process($csv);
            $totalRows = $records->count();
            $result['results']['total'] = $totalRows;

            // Mark import as started
            $this->importTrackingService->startImport($import, $totalRows);

            foreach ($records as $index => $record) {
                try {
                    // Sanitize record data to ensure valid UTF-8
                    $sanitizedRecord = $this->sanitizeRecordData($record);

                    // Process each row
                    $rowResult = $this->processRow($sanitizedRecord);

                    // Record the outcome in the import tracking system
                    $status = $rowResult['success'] ? ($rowResult['created'] ? 'created' : 'updated') : 'failed';
                    $errors = $rowResult['success'] ? null : $rowResult['errors'];
                    $modelType = $rowResult['success'] ? 'App\\Models\\Contact' : null;
                    $modelId = $rowResult['success'] && isset($rowResult['contact_id']) ? $rowResult['contact_id'] : null;

                    $this->importTrackingService->recordOutcome(
                        $import,
                        $index + 1, // Row number (1-indexed)
                        $status,
                        $sanitizedRecord,
                        $rowResult['success'] ? $rowResult : null,
                        $modelType,
                        $modelId,
                        $errors
                    );

                    // Update statistics
                    if ($rowResult['success']) {
                        if ($rowResult['created']) {
                            $result['results']['created']++;
                        } else {
                            $result['results']['updated']++;
                        }

                        if ($rowResult['school_created']) {
                            $result['results']['schools_created']++;
                        } else if ($rowResult['school_matched']) {
                            $result['results']['schools_matched']++;
                        }
                    } else {
                        $result['results']['failed']++;
                        $result['errors'][] = "Row " . ($index + 2) . ": " . implode(', ', $rowResult['errors']);
                    }
                } catch (\Exception $e) {
                    Log::error("Error processing row " . ($index + 2) . ": " . $e->getMessage(), [
                        'record' => $this->sanitizeResultsForLogging($record),
                        'exception' => $e,
                    ]);

                    // Record the exception in the import tracking system
                    $this->importTrackingService->recordOutcome(
                        $import,
                        $index + 1, // Row number (1-indexed)
                        'failed',
                        $record,
                        null,
                        null,
                        null,
                        ['exception' => $e->getMessage()]
                    );

                    $result['results']['failed']++;
                    $result['errors'][] = "Row " . ($index + 2) . ": " . $e->getMessage();
                }
            }

            // Mark import as completed
            $this->importTrackingService->completeImport($import);

            return $result;
        } catch (\Exception $e) {
            Log::error("Error processing CSV file: " . $e->getMessage(), [
                'file_path' => $this->sanitizeForLogging($filePath),
                'exception' => $e,
            ]);

            // If we have an import record, mark it as failed
            if (isset($import)) {
                $this->importTrackingService->failImport($import, $e->getMessage());
            }

            $result['success'] = false;
            $result['errors'][] = "Error processing CSV file: " . $e->getMessage();
            return $result;
        }
    }

    /**
     * Process a single row from the CSV
     *
     * @param array $row
     * @return array
     */
    public function processRow(array $row): array
    {
        $result = [
            'success' => true,
            'errors' => [],
            'created' => false,
            'updated' => false,
            'school_created' => false,
            'school_matched' => false,
        ];

        try {
            DB::beginTransaction();

            // Validate required fields
            $requiredFields = ['First Name', 'Last Name', 'Email', 'Account Name'];
            foreach ($requiredFields as $field) {
                if (empty($row[$field])) {
                    $result['success'] = false;
                    $result['errors'][] = "Missing required field: {$field}";
                }
            }

            if (!$result['success']) {
                DB::rollBack();
                return $result;
            }

            // Resolve geographical entities
            $stateCode = $row['State'] ?? null;
            $countyName = $row['County'] ?? null;
            $regionName = $row['Region'] ?? null;
            $marketName = $row['Market'] ?? null;

            // Resolve state
            $state = null;
            if ($stateCode) {
                $state = $this->getOrCreateState($stateCode);
                if (!$state) {
                    $result['success'] = false;
                    $result['errors'][] = "Could not resolve state: {$stateCode}";
                    DB::rollBack();
                    return $result;
                }
            }

            // Resolve region
            $region = null;
            if ($regionName) {
                $region = $this->getOrCreateRegion($regionName);
            }

            // Resolve market
            $market = null;
            if ($marketName && $region) {
                $market = $this->getOrCreateMarket($marketName, $region->id);
            }

            // Resolve county
            $county = null;
            if ($countyName && $stateCode) {
                $subRegionId = null; // We don't have sub-region in the CSV
                $marketId = $market ? $market->id : null;
                $county = $this->getOrCreateCounty($countyName, $marketId, $subRegionId, $stateCode);
            }

            // Resolve school - this is required for AD profiles
            $schoolName = $row['Account Name'] ?? null;
            $school = null;
            if ($schoolName) {
                if (!$county) {
                    $result['success'] = false;
                    $result['errors'][] = "Cannot create school without county information";
                    DB::rollBack();
                    return $result;
                }

                $regionId = $region ? $region->id : null;
                $school = $this->getOrCreateSchool($schoolName, $county->id, $regionId);
                $result[$school->wasRecentlyCreated ? 'school_created' : 'school_matched'] = true;
            } else {
                $result['success'] = false;
                $result['errors'][] = "School name (Account Name) is required for Athletics Directors";
                DB::rollBack();
                return $result;
            }

            // Prepare contact data
            $contactData = [
                'first_name' => $row['First Name'] ?? null,
                'last_name' => $row['Last Name'] ?? null,
                'email' => $row['Email'] ?? null,
                'phone' => $row['Phone'] ?? null,
                'school_id' => $school->id, // Ensure school ID is set
                'county_id' => $county ? $county->id : null,
                'state_id' => $state ? $state->code : null,
                'region_id' => $region ? $region->id : null,
                'market_id' => $market ? $market->id : null,
                'metadata' => [
                    'title' => $row['Title'] ?? null,
                    'address' => $row['Shipping Address'] ?? null,
                    'city' => $row['City'] ?? null,
                    'zip_code' => $row['Zip Code'] ?? null,
                    'import_source' => 'ad_csv_import',
                    'import_date' => now()->toDateTimeString(),
                ],
            ];

            // Check if contact already exists by email
            $existingContact = null;
            if (!empty($contactData['email'])) {
                $existingContact = $this->contactRepository->findByEmail($contactData['email']);
            }

            if ($existingContact) {
                // Update existing contact through ContactService
                $contact = $this->contactService->updateAthleticsDirectorContact($existingContact, $contactData);
                $result['updated'] = true;
                $result['contact_id'] = $contact->id;
            } else {
                // Create new contact through ContactService
                $contact = $this->contactService->createAthleticsDirectorContact($contactData);
                $result['created'] = true;
                $result['contact_id'] = $contact->id;
            }

            DB::commit();
            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Error processing row: " . $e->getMessage(), [
                'row' => $row,
                'exception' => $e,
            ]);

            $result['success'] = false;
            $result['errors'][] = "Error processing row: " . $e->getMessage();
            return $result;
        }
    }

    /**
     * Get or create a state
     *
     * @param string $stateCode
     * @return State|null
     */
    protected function getOrCreateState(string $stateCode): ?State
    {
        return State::query()->firstOrCreate(['code' => strtoupper($stateCode)]);
    }

    /**
     * Get or create a region
     *
     * @param string $name
     * @return Region
     */
    protected function getOrCreateRegion(string $name): Region
    {
        return Region::query()->firstOrCreate(
            ['name' => $name],
            ['slug' => Str::slug($name)]
        );
    }

    /**
     * Get or create a market
     *
     * @param string $name
     * @param string $regionId
     * @return Market
     */
    protected function getOrCreateMarket(string $name, string $regionId): Market
    {
        return Market::query()->firstOrCreate(
            ['name' => $name, 'region_id' => $regionId],
            ['slug' => Str::slug($name)]
        );
    }

    /**
     * Get or create a sub-region
     *
     * @param string $name
     * @param string $marketId
     * @return SubRegion
     */
    protected function getOrCreateSubRegion(string $name, string $marketId): SubRegion
    {
        return SubRegion::query()->firstOrCreate(['name' => $name, 'market_id' => $marketId]);
    }

    /**
     * Get or create a county
     *
     * @param string $name
     * @param string|null $marketId
     * @param string|null $subRegionId
     * @param string $stateCode
     * @return County
     */
    protected function getOrCreateCounty(string $name, ?string $marketId, ?string $subRegionId, string $stateCode): County
    {
        // Standardize the name format: trim and convert to title case
        $name = trim($name);
        $standardizedName = Str::title($name);

        return County::query()->firstOrCreate(
            ['name' => $standardizedName, 'state_code' => strtoupper($stateCode)],
            ['market_id' => $marketId, 'sub_region_id' => $subRegionId]
        );
    }

    /**
     * Get or create a school
     *
     * @param string $name
     * @param string $countyId
     * @param string|null $regionId
     * @return School
     */
    protected function getOrCreateSchool(string $name, string $countyId, ?string $regionId): School
    {
        return School::query()->firstOrCreate(
            ['name' => $name, 'county_id' => $countyId],
            ['region_id' => $regionId]
        );
    }
}
