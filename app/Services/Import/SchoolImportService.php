<?php

namespace App\Services\Import;

use App\Models\County;
use App\Models\Market;
use App\Models\Region;
use App\Models\School;
use App\Models\State;
use App\Models\SubRegion;
use App\Services\Import\Tracking\ImportTrackingService;
use App\Traits\SanitizesImportData;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use League\Csv\Reader;
use League\Csv\Statement;

class SchoolImportService
{
    use SanitizesImportData;

    /**
     * Required columns in the CSV file
     */
    protected array $requiredColumns = [
        'Account Name',
        'State',
        'County',
        'Market',
        'Region'
    ];

    /**
     * Optional columns in the CSV file
     */
    protected array $optionalColumns = [
        'Sub-Region',
        'School Address',
        'School City',
        'School Zip Code'
    ];

    /**
     * Map of CSV column names to model field names
     */
    protected array $columnMapping = [
        'Account Name' => 'School Name',
        'Sub-Region' => 'Sub Region'
    ];

    /**
     * The import tracking service instance.
     */
    protected ImportTrackingService $importTrackingService;

    /**
     * Create a new service instance.
     */
    public function __construct(ImportTrackingService $importTrackingService)
    {
        $this->importTrackingService = $importTrackingService;
    }

    /**
     * Validate the structure of the CSV file
     */
    public function validateCsvStructure(string $filePath): array
    {
        // Only log the start of validation
        Log::info('Validating school import CSV structure');

        try {
            // Determine if we're dealing with a relative path or full path
            $isFullPath = str_starts_with($filePath, '/');
            $relativePath = $isFullPath ? str_replace(Storage::disk('local')->path(''), '', $filePath) : $filePath;
            $fullPath = $isFullPath ? $filePath : Storage::disk('local')->path($relativePath);

            if (!file_exists($fullPath)) {
                Log::error('CSV file not found');
                return [
                    'valid' => false,
                    'errors' => ['CSV file not found.'],
                ];
            }

            // Use League/CSV to read the file
            $csv = Reader::createFromPath($fullPath, 'r');
            $csv->setHeaderOffset(0); // First row contains headers

            // Set UTF-8 encoding for the CSV reader
            $csv->addStreamFilter('convert.iconv.ISO-8859-1/UTF-8//TRANSLIT');

            // Get the header row
            $header = $csv->getHeader();

            // Check for required columns
            $missingColumns = [];
            foreach ($this->requiredColumns as $requiredColumn) {
                if (!in_array($requiredColumn, $header)) {
                    $missingColumns[] = $requiredColumn;
                }
            }

            if (!empty($missingColumns)) {
                Log::error('Missing required columns: ' . implode(', ', $missingColumns));
                return [
                    'valid' => false,
                    'errors' => ['Missing required columns: ' . implode(', ', $missingColumns)],
                ];
            }

            // Count total rows
            $records = Statement::create()->process($csv);
            $totalRows = count($records);

            // Only log the completion of validation
            Log::info('School import CSV validation successful', ['total_rows' => $totalRows]);

            return [
                'valid' => true,
                'total_rows' => $totalRows,
            ];
        } catch (\Exception $e) {
            Log::error('Exception during CSV validation: ' . $this->sanitizeForLogging($e->getMessage()));

            return [
                'valid' => false,
                'errors' => ['Error validating CSV file: ' . $e->getMessage()],
            ];
        }
    }

    /**
     * Process a single row from the CSV file
     */
    public function processRow(array $row): array
    {
        try {
            DB::beginTransaction();

            // Get or create state
            $state = $this->getOrCreateState($row['State']);
            if (!$state) {
                throw new \Exception("Invalid state code: {$row['State']}");
            }

            // Get or create region
            $region = $this->getOrCreateRegion($row['Region']);

            // Get or create market
            $market = $this->getOrCreateMarket($row['Market'], $region->id);

            // Get or create sub-region if provided
            $subRegion = null;
            if (!empty($row['Sub Region'])) {
                $subRegion = $this->getOrCreateSubRegion($row['Sub Region'], $market->id);
            }

            // Get or create county
            $county = $this->getOrCreateCounty(
                $row['County'],
                $market->id,
                $subRegion ? $subRegion->id : null,
                $state->code
            );

            // Get or create school
            $school = $this->getOrCreateSchool(
                $row['School Name'],
                $county->id,
                $region->id,
                $row['School Address'] ?? null,
                $row['School City'] ?? null,
                $row['School Zip Code'] ?? null
            );

            DB::commit();

            return [
                'success' => true,
                'school' => $school,
                'school_id' => $school->id,
                'created' => $school->wasRecentlyCreated,
                'message' => "Successfully processed school: {$row['School Name']}",
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            // Only log errors with minimal information
            Log::error('Error processing school: ' . ($row['School Name'] ?? 'Unknown') . ' - ' . $e->getMessage());

            return [
                'success' => false,
                'message' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get or create a state record
     */
    protected function getOrCreateState(string $stateCode): ?State
    {
        $stateCode = strtoupper(trim($stateCode));

        return State::query()->where('code', $stateCode)->first();
    }

    /**
     * Get or create a region record
     */
    protected function getOrCreateRegion(string $name): Region
    {
        $name = trim($name);
        $slug = Str::slug($name);

        return Region::query()->firstOrCreate(
            ['slug' => $slug],
            [
                'name' => $name,
                'color' => '#' . substr(md5($name), 0, 6), // Generate a color based on the name
            ]
        );
    }

    /**
     * Get or create a market record
     */
    protected function getOrCreateMarket(string $name, string $regionId): Market
    {
        $name = trim($name);
        $slug = Str::slug($name);

        return Market::query()->firstOrCreate(
            ['slug' => $slug],
            [
                'name' => $name,
                'region_id' => $regionId,
            ]
        );
    }

    /**
     * Get or create a sub-region record
     */
    protected function getOrCreateSubRegion(string $name, string $marketId): SubRegion
    {
        $name = trim($name);
        $slug = Str::slug($name);

        return SubRegion::query()->firstOrCreate(
            ['slug' => $slug, 'market_id' => $marketId],
            ['name' => $name]
        );
    }

    /**
     * Get or create a county record
     */
    protected function getOrCreateCounty(string $name, string $marketId, ?string $subRegionId, string $stateCode): County
    {
        // Standardize the name format: trim and convert to title case
        $name = trim($name);
        $standardizedName = Str::title($name);

        return County::query()->firstOrCreate(
            ['name' => $standardizedName, 'state_code' => $stateCode],
            [
                'market_id' => $marketId,
                'sub_region_id' => $subRegionId,
            ]
        );
    }

    /**
     * Get or create a school record
     */
    protected function getOrCreateSchool(string $name, string $countyId, string $regionId, ?string $address = null, ?string $city = null, ?string $zipCode = null): School
    {
        $name = trim($name);

        return School::query()->firstOrCreate(
            ['name' => $name, 'county_id' => $countyId],
            [
                'region_id' => $regionId,
                'address' => $address,
                'city' => $city,
                'zip_code' => $zipCode,
            ]
        );
    }

    /**
     * Process the CSV file
     */
    public function processCsvFile(string $filePath): array
    {
        // Log the start of processing
        Log::info('Starting school import from CSV');

        try {
            // Determine if we're dealing with a relative path or full path
            $isFullPath = str_starts_with($filePath, '/');
            $relativePath = $isFullPath ? str_replace(Storage::disk('local')->path(''), '', $filePath) : $filePath;
            $fullPath = $isFullPath ? $filePath : Storage::disk('local')->path($relativePath);
            $fileName = basename($fullPath);

            // Validate the CSV structure
            $validationResult = $this->validateCsvStructure($fullPath);
            if (!$validationResult['valid']) {
                return [
                    'success' => false,
                    'errors' => $validationResult['errors'],
                ];
            }

            // Create import record
            $import = $this->importTrackingService->createImport(
                'schools',
                $fullPath,
                $fileName,
                ['source' => 'admin_upload']
            );

            // Use League/CSV to read the file
            $csv = Reader::createFromPath($fullPath, 'r');
            $csv->setHeaderOffset(0); // First row contains headers

            // Set UTF-8 encoding for the CSV reader
            $csv->addStreamFilter('convert.iconv.ISO-8859-1/UTF-8//TRANSLIT');

            // Create a statement to process the CSV
            $records = Statement::create()->process($csv);

            $successCount = 0;
            $errorCount = 0;
            $errors = [];
            $totalRows = count($records);
            $processedRows = 0;

            // Mark import as started
            $this->importTrackingService->startImport($import, $totalRows);

            // Process each row within a transaction
            foreach ($records as $offset => $record) {
                $processedRows++;

                // Skip empty rows
                if (empty(array_filter($record))) {
                    continue;
                }

                // Sanitize record data to ensure valid UTF-8
                $sanitizedRecord = $this->sanitizeRecordData($record);

                // Map column names if needed
                $mappedRow = [];
                foreach ($sanitizedRecord as $key => $value) {
                    $mappedKey = $this->columnMapping[$key] ?? $key;
                    $mappedRow[$mappedKey] = $value;
                }

                // Process the row
                $result = $this->processRow($mappedRow);

                // Record the outcome in the import tracking system
                $status = $result['success'] ? ($result['created'] ? 'created' : 'updated') : 'failed';
                $errors = $result['success'] ? null : ['error' => $result['message']];
                $modelType = $result['success'] ? 'App\\Models\\School' : null;
                $modelId = $result['success'] ? $result['school_id'] : null;

                $this->importTrackingService->recordOutcome(
                    $import,
                    $offset + 1, // Row number (1-indexed)
                    $status,
                    $sanitizedRecord,
                    $result['success'] ? $result : null,
                    $modelType,
                    $modelId,
                    $errors
                );

                if ($result['success']) {
                    $successCount++;
                } else {
                    $errorCount++;
                    $errors[] = "Row " . ($offset + 2) . ": " . $result['message']; // +2 because offset is 0-based and we have a header row
                }

                // Log progress periodically (every 100 rows)
                if ($processedRows % 500 === 0 || $processedRows === $totalRows) {
                    Log::info("School import progress: {$processedRows}/{$totalRows} rows processed");
                }
            }

            // Mark import as completed
            $this->importTrackingService->completeImport($import);

            // Log completion
            Log::info("School import completed: {$totalRows} total, {$successCount} successful, {$errorCount} failed");

            return [
                'success' => true,
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'errors' => $errors,
            ];
        } catch (\Exception $e) {
            Log::error('Error processing CSV file: ' . $this->sanitizeForLogging($e->getMessage()));

            // If we have an import record, mark it as failed
            if (isset($import)) {
                $this->importTrackingService->failImport($import, $e->getMessage());
            }

            return [
                'success' => false,
                'errors' => ['Error processing CSV file: ' . $e->getMessage()],
            ];
        }
    }
}
