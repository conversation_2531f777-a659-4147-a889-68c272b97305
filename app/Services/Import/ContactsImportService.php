<?php

namespace App\Services\Import;

use App\Models\County;
use App\Models\Market;
use App\Models\Organization;
use App\Models\Region;
use App\Models\School;
use App\Models\State;
use App\Models\SubRegion;
use App\Repositories\ContactRepository;
use App\Services\Import\Tracking\ImportTrackingService;
use App\Services\ContactService;
use App\Traits\SanitizesImportData;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use League\Csv\Reader;
use League\Csv\Statement;

class ContactsImportService
{
    use SanitizesImportData;

    /**
     * Required columns in the CSV file
     */
    protected array $requiredColumns = [
        'Account Name',
        'Account Type',
        'First Name',
        'Last Name',
        'Email',
        'State'
    ];

    /**
     * Optional columns in the CSV file
     */
    protected array $optionalColumns = [
        'Title',
        'Region',
        'Market',
        'Sub-Region',
        'Category',
        'Level'
    ];

    /**
     * The contact repository instance.
     */
    protected ContactRepository $contactRepository;

    /**
     * The import tracking service instance.
     */
    protected ImportTrackingService $importTrackingService;

    /**
     * The contact service instance.
     */
    protected ContactService $contactService;

    /**
     * Create a new service instance.
     */
    public function __construct(
        ContactRepository $contactRepository,
        ImportTrackingService $importTrackingService,
        ContactService $contactService
    ) {
        $this->contactRepository = $contactRepository;
        $this->importTrackingService = $importTrackingService;
        $this->contactService = $contactService;
    }

    /**
     * Validate the CSV structure
     *
     * @param string $filePath
     * @return array
     */
    public function validateCsvStructure(string $filePath): array
    {
        $result = [
            'valid' => true,
            'errors' => [],
        ];

        try {
            // Check if file exists
            if (!file_exists($filePath)) {
                $result['valid'] = false;
                $result['errors'][] = "File not found: {$filePath}";
                return $result;
            }

            // Read the CSV file
            $csv = Reader::createFromPath($filePath, 'r');
            $csv->setHeaderOffset(0);

            // Get the header row
            $headers = $csv->getHeader();

            // Check for required columns
            $missingColumns = [];
            foreach ($this->requiredColumns as $requiredColumn) {
                if (!in_array($requiredColumn, $headers)) {
                    $missingColumns[] = $requiredColumn;
                }
            }

            if (!empty($missingColumns)) {
                $result['valid'] = false;
                $result['errors'][] = "Missing required columns: " . implode(', ', $missingColumns);
            }

            return $result;
        } catch (\Exception $e) {
            $result['valid'] = false;
            $result['errors'][] = "Error validating CSV structure: " . $e->getMessage();
            return $result;
        }
    }

    /**
     * Process the CSV file
     *
     * @param string $filePath
     * @return array
     */
    public function processCsvFile(string $filePath): array
    {
        $result = [
            'success' => true,
            'errors' => [],
            'results' => [
                'total' => 0,
                'created' => 0,
                'updated' => 0,
                'failed' => 0,
                'schools_created' => 0,
                'schools_matched' => 0,
                'organizations_created' => 0,
                'organizations_matched' => 0,
            ],
        ];

        try {
            // Extract file name from path
            $fileName = basename($filePath);

            // Create import record
            $import = $this->importTrackingService->createImport(
                'contacts',
                $filePath,
                $fileName,
                ['source' => 'admin_upload']
            );

            // Read the CSV file
            $csv = Reader::createFromPath(Storage::disk('local')->path($filePath), 'r');
            $csv->setHeaderOffset(0);

            // Set UTF-8 encoding for the CSV reader
            $csv->addStreamFilter('convert.iconv.ISO-8859-1/UTF-8//TRANSLIT');

            $records = Statement::create()->process($csv);
            $totalRows = $records->count();
            $result['results']['total'] = $totalRows;

            // Mark import as started
            $this->importTrackingService->startImport($import, $totalRows);

            foreach ($records as $index => $record) {
                try {
                    // Sanitize record data to ensure valid UTF-8
                    $sanitizedRecord = $this->sanitizeRecordData($record);

                    // Process each row
                    $rowResult = $this->processRow($sanitizedRecord);

                    // Record the outcome in the import tracking system
                    $status = $rowResult['success'] ? ($rowResult['created'] ? 'created' : 'updated') : 'failed';
                    $errors = $rowResult['success'] ? null : $rowResult['errors'];
                    $modelType = $rowResult['success'] ? 'App\\Models\\Contact' : null;
                    $modelId = $rowResult['success'] && isset($rowResult['contact_id']) ? $rowResult['contact_id'] : null;

                    $this->importTrackingService->recordOutcome(
                        $import,
                        $index + 1, // Row number (1-indexed)
                        $status,
                        $sanitizedRecord,
                        $rowResult['success'] ? $rowResult : null,
                        $modelType,
                        $modelId,
                        $errors
                    );

                    // Update statistics
                    if ($rowResult['success']) {
                        if ($rowResult['created']) {
                            $result['results']['created']++;
                        } else {
                            $result['results']['updated']++;
                        }

                        if ($rowResult['school_created']) {
                            $result['results']['schools_created']++;
                        } else if ($rowResult['school_matched']) {
                            $result['results']['schools_matched']++;
                        }

                        if ($rowResult['organization_created']) {
                            $result['results']['organizations_created']++;
                        } else if ($rowResult['organization_matched']) {
                            $result['results']['organizations_matched']++;
                        }
                    } else {
                        $result['results']['failed']++;
                        $result['errors'][] = "Row " . ($index + 2) . ": " . implode(', ', $rowResult['errors']);
                    }
                } catch (\Exception $e) {
                    Log::error("Error processing row " . ($index + 2) . ": " . $e->getMessage(), [
                        'record' => $record,
                        'exception' => $e,
                    ]);

                    // Record the exception in the import tracking system
                    $this->importTrackingService->recordOutcome(
                        $import,
                        $index + 1, // Row number (1-indexed)
                        'failed',
                        $record,
                        null,
                        null,
                        null,
                        ['exception' => $e->getMessage()]
                    );

                    $result['results']['failed']++;
                    $result['errors'][] = "Row " . ($index + 2) . ": " . $e->getMessage();
                }
            }

            // Mark import as completed
            $this->importTrackingService->completeImport($import);

            return $result;
        } catch (\Exception $e) {
            Log::error("Error processing CSV file: " . $e->getMessage(), [
                'file_path' => $filePath,
                'exception' => $e,
            ]);

            // If we have an import record, mark it as failed
            if (isset($import)) {
                $this->importTrackingService->failImport($import, $e->getMessage());
            }

            $result['success'] = false;
            $result['errors'][] = "Error processing CSV file: " . $e->getMessage();
            return $result;
        }
    }

    /**
     * Process a single row from the CSV
     *
     * @param array $row
     * @return array
     */
    public function processRow(array $row): array
    {
        $result = [
            'success' => true,
            'errors' => [],
            'created' => false,
            'updated' => false,
            'school_created' => false,
            'school_matched' => false,
            'organization_created' => false,
            'organization_matched' => false,
        ];

        try {
            DB::beginTransaction();

            // Validate required fields
            $requiredFields = ['First Name', 'Last Name', 'Email', 'Account Name', 'Account Type', 'State'];
            foreach ($requiredFields as $field) {
                if (empty($row[$field])) {
                    $result['success'] = false;
                    $result['errors'][] = "Missing required field: {$field}";
                }
            }

            if (!$result['success']) {
                DB::rollBack();
                return $result;
            }

            // Resolve geographical entities
            $stateCode = $row['State'] ?? null;
            $regionName = $row['Region'] ?? null;
            $marketName = $row['Market'] ?? null;
            $subRegionName = $row['Sub-Region'] ?? null;

            // Resolve state
            $state = null;
            if ($stateCode) {
                $state = $this->getOrCreateState($stateCode);
                if (!$state) {
                    $result['success'] = false;
                    $result['errors'][] = "Could not resolve state: {$stateCode}";
                    DB::rollBack();
                    return $result;
                }
            }

            // Resolve region
            $region = null;
            if ($regionName) {
                $region = $this->getOrCreateRegion($regionName);
            }

            // Resolve market
            $market = null;
            if ($marketName && $region) {
                $market = $this->getOrCreateMarket($marketName, $region->id);
            }

            // Resolve sub-region
            $subRegion = null;
            if ($subRegionName && $market) {
                $subRegion = $this->getOrCreateSubRegion($subRegionName, $market->id);
            }

            // Determine if we need to create/match a school or organization
            $accountName = $row['Account Name'];
            $accountType = $row['Account Type'];
            $school = null;
            $organization = null;

            if (strtolower($accountType) === 'school') {
                // Handle school
                $school = $this->getOrCreateSchool($accountName, null, $region ? $region->id : null, $result);
                $result['school_matched'] = !$result['school_created'];
            } else {
                // Handle organization
                $organization = $this->getOrCreateOrganization($accountName, $accountType, $result);
                $result['organization_matched'] = !$result['organization_created'];
            }

            // Create or update contact
            $contactData = [
                'first_name' => $row['First Name'],
                'last_name' => $row['Last Name'],
                'email' => $row['Email'],
                'title' => $row['Title'] ?? null,
                'state_id' => $state ? $state->id : null,
                'region_id' => $region ? $region->id : null,
                'market_id' => $market ? $market->id : null,
                'sub_region_id' => $subRegion ? $subRegion->id : null,
                'school_id' => $school ? $school->id : null,
                'organization_id' => $organization ? $organization->id : null,
                'metadata' => [
                    'category' => $row['Category'] ?? null,
                    'level' => $row['Level'] ?? null,
                    'import_source' => 'csv_import',
                    'import_date' => now()->toDateTimeString(),
                ],
            ];

            // Check if contact already exists
            $existingContact = $this->contactRepository->findByEmail($row['Email']);

            if ($existingContact) {
                // Update existing contact through ContactService
                $contact = $this->contactService->update($existingContact, $contactData);
                $result['updated'] = true;
                $result['contact_id'] = $contact->id;
            } else {
                // Create new contact through ContactService
                $contact = $this->contactService->create($contactData);
                $result['created'] = true;
                $result['contact_id'] = $contact->id;
            }

            DB::commit();
            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Error processing row: " . $e->getMessage(), [
                'row' => $row,
                'exception' => $e,
            ]);

            $result['success'] = false;
            $result['errors'][] = "Error processing row: " . $e->getMessage();
            return $result;
        }
    }

    /**
     * Get or create a state
     *
     * @param string $stateCode
     * @return State|null
     */
    protected function getOrCreateState(string $stateCode): ?State
    {
        return State::query()->firstWhere('code', $stateCode);
    }

    /**
     * Get or create a region
     *
     * @param string $name
     * @return Region
     */
    protected function getOrCreateRegion(string $name): Region
    {
        return Region::query()->firstOrCreate(
            ['name' => $name],
            ['name' => $name]
        );
    }

    /**
     * Get or create a market
     *
     * @param string $name
     * @param string $regionId
     * @return Market
     */
    protected function getOrCreateMarket(string $name, string $regionId): Market
    {
        return Market::query()->firstOrCreate(
            ['name' => $name, 'region_id' => $regionId],
            ['name' => $name, 'region_id' => $regionId]
        );
    }

    /**
     * Get or create a sub-region
     *
     * @param string $name
     * @param string $marketId
     * @return SubRegion
     */
    protected function getOrCreateSubRegion(string $name, string $marketId): SubRegion
    {
        return SubRegion::query()->firstOrCreate(
            ['name' => $name, 'market_id' => $marketId],
            ['name' => $name, 'market_id' => $marketId]
        );
    }

    /**
     * Get or create a school
     *
     * @param string $name
     * @param string|null $countyId
     * @param string|null $regionId
     * @param array $result Reference to the result array to update
     * @return School
     */
    protected function getOrCreateSchool(string $name, ?string $countyId, ?string $regionId, array &$result): School
    {
        $school = School::query()->where('name', $name)->first();

        if (!$school) {
            $school = School::query()->create([
                'name' => $name,
                'county_id' => $countyId,
                'region_id' => $regionId,
            ]);

            $result['school_created'] = true;
        }

        return $school;
    }

    /**
     * Get or create an organization
     *
     * @param string $name
     * @param string $type
     * @param array $result Reference to the result array to update
     * @return Organization
     */
    protected function getOrCreateOrganization(string $name, string $type, array &$result): Organization
    {
        $organization = Organization::query()->where('name', $name)->first();

        if (!$organization) {
            $organization = Organization::query()->create([
                'name' => $name,
                'type' => $type,
            ]);

            $result['organization_created'] = true;
        }

        return $organization;
    }
}
