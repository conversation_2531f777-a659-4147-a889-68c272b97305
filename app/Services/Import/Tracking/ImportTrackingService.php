<?php

namespace App\Services\Import\Tracking;

use App\Models\Import;
use App\Models\ImportRecord;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use League\Csv\Writer;
use SplTempFileObject;

class ImportTrackingService
{
    /**
     * Create a new import record.
     *
     * @param string $type
     * @param string $filePath
     * @param string $fileName
     * @param array $metadata
     * @return Import
     */
    public function createImport(string $type, string $filePath, string $fileName, array $metadata = []): Import
    {
        $import = new Import();
        $import->batch_id = (string) Str::uuid();
        $import->type = $type;
        $import->file_path = $filePath;
        $import->file_name = $fileName;
        $import->status = 'pending';
        $import->metadata = $metadata;
        $import->save();

        return $import;
    }

    /**
     * Mark an import as started.
     *
     * @param Import $import
     * @param int $totalRows
     * @return Import
     */
    public function startImport(Import $import, int $totalRows): Import
    {
        $import->update([
            'status' => 'processing',
            'started_at' => now(),
            'total_rows' => $totalRows,
        ]);

        return $import;
    }

    /**
     * Mark an import as completed.
     *
     * @param Import $import
     * @return Import
     */
    public function completeImport(Import $import): Import
    {
        // Calculate counts from records
        $createdCount = $import->records()->where('status', 'created')->count();
        $updatedCount = $import->records()->where('status', 'updated')->count();
        $failedCount = $import->records()->where('status', 'failed')->count();

        $import->update([
            'status' => 'completed',
            'completed_at' => now(),
            'created_count' => $createdCount,
            'updated_count' => $updatedCount,
            'failed_count' => $failedCount,
        ]);

        return $import;
    }

    /**
     * Mark an import as failed.
     *
     * @param Import $import
     * @param string $error
     * @return Import
     */
    public function failImport(Import $import, string $error): Import
    {
        // Update the metadata to include the error
        $metadata = $import->metadata ?? [];
        $metadata['error'] = $error;

        $import->update([
            'status' => 'failed',
            'completed_at' => now(),
            'metadata' => $metadata,
        ]);

        return $import;
    }

    /**
     * Record a row outcome.
     *
     * @param Import $import
     * @param int $rowNumber
     * @param string $status
     * @param array $rawData
     * @param array|null $processedData
     * @param string|null $modelType
     * @param string|null $modelId
     * @param array|null $errors
     * @return ImportRecord
     */
    public function recordOutcome(
        Import $import,
        int $rowNumber,
        string $status,
        array $rawData,
        ?array $processedData = null,
        ?string $modelType = null,
        ?string $modelId = null,
        ?array $errors = null
    ): ImportRecord {
        $record = new ImportRecord();
        $record->import_id = $import->id;
        $record->row_number = $rowNumber;
        $record->status = $status;
        $record->raw_data = $rawData;
        $record->processed_data = $processedData;
        $record->model_type = $modelType;
        $record->model_id = $modelId;
        $record->errors = $errors;
        $record->save();

        return $record;
    }

    /**
     * Generate a CSV file for records with a specific status.
     *
     * @param Import $import
     * @param string $status
     * @return string The path to the generated CSV file
     */
    public function generateCsvForStatus(Import $import, string $status): string
    {
        try {
            // Get records with the specified status
            $records = $import->getRecordsByStatus($status);

            if ($records->isEmpty()) {
                throw new \Exception("No {$status} records found for this import.");
            }

            // Create a CSV writer
            $csv = Writer::createFromFileObject(new SplTempFileObject());

            // Determine headers from the first record's raw data
            $firstRecord = $records->first();
            $headers = array_keys($firstRecord->raw_data);

            // Add error column for failed records
            if ($status === 'failed') {
                $headers[] = 'Error';
            }

            // Insert headers
            $csv->insertOne($headers);

            // Insert data rows
            foreach ($records as $record) {
                $row = $record->raw_data;

                // Add error message for failed records
                if ($status === 'failed' && $record->errors) {
                    $errorMessages = [];
                    foreach ($record->errors as $field => $errors) {
                        if (is_array($errors)) {
                            foreach ($errors as $error) {
                                $errorMessages[] = "{$field}: {$error}";
                            }
                        } else {
                            $errorMessages[] = $errors;
                        }
                    }
                    $row['Error'] = implode('; ', $errorMessages);
                }

                $csv->insertOne($row);
            }

            // Generate a filename
            $filename = "import_{$import->id}_{$status}_" . now()->format('Y-m-d_H-i-s') . '.csv';
            $path = storage_path("app/exports/{$filename}");

            // Ensure the directory exists
            if (!file_exists(dirname($path))) {
                mkdir(dirname($path), 0755, true);
            }

            // Save the CSV to a file
            file_put_contents($path, $csv->getContent());

            return $path;
        } catch (\Exception $e) {
            Log::error('Error generating CSV', [
                'import_id' => $import->id,
                'status' => $status,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }
}
