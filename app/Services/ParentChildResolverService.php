<?php

namespace App\Services;

use App\Enums\ProfileType;
use App\Models\User;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class ParentChildResolverService
{
    /**
     * Resolve the child account for a parent user
     *
     * @param User $user The parent user
     * @return User|null The linked child user (positive athlete)
     */
    public function resolveChildAccount(User $user): ?User
    {
        // Verify the user is a parent
        if ($user->profile_type !== ProfileType::PARENT) {
            return null;
        }

        // Try to find linked positive athlete children
        return User::query()
            ->where('profile_type', ProfileType::POSITIVE_ATHLETE)
            ->where('parent_id', $user->id)
            ->first();
    }

    /**
     * Check if a parent user has any linked children
     *
     * @param User $user The parent user
     * @return bool Whether the parent has any linked children
     */
    public function hasLinkedChildren(User $user): bool
    {
        return $user->profile_type === ProfileType::PARENT &&
               User::query()
                   ->where('profile_type', ProfileType::POSITIVE_ATHLETE)
                   ->where('parent_id', $user->id)
                   ->exists();
    }

    /**
     * Get the effective user for API operations
     *
     * For parents, returns the linked child if available
     * For other users, returns the user themselves
     *
     * @param User $user The authenticated user
     * @return User The effective user for API operations
     * @throws ModelNotFoundException If no linked child exists for a parent
     */
    public function getEffectiveUser(User $user): User
    {
        if ($user->profile_type !== ProfileType::PARENT) {
            return $user;
        }

        $child = $this->resolveChildAccount($user);

        if (!$child) {
            throw new ModelNotFoundException('No linked athlete account found for this parent');
        }

        return $child;
    }
}
