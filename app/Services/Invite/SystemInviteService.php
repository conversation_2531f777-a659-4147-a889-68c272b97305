<?php

namespace App\Services\Invite;

use App\Data\SystemInvites\AlumniInviteData;
use App\Data\SystemInvites\CollegeAthleteInviteData;
use App\Data\SystemInvites\ParentSystemInviteData;
use App\Data\SystemInvites\PositiveAthleteInviteData;
use App\Data\SystemInvites\TeamStudentInviteData;
use App\Mail\SystemInvites\AlumniInviteMail;
use App\Mail\SystemInvites\CollegeAthleteInviteMail;
use App\Models\Nomination;
use App\Models\SystemInvite;
use App\Enums\ProfileType;
use App\Enums\SystemInviteStatus;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use App\Mail\SystemInvites\ParentInviteMail;
use App\Mail\SystemInvites\PositiveAthleteInviteMail;
use App\Data\SystemInvites\ProfessionalInviteData;
use App\Mail\SystemInvites\ProfessionalInviteMail;
use App\Data\SystemInvites\PositiveCoachInviteData;
use App\Mail\SystemInvites\PositiveCoachInviteMail;
use App\Mail\SystemInvites\TeamStudentInviteMail;
use App\Data\SystemInvites\TeamCoachInviteData;
use App\Mail\SystemInvites\TeamCoachInviteMail;
use App\Data\SystemInvites\AthleticsDirectorInviteData;
use App\Mail\SystemInvites\AthleticsDirectorInviteMail;
use App\Data\SystemInvites\SponsorInviteData;
use App\Mail\SystemInvites\SponsorInviteMail;
use App\Models\Organization;


class SystemInviteService
{
    /**
     * Generate a unique mobile code for invites
     *
     * @return string
     */
    private function generateMobileCode(): string
    {
        do {
            $mobileCode = strtoupper(Str::random(6));
        } while (SystemInvite::query()->where('mobile_code', $mobileCode)->exists());

        return $mobileCode;
    }

    public function createForPositiveAthlete(Nomination $nomination): SystemInvite
    {
        $token = Str::random(64);
        $invite = SystemInvite::query()->create([
            'email' => $nomination->email,
            'type' => ProfileType::POSITIVE_ATHLETE->value,
            'status' => SystemInviteStatus::PENDING->value,
            'token' => $token,
            'mobile_code' => $this->generateMobileCode(),
            'expires_at' => now()->addDays(30),
            'invite_data' => PositiveAthleteInviteData::fromNomination($nomination, $token),
        ]);

        $nomination->systemInvite()->associate($invite);
        $nomination->save();

        $invite->onboarding()->create([
            'profile_type' => $invite->type,
            'data' => [
                'nomination_id' => $nomination->id,
            ]
        ]);

        $this->sendInviteEmail($invite, $nomination);

        return $invite;
    }

    public function createForParent(ParentSystemInviteData $data): SystemInvite
    {
        $invite = SystemInvite::query()->create([
            'email' => $data->email,
            'type' => ProfileType::PARENT->value,
            'status' => SystemInviteStatus::PENDING->value,
            'token' => $data->token,
            'mobile_code' => $this->generateMobileCode(),
            'expires_at' => now()->addDays(30),
            'invite_data' => $data,
        ]);

        $invite->onboarding()->create([
            'profile_type' => $invite->type,
            'data' => [
                'first_name' => $data->first_name,
                'last_name' => $data->last_name,
                'athlete_user_id' => $data->athlete_user_id,
                'athlete_name' => $data->athlete_name,
            ]
        ]);

        $this->sendInviteEmail($invite);

        return $invite;
    }

    public function createForCollegeAthlete(CollegeAthleteInviteData $data): SystemInvite
    {
        $invite = SystemInvite::query()->create([
            'email' => $data->email,
            'type' => ProfileType::COLLEGE_ATHLETE->value,
            'status' => SystemInviteStatus::PENDING->value,
            'token' => $data->token,
            'mobile_code' => $this->generateMobileCode(),
            'expires_at' => now()->addDays(30),
            'invite_data' => $data,
        ]);

        $invite->onboarding()->create([
            'profile_type' => $invite->type,
            'data' => [
                'first_name' => $data->first_name,
                'last_name' => $data->last_name,
                'college_name' => $data->college_name,
                'sport' => $data->sport,
            ]
        ]);

        $this->sendInviteEmail($invite);

        return $invite;
    }

    public function createForProfessional(ProfessionalInviteData $data): SystemInvite
    {
        $invite = SystemInvite::query()->create([
            'email' => $data->email,
            'type' => ProfileType::PROFESSIONAL->value,
            'status' => SystemInviteStatus::PENDING->value,
            'token' => Str::random(64),
            'mobile_code' => $this->generateMobileCode(),
            'expires_at' => now()->addDays(30),
            'invite_data' => $data,
        ]);

        $invite->onboarding()->create([
            'profile_type' => $invite->type,
            'data' => [
                'first_name' => $data->first_name,
                'last_name' => $data->last_name,
                'company' => $data->company,
            ]
        ]);

        $this->sendInviteEmail($invite);

        return $invite;
    }

    public function createForPositiveCoach(Nomination $nomination): SystemInvite
    {
        $invite = SystemInvite::query()->create([
            'email' => $nomination->email,
            'type' => ProfileType::POSITIVE_COACH->value,
            'status' => SystemInviteStatus::PENDING->value,
            'token' => Str::random(64),
            'mobile_code' => $this->generateMobileCode(),
            'expires_at' => now()->addDays(30),
            'invite_data' => PositiveCoachInviteData::fromNomination($nomination),
        ]);

        $nomination->systemInvite()->associate($invite);
        $nomination->save();

        $invite->onboarding()->create([
            'profile_type' => $invite->type,
            'data' => [
                'nomination_id' => $nomination->id,
            ]
        ]);

        $this->sendInviteEmail($invite);

        return $invite;
    }

    public function createForTeamStudent(TeamStudentInviteData $data): SystemInvite
    {
        $invite = SystemInvite::query()->create([
            'email' => $data->email,
            'type' => ProfileType::TEAM_STUDENT->value,
            'status' => SystemInviteStatus::PENDING->value,
            'token' => Str::random(64),
            'mobile_code' => $this->generateMobileCode(),
            'expires_at' => now()->addDays(30),
            'invite_data' => $data,
        ]);

        $invite->onboarding()->create([
            'profile_type' => $invite->type,
            'data' => [
                'first_name' => $data->first_name,
                'last_name' => $data->last_name,
                'team_name' => $data->team_name,
                'school_name' => $data->school_name,
            ]
        ]);

        $this->sendInviteEmail($invite);

        return $invite;
    }

    public function createForTeamCoach(TeamCoachInviteData $data): SystemInvite
    {
        $invite = SystemInvite::query()->create([
            'email' => $data->email,
            'type' => ProfileType::TEAM_COACH->value,
            'status' => SystemInviteStatus::PENDING->value,
            'token' => Str::random(64),
            'mobile_code' => $this->generateMobileCode(),
            'expires_at' => now()->addDays(30),
            'invite_data' => $data,
        ]);

        $invite->onboarding()->create([
            'profile_type' => $invite->type,
            'data' => [
                'first_name' => $data->first_name,
                'last_name' => $data->last_name,
                'school_name' => $data->school_name,
            ]
        ]);

        $this->sendInviteEmail($invite);

        return $invite;
    }

    public function createForAthleticsDirector(AthleticsDirectorInviteData $data): SystemInvite
    {
        $invite = SystemInvite::query()->create([
            'email' => $data->email,
            'type' => ProfileType::ATHLETICS_DIRECTOR->value,
            'status' => SystemInviteStatus::PENDING->value,
            'token' => $data->token,
            'mobile_code' => $this->generateMobileCode(),
            'expires_at' => now()->addDays(30),
            'invite_data' => $data,
        ]);

        $invite->onboarding()->create([
            'profile_type' => $invite->type,
            'data' => [
                'first_name' => $data->first_name,
                'last_name' => $data->last_name,
                'school_name' => $data->school_name,
                'school_district' => $data->school_district,
            ]
        ]);

        $this->sendInviteEmail($invite);

        return $invite;
    }

    public function createForSponsor(SponsorInviteData $data): SystemInvite
    {
        // Validate that organization exists
        $organization = Organization::query()->find($data->organization_id);
        if (!$organization) {
            throw new \InvalidArgumentException("Organization with ID {$data->organization_id} not found");
        }

        $invite = SystemInvite::query()->create([
            'email' => $data->email,
            'type' => ProfileType::SPONSOR->value,
            'status' => SystemInviteStatus::PENDING->value,
            'token' => $data->token,
            'mobile_code' => $this->generateMobileCode(),
            'expires_at' => now()->addDays(30),
            'invite_data' => $data,
        ]);

        $invite->onboarding()->create([
            'profile_type' => $invite->type,
            'state' => \App\States\Onboarding\States\AccountInfo::class,
            'data' => [
                'first_name' => $data->first_name,
                'last_name' => $data->last_name,
                'company_name' => $data->company_name,
                'organization_id' => $data->organization_id,
                'organization_name' => $organization->name, // Store the organization name for reference
            ]
        ]);

        $this->sendInviteEmail($invite);

        return $invite;
    }

    public function createForAlumni(AlumniInviteData $data): SystemInvite
    {
        $invite = SystemInvite::query()->create([
            'email' => $data->email,
            'type' => ProfileType::ALUMNI->value,
            'status' => SystemInviteStatus::PENDING->value,
            'token' => $data->token,
            'mobile_code' => $this->generateMobileCode(),
            'expires_at' => now()->addDays(30),
            'invite_data' => $data,
        ]);

        $invite->onboarding()->create([
            'profile_type' => $invite->type,
            'state' => \App\States\Onboarding\States\AccountInfo::class,
            'data' => [
                'first_name' => $data->first_name,
                'last_name' => $data->last_name,
                'life_stage' => $data->life_stage ? $data->life_stage->value : null,
                'college_name' => $data->college_name,
                'company' => $data->company,
                'sport' => $data->sport,
                'intended_profile_type' => ProfileType::ALUMNI->value,
            ]
        ]);

        $this->sendInviteEmail($invite);

        return $invite;
    }

    private function sendInviteEmail(SystemInvite $invite): void
    {
        try {
            $registrationUrl = config('app.frontend_url') . "/invite/{$invite->token}";

            $mail = match ($invite->type) {
                ProfileType::POSITIVE_ATHLETE => new PositiveAthleteInviteMail($invite, $registrationUrl),
                ProfileType::POSITIVE_COACH => new PositiveCoachInviteMail($invite, $registrationUrl),
                ProfileType::PARENT => new ParentInviteMail($invite, $registrationUrl),
                ProfileType::COLLEGE_ATHLETE => new CollegeAthleteInviteMail($invite, $registrationUrl),
                ProfileType::PROFESSIONAL => new ProfessionalInviteMail($invite, $registrationUrl),
                ProfileType::TEAM_STUDENT => new TeamStudentInviteMail($invite, $registrationUrl),
                ProfileType::TEAM_COACH => new TeamCoachInviteMail($invite, $registrationUrl),
                ProfileType::ATHLETICS_DIRECTOR => new AthleticsDirectorInviteMail($invite, $registrationUrl),
                ProfileType::SPONSOR => new SponsorInviteMail($invite, $registrationUrl),
                ProfileType::ALUMNI => new AlumniInviteMail($invite),
                default => throw new \InvalidArgumentException("No mail template for invite type: {$invite->type->value}"),
            };

            Mail::to($invite->email)->send($mail);

            $invite->update(['email_sent_at' => now()]);
        } catch (\Throwable $e) {
            report($e);
        }
    }
}
