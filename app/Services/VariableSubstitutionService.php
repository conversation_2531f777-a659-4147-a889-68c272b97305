<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class VariableSubstitutionService
{
    /**
     * Default data sources for context resolution.
     */
    protected array $dataSources = [];

    /**
     * Cache for resolved variables during processing.
     */
    protected array $variableCache = [];

    /**
     * Available variable contexts and their default fields.
     */
    protected array $availableContexts = [
        'user' => [
            'id', 'firstname', 'lastname', 'name', 'email', 'phone',
            'created_at', 'updated_at', 'graduation_year'
        ],
        'nominee' => [
            'id', 'firstname', 'lastname', 'name', 'email', 'phone',
            'grade', 'gender', 'sport1', 'sport2', 'sport3', 'school',
            'nominator_name', 'nomination_type', 'created_at'
        ],
        'nominator' => [
            'id', 'firstname', 'lastname', 'name', 'email', 'phone',
            'title', 'organization', 'created_at'
        ],
        'system' => [
            'app_name', 'app_url', 'current_date', 'current_year'
        ]
    ];

    /**
     * Set the data sources for variable resolution.
     *
     * @param array $dataSources Associative array with context keys
     */
    public function setDataSources(array $dataSources): self
    {
        $this->dataSources = $dataSources;
        $this->variableCache = []; // Clear cache when data sources change
        return $this;
    }

    /**
     * Add a data source for a specific context.
     *
     * @param string $context The context key (user, nominee, nominator, etc.)
     * @param mixed $data The data object or array
     */
    public function addDataSource(string $context, $data): self
    {
        $this->dataSources[$context] = $data;
        unset($this->variableCache[$context]); // Clear cache for this context
        return $this;
    }

    /**
     * Process a template string by substituting all variables.
     *
     * @param string|null $template The template string to process
     * @param array $additionalData Additional data to merge with existing sources
     * @return string The processed template with variables substituted
     */
    public function processTemplate(?string $template, array $additionalData = []): string
    {
        if (empty($template)) {
            return '';
        }

        // Merge additional data with existing sources
        $workingData = array_merge($this->dataSources, $additionalData);

        return preg_replace_callback(
            '/\{([^}]+)\}/',
            function ($matches) use ($workingData) {
                return $this->resolveVariable($matches[1], $workingData);
            },
            $template
        );
    }

    /**
     * Extract all variables from a template string.
     *
     * @param string|null $template The template to analyze
     * @return array Array of unique variables found
     */
    public function extractVariables(?string $template): array
    {
        if (empty($template)) {
            return [];
        }

        preg_match_all('/\{([^}]+)\}/', $template, $matches);

        return array_unique($matches[1] ?? []);
    }

    /**
     * Get available variables for a given context.
     *
     * @param string|null $context The context to get variables for
     * @return array Array of available variable names
     */
    public function getAvailableVariables(?string $context = null): array
    {
        if ($context && isset($this->availableContexts[$context])) {
            return array_map(
                fn($field) => "{$context}.{$field}",
                $this->availableContexts[$context]
            );
        }

        // Return all available variables across all contexts
        $allVariables = [];
        foreach ($this->availableContexts as $contextName => $fields) {
            foreach ($fields as $field) {
                $allVariables[] = "{$contextName}.{$field}";
            }
        }

        return $allVariables;
    }

    /**
     * Validate that all variables in a template have available data sources.
     *
     * @param string|null $template The template to validate
     * @param array $additionalData Additional data to consider
     * @return array Array of missing variables
     */
    public function validateTemplate(?string $template, array $additionalData = []): array
    {
        $variables = $this->extractVariables($template);
        $workingData = array_merge($this->dataSources, $additionalData);
        $missing = [];

        foreach ($variables as $variable) {
            $value = $this->resolveVariable($variable, $workingData, false);
            if ($value === null) {
                $missing[] = $variable;
            }
        }

        return $missing;
    }

    /**
     * Resolve a single variable to its value.
     *
     * @param string $variable The variable name (e.g., 'user.firstname')
     * @param array $data The data sources to resolve from
     * @param bool $gracefulFallback Whether to return empty string for missing values
     * @return string|null The resolved value or null if not found
     */
    protected function resolveVariable(string $variable, array $data, bool $gracefulFallback = true): ?string
    {
        // Check cache first
        $cacheKey = md5($variable . serialize($data));
        if (isset($this->variableCache[$cacheKey])) {
            return $this->variableCache[$cacheKey];
        }

        $value = null;

        // Handle system variables
        if (Str::startsWith($variable, 'system.')) {
            $value = $this->resolveSystemVariable($variable);
        } else {
            // Handle data context variables (user.field, nominee.field, etc.)
            $value = data_get($data, $variable);

            // If not found directly, try to resolve from object properties
            if ($value === null) {
                $value = $this->resolveFromObject($variable, $data);
            }
        }

        // Convert to string for template usage
        if ($value !== null) {
            $value = $this->formatValue($value);
        } elseif ($gracefulFallback) {
            $value = '';
            Log::debug("Variable '{$variable}' not found, using empty string");
        }

        // Cache the resolved value
        $this->variableCache[$cacheKey] = $value;

        return $value;
    }

    /**
     * Resolve system variables.
     *
     * @param string $variable The system variable name
     * @return string|null The resolved value
     */
    protected function resolveSystemVariable(string $variable): ?string
    {
        return match ($variable) {
            'system.app_name' => config('app.name'),
            'system.app_url' => config('app.url'),
            'system.current_date' => now()->format('F j, Y'),
            'system.current_year' => (string) now()->year,
            default => null
        };
    }

    /**
     * Resolve variables from objects when direct data_get fails.
     *
     * @param string $variable The variable name
     * @param array $data The data sources
     * @return mixed The resolved value
     */
    protected function resolveFromObject(string $variable, array $data)
    {
        $parts = explode('.', $variable);
        if (count($parts) < 2) {
            return null;
        }

        $context = $parts[0];
        $field = $parts[1];

        $object = $data[$context] ?? null;
        if (!$object) {
            return null;
        }

        // Handle Eloquent models
        if (is_object($object) && method_exists($object, 'getAttribute')) {
            return $object->getAttribute($field);
        }

        // Handle arrays
        if (is_array($object)) {
            return $object[$field] ?? null;
        }

        // Handle objects with properties
        if (is_object($object) && property_exists($object, $field)) {
            return $object->{$field};
        }

        return null;
    }

    /**
     * Format a value for template output.
     *
     * @param mixed $value The value to format
     * @return string The formatted value
     */
    protected function formatValue($value): string
    {
        if (is_null($value)) {
            return '';
        }

        if (is_bool($value)) {
            return $value ? 'Yes' : 'No';
        }

        if ($value instanceof \Carbon\Carbon) {
            return $value->format('F j, Y');
        }

        return (string) $value;
    }

    /**
     * Create sample data for testing templates.
     *
     * @param string $context The context to create sample data for
     * @return array Sample data structure
     */
    public function createSampleData(string $context = 'all'): array
    {
        $sampleData = [];

        if ($context === 'all' || $context === 'user') {
            $sampleData['user'] = [
                'id' => 1,
                'firstname' => 'John',
                'lastname' => 'Smith',
                'name' => 'John Smith',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'graduation_year' => 2025,
                'created_at' => now()->format('F j, Y'),
                'updated_at' => now()->format('F j, Y'),
            ];
        }

        if ($context === 'all' || $context === 'nominee') {
            $sampleData['nominee'] = [
                'id' => 1,
                'firstname' => 'Sarah',
                'lastname' => 'Johnson',
                'name' => 'Sarah Johnson',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'grade' => 11,
                'gender' => 'Female',
                'sport1' => 'Basketball',
                'sport2' => 'Track & Field',
                'sport3' => '',
                'school' => 'Example High School',
                'nominator_name' => 'Coach Williams',
                'nomination_type' => 'athlete',
                'created_at' => now()->format('F j, Y'),
            ];
        }

        if ($context === 'all' || $context === 'nominator') {
            $sampleData['nominator'] = [
                'id' => 1,
                'firstname' => 'Mike',
                'lastname' => 'Williams',
                'name' => 'Mike Williams',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'title' => 'Athletic Director',
                'organization' => 'Example High School',
                'created_at' => now()->format('F j, Y'),
            ];
        }

        return $sampleData;
    }

    /**
     * Clear the variable cache.
     */
    public function clearCache(): void
    {
        $this->variableCache = [];
    }
}
