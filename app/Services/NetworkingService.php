<?php

namespace App\Services;

use App\Enums\ConnectionStatus;
use App\Enums\ProfileType;
use App\Models\Connection;
use App\Models\User;
use App\Repositories\ConnectionRepository;
use Exception;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class NetworkingService
{
    /**
     * @var ConnectionRepository
     */
    protected $connectionRepository;

    /**
     * Profile types that should be indexed in the networking search
     */
    public const SEARCHABLE_TYPES = [
        ProfileType::POSITIVE_ATHLETE,
        ProfileType::COLLEGE_ATHLETE,
        ProfileType::PROFESSIONAL,
        ProfileType::POSITIVE_COACH,
    ];

    /**
     * Connection permissions map - defines which profile types can initiate connections
     * with which other profile types
     */
    protected const CONNECTION_MAP = [
        // Positive athletes can connect with other positive athletes, sponsors, and athletics directors
        ProfileType::POSITIVE_ATHLETE->value => [
            ProfileType::POSITIVE_ATHLETE->value,
            ProfileType::SPONSOR->value,
            ProfileType::ATHLETICS_DIRECTOR->value,
        ],

        // Positive coaches can connect with other adults (but not athletes)
        ProfileType::POSITIVE_COACH->value => [
            ProfileType::POSITIVE_COACH->value,
            ProfileType::ATHLETICS_DIRECTOR->value,
            ProfileType::COLLEGE_ATHLETE->value,
            ProfileType::PROFESSIONAL->value,
        ],

        // Athletics directors can connect with other adults AND positive athletes (exception for student communication)
        ProfileType::ATHLETICS_DIRECTOR->value => [
            ProfileType::POSITIVE_COACH->value,
            ProfileType::ATHLETICS_DIRECTOR->value,
            ProfileType::COLLEGE_ATHLETE->value,
            ProfileType::PROFESSIONAL->value,
            ProfileType::POSITIVE_ATHLETE->value,
        ],

        // Sponsors can connect with college athletes and professionals
        ProfileType::SPONSOR->value => [
            ProfileType::COLLEGE_ATHLETE->value,
            ProfileType::PROFESSIONAL->value,
            ProfileType::POSITIVE_ATHLETE->value,
        ],

        // College athletes can connect with adults
        ProfileType::COLLEGE_ATHLETE->value => [
            ProfileType::POSITIVE_COACH->value,
            ProfileType::ATHLETICS_DIRECTOR->value,
            ProfileType::COLLEGE_ATHLETE->value,
            ProfileType::PROFESSIONAL->value,
            ProfileType::SPONSOR->value,
        ],

        // Professionals can connect with adults
        ProfileType::PROFESSIONAL->value => [
            ProfileType::POSITIVE_COACH->value,
            ProfileType::ATHLETICS_DIRECTOR->value,
            ProfileType::COLLEGE_ATHLETE->value,
            ProfileType::PROFESSIONAL->value,
            ProfileType::SPONSOR->value,
        ],

        // Parents have read-only access, no connections
        ProfileType::PARENT->value => [],

        // Utility users have limited access based on configuration
        ProfileType::UTILITY->value => [],

        // Admins can connect with any profile type
        ProfileType::ADMIN->value => [
            ProfileType::POSITIVE_ATHLETE->value,
            ProfileType::POSITIVE_COACH->value,
            ProfileType::ATHLETICS_DIRECTOR->value,
            ProfileType::SPONSOR->value,
            ProfileType::COLLEGE_ATHLETE->value,
            ProfileType::PROFESSIONAL->value,
            ProfileType::PARENT->value,
            ProfileType::UTILITY->value,
            ProfileType::ADMIN->value,
            ProfileType::TEAM_STUDENT->value,
            ProfileType::TEAM_COACH->value,
            ProfileType::ALUMNI->value,
        ],

        // Alumni can connect with other alumni and professionals
        ProfileType::ALUMNI->value => [
            ProfileType::ALUMNI->value,
            ProfileType::PROFESSIONAL->value,
            ProfileType::POSITIVE_COACH->value,
            ProfileType::ATHLETICS_DIRECTOR->value,
            ProfileType::SPONSOR->value,
        ],

        // Team students (future)
        ProfileType::TEAM_STUDENT->value => [
            ProfileType::TEAM_STUDENT->value,
            ProfileType::TEAM_COACH->value,
        ],

        // Team coaches (future)
        ProfileType::TEAM_COACH->value => [
            ProfileType::TEAM_STUDENT->value,
            ProfileType::TEAM_COACH->value,
        ],
    ];

    /**
     * Create a new service instance.
     *
     * @param ConnectionRepository $connectionRepository
     */
    public function __construct(
        ConnectionRepository $connectionRepository
    ) {
        $this->connectionRepository = $connectionRepository;
    }

    /**
     * Create a connection request.
     *
     * @param int $requesterId
     * @param int $recipientId
     * @return Connection
     * @throws Exception
     */
    public function createConnectionRequest(int $requesterId, int $recipientId): Connection
    {
        // Check if users are the same
        if ($requesterId === $recipientId) {
            throw new Exception('Users cannot connect with themselves');
        }

        // Check if users are already connected
        if ($this->connectionRepository->areConnected($requesterId, $recipientId)) {
            throw new Exception('Users are already connected');
        }

        // Check if the connection is blocked
        if ($this->connectionRepository->isBlocked($requesterId, $recipientId)) {
            throw new Exception('Cannot create connection due to blocking restrictions');
        }

        return $this->connectionRepository->createRequest($requesterId, $recipientId);
    }

    /**
     * Accept a connection request.
     *
     * @param int $connectionId
     * @param int $userId
     * @return bool
     * @throws Exception
     */
    public function acceptConnectionRequest(int $connectionId, int $userId): bool
    {
        $result = $this->connectionRepository->acceptRequest($connectionId, $userId);

        if (!$result) {
            throw new Exception('Connection request not found or cannot be accepted');
        }

        return true;
    }

    /**
     * Reject a connection request.
     *
     * @param int $connectionId
     * @param int $userId
     * @return bool
     * @throws Exception
     */
    public function rejectConnectionRequest(int $connectionId, int $userId): bool
    {
        $result = $this->connectionRepository->rejectRequest($connectionId, $userId);

        if (!$result) {
            throw new Exception('Connection request not found or cannot be rejected');
        }

        return true;
    }

    /**
     * Block a user.
     *
     * @param int $userId
     * @param int $blockedUserId
     * @return bool
     * @throws Exception
     */
    public function blockUser(int $userId, int $blockedUserId): bool
    {
        // Check if users are the same
        if ($userId === $blockedUserId) {
            throw new Exception('Users cannot block themselves');
        }

        // Check if the user is already blocked
        if ($this->isUserBlocked($userId, $blockedUserId)) {
            return true;
        }

        // Soft delete the connection to block the user
        return $this->connectionRepository->blockConnection($userId, $blockedUserId);
    }

    /**
     * Unblock a user.
     *
     * @param int $userId
     * @param int $blockedUserId
     * @return bool
     */
    public function unblockUser(int $userId, int $blockedUserId): bool
    {
        // Check if the user is actually blocked
        if (!$this->isUserBlocked($userId, $blockedUserId)) {
            return true;
        }

        // Restore the connection from soft delete to unblock the user
        return $this->connectionRepository->unblockConnection($userId, $blockedUserId);
    }

    /**
     * Get all connections for a user.
     *
     * @param int $userId
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getConnections(int $userId, int $perPage = 15): LengthAwarePaginator
    {
        return $this->connectionRepository->getConnections($userId, $perPage);
    }

    /**
     * Get accepted connections for a user.
     *
     * @param int $userId
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAcceptedConnections(int $userId, int $perPage = 15): LengthAwarePaginator
    {
        return $this->connectionRepository->getAcceptedConnections($userId, $perPage);
    }

    /**
     * Get pending connection requests for a user.
     *
     * @param int $userId
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getPendingRequests(int $userId, int $perPage = 15): LengthAwarePaginator
    {
        return $this->connectionRepository->getPendingRequests($userId, $perPage);
    }

    /**
     * Check if two users are connected.
     *
     * @param int $userOneId
     * @param int $userTwoId
     * @return bool
     */
    public function areUsersConnected(int $userOneId, int $userTwoId): bool
    {
        return $this->connectionRepository->areConnected($userOneId, $userTwoId);
    }

    /**
     * Check if a connection request is pending between two users.
     *
     * @param int $userOneId
     * @param int $userTwoId
     * @return bool
     */
    public function isConnectionPending(int $userOneId, int $userTwoId): bool
    {
        return $this->connectionRepository->isPending($userOneId, $userTwoId);
    }

    /**
     * Check if a user is blocked.
     *
     * @param int $userOneId
     * @param int $userTwoId
     * @return bool
     */
    public function isUserBlocked(int $userOneId, int $userTwoId): bool
    {
        return $this->connectionRepository->isBlocked($userOneId, $userTwoId);
    }

    /**
     * Get a specific connection between two users.
     *
     * @param int $userOneId
     * @param int $userTwoId
     * @return Connection|null
     */
    public function getConnection(int $userOneId, int $userTwoId): ?Connection
    {
        return $this->connectionRepository->getConnection($userOneId, $userTwoId);
    }

    /**
     * Search for users with filtering based on business rules.
     *
     * @param int $userId
     * @param string $query
     * @param array $filters
     * @param int $perPage
     * @param int $page
     * @return array
     */
    public function searchUsers(int $userId, string $query = '', array $filters = [], int $perPage = 15, int $page = 1): array
    {
        // Get the current user
        $currentUser = User::find($userId);
        if (!$currentUser) {
            return [
                'hits' => [],
                'total' => 0,
                'per_page' => $perPage,
                'current_page' => $page,
                'last_page' => 0,
            ];
        }

        // Get IDs of users who have blocked the current user
        $blockedByUserIds = Connection::query()
            ->withTrashed()
            ->whereNotNull('deleted_at')
            ->where(function ($query) use ($userId) {
                $query->where('requester_id', $userId)
                    ->orWhere('recipient_id', $userId);
            })
            ->get()
            ->map(function ($connection) use ($userId) {
                // Return the ID of the other user in the connection
                return $connection->requester_id == $userId
                    ? $connection->recipient_id
                    : $connection->requester_id;
            })
            ->toArray();

        // Build the search query
        $searchBuilder = User::search($query);

        // Apply filters to exclude blocked users and the current user
        $excludeIds = $blockedByUserIds;

        // Add the current user's ID to the excluded IDs
        $excludeIds[] = $userId;

        if (!empty($excludeIds)) {
            $searchBuilder->whereNotIn('id', $excludeIds);
        }

        // Apply additional filters from request
        if (!empty($filters)) {
            foreach ($filters as $key => $value) {
                if (is_array($value)) {
                    $searchBuilder->whereIn($key, $value);
                } else {
                    $searchBuilder->where($key, $value);
                }
            }
        }

        // Execute the search
        $results = $searchBuilder->paginate($perPage, 'page', $page);

        // Post-process results to check if the current user can connect with each result
        $processedResults = collect($results->items())->map(function ($user) use ($currentUser) {
            $user['can_connect'] = $this->canConnect($currentUser->id, $user['id']);
            return $user;
        });

        return [
            'hits' => $processedResults,
            'total' => $results->total(),
            'per_page' => $results->perPage(),
            'current_page' => $results->currentPage(),
            'last_page' => $results->lastPage(),
        ];
    }

    /**
     * Check if a user can connect with another user based on business rules.
     *
     * @param int $userId
     * @param int $targetUserId
     * @return bool
     */
    public function canConnect(int $userId, int $targetUserId): bool
    {
        // Get both users
        $user = User::find($userId);
        $targetUser = User::find($targetUserId);

        if (!$user || !$targetUser) {
            return false;
        }

        // Check if the target user has a public profile
        if (!$targetUser->public_profile) {
            return false;
        }

        // Check if the target user has blocked the current user
        if ($this->connectionRepository->isBlocked($userId, $targetUserId)) {
            return false;
        }

        // Check if there's already a connection
        if ($this->connectionRepository->areConnected($userId, $targetUserId)) {
            return true; // Already connected
        }

        // Use the profile type-based connection rules
        return $this->canProfileTypesConnect($user->profile_type, $targetUser->profile_type);
    }

    /**
     * Create a connection and send an initial message.
     *
     * @param int $senderId
     * @param int $recipientId
     * @param string $message
     * @return array
     */
    public function connectWithMessage(int $senderId, int $recipientId, string $message): array
    {
        // Check if the users can connect
        if (!$this->canConnect($senderId, $recipientId)) {
            return [
                'success' => false,
                'message' => 'You cannot connect with this user.',
            ];
        }

        // Get existing connection if any
        $existingConnection = $this->getConnection($senderId, $recipientId);

        // Handle different connection statuses before proceeding to transaction
        if ($existingConnection) {
            // Case 1: REJECTED connection - Cannot send message
            if ($existingConnection->status === ConnectionStatus::REJECTED->value) {
                return [
                    'success' => false,
                    'message' => 'Cannot send message due to rejected connection.',
                ];
            }

            // Case 2: PENDING connection - Can only send a message if the sender is the original requester
            if ($existingConnection->status === ConnectionStatus::PENDING->value) {
                if ($existingConnection->requester_id !== $senderId) {
                    return [
                        'success' => false,
                        'message' => 'Cannot send message until connection request is accepted.',
                    ];
                }
                // Allow original requester to continue for a PENDING connection
            }

            // Case 3: ACCEPTED connection - Allow message to proceed
        }

        // Start a database transaction
        return DB::transaction(function () use ($senderId, $recipientId, $message, $existingConnection) {
            // At this point only two possibilities remain:
            // 1. No existing connection (create a new one)
            // 2. Existing connection in ACCEPTED status (or PENDING where sender is requester)

            $connection = $existingConnection;

            // If no connection exists, create one
            if (!$connection) {
                $connection = $this->createConnectionRequest($senderId, $recipientId);
            }

            // Get the message service
            $messageService = app(MessageService::class);

            // Find or create a conversation
            $conversation = $messageService->findOrCreateConversation($senderId, $recipientId);

            // Send the message
            $messageSent = $messageService->sendMessage($senderId, $recipientId, $message);

            return [
                'success' => true,
                'connection' => $connection,
                'conversation' => $conversation,
                'message' => $messageSent,
            ];
        });
    }

    /**
     * Check if a profile type should be included in the search index
     */
    public function isSearchable(ProfileType $profileType): bool
    {
        return in_array($profileType, self::SEARCHABLE_TYPES);
    }

    /**
     * Check if a requester profile type can connect with a target profile type based on CONNECTION_MAP
     */
    public function canProfileTypesConnect(ProfileType $requesterType, ProfileType $targetType): bool
    {
        return isset(self::CONNECTION_MAP[$requesterType->value]) &&
               in_array($targetType->value, self::CONNECTION_MAP[$requesterType->value]);
    }

    /**
     * Check if a specific user can connect with another user
     * This is a convenience method that uses the profile types
     */
    public function canUserConnect(User $requester, User $target): bool
    {
        // Verify both users have valid profile types
        if (!$requester->profile_type || !$target->profile_type) {
            return false;
        }

        // Check if target has public profile enabled
        if (!$target->public_profile) {
            return false;
        }

        return $this->canProfileTypesConnect($requester->profile_type, $target->profile_type);
    }

    /**
     * Get all profile types that can connect with the given profile type
     */
    public function getConnectableByTypes(ProfileType $profileType): array
    {
        $connectable = [];

        foreach (ProfileType::cases() as $requesterType) {
            if ($this->canProfileTypesConnect($requesterType, $profileType)) {
                $connectable[] = $requesterType->value;
            }
        }

        return $connectable;
    }

    /**
     * Get all profile types that the given profile type can connect with
     */
    public function getConnectableToTypes(ProfileType $profileType): array
    {
        return self::CONNECTION_MAP[$profileType->value] ?? [];
    }

    /**
     * Check if a user should have networking menu access
     */
    public function hasNetworkingAccess(User $user): bool
    {
        // These types have no networking access
        $restrictedTypes = [
            // Athletics Directors removed - they need access to communicate with students
        ];

        return !is_null($user->profile_type) && !in_array($user->profile_type, $restrictedTypes);
    }

    /**
     * Check if a user has read-only networking access
     */
    public function hasReadOnlyAccess(User $user): bool
    {
        $readOnlyTypes = [
            ProfileType::PARENT,
        ];

        return !is_null($user->profile_type) && in_array($user->profile_type, $readOnlyTypes);
    }

    /**
     * Check if a user has limited networking access (can initiate but with restrictions)
     */
    public function hasLimitedAccess(User $user): bool
    {
        $limitedTypes = [
            ProfileType::SPONSOR,
        ];

        return !is_null($user->profile_type) && in_array($user->profile_type, $limitedTypes);
    }

    /**
     * Get the entire connection map for use in API
     */
    public function getConnectionMap(): array
    {
        return self::CONNECTION_MAP;
    }
}
