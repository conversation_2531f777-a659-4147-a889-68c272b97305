<?php

namespace App\Services;

use App\Models\UserBlock;
use App\Repositories\UserBlockRepository;
use Exception;
use Illuminate\Pagination\LengthAwarePaginator;

class UserBlockService
{
    /**
     * @var UserBlockRepository
     */
    protected $userBlockRepository;

    /**
     * Create a new service instance.
     *
     * @param UserBlockRepository $userBlockRepository
     */
    public function __construct(UserBlockRepository $userBlockRepository)
    {
        $this->userBlockRepository = $userBlockRepository;
    }

    /**
     * Block a user.
     *
     * @param int $blockerId
     * @param int $blockedId
     * @return UserBlock
     * @throws Exception
     */
    public function blockUser(int $blockerId, int $blockedId): UserBlock
    {
        if ($blockerId === $blockedId) {
            throw new Exception('Users cannot block themselves');
        }

        return $this->userBlockRepository->blockUser($blockerId, $blockedId);
    }

    /**
     * Unblock a user.
     *
     * @param int $blockerId
     * @param int $blockedId
     * @return bool
     */
    public function unblockUser(int $blockerId, int $blockedId): bool
    {
        return $this->userBlockRepository->unblockUser($blockerId, $blockedId);
    }

    /**
     * Get all users blocked by a user.
     *
     * @param int $userId
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getBlockedUsers(int $userId, int $perPage = 15): LengthAwarePaginator
    {
        return $this->userBlockRepository->getBlockedUsers($userId, $perPage);
    }

    /**
     * Check if a user is blocked.
     *
     * @param int $userId
     * @param int $otherUserId
     * @return bool
     */
    public function isBlocked(int $userId, int $otherUserId): bool
    {
        return $this->userBlockRepository->isBlocked($userId, $otherUserId);
    }
}
