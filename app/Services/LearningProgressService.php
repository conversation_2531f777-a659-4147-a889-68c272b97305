<?php

namespace App\Services;

use App\Data\Learning\BadgeProgressData;
use App\Data\Learning\CertificateData;
use App\Data\Learning\LearningInvestmentData;
use App\Data\Learning\LearningProgressSummaryData;
use App\Data\Learning\LearningStatsData;
use App\Data\Learning\TopicInvestmentData;
use App\Enums\TestStatus;
use App\Models\TestAttempt;
use App\Models\User;
use Carbon\Carbon;
use Spatie\LaravelData\DataCollection;

class LearningProgressService
{
    /**
     * Get a complete summary of a user's learning progress
     */
    public function getLearningProgressSummary(User $user): LearningProgressSummaryData
    {
        return new LearningProgressSummaryData(
            certificates: $this->getCertificates($user),
            stats: $this->getLearningStats($user),
            investment: $this->getLearningInvestment($user),
            badges: $this->getBadgeProgress($user),
        );
    }

    /**
     * Get certificates earned by the user
     */
    public function getCertificates(User $user): DataCollection
    {
        // Get completed courses
        $completedCourses = $user->courses()
            ->wherePivotNotNull('completed_at')
            ->withPivot(['completed_at'])
            ->get();

        // Create certificate data for each completed course
        $certificates = $completedCourses
            ->map(function ($course) {
                return CertificateData::fromCompletedCourse($course);
            })
            ->values();

        return new DataCollection(CertificateData::class, $certificates->all());
    }

    /**
     * Get learning statistics for the user
     */
    public function getLearningStats(User $user): LearningStatsData
    {
        // Get completed modules
        $completedModules = $user->modules()
            ->wherePivotNotNull('completed_at')
            ->withPivot(['started_at', 'completed_at'])
            ->get();

        // Count completed courses
        $coursesCompleted = $user->courses()
            ->wherePivotNotNull('completed_at')
            ->count();

        // Calculate average module score
        $testAttempts = TestAttempt::query()
            ->where('user_id', $user->id)
            ->where('status', TestStatus::Complete)
            ->whereNotNull('score')
            ->get();

        $averageScore = $testAttempts->isEmpty()
            ? 0
            : round($testAttempts->avg('score'), 0);

        // Calculate hours spent learning
        $totalMinutes = $this->calculateTotalMinutesSpent($completedModules);
        $hoursSpent = max(1, ceil($totalMinutes / 60)); // Minimum 1 hour

        // Get leaderboard rankings
        // TODO: Replace with actual leaderboard calculations
        $stateLeaderboardRank = $this->getStateLeaderboardRank($user);
        $nationalLeaderboardRank = $this->getNationalLeaderboardRank($user);
        $stateName = $user->state ? $user->state->name : 'Georgia';
        $academicYear = $this->getCurrentAcademicYear();

        return new LearningStatsData(
            averageModuleScore: $averageScore,
            hoursSpent: $hoursSpent,
            modulesCompleted: $completedModules->count(),
            coursesCompleted: $coursesCompleted,
            stateLeaderboardRank: $stateLeaderboardRank,
            stateName: $stateName,
            nationalLeaderboardRank: $nationalLeaderboardRank,
            academicYear: $academicYear,
        );
    }

    /**
     * Get learning investment distribution for the user
     */
    public function getLearningInvestment(User $user): LearningInvestmentData
    {
        // Get completed modules with their topics
        $completedModules = $user->modules()
            ->wherePivotNotNull('completed_at')
            ->withPivot(['started_at', 'completed_at'])
            ->with('topics')
            ->get();

        // Calculate time spent per topic
        $topicMinutes = [];
        $topicModuleCounts = [];

        foreach ($completedModules as $module) {
            // Calculate minutes spent on this module
            $minutes = $this->calculateModuleMinutes($module);

            // Distribute minutes among topics
            $moduleTopics = $module->topics;
            if ($moduleTopics->isEmpty()) {
                // If no topics, assign to "Uncategorized"
                $topicMinutes['Uncategorized'] = ($topicMinutes['Uncategorized'] ?? 0) + $minutes;
                $topicModuleCounts['Uncategorized'] = ($topicModuleCounts['Uncategorized'] ?? 0) + 1;
            } else {
                // Distribute minutes evenly among topics
                $minutesPerTopic = $minutes / $moduleTopics->count();
                foreach ($moduleTopics as $topic) {
                    $topicMinutes[$topic->name] = ($topicMinutes[$topic->name] ?? 0) + $minutesPerTopic;
                    $topicModuleCounts[$topic->name] = ($topicModuleCounts[$topic->name] ?? 0) + 1;
                }
            }
        }

        // Calculate total minutes
        $totalMinutes = array_sum($topicMinutes);

        // Create topic investment data
        $topicInvestments = [];
        foreach ($topicMinutes as $topicName => $minutes) {
            $percentage = $totalMinutes > 0 ? floor(($minutes / $totalMinutes) * 100) : 0;
            // Cap percentage between 0 and 100
            $percentage = max(0, min(100, $percentage));

            $topicInvestments[] = new TopicInvestmentData(
                name: $topicName,
                percentage: $percentage,
                minutesSpent: round($minutes),
                modulesCompleted: $topicModuleCounts[$topicName] ?? 0,
            );
        }

        // Sort by percentage (descending)
        usort($topicInvestments, function ($a, $b) {
            return $b->percentage <=> $a->percentage;
        });

        // Get primary topic (highest percentage)
        $primaryTopic = !empty($topicInvestments) ? $topicInvestments[0] : null;

        return new LearningInvestmentData(
            topics: new DataCollection(TopicInvestmentData::class, $topicInvestments),
            primaryTopic: $primaryTopic?->name,
            primaryTopicPercentage: $primaryTopic?->percentage,
        );
    }

    /**
     * Get badge progress for the user
     */
    public function getBadgeProgress(User $user): DataCollection
    {
        // Get user's badges from the pivot table
        $userBadges = $user->badges()->withPivot('is_achieved')->get();

        // Count completed standalone modules for progress calculation
        $completedModulesCount = $user->modules()
            ->whereDoesntHave('courses')
            ->wherePivotNotNull('completed_at')
            ->count();

        // Create badge progress data only for badges in the pivot table
        $badgeProgress = $userBadges->map(function ($badge) use ($completedModulesCount) {
            return BadgeProgressData::fromBadge(
                $badge,
                $badge->pivot->is_achieved,
                $completedModulesCount
            );
        });

        return new DataCollection(BadgeProgressData::class, $badgeProgress->all());
    }

    /**
     * Calculate total minutes spent on completed modules
     */
    private function calculateTotalMinutesSpent($completedModules): int
    {
        return $completedModules->sum(function ($module) {
            return $this->calculateModuleMinutes($module);
        });
    }

    /**
     * Calculate minutes spent on a single module
     */
    private function calculateModuleMinutes($module): int
    {
        $startedAt = Carbon::parse($module->pivot->started_at);
        $completedAt = Carbon::parse($module->pivot->completed_at);

        // If the time difference is unreasonably large (more than 2 hours),
        // use the module's estimated minutes instead
        $minutesDiff = $startedAt->diffInMinutes($completedAt);
        return $minutesDiff > 120 ? ($module->minutes ?? 15) : $minutesDiff;
    }

    /**
     * Get the user's rank in the state leaderboard
     * TODO: Implement actual leaderboard calculation
     */
    private function getStateLeaderboardRank(User $user): int
    {
        return 26; // Placeholder value
    }

    /**
     * Get the user's rank in the national leaderboard
     * TODO: Implement actual leaderboard calculation
     */
    private function getNationalLeaderboardRank(User $user): int
    {
        return 213; // Placeholder value
    }

    /**
     * Get the current academic year
     */
    private function getCurrentAcademicYear(): string
    {
        $currentYear = Carbon::now()->year;
        $currentMonth = Carbon::now()->month;

        // If we're in the second half of the academic year (Jan-Jul)
        if ($currentMonth < 8) {
            return ($currentYear - 1) . '-' . substr($currentYear, -2);
        }

        // If we're in the first half of the academic year (Aug-Dec)
        return $currentYear . '-' . substr($currentYear + 1, -2);
    }
}
