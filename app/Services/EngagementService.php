<?php

namespace App\Services;

use App\Models\Engagement;
use App\Models\EngagementAggregate;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;

class EngagementService
{
    /**
     * Record an impression for an engageable model.
     *
     * @param Model $engageable The model to record an impression for
     * @param array|null $metadata Additional metadata to store with the impression
     * @return Engagement The created engagement record
     */
    public function recordImpression(Model $engageable, ?array $metadata = null): Engagement
    {
        // Also update the legacy counter if this is an advertisement
        if (get_class($engageable) === 'App\Models\Advertisement') {
            $engageable->increment('impressions');
        }

        return $this->recordEngagement($engageable, Engagement::EVENT_IMPRESSION, $metadata);
    }

    /**
     * Record a click for an engageable model.
     *
     * @param Model $engageable The model to record a click for
     * @param array|null $metadata Additional metadata to store with the click
     * @return Engagement The created engagement record
     */
    public function recordClick(Model $engageable, ?array $metadata = null): Engagement
    {
        // Also update the legacy counter if this is an advertisement
        if (get_class($engageable) === 'App\Models\Advertisement') {
            $engageable->increment('clicks');
        }

        return $this->recordEngagement($engageable, Engagement::EVENT_CLICK, $metadata);
    }

    /**
     * Record an engagement for an engageable model.
     *
     * @param Model $engageable The model to record an engagement for
     * @param string $eventType The type of engagement (impression, click)
     * @param array|null $metadata Additional metadata to store with the engagement
     * @return Engagement The created engagement record
     */
    private function recordEngagement(Model $engageable, string $eventType, ?array $metadata = null): Engagement
    {
        $engagement = new Engagement([
            'event_type' => $eventType,
            'user_id' => Auth::id(),
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'metadata' => $metadata,
        ]);

        $engagement->engageable()->associate($engageable);
        $engagement->created_at = Carbon::now();
        $engagement->save();

        // Also increment the daily aggregate counter
        EngagementAggregate::incrementCount($engageable, $eventType);

        return $engagement;
    }

    /**
     * Get total impressions for an engageable model within a date range.
     *
     * @param Model $engageable The model to get impressions for
     * @param Carbon|null $startDate Start date (inclusive)
     * @param Carbon|null $endDate End date (inclusive)
     * @return int Total impressions
     */
    public function getImpressions(Model $engageable, ?Carbon $startDate = null, ?Carbon $endDate = null): int
    {
        return $this->getEngagementCount($engageable, Engagement::EVENT_IMPRESSION, $startDate, $endDate);
    }

    /**
     * Get total clicks for an engageable model within a date range.
     *
     * @param Model $engageable The model to get clicks for
     * @param Carbon|null $startDate Start date (inclusive)
     * @param Carbon|null $endDate End date (inclusive)
     * @return int Total clicks
     */
    public function getClicks(Model $engageable, ?Carbon $startDate = null, ?Carbon $endDate = null): int
    {
        return $this->getEngagementCount($engageable, Engagement::EVENT_CLICK, $startDate, $endDate);
    }

    /**
     * Get click-through rate for an engageable model within a date range.
     *
     * @param Model $engageable The model to get CTR for
     * @param Carbon|null $startDate Start date (inclusive)
     * @param Carbon|null $endDate End date (inclusive)
     * @return float CTR percentage (0-100)
     */
    public function getClickThroughRate(Model $engageable, ?Carbon $startDate = null, ?Carbon $endDate = null): float
    {
        $impressions = $this->getImpressions($engageable, $startDate, $endDate);

        if ($impressions === 0) {
            return 0.0;
        }

        $clicks = $this->getClicks($engageable, $startDate, $endDate);

        return ($clicks / $impressions) * 100;
    }

    /**
     * Get the total count of engagements of a specific type for an engageable model.
     *
     * @param Model $engageable The model to get counts for
     * @param string $eventType The type of engagement (impression, click)
     * @param Carbon|null $startDate Start date (inclusive)
     * @param Carbon|null $endDate End date (inclusive)
     * @return int Total count
     */
    public function getEngagementCount(
        Model $engageable,
        string $eventType,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null
    ): int {
        // Default to all-time if no dates specified
        $startDate = $startDate ?? Carbon::createFromTimestamp(0);
        $endDate = $endDate ?? Carbon::now();

        // Define the cutoff date for detailed records vs. aggregates
        $detailCutoff = Carbon::now()->subDays(30);

        // For time periods that span both, combine counts from both tables
        if ($startDate->lt($detailCutoff) && $endDate->gte($detailCutoff)) {
            // Get aggregate count for the older period
            $aggregateCount = $engageable->engagementAggregates()
                ->where('event_type', $eventType)
                ->whereBetween('date', [$startDate->toDateString(), $detailCutoff->subDay()->toDateString()])
                ->sum('count');

            // Get detailed count for the recent period
            $detailCount = $engageable->engagements()
                ->where('event_type', $eventType)
                ->whereBetween('created_at', [$detailCutoff->startOfDay(), $endDate->endOfDay()])
                ->count();

            return $aggregateCount + $detailCount;
        }
        // For any time period fully within the detail window, use the engagements table
        elseif ($startDate->gte($detailCutoff)) {
            return $engageable->engagements()
                ->where('event_type', $eventType)
                ->whereBetween('created_at', [$startDate->startOfDay(), $endDate->endOfDay()])
                ->count();
        }
        // For any time period fully before the detail window, use the aggregates table
        else {
            return $engageable->engagementAggregates()
                ->where('event_type', $eventType)
                ->whereBetween('date', [$startDate->toDateString(), $endDate->toDateString()])
                ->sum('count');
        }
    }

    /**
     * Get daily engagement counts for a given date range.
     *
     * @param Model $engageable The model to get data for
     * @param string $eventType The type of engagement (impression, click)
     * @param Carbon|null $startDate Start date (inclusive)
     * @param Carbon|null $endDate End date (inclusive)
     * @return Collection Collection of daily counts
     */
    public function getDailyEngagementCounts(
        Model $engageable,
        string $eventType,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null
    ): Collection {
        // Default to last 30 days if no dates specified
        $startDate = $startDate ?? Carbon::now()->subDays(30);
        $endDate = $endDate ?? Carbon::now();

        // Define the cutoff date for detailed records vs. aggregates
        $detailCutoff = Carbon::now()->subDays(30);

        // Initialize results collection with dates
        $dateRange = collect();
        for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
            $dateString = $date->toDateString();
            $dateRange->put($dateString, [
                'date' => $dateString,
                'count' => 0
            ]);
        }

        // For time periods that span both, combine results from both tables
        if ($startDate->lt($detailCutoff) && $endDate->gte($detailCutoff)) {
            // Get aggregate data for the older period
            $aggregates = $engageable->engagementAggregates()
                ->where('event_type', $eventType)
                ->whereBetween('date', [$startDate->toDateString(), $detailCutoff->subDay()->toDateString()])
                ->get(['date', 'count']);

            foreach ($aggregates as $aggregate) {
                $dateString = $aggregate->date->toDateString();
                if ($dateRange->has($dateString)) {
                    $dateData = $dateRange->get($dateString);
                    $dateData['count'] = $aggregate->count;
                    $dateRange->put($dateString, $dateData);
                }
            }

            // Get detailed data for the recent period
            $details = $engageable->engagements()
                ->where('event_type', $eventType)
                ->whereBetween('created_at', [$detailCutoff->startOfDay(), $endDate->endOfDay()])
                ->select(DB::raw('DATE(created_at) as date'), DB::raw('COUNT(*) as count'))
                ->groupBy(DB::raw('DATE(created_at)'))
                ->get();

            foreach ($details as $detail) {
                $dateString = $detail->date;
                if ($dateRange->has($dateString)) {
                    $dateData = $dateRange->get($dateString);
                    $dateData['count'] = $detail->count;
                    $dateRange->put($dateString, $dateData);
                }
            }
        }
        // For any time period fully within the detail window, use the engagements table
        elseif ($startDate->gte($detailCutoff)) {
            $details = $engageable->engagements()
                ->where('event_type', $eventType)
                ->whereBetween('created_at', [$startDate->startOfDay(), $endDate->endOfDay()])
                ->select(DB::raw('DATE(created_at) as date'), DB::raw('COUNT(*) as count'))
                ->groupBy(DB::raw('DATE(created_at)'))
                ->get();

            foreach ($details as $detail) {
                $dateString = $detail->date;
                if ($dateRange->has($dateString)) {
                    $dateData = $dateRange->get($dateString);
                    $dateData['count'] = $detail->count;
                    $dateRange->put($dateString, $dateData);
                }
            }
        }
        // For any time period fully before the detail window, use the aggregates table
        else {
            $aggregates = $engageable->engagementAggregates()
                ->where('event_type', $eventType)
                ->whereBetween('date', [$startDate->toDateString(), $endDate->toDateString()])
                ->get(['date', 'count']);

            foreach ($aggregates as $aggregate) {
                $dateString = $aggregate->date->toDateString();
                if ($dateRange->has($dateString)) {
                    $dateData = $dateRange->get($dateString);
                    $dateData['count'] = $aggregate->count;
                    $dateRange->put($dateString, $dateData);
                }
            }
        }

        return $dateRange->values();
    }

    /**
     * Aggregate engagements for a specific date.
     *
     * @param Carbon $date The date to aggregate for
     * @return int Number of aggregated records
     */
    public function aggregateEngagementsForDate(Carbon $date): int
    {
        $date = $date->copy()->startOfDay();
        $nextDay = $date->copy()->addDay();

        // Find all unique engageable types and IDs for the given date
        $uniqueEngeageables = Engagement::query()
            ->whereBetween('created_at', [$date, $nextDay])
            ->select('engageable_type', 'engageable_id', 'event_type')
            ->distinct()
            ->get();

        $count = 0;

        foreach ($uniqueEngeageables as $engageable) {
            // Count the engagements for this engageable and event type
            $engagementCount = Engagement::query()
                ->where('engageable_type', $engageable->engageable_type)
                ->where('engageable_id', $engageable->engageable_id)
                ->where('event_type', $engageable->event_type)
                ->whereBetween('created_at', [$date, $nextDay])
                ->count();

            // Create or update the aggregate record
            $aggregate = EngagementAggregate::query()
                ->where('engageable_type', $engageable->engageable_type)
                ->where('engageable_id', $engageable->engageable_id)
                ->where('event_type', $engageable->event_type)
                ->where('date', $date->toDateString())
                ->first();

            if (!$aggregate) {
                $aggregate = new EngagementAggregate([
                    'engageable_type' => $engageable->engageable_type,
                    'engageable_id' => $engageable->engageable_id,
                    'event_type' => $engageable->event_type,
                    'date' => $date->toDateString(),
                    'count' => $engagementCount,
                ]);
            } else {
                $aggregate->count = $engagementCount;
            }

            $aggregate->save();
            $count++;
        }

        return $count;
    }

    /**
     * Clean up detailed engagement records older than the specified days.
     *
     * @param int $olderThanDays Number of days to keep detailed records for
     * @return int Number of deleted records
     */
    public function cleanupOldEngagements(int $olderThanDays = 30): int
    {
        $cutoffDate = Carbon::now()->subDays($olderThanDays);

        return Engagement::query()
            ->where('created_at', '<', $cutoffDate)
            ->delete();
    }
}
