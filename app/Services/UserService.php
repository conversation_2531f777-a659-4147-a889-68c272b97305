<?php

namespace App\Services;

use App\Models\User;
use App\Enums\Gender;
use App\Enums\ProfileType;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use App\Enums\UserType;

class UserService
{
    public function createFromOnboarding(array $data): User
    {
        // Create a clean user data array with only the fields that exist in the users table
        $userData = [
            // Required fields
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'email' => $data['email'],
            'password' => $data['password'], // Already hashed from DTO
            'profile_type' => ProfileType::POSITIVE_ATHLETE->value,

            // Optional fields mapped to their database column names
            'phone' => $data['phone'] ?? null,
            'street_address_1' => $data['street_address'] ?? null,
            'street_address_2' => $data['unit'] ?? null,
            'city' => $data['city'] ?? null,
            'state_code' => $data['state'] ?? null,
            'zip' => $data['zip'] ?? null,
            'county_id' => $data['county_id'] ?? null,
        ];

        $user = User::query()->create($userData);

        $user->assignRole('user');

        return $user;
    }

    public function updateDetails(User $user, array $data): void
    {
        $updateData = [];

        // All these fields are nullable in the schema, so only update if present
        $optionalFields = [
            'state_code',
            'school_id',
            'graduation_year',
            'gpa',
            'class_rank',
            'gender',
            'height_in_inches',
            'weight',
            'content',
        ];

        foreach ($optionalFields as $field) {
            if (isset($data[$field])) {
                // Special handling for specific fields
                $value = match($field) {
                    'gender' => Gender::from(strtolower($data[$field])),
                    'height_in_inches' => (int) $data[$field],
                    'weight' => (int) $data[$field],
                    default => $data[$field]
                };

                $updateData[$field] = $value;
            }
        }

        if (!empty($updateData)) {
            $user->update($updateData);
        }

        if (isset($data['profile_photo'])) {
            // Handle profile photo upload/update
        }
    }

    public function updateStory(User $user, array $data): void
    {
        $user->update([
            'content' => $data['content']
        ]);
    }

    public function attemptLogin(array $credentials): ?array
    {
        $user = User::query()
            ->where('email', $credentials['email'])
            ->first();

        if (!$user || !Hash::check($credentials['password'], $user->password)) {
            return null;
        }

        // Return user without creating a token
        // Token creation will be handled by AuthController only when needed for API auth
        return [
            'user' => $user,
            'token' => null
        ];
    }

    public function logout(User $user): void
    {
        // Revoke all tokens for the user
        $user->tokens()->delete();
    }

    /**
     * Create a new athletics director user
     */
    public function createAthleticsDirector(array $data): User
    {
        return DB::transaction(function () use ($data) {
            // Create user with AD role
            $user = User::create([
                'profile_type' => ProfileType::ATHLETICS_DIRECTOR->value,
                'first_name' => $data['first_name'],
                'last_name' => $data['last_name'],
                'email' => $data['email'],
                'password' => $data['password'],
                'handle' => $data['handle'] ?? null,
                'phone' => $data['phone'] ?? null,
                'street_address_1' => $data['street_address_1'] ?? null,
                'street_address_2' => $data['street_address_2'] ?? null,
                'city' => $data['city'] ?? null,
                'state_code' => $data['state_code'] ?? null,
                'zip' => $data['zip'] ?? null,
                'county_id' => $data['county_id'] ?? null,
                'recruiter_enabled' => $data['recruiter_enabled'] ?? false,
                'content' => $data['content'] ?? null,
                'school_id' => $data['school_id'] ?? null,
            ]);

            // Create AD profile
            app(AthleticsDirectorProfileService::class)->createForUser($user);

            return $user;
        });
    }
}
