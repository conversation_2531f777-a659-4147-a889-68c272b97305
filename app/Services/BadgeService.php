<?php

namespace App\Services;

use App\Models\Badge;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BadgeService
{
    /**
     * Update a user's badges based on their completed standalone modules.
     *
     * This method:
     * 1. Counts the user's completed standalone modules
     * 2. Determines which badges should be achieved
     * 3. Finds the next unachieved badge
     * 4. Updates the badge_user pivot table accordingly
     *
     * @param User $user The user to update badges for
     * @return array Information about badge updates
     */
    public function updateUserBadges(User $user): array
    {
        // Count completed standalone modules
        $completedModulesCount = $this->countCompletedStandaloneModules($user);

        // Get all badges ordered by module_requirement
        $allBadges = Badge::query()->orderBy('module_requirement')->get();

        // Determine which badges should be achieved
        $achievedBadges = $allBadges->filter(function ($badge) use ($completedModulesCount) {
            return $badge->module_requirement <= $completedModulesCount;
        });

        // Find the next unachieved badge
        $nextBadge = $allBadges->first(function ($badge) use ($completedModulesCount) {
            return $badge->module_requirement > $completedModulesCount;
        });

        // Track if any new badges were earned
        $newlyEarnedBadges = collect();

        DB::beginTransaction();

        try {
            // Get current achieved badges
            $currentAchievedBadgeIds = $user->badges()
                ->wherePivot('is_achieved', true)
                ->pluck('badges.id')
                ->toArray();

            // Get current unachieved badge (should be only one)
            $currentUnachievedBadge = $user->badges()
                ->wherePivot('is_achieved', false)
                ->first();

            // Remove any existing unachieved badges
            if ($currentUnachievedBadge) {
                $user->badges()->detach($currentUnachievedBadge->id);
            }

            // Ensure all earned badges are attached with is_achieved=true
            foreach ($achievedBadges as $badge) {
                if (!in_array($badge->id, $currentAchievedBadgeIds)) {
                    // This is a newly achieved badge
                    $newlyEarnedBadges->push($badge);
                }

                $user->badges()->syncWithoutDetaching([
                    $badge->id => ['is_achieved' => true]
                ]);
            }

            // Attach the next unachieved badge if it exists
            if ($nextBadge) {
                $user->badges()->syncWithoutDetaching([
                    $nextBadge->id => ['is_achieved' => false]
                ]);
            }

            DB::commit();

            return [
                'success' => true,
                'completed_modules' => $completedModulesCount,
                'achieved_badges_count' => $achievedBadges->count(),
                'newly_earned_badges' => $newlyEarnedBadges,
                'next_badge' => $nextBadge
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update user badges', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Count the number of standalone modules completed by a user.
     *
     * A standalone module is one that is not associated with any course.
     *
     * @param User $user The user to count modules for
     * @return int The number of completed standalone modules
     */
    private function countCompletedStandaloneModules(User $user): int
    {
        return DB::table('module_user')
            ->where('user_id', $user->id)
            ->whereNotNull('completed_at')
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('course_module')
                    ->whereColumn('course_module.module_id', 'module_user.module_id');
            })
            ->count();
    }
}
