<?php
namespace App\Http\Controllers;

use App\Models\Interest;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="Interests",
 *     description="API Endpoints for interests management"
 * )
 */
class InterestsController extends Controller
{
    /**
     * Search interests by name
     *
     * @OA\Get(
     *     path="/api/v1/interests/search",
     *     operationId="searchInterests",
     *     tags={"Interests"},
     *     summary="Search interests by name",
     *     description="Returns a list of interests matching the search query",
     *     @OA\Parameter(
     *         name="query",
     *         in="query",
     *         description="Search query string",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(ref="#/components/schemas/Interest")
     *             )
     *         )
     *     )
     * )
     */
    public function search(Request $request)
    {
        $query = $request->get('query');

        if (! $query) {
            return response()->json([
                'success' => true,
                'data'    => Interest::query()
                    ->orderBy('name')
                    ->limit(10)
                    ->get(['id', 'name']),
            ]);
        }

        $interests = Interest::query()
            ->where('name', 'ilike', "%{$query}%")
            ->orderBy('name')
            ->limit(10)
            ->get(['id', 'name', 'icon']);

        return response()->json([
            'success' => true,
            'data'    => $interests,
        ]);
    }
}
