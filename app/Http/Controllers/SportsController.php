<?php

namespace App\Http\Controllers;

use App\Models\Sport;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use OpenApi\Annotations as OA;

/**
 * @OA\Tag(
 *     name="Sports",
 *     description="API Endpoints for sports-related operations"
 * )
 * @OA\Schema(
 *     schema="SportsSearchItem",
 *     type="object",
 *     @OA\Property(property="id", type="integer", example=1),
 *     @OA\Property(property="name", type="string", example="Basketball"),
 *     @OA\Property(property="icon", type="string", example="basketball-icon", nullable=true),
 *     @OA\Property(property="slug", type="string", example="basketball", nullable=true)
 * )
 */
class SportsController extends Controller
{
    /**
     * Search for platform-curated sports
     *
     * @OA\Get(
     *     path="/api/v1/sports/search",
     *     operationId="searchSports",
     *     tags={"Sports"},
     *     summary="Search for sports by name",
     *     description="Returns a list of sports matching the search query. This is a public endpoint that does not require authentication.",
     *     @OA\Parameter(
     *         name="query",
     *         in="query",
     *         description="Search term for sport names",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/SportsSearchItem")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(ref="#/components/schemas/ValidationError")
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Server error",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     )
     * )
     */
    public function search(Request $request): JsonResponse
    {
        $query = $request->get('query', '');

        $sports = Sport::query()
            ->where('name', 'ilike', "%{$query}%")
            ->orderBy('name')
            ->get(['id', 'name', 'icon', 'slug']);

        return response()->json($sports);
    }
}
