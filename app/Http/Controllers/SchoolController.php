<?php

namespace App\Http\Controllers;

use App\Data\School\SchoolData;
use App\Http\Requests\School\SearchSchoolRequest;
use App\Services\SchoolService;
use OpenApi\Annotations as OA;

/**
 * @OA\Tag(
 *     name="Schools",
 *     description="API Endpoints for school-related operations"
 * )
 */
class SchoolController extends Controller
{
    public function __construct(
        private readonly SchoolService $schoolService
    ) {}

    /**
     * Search schools by name with optional county and state filters
     *
     * @OA\Get(
     *     path="/api/v1/schools/search",
     *     operationId="searchSchools",
     *     tags={"Schools"},
     *     summary="Search for schools by name with optional filters",
     *     description="Returns a list of schools matching the search query and filters (limited to 10 results). This is a public endpoint that does not require authentication.",
     *     @OA\Parameter(
     *         name="query",
     *         in="query",
     *         description="Search term for school names",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="county_id",
     *         in="query",
     *         description="Filter by county ID",
     *         required=false,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="state_code",
     *         in="query",
     *         description="Filter by state code",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/SchoolData")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(ref="#/components/schemas/ValidationError")
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Server error",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     )
     * )
     */
    public function search(SearchSchoolRequest $request): array
    {
        return $this->schoolService->searchSchools($request);
    }

    /**
     * Get a school by ID
     *
     * @OA\Get(
     *     path="/api/schools/{id}",
     *     operationId="getSchoolById",
     *     tags={"Schools"},
     *     summary="Get a school by ID",
     *     description="Returns a school by ID with county and state information. This is a public endpoint that does not require authentication.",
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="School ID",
     *         required=true,
     *         @OA\Schema(type="integer", format="int64")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="id", type="integer", format="int64", example=1),
     *             @OA\Property(property="name", type="string", example="Lincoln High School"),
     *             @OA\Property(property="county_name", type="string", example="Franklin", nullable=true),
     *             @OA\Property(property="state_code", type="string", example="OH", nullable=true),
     *             @OA\Property(property="state_name", type="string", example="Ohio", nullable=true)
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="School not found",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Server error",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     )
     * )
     */
    public function show(int $id): ?SchoolData
    {
        return $this->schoolService->getSchoolById($id);
    }
}
