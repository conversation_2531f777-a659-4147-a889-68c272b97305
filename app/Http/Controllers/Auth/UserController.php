<?php

namespace App\Http\Controllers\Auth;

use App\Data\Auth\AthleteData;
use App\Data\Auth\AuthUserData;
use App\Enums\ProfileType;
use App\Http\Controllers\Controller;
use App\Services\ParentChildResolverService;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="Authentication",
 *     description="API Endpoints for user authentication"
 * )
 */
class UserController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct(
        private readonly ParentChildResolverService $parentChildResolver
    ) {}

    /**
     * @OA\Get(
     *     path="/api/v1/user",
     *     summary="Get authenticated user information",
     *     tags={"Authentication"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Successfully retrieved authenticated user details",
     *         @OA\JsonContent(ref="#/components/schemas/AuthUserData")
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated"
     *     )
     * )
     */
    public function getCurrentUser(Request $request)
    {
        $user = $request->user();
        $athleteUser = null;

        // If the user is a parent, try to resolve the linked athlete
        if ($user->profile_type === ProfileType::PARENT) {
            $athleteUser = $this->parentChildResolver->resolveChildAccount($user);
        }

        // Load athletics director profile if user is an athletics director
        if ($user->profile_type === ProfileType::ATHLETICS_DIRECTOR) {
            $user->load(['athleticsDirectorProfile.school']);
        }

        return AuthUserData::fromUser($user, $athleteUser);
    }
}
