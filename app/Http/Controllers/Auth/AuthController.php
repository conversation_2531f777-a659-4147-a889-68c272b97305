<?php

namespace App\Http\Controllers\Auth;

use App\Data\Auth\AuthUserData;
use App\Enums\ProfileType;
use App\Http\Controllers\Controller;
use App\Services\ParentChildResolverService;
use App\Services\UserService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Requests\Auth\LoginRequest;
use Illuminate\Support\Facades\Auth;

/**
 * @OA\Tag(
 *     name="Authentication",
 *     description="API Endpoints for user authentication"
 * )
 */
class AuthController extends Controller
{
    public function __construct(
        private readonly UserService $userService,
        private readonly ParentChildResolverService $parentChildResolver
    ) {}

    /**
     * @OA\Post(
     *     path="/api/v1/login",
     *     summary="Authenticate a user",
     *     description="Authenticates a user and returns a token for API access or establishes a session for web access",
     *     tags={"Authentication"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"email", "password"},
     *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *             @OA\Property(property="password", type="string", format="password", example="password123")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Login successful",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="user", ref="#/components/schemas/AuthUserData"),
     *                 @OA\Property(property="token", type="string", example="1|laravel_sanctum_token...", description="Present for API-based login")
     *             ),
     *             @OA\Property(property="message", type="string", example="Login successful")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Invalid credentials",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Invalid credentials"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="credentials",
     *                     type="array",
     *                     @OA\Items(type="string", example="The provided credentials are incorrect.")
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function login(LoginRequest $request): JsonResponse
    {
        $credentials = $request->validated();

        $loginAttemptResult = $this->userService->attemptLogin($credentials);

        if (!$loginAttemptResult) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid credentials',
                'errors' => ['credentials' => ['The provided credentials are incorrect.']]
            ], 401);
        }

        $user = $loginAttemptResult['user'];
        $athleteUser = null;

        if ($user->profile_type === ProfileType::PARENT) {
            $athleteUser = $this->parentChildResolver->resolveChildAccount($user);
        }

        $userData = AuthUserData::fromUser($user, $athleteUser);

        // Check if request has CSRF token (session-based auth)
        if ($request->hasHeader('X-XSRF-TOKEN')) {
            Auth::login($user);
            return response()->json([
                'success' => true,
                'data' => ['user' => $userData],
                'message' => 'Login successful'
            ]);
        }

        // Handle API-based login - create token only for API requests
        $token = $user->createToken('auth_token')->plainTextToken;
        return response()->json([
            'success' => true,
            'data' => [
                'user' => $userData,
                'token' => $token
            ],
            'message' => 'Login successful'
        ]);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/logout",
     *     summary="Logout a user",
     *     description="Logs out the authenticated user by invalidating their token or session",
     *     tags={"Authentication"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Logout successful",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Successfully logged out")
     *         )
     *     )
     * )
     */
    public function logout(Request $request): JsonResponse
    {
        // Check if request has CSRF token (session-based auth)
        if ($request->hasHeader('X-XSRF-TOKEN')) {
            auth()->guard('web')->logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();
        } else {
            // Handle API-based logout
            $request->user()->tokens()->delete();
        }

        return response()->json([
            'success' => true,
            'message' => 'Successfully logged out'
        ]);
    }
}
