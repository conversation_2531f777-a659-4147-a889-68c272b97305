<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Auth\Events\Verified;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Models\User;
use Carbon\Carbon;

class VerifyEmailController extends Controller
{
    /**
     * Mark the authenticated user's email address as verified.
     */
    public function __invoke(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'id' => 'required',
            'hash' => 'required',
            'expires' => 'required|numeric',
            'signature' => 'required'
        ]);

        if (Carbon::createFromTimestamp($validated['expires'])->isPast()) {
            return response()->json([
                'message' => 'Verification link has expired',
            ], 400);
        }

        $signature = hash_hmac(
            'sha256',
            $validated['id'] . $validated['hash'] . $validated['expires'],
            config('app.key')
        );

        if (!hash_equals($signature, $validated['signature'])) {
            return response()->json([
                'message' => 'Invalid signature',
            ], 400);
        }

        /** @var User $user */
        $user = User::query()->findOrFail($validated['id']);

        if (!hash_equals(
            sha1($user->getEmailForVerification()),
            $validated['hash']
        )) {
            return response()->json([
                'message' => 'Invalid verification link',
            ], 400);
        }

        if ($user->hasVerifiedEmail()) {
            return response()->json([
                'message' => 'Email already verified',
            ], 200);
        }

        if ($user->markEmailAsVerified()) {
            event(new Verified($user));
        }

        return response()->json([
            'message' => 'Email verified successfully',
        ], 200);
    }
}
