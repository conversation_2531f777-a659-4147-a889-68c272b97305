<?php

namespace App\Http\Controllers\Api;

use App\Data\Nomination\NominationData;
use App\Http\Controllers\Controller;
use App\Services\JotFormFieldMappingService;
use App\Services\Nomination\NominationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;

/**
 * JotForm Webhook Controller
 *
 * Handles incoming JotForm webhook submissions and creates nominations
 */
class JotFormWebhookController extends Controller
{
    public function __construct(
        private readonly JotFormFieldMappingService $fieldMappingService,
        private readonly NominationService $nominationService
    ) {}

    /**
     * Handle incoming JotForm webhook
     */
    public function receive(Request $request): JsonResponse
    {
        try {
            // Verify webhook signature if secret is configured
            if (config('services.jotform.webhook_secret')) {
                $this->verifyWebhookSignature($request);
            }

            // Log the incoming webhook
            Log::info('JotForm webhook received', [
                'form_id' => $request->input('formID'),
                'submission_id' => $request->input('submissionID'),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            // Extract and validate webhook data
            $webhookData = $request->all();

            if (empty($webhookData['rawRequest'])) {
                Log::warning('JotForm webhook missing rawRequest data', ['data' => $webhookData]);
                return response()->json([
                    'error' => 'Invalid webhook data: missing rawRequest'
                ], 400);
            }

            // Store webhook submission for audit purposes
            $this->storeWebhookSubmission($webhookData);

            // Extract nomination data from webhook
            $nominationData = $this->fieldMappingService->extractNominationData($webhookData);

            // Check for duplicate nominations
            $existingNomination = $this->fieldMappingService->checkForDuplicateNomination($nominationData);

            if ($existingNomination) {
                Log::info('Duplicate nomination attempt detected', [
                    'existing_nomination_id' => $existingNomination->id,
                    'nominee_email' => $nominationData['email'],
                    'nominator_email' => $nominationData['nominator_email'],
                    'jotform_submission_id' => $webhookData['submissionID'] ?? null
                ]);

                return response()->json([
                    'message' => 'Duplicate nomination detected. This nominee has already been nominated by this nominator.',
                    'existing_nomination_id' => $existingNomination->id
                ], 409);
            }

            // Create NominationData instance
            $data = NominationData::from($nominationData);

            // Create the nomination and system invite
            $nomination = $this->nominationService->handleSingleNomination($data);

            // Update processing status
            $nomination->update([
                'processing_status' => 'validated',
                'processed_at' => now()
            ]);

            Log::info('Nomination created successfully from JotForm', [
                'nomination_id' => $nomination->id,
                'nominee_email' => $nomination->email,
                'nominator_email' => $nomination->nominator_email,
                'jotform_submission_id' => $nomination->jotform_submission_id,
                'system_invite_id' => $nomination->system_invite_id
            ]);

            return response()->json([
                'nomination_id' => $nomination->id,
                'system_invite_id' => $nomination->system_invite_id,
                'processing_status' => $nomination->processing_status
            ]);

        } catch (ValidationException $e) {
            Log::error('JotForm webhook validation failed', [
                'errors' => $e->errors(),
                'webhook_data' => $webhookData ?? null
            ]);

            return response()->json([
                'error' => 'Validation failed: ' . $e->getMessage(),
                'details' => $e->errors()
            ], 422);

        } catch (\InvalidArgumentException $e) {
            Log::error('JotForm webhook data invalid', [
                'error' => $e->getMessage(),
                'webhook_data' => $webhookData ?? null
            ]);

            return response()->json([
                'error' => $e->getMessage()
            ], 422);

        } catch (\Exception $e) {
            Log::error('JotForm webhook processing failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'webhook_data' => $webhookData ?? null
            ]);

            return response()->json([
                'error' => 'Internal server error while processing webhook'
            ], 500);
        }
    }

    /**
     * List webhook submissions (admin only)
     */
    public function list(Request $request): JsonResponse
    {
        try {
            $files = Storage::disk('local')->files('webhooks/submissions');

            $submissions = collect($files)
                ->map(function ($file) {
                    $filename = basename($file);
                    $timestamp = Storage::disk('local')->lastModified($file);

                    return [
                        'filename' => $filename,
                        'timestamp' => date('Y-m-d H:i:s', $timestamp),
                        'size' => Storage::disk('local')->size($file),
                    ];
                })
                ->sortByDesc('timestamp')
                ->values();

            return response()->json($submissions);

        } catch (\Exception $e) {
            Log::error('Failed to list webhook submissions', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'error' => 'Failed to retrieve webhook submissions'
            ], 500);
        }
    }

    /**
     * Show specific webhook submission (admin only)
     */
    public function show(Request $request, string $filename): JsonResponse
    {
        try {
            $filepath = 'webhooks/submissions/' . $filename;

            if (!Storage::disk('local')->exists($filepath)) {
                return response()->json([
                    'error' => 'Webhook submission not found'
                ], 404);
            }

            $content = Storage::disk('local')->get($filepath);
            $data = json_decode($content, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                return response()->json([
                    'error' => 'Invalid webhook submission data'
                ], 422);
            }

            return response()->json([
                'filename' => $filename,
                'timestamp' => date('Y-m-d H:i:s', Storage::disk('local')->lastModified($filepath)),
                'data' => $data
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to show webhook submission', [
                'filename' => $filename,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'error' => 'Failed to retrieve webhook submission'
            ], 500);
        }
    }

    /**
     * Verify webhook signature
     */
    private function verifyWebhookSignature(Request $request): void
    {
        $payload = $request->getContent();
        $signature = $request->header('X-JotForm-Signature') ?? $request->query('signature');

        if (empty($signature)) {
            throw new \InvalidArgumentException('Missing webhook signature');
        }

        $expectedSignature = hash_hmac('sha256', $payload, config('services.jotform.webhook_secret'));

        if (!hash_equals($expectedSignature, $signature)) {
            throw new \InvalidArgumentException('Invalid webhook signature');
        }
    }

    /**
     * Store webhook submission for audit purposes
     */
    private function storeWebhookSubmission(array $webhookData): void
    {
        try {
            $timestamp = now()->format('Y-m-d_H-i-s');
            $submissionId = $webhookData['submissionID'] ?? 'unknown';
            $filename = "webhook_submission_{$timestamp}_{$submissionId}.json";

            $filepath = 'webhooks/submissions/' . $filename;

            Storage::disk('local')->put($filepath, json_encode([
                'timestamp' => now()->toISOString(),
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'data' => $webhookData
            ], JSON_PRETTY_PRINT));

        } catch (\Exception $e) {
            // Don't fail the webhook processing if storage fails
            Log::warning('Failed to store webhook submission', [
                'error' => $e->getMessage(),
                'submission_id' => $webhookData['submissionID'] ?? null
            ]);
        }
    }
}
