<?php

namespace App\Http\Controllers\Api;

use App\Data\Engagement\EngagementMetricsData;
use App\Data\Engagement\EngagementRequestData;
use App\Http\Controllers\Controller;
use App\Models\Advertisement;
use App\Models\Opportunity;
use App\Services\EngagementService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

/**
 * @OA\Tag(
 *     name="Engagements",
 *     description="API Endpoints for tracking impressions and clicks"
 * )
 */
class EngagementController extends Controller
{
    private EngagementService $engagementService;

    public function __construct(EngagementService $engagementService)
    {
        $this->engagementService = $engagementService;
    }

    /**
     * Record an impression for a trackable item.
     *
     * @OA\Post(
     *     path="/api/v1/engagements/impression",
     *     tags={"Engagements"},
     *     summary="Record an impression event for an advertisement or opportunity",
     *     description="Records when a user views an advertisement or opportunity",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/EngagementRequestData")
     *     ),
     *     @OA\Response(
     *         response=204,
     *         description="Impression recorded successfully"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Trackable item not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Item not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     )
     * )
     *
     * @param Request $request
     * @return Response
     */
    public function recordImpression(Request $request)
    {
        $validated = $request->validate([
            'type' => 'required|string|in:advertisement,opportunity',
            'id' => 'required|integer',
            'metadata' => 'nullable|array',
        ]);

        // Additional validation for ID existence only after type is validated
        if (in_array($validated['type'], ['advertisement', 'opportunity'])) {
            $request->validate([
                'id' => 'exists:'.$validated['type'].'s,id',
            ]);
        }

        $trackableItem = $this->getTrackableItem($validated['type'], $validated['id']);

        if (!$trackableItem) {
            return response()->json(['message' => 'Item not found'], 404);
        }

        $this->engagementService->recordImpression($trackableItem, $validated['metadata'] ?? null);

        return response()->noContent();
    }

    /**
     * Record a click for a trackable item.
     *
     * @OA\Post(
     *     path="/api/v1/engagements/click",
     *     tags={"Engagements"},
     *     summary="Record a click event for an advertisement or opportunity",
     *     description="Records when a user clicks on an advertisement or opportunity",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/EngagementRequestData")
     *     ),
     *     @OA\Response(
     *         response=204,
     *         description="Click recorded successfully"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Trackable item not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Item not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     )
     * )
     *
     * @param Request $request
     * @return Response
     */
    public function recordClick(Request $request)
    {
        $validated = $request->validate([
            'type' => 'required|string|in:advertisement,opportunity',
            'id' => 'required|integer',
            'metadata' => 'nullable|array',
        ]);

        // Additional validation for ID existence only after type is validated
        if (in_array($validated['type'], ['advertisement', 'opportunity'])) {
            $request->validate([
                'id' => 'exists:'.$validated['type'].'s,id',
            ]);
        }

        $trackableItem = $this->getTrackableItem($validated['type'], $validated['id']);

        if (!$trackableItem) {
            return response()->json(['message' => 'Item not found'], 404);
        }

        $this->engagementService->recordClick($trackableItem, $validated['metadata'] ?? null);

        return response()->noContent();
    }

    /**
     * Get metrics for a trackable item.
     *
     * @OA\Get(
     *     path="/api/v1/engagements/metrics",
     *     tags={"Engagements"},
     *     summary="Get engagement metrics for an advertisement or opportunity",
     *     description="Returns impressions, clicks, CTR, and daily metrics for the specified item",
     *     @OA\Parameter(
     *         name="type",
     *         in="query",
     *         required=true,
     *         description="Type of trackable item",
     *         @OA\Schema(type="string", enum={"advertisement", "opportunity"})
     *     ),
     *     @OA\Parameter(
     *         name="id",
     *         in="query",
     *         required=true,
     *         description="ID of the trackable item",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="start_date",
     *         in="query",
     *         required=false,
     *         description="Start date for metrics (format: YYYY-MM-DD)",
     *         @OA\Schema(type="string", format="date")
     *     ),
     *     @OA\Parameter(
     *         name="end_date",
     *         in="query",
     *         required=false,
     *         description="End date for metrics (format: YYYY-MM-DD)",
     *         @OA\Schema(type="string", format="date")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Engagement metrics retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/EngagementMetricsData")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Trackable item not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Item not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     )
     * )
     *
     * @param Request $request
     * @return Response
     */
    public function getMetrics(Request $request)
    {
        $validated = $request->validate([
            'type' => 'required|string|in:advertisement,opportunity',
            'id' => 'required|integer',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        // Additional validation for ID existence only after type is validated
        if (in_array($validated['type'], ['advertisement', 'opportunity'])) {
            $request->validate([
                'id' => 'exists:'.$validated['type'].'s,id',
            ]);
        }

        $trackableItem = $this->getTrackableItem($validated['type'], $validated['id']);

        if (!$trackableItem) {
            return response()->json(['message' => 'Item not found'], 404);
        }

        $startDate = $request->has('start_date') ? new \Carbon\Carbon($validated['start_date']) : null;
        $endDate = $request->has('end_date') ? new \Carbon\Carbon($validated['end_date']) : null;

        $impressions = $this->engagementService->getImpressions($trackableItem, $startDate, $endDate);
        $clicks = $this->engagementService->getClicks($trackableItem, $startDate, $endDate);
        $ctr = $this->engagementService->getClickThroughRate($trackableItem, $startDate, $endDate);
        $dailyImpressions = $this->engagementService->getDailyEngagementCounts($trackableItem, 'impression', $startDate, $endDate);
        $dailyClicks = $this->engagementService->getDailyEngagementCounts($trackableItem, 'click', $startDate, $endDate);

        $metrics = EngagementMetricsData::fromRawData(
            impressions: $impressions,
            clicks: $clicks,
            ctr: $ctr,
            dailyImpressions: $dailyImpressions,
            dailyClicks: $dailyClicks
        );

        return response()->json($metrics);
    }

    /**
     * Get the trackable item based on type and ID.
     *
     * @param string $type
     * @param int $id
     * @return Advertisement|Opportunity|null
     */
    private function getTrackableItem(string $type, int $id)
    {
        return match ($type) {
            'advertisement' => Advertisement::query()->find($id),
            'opportunity' => Opportunity::query()->find($id),
            default => null,
        };
    }
}
