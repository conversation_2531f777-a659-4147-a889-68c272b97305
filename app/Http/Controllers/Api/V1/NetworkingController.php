<?php

namespace App\Http\Controllers\Api\V1;

use App\Data\ConnectionData;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\NetworkingService;
use App\Services\ParentChildResolverService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Spatie\LaravelData\DataCollection;
use Exception;
use OpenApi\Attributes as OA;
use Illuminate\Database\Eloquent\ModelNotFoundException;

/**
 * @OA\Tag(
 *     name="Networking",
 *     description="Endpoints for managing user connections (following/followers, blocking)"
 * )
 */
class NetworkingController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct(
        private readonly NetworkingService $networkingService,
        private readonly ParentChildResolverService $parentChildResolver
    ) {
    }

    /**
     * @OA\Get(
     *     path="/api/v1/connections",
     *     summary="Get all connections for the authenticated user",
     *     tags={"Networking"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         required=false,
     *         description="Number of connections per page",
     *         @OA\Schema(type="integer", default=15)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="List of all connections (pending, accepted)",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/ConnectionData")
     *         )
     *     )
     * )
     *
     * Get all connections for the authenticated user.
     *
     * @param Request $request
     * @return DataCollection
     */
    public function index(Request $request): DataCollection
    {
        $perPage = $request->input('per_page', 15);
        /** @var User $user */
        $user = $request->user();

        try {
            // For parents, get the linked child
            // For other users, returns the user themselves
            $effectiveUser = $this->parentChildResolver->getEffectiveUser($user);

            $connections = $this->networkingService->getConnections($effectiveUser->id, $perPage);

            // Map each item to a ConnectionData object
            $data = collect($connections->items())->map(function ($item) {
                try {
                    return ConnectionData::fromConnection($item);
                } catch (\Throwable $e) {
                    Log::error('Error creating ConnectionData in index method', [
                        'error' => $e->getMessage()
                    ]);
                    throw $e;
                }
            });

            return new DataCollection(ConnectionData::class, $data);
        } catch (ModelNotFoundException $e) {
            // Handle case where parent has no linked child
            if ($user->profile_type === 'parent') {
                Log::warning('Parent without linked child tried to access network connections', [
                    'parent_id' => $user->id
                ]);
                return new DataCollection(ConnectionData::class, collect([]));
            }

            throw $e;
        } catch (\Throwable $e) {
            Log::error('Error in NetworkingController::index method', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/connections/accepted",
     *     summary="Get all accepted connections for the authenticated user",
     *     tags={"Networking"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         required=false,
     *         description="Number of connections per page",
     *         @OA\Schema(type="integer", default=15)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="List of accepted connections",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/ConnectionData")
     *         )
     *     )
     * )
     *
     * Get all accepted connections for the authenticated user.
     *
     * @param Request $request
     * @return DataCollection
     */
    public function accepted(Request $request): DataCollection
    {
        $perPage = $request->input('per_page', 15);
        /** @var User $user */
        $user = $request->user();

        try {
            $connections = $this->networkingService->getAcceptedConnections(
                $user->id,
                $perPage
            );

            // Map each item to a ConnectionData object
            $data = collect($connections->items())->map(function ($item) {
                try {
                    return ConnectionData::fromConnection($item);
                } catch (\Throwable $e) {
                    Log::error('Error creating ConnectionData in accepted method', [
                        'error' => $e->getMessage()
                    ]);
                    throw $e;
                }
            });

            return new DataCollection(ConnectionData::class, $data);
        } catch (\Throwable $e) {
            Log::error('Error in NetworkingController::accepted method', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/connections/pending",
     *     summary="Get all pending connection requests for the authenticated user",
     *     tags={"Networking"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         required=false,
     *         description="Number of connections per page",
     *         @OA\Schema(type="integer", default=15)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="List of pending connection requests",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/ConnectionData")
     *         )
     *     )
     * )
     *
     * Get all pending connections for the authenticated user.
     *
     * @param Request $request
     * @return DataCollection
     */
    public function pending(Request $request): DataCollection
    {
        $perPage = $request->input('per_page', 15);
        /** @var User $user */
        $user = $request->user();

        try {
            $connections = $this->networkingService->getPendingRequests(
                $user->id,
                $perPage
            );

            // Try to use the same approach as in MessageController
            $data = collect($connections->items())->map(function ($item) {
                try {
                    return ConnectionData::fromConnection($item);
                } catch (\Throwable $e) {
                    Log::error('Error creating ConnectionData in pending method', [
                        'error' => $e->getMessage()
                    ]);
                    throw $e;
                }
            });

            return new DataCollection(ConnectionData::class, $data);
        } catch (\Throwable $e) {
            Log::error('Error in NetworkingController::pending method', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            throw $e;
        }
    }

    /**
     * @OA\Post(
     *     path="/api/v1/connections",
     *     summary="Create a new connection request",
     *     tags={"Networking"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             type="object",
     *             required={"recipientId"},
     *             @OA\Property(property="recipientId", type="integer", description="ID of the user to connect with")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Connection request created successfully",
     *         @OA\JsonContent(ref="#/components/schemas/ConnectionData")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error or user already connected/blocked"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Recipient user not found"
     *     )
     * )
     *
     * Create a new connection request.
     *
     * @param Request $request
     * @return ConnectionData
     */
    public function store(Request $request): ConnectionData
    {
        $request->validate([
            'recipientId' => 'required|exists:users,id',
        ]);

        /** @var User $user */
        $user = $request->user();

        try {
            $connection = $this->networkingService->createConnectionRequest(
                $user->id,
                $request->recipientId
            );

            return ConnectionData::fromConnection($connection);
        } catch (\Throwable $e) {
            Log::error('Error in NetworkingController::store method', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * @OA\Patch(
     *     path="/api/v1/connections/{id}/accept",
     *     summary="Accept a connection request",
     *     tags={"Networking"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="ID of the connection request to accept",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=204,
     *         description="Connection request accepted successfully"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Connection request not found"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Not the recipient of the request"
     *     )
     * )
     *
     * Accept a connection request.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function accept(Request $request, int $id): JsonResponse
    {
        /** @var User $user */
        $user = $request->user();

        $this->networkingService->acceptConnectionRequest($id, $user->id);

        return response()->json(null, Response::HTTP_NO_CONTENT);
    }

    /**
     * @OA\Patch(
     *     path="/api/v1/connections/{id}/reject",
     *     summary="Reject a connection request",
     *     tags={"Networking"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="ID of the connection request to reject",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=204,
     *         description="Connection request rejected successfully"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Connection request not found"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Not the recipient of the request"
     *     )
     * )
     *
     * Reject a connection request.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function reject(Request $request, int $id): JsonResponse
    {
        /** @var User $user */
        $user = $request->user();

        $this->networkingService->rejectConnectionRequest($id, $user->id);

        return response()->json(null, Response::HTTP_NO_CONTENT);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/connections/block",
     *     summary="Block a user",
     *     tags={"Networking"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             type="object",
     *             required={"userId"},
     *             @OA\Property(property="userId", type="integer", description="ID of the user to block")
     *         )
     *     ),
     *     @OA\Response(
     *         response=204,
     *         description="User blocked successfully"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User to block not found"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Cannot block self"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Parent users cannot block other users"
     *     )
     * )
     *
     * Block a user.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function block(Request $request): JsonResponse
    {
        $request->validate([
            'userId' => 'required|exists:users,id',
        ]);

        /** @var User $user */
        $user = $request->user();

        // Prevent parents from blocking users
        if ($user->profile_type === \App\Enums\ProfileType::PARENT) {
            return response()->json([
                'message' => 'Parent users cannot block other users'
            ], Response::HTTP_FORBIDDEN);
        }

        try {
            $this->networkingService->blockUser($user->id, $request->userId);

            return response()->json(null, Response::HTTP_NO_CONTENT);
        } catch (\Throwable $e) {
            Log::error('Error in NetworkingController::block method', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * @OA\Delete(
     *     path="/api/v1/connections/block/{userId}",
     *     summary="Unblock a user",
     *     tags={"Networking"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="userId",
     *         in="path",
     *         required=true,
     *         description="ID of the user to unblock",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=204,
     *         description="User unblocked successfully"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User to unblock not found or not blocked by you"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Parent users cannot unblock other users"
     *     )
     * )
     *
     * Unblock a user.
     *
     * @param Request $request
     * @param int $userId
     * @return JsonResponse
     */
    public function unblockUser(Request $request, int $userId): JsonResponse
    {
        /** @var User $user */
        $user = $request->user();

        // Prevent parents from unblocking users
        if ($user->profile_type === \App\Enums\ProfileType::PARENT) {
            return response()->json([
                'message' => 'Parent users cannot unblock other users'
            ], Response::HTTP_FORBIDDEN);
        }

        try {
            $this->networkingService->unblockUser($user->id, $userId);

            return response()->json(null, Response::HTTP_NO_CONTENT);
        } catch (\Throwable $e) {
            Log::error('Error in NetworkingController::unblock method', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/connections/can-connect/{targetUserId}",
     *     summary="Check if the authenticated user can connect with another user",
     *     tags={"Networking"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="targetUserId",
     *         in="path",
     *         required=true,
     *         description="ID of the target user to check connection possibility with",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Connection possibility status",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="can_connect", type="boolean", description="True if a connection can be initiated, false otherwise (e.g., blocked, already connected, pending request exists)")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Target user not found"
     *     )
     * )
     *
     * Check if a user can connect with another user.
     *
     * @param Request $request
     * @param int $targetUserId
     * @return JsonResponse
     */
    public function canConnect(Request $request, int $targetUserId): JsonResponse
    {
        /** @var User $user */
        $user = $request->user();

        try {
            $canConnect = $this->networkingService->canConnect($user->id, $targetUserId);

            return response()->json([
                'can_connect' => $canConnect,
            ]);
        } catch (\Throwable $e) {
            Log::error('Error in NetworkingController::canConnect method', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * @OA\Post(
     *     path="/api/v1/connections/message",
     *     summary="Create a connection request and send an initial message",
     *     tags={"Networking"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             type="object",
     *             required={"recipientId", "message"},
     *             @OA\Property(property="recipientId", type="integer", description="ID of the user to connect with"),
     *             @OA\Property(property="message", type="string", maxLength=1000, description="Initial message content")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Connection request and message sent successfully",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="connection", ref="#/components/schemas/ConnectionData"),
     *             @OA\Property(property="message", ref="#/components/schemas/MessageData")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Cannot connect (e.g., blocked, already connected)",
     *         @OA\JsonContent(type="object", @OA\Property(property="message", type="string"))
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Recipient user not found"
     *     )
     * )
     *
     * Create a connection and send an initial message.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function connectWithMessage(Request $request): JsonResponse
    {
        $request->validate([
            'recipientId' => 'required|exists:users,id',
            'message' => 'required|string|max:1000',
        ]);

        /** @var User $user */
        $user = $request->user();

        try {
            $result = $this->networkingService->connectWithMessage(
                $user->id,
                $request->recipientId,
                $request->message
            );

            if (!$result['success']) {
                return response()->json([
                    'message' => $result['message'],
                ], Response::HTTP_FORBIDDEN);
            }

            return response()->json($result);
        } catch (\Throwable $e) {
            Log::error('Error in NetworkingController::connectWithMessage method', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}
