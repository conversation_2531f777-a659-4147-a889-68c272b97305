<?php

namespace App\Http\Controllers\Api\V1;

use App\Data\Learning\LearningInvestmentData;
use App\Data\Learning\LearningProgressSummaryData;
use App\Data\Learning\LearningStatsData;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\LearningProgressService;
use Illuminate\Http\Request;
use OpenApi\Attributes as OA;

class LearningProgressController extends Controller
{
    public function __construct(
        protected LearningProgressService $learningProgressService
    ) {
    }

    /**
     * Get a complete summary of a user's learning progress
     *
     * @param int $userId The ID of the user to get learning progress for
     * @return LearningProgressSummaryData
     *
     * @OA\Get(
     *     path="/api/v1/users/{userId}/learning/summary",
     *     operationId="getLearningProgressSummary",
     *     summary="Get a complete summary of a user's learning progress",
     *     tags={"Learning Progress"},
     *     @OA\Parameter(
     *         name="userId",
     *         description="The ID of the user to get learning progress for",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Learning progress summary retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/LearningProgressSummaryData")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User not found"
     *     )
     * )
     */
    public function summary(Request $request, int $userId): LearningProgressSummaryData
    {
        $targetUser = User::query()->findOrFail($userId);
        return $this->learningProgressService->getLearningProgressSummary($targetUser);
    }

    /**
     * Get certificates earned by a user
     *
     * @param int $userId The ID of the user to get certificates for
     * @return \Spatie\LaravelData\DataCollection
     *
     * @OA\Get(
     *     path="/api/v1/users/{userId}/learning/certificates",
     *     operationId="getLearningCertificates",
     *     summary="Get certificates earned by a user",
     *     tags={"Learning Progress"},
     *     @OA\Parameter(
     *         name="userId",
     *         description="The ID of the user to get certificates for",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Certificates retrieved successfully",
     *         @OA\JsonContent(
     *             type="array",
     *             items=@OA\Items(ref="#/components/schemas/CertificateData")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User not found"
     *     )
     * )
     */
    public function certificates(Request $request, int $userId)
    {
        $targetUser = User::query()->findOrFail($userId);
        return $this->learningProgressService->getCertificates($targetUser);
    }

    /**
     * Get learning statistics for a user
     *
     * @param int $userId The ID of the user to get learning statistics for
     * @return LearningStatsData
     *
     * @OA\Get(
     *     path="/api/v1/users/{userId}/learning/stats",
     *     operationId="getLearningStats",
     *     summary="Get learning statistics for a user",
     *     tags={"Learning Progress"},
     *     @OA\Parameter(
     *         name="userId",
     *         description="The ID of the user to get learning statistics for",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Learning statistics retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/LearningStatsData")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User not found"
     *     )
     * )
     */
    public function stats(Request $request, int $userId): LearningStatsData
    {
        $targetUser = User::query()->findOrFail($userId);
        return $this->learningProgressService->getLearningStats($targetUser);
    }

    /**
     * Get learning investment distribution for a user
     *
     * @param int $userId The ID of the user to get learning investment for
     * @return LearningInvestmentData
     *
     * @OA\Get(
     *     path="/api/v1/users/{userId}/learning/investment",
     *     operationId="getLearningInvestment",
     *     summary="Get learning investment distribution for a user",
     *     tags={"Learning Progress"},
     *     @OA\Parameter(
     *         name="userId",
     *         description="The ID of the user to get learning investment for",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Learning investment distribution retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/LearningInvestmentData")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User not found"
     *     )
     * )
     */
    public function investment(Request $request, int $userId): LearningInvestmentData
    {
        $targetUser = User::query()->findOrFail($userId);
        return $this->learningProgressService->getLearningInvestment($targetUser);
    }

    /**
     * Get badge progress for a user
     *
     * @param int $userId The ID of the user to get badge progress for
     * @return \Spatie\LaravelData\DataCollection
     *
     * @OA\Get(
     *     path="/api/v1/users/{userId}/learning/badges",
     *     operationId="getBadgeProgress",
     *     summary="Get badge progress for a user",
     *     tags={"Learning Progress"},
     *     @OA\Parameter(
     *         name="userId",
     *         description="The ID of the user to get badge progress for",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Badge progress retrieved successfully",
     *         @OA\JsonContent(
     *             type="array",
     *             items=@OA\Items(ref="#/components/schemas/BadgeProgressData")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User not found"
     *     )
     * )
     */
    public function badges(Request $request, int $userId)
    {
        $targetUser = User::query()->findOrFail($userId);
        return $this->learningProgressService->getBadgeProgress($targetUser);
    }
}
