<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\State;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="States",
 *     description="API Endpoints for state information"
 * )
 */

/**
 * @OA\Schema(
 *     schema="USState",
 *     title="US State",
 *     description="Represents a US State.",
 *     @OA\Property(property="code", type="string", description="Two-letter state code (Primary Key)", example="GA"),
 *     @OA\Property(property="name", type="string", description="Full name of the state", example="Georgia"),
 * )
 */
class StateController extends Controller
{
    /**
     *
     * List all states
     *
     * @OA\Get(
     *     path="/api/v1/states",
     *     summary="List all states",
     *     description="Returns a list of all states for dropdown selections",
     *     tags={"States"},
     *     @OA\Response(
     *         response=200,
     *         description="List of states",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(ref="#/components/schemas/USState")
     *             )
     *         )
     *     )
     * )
     */
    public function index(): JsonResponse
    {
        $states = State::query()
            ->orderBy('name')
            ->get(['code', 'name']);

        return response()->json([
            'success' => true,
            'data' => $states
        ]);
    }

    /**
     * Get a specific state by code
     *
     * @OA\Get(
     *     path="/api/v1/states/{code}",
     *     summary="Get state details",
     *     description="Returns detailed information about a specific state",
     *     tags={"States"},
     *     @OA\Parameter(
     *         name="code",
     *         in="path",
     *         required=true,
     *         description="State code (e.g. CA, NY)",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="State information",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 ref="#/components/schemas/USState"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="State not found",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="State not found"),
     *         )
     *     )
     * )
     */
    public function show(string $code): JsonResponse
    {
        $state = State::query()
            ->where('code', $code)
            ->first(['code', 'name']);

        if (!$state) {
            return response()->json([
                'success' => false,
                'message' => 'State not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $state
        ]);
    }
}
