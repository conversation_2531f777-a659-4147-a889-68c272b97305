<?php

namespace App\Http\Controllers\Api\V1\XFactor;

use App\Data\XFactor\LeaderboardRequest;
use App\Http\Controllers\Controller;
use App\Services\XFactor\XFactorLeaderboardService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

/**
 * @OA\Tag(
 *     name="X-Factor Leaderboard",
 *     description="API Endpoints for X-Factor leaderboard"
 * )
 */
class XFactorLeaderboardController extends Controller
{
    public function __construct(
        private readonly XFactorLeaderboardService $leaderboardService,
    ) {}

    /**
     * Get X-Factor leaderboard
     *
     * @OA\Get(
     *     path="/api/v1/x-factor/leaderboard",
     *     summary="Get X-Factor leaderboard",
     *     description="Returns the ranked leaderboard for X-Factor module completion",
     *     tags={"X-Factor Leaderboard"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="region",
     *         in="query",
     *         description="Filter by region ID (e.g., '1')",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="state",
     *         in="query",
     *         description="Filter by state code (e.g., 'GA')",
     *         @OA\Schema(type="string", minLength=2, maxLength=2)
     *     ),
     *     @OA\Parameter(
     *         name="graduation_year",
     *         in="query",
     *         description="Filter by graduation year (e.g., '2025')",
     *         @OA\Schema(type="integer", minimum=2000, maximum=2100)
     *     ),
     *     @OA\Parameter(
     *         name="academic_year",
     *         in="query",
     *         description="Academic year filter (e.g., '2023-24')",
     *         @OA\Schema(type="string", pattern="^\d{4}-\d{2}$")
     *     ),
     *     @OA\Parameter(
     *         name="all_time",
     *         in="query",
     *         description="If true, returns all-time data regardless of academic year",
     *         @OA\Schema(type="boolean")
     *     ),
     *     @OA\Parameter(
     *         name="start_date",
     *         in="query",
     *         description="Start date for custom date range (format: YYYY-MM-DD)",
     *         @OA\Schema(type="string", format="date")
     *     ),
     *     @OA\Parameter(
     *         name="end_date",
     *         in="query",
     *         description="End date for custom date range (format: YYYY-MM-DD)",
     *         @OA\Schema(type="string", format="date")
     *     ),
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number for pagination",
     *         @OA\Schema(type="integer", default=1, minimum=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of results per page",
     *         @OA\Schema(type="integer", default=50, minimum=1, maximum=100)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Leaderboard retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/LeaderboardResponse")
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(ref="#/components/schemas/ValidationError")
     *     )
     * )
     */
    public function index(LeaderboardRequest $request): JsonResponse
    {
        $user = Auth::user();
        $response = $this->leaderboardService->getLeaderboard($request, $user);
        return response()->json($response);
    }
}
