<?php
namespace App\Http\Controllers\Api\V1\XFactor;

use App\Data\XFactor\Exam\ExamData;
use App\Data\XFactor\Exam\Requests\SubmitExamRequest;
use App\Data\XFactor\Exam\TestAttemptData;
use App\Http\Controllers\Controller;
use App\Models\Module;
use App\Services\XFactor\XFactorExamService;

/**
 * @OA\Tag(
 *     name="X-Factor Exams",
 *     description="API Endpoints for managing X-Factor exams"
 * )
 */
class XFactorExamController extends Controller
{
    public function __construct(
        private readonly XFactorExamService $examService,
    ) {}

    /**
     * Get exam details for a module
     *
     * @OA\Get(
     *     path="/api/v1/x-factor/modules/{moduleId}/exam",
     *     summary="Get exam details for a module",
     *     description="Returns exam details including questions for a specific module",
     *     tags={"X-Factor Exams"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="moduleId",
     *         in="path",
     *         required=true,
     *         description="Module identifier",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Exam details retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/ExamData")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Module not found or has no exam",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     )
     * )
     */
    public function show(int $moduleId): ExamData
    {
        /** @var \App\Models\User $user */
        $user = request()->user();

        $examData = $this->examService->getExamByModule($moduleId, $user->id);

        // If there's a latest attempt that has expired, mark it as such
        if ($examData->latestAttempt && $examData->latestAttempt->endsAt->isPast()) {
            $this->examService->markAttemptExpired($examData->latestAttempt->id);
            // Refresh exam data to get updated attempt status
            $examData = $this->examService->getExamByModule($moduleId, $user->id);
        }

        return $examData;
    }

    /**
     * Start a new exam attempt
     *
     * @OA\Post(
     *     path="/api/v1/x-factor/modules/{moduleId}/exam/attempts",
     *     summary="Start a new exam attempt",
     *     description="Creates a new attempt for a module exam",
     *     tags={"X-Factor Exams"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="moduleId",
     *         in="path",
     *         required=true,
     *         description="Module identifier",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Exam attempt started successfully",
     *         @OA\JsonContent(ref="#/components/schemas/TestAttemptData")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Module not found",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Module has no exam",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     )
     * )
     */
    public function store(int $moduleId): TestAttemptData
    {
        $module = Module::query()->findOrFail($moduleId);

        if (! $module->tests()->exists()) {
            abort(422, 'Module has no exam');
        }

        /** @var \App\Models\User $user */
        $user = request()->user();

        // Check for and handle any existing expired attempts
        $exam = $this->examService->getExamByModule($moduleId, $user->id);
        if ($exam->latestAttempt && $exam->latestAttempt->endsAt->isPast()) {
            $this->examService->markAttemptExpired($exam->latestAttempt->id);
        }

        return $this->examService->startExamAttempt(
            $moduleId,
            $user->id
        );
    }

    /**
     * Submit completed exam
     *
     * @OA\Post(
     *     path="/api/v1/x-factor/modules/{moduleId}/exam/submit",
     *     summary="Submit completed exam",
     *     description="Submits all responses for an exam and finalizes the attempt",
     *     tags={"X-Factor Exams"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="moduleId",
     *         in="path",
     *         required=true,
     *         description="Module identifier",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"responses", "status"},
     *             @OA\Property(
     *                 property="responses",
     *                 type="array",
     *                 description="Array of question responses",
     *                 @OA\Items(
     *                     type="object",
     *                     required={"questionId", "response"},
     *                     @OA\Property(
     *                         property="questionId",
     *                         type="integer",
     *                         description="ID of the question being answered",
     *                         example=1
     *                     ),
     *                     @OA\Property(
     *                         property="response",
     *                         type="string",
     *                         description="The selected answer identifier or text response",
     *                         example=1
     *                     )
     *                 )
     *             ),
     *             @OA\Property(
     *                 property="status",
     *                 type="string",
     *                 enum={"complete", "expired", "pending_review"},
     *                 description="Status of the exam submission",
     *                 example="completed"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Exam submitted successfully",
     *         @OA\JsonContent(ref="#/components/schemas/TestAttemptData")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Module or attempt not found",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Invalid responses or exam status",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     )
     * )
     */
    public function submit(SubmitExamRequest $request, int $moduleId): TestAttemptData
    {
        /** @var \App\Models\User $user */
        $user = request()->user();

        return $this->examService->submitExam(
            $moduleId,
            $user->id,
            $request->responses,
            $request->status
        );
    }
}
