<?php
namespace App\Http\Controllers\Api\V1\XFactor;

use App\Data\XFactor\Quiz\QuestionResponseData;
use App\Data\XFactor\Quiz\QuizData;
use App\Data\XFactor\Quiz\Requests\CompleteQuizAttemptRequest;
use App\Data\XFactor\Quiz\Requests\SubmitQuestionResponseRequest;
use App\Data\XFactor\Quiz\TestAttemptData;
use App\Http\Controllers\Controller;
use App\Models\Module;
use App\Models\TestAttempt;
use App\Services\XFactor\XFactorQuizService;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="X-Factor Quizzes",
 *     description="API Endpoints for managing X-Factor quizzes"
 * )
 */
class XFactorQuizController extends Controller
{
    public function __construct(
        private readonly XFactorQuizService $quizService,
    ) {}

    /**
     * Get quiz details for a module
     *
     * @OA\Get(
     *     path="/api/v1/x-factor/modules/{moduleId}/quiz",
     *     summary="Get quiz details for a module",
     *     description="Returns quiz details including questions for a specific module",
     *     tags={"X-Factor Quizzes"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="moduleId",
     *         in="path",
     *         required=true,
     *         description="Module identifier",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Quiz details retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/QuizData")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Module not found or has no quiz",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     )
     * )
     */
    public function show(int $moduleId, Request $request): QuizData
    {
        return $this->quizService->getQuizByModule($moduleId, $request->user()?->id);
    }

    /**
     * Start a new quiz attempt
     *
     * @OA\Post(
     *     path="/api/v1/x-factor/modules/{moduleId}/quiz/attempts",
     *     summary="Start a new quiz attempt",
     *     description="Creates a new attempt for a module quiz",
     *     tags={"X-Factor Quizzes"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="moduleId",
     *         in="path",
     *         required=true,
     *         description="Module identifier",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Quiz attempt started successfully",
     *         @OA\JsonContent(ref="#/components/schemas/TestAttemptData")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Module not found",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Module has no tests",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     )
     * )
     */
    public function store(int $moduleId): TestAttemptData
    {
        $module = Module::query()->findOrFail($moduleId);

        if (! $module->tests()->exists()) {
            abort(422, 'Module has no tests');
        }

        /** @var \App\Models\User $user */
        $user = request()->user();

        return $this->quizService->startQuizAttempt(
            $module->id,
            $user->id
        );
    }

    /**
     * Submit a response for a quiz question
     *
     * @OA\Post(
     *     path="/api/v1/x-factor/modules/{moduleId}/quiz/attempts/{attemptId}/responses",
     *     summary="Submit a response for a quiz question",
     *     description="Submits a user's response to a specific question in a quiz attempt",
     *     tags={"X-Factor Quizzes"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="moduleId",
     *         in="path",
     *         required=true,
     *         description="Module identifier",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="attemptId",
     *         in="path",
     *         required=true,
     *         description="Quiz attempt identifier",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"questionId", "response"},
     *             @OA\Property(property="questionId", type="integer", example=1),
     *             @OA\Property(property="response", type="string", example="option_a")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Response submitted successfully",
     *         @OA\JsonContent(ref="#/components/schemas/QuestionResponseData")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Module, attempt, or question not found",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Invalid response",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     )
     * )
     */
    public function submitResponse(
        SubmitQuestionResponseRequest $request,
        int $moduleId,
        int $attemptId
    ): QuestionResponseData {
        /** @var \App\Models\User $user */
        $user = request()->user();

        return $this->quizService->submitQuestionResponse(
            $attemptId,
            $request->questionId,
            $user->id,
            $request->response
        );
    }

    /**
     * Complete a quiz attempt
     *
     * @OA\Post(
     *     path="/api/v1/x-factor/modules/{moduleId}/quiz/complete",
     *     summary="Complete a quiz attempt",
     *     description="Finalizes a quiz attempt and calculates the score",
     *     tags={"X-Factor Quizzes"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="moduleId",
     *         in="path",
     *         required=true,
     *         description="Module identifier",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/CompleteQuizAttemptRequest")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Quiz completed successfully",
     *         @OA\JsonContent(ref="#/components/schemas/TestAttemptData")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Module or attempt not found",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Invalid responses or incomplete quiz",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     )
     * )
     */
    public function complete(
        CompleteQuizAttemptRequest $request,
        int $moduleId
    ): TestAttemptData {
        /** @var \App\Models\User $user */
        $user = request()->user();

        // Validate and get the latest attempt
        $latestAttempt = CompleteQuizAttemptRequest::validateAttempt(
            $moduleId,
            $user->id,
            $request->responses
        );

        return $this->quizService->completeQuizAttempt($latestAttempt->id, $request->responses);
    }
}
