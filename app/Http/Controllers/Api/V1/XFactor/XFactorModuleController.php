<?php

namespace App\Http\Controllers\Api\V1\XFactor;

use App\Data\XFactor\ModuleBrowseRequest;
use App\Data\XFactor\ModuleResponse;
use App\Data\XFactor\SearchModulesResponse;
use App\Data\XFactor\TopicModulesRequest;
use App\Data\XFactor\TopicModulesResponse as TopicModulesResponseData;
use App\Http\Controllers\Controller;
use App\Models\Module;
use App\Models\Topic;
use App\Models\User;
use App\Services\XFactor\XFactorModuleService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Spatie\LaravelData\Data;

/**
 * @OA\Tag(
 *     name="X-Factor Modules",
 *     description="API Endpoints for managing X-Factor modules"
 * )
 */
class XFactorModuleController extends Controller
{
    public function __construct(
        private readonly XFactorModuleService $moduleService,
    ) {}

    /**
     * Get all modules grouped by topics
     *
     * @OA\Get(
     *     path="/api/v1/x-factor/modules",
     *     summary="Get all modules grouped by topics",
     *     description="Returns all modules grouped by their respective topics",
     *     tags={"X-Factor Modules"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Modules retrieved successfully",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/TopicModulesResponse")
     *         )
     *     )
     * )
     */
    public function index(): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();
        $topics = $this->moduleService->getAllModulesByTopics($user);
        return response()->json($topics);
    }

    /**
     * Get modules for a specific topic
     *
     * @OA\Get(
     *     path="/api/v1/x-factor/modules/topics/{topic_id}",
     *     summary="Get modules for a specific topic",
     *     description="Returns modules for a specific topic",
     *     tags={"X-Factor Modules"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="topic_id",
     *         in="path",
     *         required=true,
     *         description="Topic identifier",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Topic modules retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/TopicModulesResponse")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Topic not found",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     )
     * )
     */
    public function byTopic(TopicModulesRequest $request): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();
        $topic = Topic::query()->findOrFail($request->topic_id);
        $response = $this->moduleService->getModulesByTopic($topic, $user);
        return response()->json(TopicModulesResponseData::from($response));
    }

    /**
     * Search modules with filters
     *
     * @OA\Get(
     *     path="/api/v1/x-factor/modules/search",
     *     summary="Search modules with filters",
     *     description="Search for modules using various filters with pagination support",
     *     tags={"X-Factor Modules"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="search",
     *         in="query",
     *         required=false,
     *         description="Search query for module title or description",
     *         @OA\Schema(type="string", maxLength=255)
     *     ),
     *     @OA\Parameter(
     *         name="sort",
     *         in="query",
     *         required=false,
     *         description="Sort order for results",
     *         @OA\Schema(type="string", enum={"newest", "title", "duration"}, default="newest")
     *     ),
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         required=false,
     *         description="Page number for pagination",
     *         @OA\Schema(type="integer", default=1)
     *     ),
     *     @OA\Parameter(
     *         name="perPage",
     *         in="query",
     *         required=false,
     *         description="Number of items per page",
     *         @OA\Schema(type="integer", default=10)
     *     ),
     *     @OA\Parameter(
     *         name="topics",
     *         in="query",
     *         required=false,
     *         description="Filter by topic IDs (array format)",
     *         @OA\Schema(
     *             type="array",
     *             @OA\Items(type="integer")
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         required=false,
     *         description="Filter by module completion status",
     *         @OA\Schema(type="string", enum={"not_started", "in_progress", "completed"})
     *     ),
     *     @OA\Parameter(
     *         name="standalone",
     *         in="query",
     *         required=false,
     *         description="Filter for standalone modules",
     *         @OA\Schema(type="boolean")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Search results",
     *         @OA\JsonContent(ref="#/components/schemas/SearchModulesResponse")
     *     )
     * )
     */
    public function search(ModuleBrowseRequest $request): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();
        $paginator = $this->moduleService->searchModules($request, $user);
        return response()->json(SearchModulesResponse::fromPaginator($paginator));
    }

    /**
     * Get detailed information about a specific module
     *
     * @OA\Get(
     *     path="/api/v1/x-factor/modules/{module}",
     *     summary="Get module details",
     *     description="Get detailed information about a specific module",
     *     tags={"X-Factor Modules"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="module",
     *         in="path",
     *         required=true,
     *         description="Module identifier",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Module details retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/ModuleResponse")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Module not found",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     )
     * )
     */
    public function show(Module $module): ModuleResponse
    {
        /** @var User $user */
        $user = Auth::user();

        // Load relationships needed for completion check
        $module->load(['test', 'users' => fn($q) => $q->where('user_id', $user->id)]);

        // If module has no quiz and isn't completed yet, mark it as complete
        if (!$module->test && !$module->users->first()?->pivot->completed_at) {
            $module->users()->syncWithoutDetaching([
                $user->id => [
                    'completed_at' => now(),
                    'completion_metadata' => [
                        'completion_type' => 'view',
                        'completed_via' => 'auto',
                    ],
                ]
            ]);

            // If module is part of a course, recalculate course completion
            if ($module->courses()->count() > 0) {
                $module->courses()->first()->calculateCompletionForUser($user);
            }
        }

        return ModuleResponse::fromModel($module, $user);
    }
}
