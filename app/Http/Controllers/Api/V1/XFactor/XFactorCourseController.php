<?php

namespace App\Http\Controllers\Api\V1\XFactor;

use App\Data\XFactor\CourseBrowseRequest;
use App\Data\XFactor\CourseDetailResponse;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\XFactor\XFactorCourseService;
use Illuminate\Http\JsonResponse;

/**
 * @OA\Tag(
 *     name="X-Factor Courses",
 *     description="API Endpoints for managing X-Factor courses"
 * )
 * @OA\Schema(
 *      schema="TopicWithCourses",
 *      type="object",
 *      @OA\Property(property="topic", type="string", example="Leadership"),
 *      @OA\Property(
 *          property="courses",
 *          type="array",
 *          @OA\Items(ref="#/components/schemas/XFactorCourseSummary")
 *      )
 *  )
 */
class XFactorCourseController extends Controller
{
    public function __construct(
        private XFactorCourseService $service
    ) {}

    /**
     * Get user's in-progress courses for the featured slider
     *
     * @OA\Get(
     *     path="/api/v1/x-factor/courses/featured",
     *     summary="Get featured courses for the user",
     *     description="Returns a list of featured courses, including in-progress courses for the authenticated user",
     *     tags={"X-Factor Courses"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Featured courses retrieved successfully",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/XFactorCourseSummary")
     *         )
     *     )
     * )
     */
    public function featured(Request $request): JsonResponse
    {
        $courses = $this->service->getFeaturedCourses(
            user: $request->user(),
            limit: 5
        );

        return response()->json($courses);
    }

    /**
     * Get courses grouped by topics for the main browse grid
     *
     * @OA\Get(
     *     path="/api/v1/x-factor/courses",
     *     summary="Get courses grouped by topics",
     *     description="Returns courses grouped by topics for the main browse grid",
     *     tags={"X-Factor Courses"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Courses retrieved successfully",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/TopicWithCourses")
     *         )
     *     )
     * )
     */
    public function index(Request $request): JsonResponse
    {
        $topics = $this->service->getTopicCourses(
            user: $request->user(),
            coursesPerTopic: 10
        );

        return response()->json($topics);
    }

    /**
     * Get more courses for a specific topic (for carousel pagination)
     *
     * @OA\Get(
     *     path="/api/v1/x-factor/courses/topics/{topic}",
     *     summary="Get courses for a specific topic",
     *     description="Returns courses for a specific topic, used for carousel pagination",
     *     tags={"X-Factor Courses"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="topic",
     *         in="path",
     *         required=true,
     *         description="Topic identifier",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Topic courses retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/TopicWithCourses")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Topic not found",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     )
     * )
     */
    public function topicCourses(Request $request, string $topic): JsonResponse
    {
        $paginator = $this->service->getTopicCourses(
            user: $request->user(),
            topic: $topic,
            page: $request->input('page', 1),
            perPage: $request->input('per_page', 5)
        );

        return response()->json($paginator);
    }

    /**
     * Search courses across all topics
     *
     * @OA\Get(
     *     path="/api/v1/x-factor/courses/search",
     *     summary="Search for courses",
     *     description="Search for courses by keyword",
     *     tags={"X-Factor Courses"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="query",
     *         in="query",
     *         required=true,
     *         description="Search query",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Search results",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/XFactorCourseSummary")
     *         )
     *     )
     * )
     */
    public function search(Request $request, CourseBrowseRequest $data): JsonResponse
    {
        $paginator = $this->service->searchCourses(
            user: $request->user(),
            request: $data
        );

        return response()->json($paginator);
    }

    /**
     * Get detailed information about a specific course
     *
     * @OA\Get(
     *     path="/api/v1/x-factor/courses/{courseId}",
     *     summary="Get course details",
     *     description="Get detailed information about a specific course",
     *     tags={"X-Factor Courses"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="courseId",
     *         in="path",
     *         required=true,
     *         description="Course identifier",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Course details retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/XFactorCourseDetail")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Course not found",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     )
     * )
     */
    public function show(Request $request, string $courseId): CourseDetailResponse
    {
        $course = $this->service->getCourseDetail(
            user: $request->user(),
            courseId: $courseId
        );

        return $course;
    }
}
