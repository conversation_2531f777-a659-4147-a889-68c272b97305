<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\NetworkingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

/**
 * @OA\Tag(
 *     name="User Blocking",
 *     description="Endpoints for managing blocked users"
 * )
 */
class UserBlockController extends Controller
{
    /**
     * @var NetworkingService
     */
    protected $networkingService;

    /**
     * Create a new controller instance.
     *
     * @param NetworkingService $networkingService
     */
    public function __construct(NetworkingService $networkingService)
    {
        $this->networkingService = $networkingService;
    }

    /**
     * @OA\Get(
     *     path="/api/v1/blocks",
     *     summary="Get all blocked users for the authenticated user",
     *     tags={"User Blocking"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="List of blocked users",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=123, description="User ID"),
     *                     @OA\Property(property="first_name", type="string", example="John", description="User first name"),
     *                     @OA\Property(property="last_name", type="string", example="Doe", description="User last name"),
     *                     @OA\Property(property="profile_image_url", type="string", format="uri", nullable=true, example="https://example.com/images/profile.jpg", description="URL to user's profile image"),
     *                     @OA\Property(property="blocked_at", type="string", format="date-time", example="2023-05-15T14:30:00Z", description="When the user was blocked")
     *                 )
     *             )
     *         )
     *     )
     * )
     *
     * Get all blocked users for the authenticated user.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        /** @var User $user */
        $user = $request->user();

        try {
            $blockedUsers = $user->blockedUsers()->with('blocked')->get();

            return response()->json([
                'data' => $blockedUsers->map(function ($block) {
                    return [
                        'id' => $block->blocked->id,
                        'first_name' => $block->blocked->first_name,
                        'last_name' => $block->blocked->last_name,
                        'profile_image_url' => $block->blocked->getFirstMediaUrl('profile_image', 'thumbnail'),
                        'blocked_at' => $block->created_at,
                    ];
                }),
            ]);
        } catch (\Throwable $e) {
            Log::error('Error in UserBlockController::index method', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * @OA\Post(
     *     path="/api/v1/blocks",
     *     summary="Block a user",
     *     tags={"User Blocking"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             type="object",
     *             required={"userId"},
     *             @OA\Property(
     *                 property="userId",
     *                 type="integer",
     *                 description="ID of the user to block",
     *                 example=123
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=204,
     *         description="User blocked successfully"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User not found"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\AdditionalProperties(
     *                     type="array",
     *                     @OA\Items(type="string")
     *                 )
     *             )
     *         )
     *     )
     * )
     *
     * Block a user.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'userId' => 'required|exists:users,id',
        ]);

        /** @var User $user */
        $user = $request->user();

        // Prevent parents from blocking users
        if ($user->profile_type === \App\Enums\ProfileType::PARENT) {
            return response()->json([
                'message' => 'Parent users cannot block other users'
            ], Response::HTTP_FORBIDDEN);
        }

        try {
            $this->networkingService->blockUser($user->id, $request->userId);

            return response()->json(null, Response::HTTP_NO_CONTENT);
        } catch (\Throwable $e) {
            Log::error('Error in UserBlockController::store method', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * @OA\Delete(
     *     path="/api/v1/blocks/{userId}",
     *     summary="Unblock a user",
     *     tags={"User Blocking"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="userId",
     *         in="path",
     *         required=true,
     *         description="ID of the user to unblock",
     *         @OA\Schema(type="integer", format="int64")
     *     ),
     *     @OA\Response(
     *         response=204,
     *         description="User unblocked successfully"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User not found or not blocked"
     *     )
     * )
     *
     * Unblock a user.
     *
     * @param Request $request
     * @param int $userId
     * @return JsonResponse
     */
    public function destroy(Request $request, int $userId): JsonResponse
    {
        /** @var User $user */
        $user = $request->user();

        // Prevent parents from unblocking users
        if ($user->profile_type === \App\Enums\ProfileType::PARENT) {
            return response()->json([
                'message' => 'Parent users cannot unblock other users'
            ], Response::HTTP_FORBIDDEN);
        }

        try {
            $this->networkingService->unblockUser($user->id, $userId);

            return response()->json(null, Response::HTTP_NO_CONTENT);
        } catch (\Throwable $e) {
            Log::error('Error in UserBlockController::destroy method', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}
