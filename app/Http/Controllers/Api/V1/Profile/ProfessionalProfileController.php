<?php

namespace App\Http\Controllers\Api\V1\Profile;

use App\Data\Profile\ProfessionalDetailsData;
use App\Enums\ProfileType;
use App\Http\Controllers\Controller;
use App\Services\ProfessionalProfileService;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="Professional Profile",
 *     description="Endpoints for managing professional profile details"
 * )
 */
class ProfessionalProfileController extends Controller
{
    public function __construct(
        private readonly ProfessionalProfileService $professionalProfileService
    ) {}

    /**
     * @OA\Get(
     *     path="/api/v1/profile/professional/details",
     *     summary="Get professional profile details",
     *     tags={"Professional Profile"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Professional profile details retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/ProfessionalDetailsData")
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized"
     *     )
     * )
     */
    public function getDetails(Request $request): ProfessionalDetailsData
    {
        $user = $request->user();

        return $this->professionalProfileService->getDetails($user);
    }

    /**
     * @OA\Put(
     *     path="/api/v1/profile/professional/details",
     *     summary="Update professional profile details",
     *     tags={"Professional Profile"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/ProfessionalDetailsData")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Professional profile details updated successfully",
     *         @OA\JsonContent(ref="#/components/schemas/ProfessionalDetailsData")
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\AdditionalProperties(
     *                     type="array",
     *                     @OA\Items(type="string")
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function updateDetails(Request $request, ProfessionalDetailsData $data): ProfessionalDetailsData
    {
        $user = $request->user();

        return $this->professionalProfileService->updateDetails($user, $data);
    }
}
