<?php

namespace App\Http\Controllers\Api\V1\Profile;

use App\Data\Profile\AvatarData;
use App\Data\Profile\GetStoryData;
use App\Data\Profile\ProfileDetailsData;
use App\Data\Profile\ProfilePhotosData;
use App\Data\Profile\RecruiterStatusData;
use App\Data\Profile\Sports\SportsData;
use App\Data\Profile\Sports\SportSearchResponse;
use App\Data\Profile\UpdateAvatarData;
use App\Data\Profile\UpdateInvolvementsData;
use App\Data\Profile\UpdateProfilePhotosData;
use App\Data\Profile\UpdateRecruiterStatusData;
use App\Data\Profile\UpdateStoryData;
use App\Data\Profile\UpdateWorkExperiencesData;
use App\Http\Controllers\Controller;
use App\Services\PositiveAthleteProfileService;
use App\Services\ParentChildResolverService;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;

/**
 * @OA\Tag(
 *     name="Positive Athlete Profile",
 *     description="Endpoints for managing positive athlete profiles"
 * )
 */
class PositiveAthleteProfileController extends Controller
{
    public function __construct(
        private readonly PositiveAthleteProfileService $positiveAthleteProfileService,
        private readonly ParentChildResolverService $parentChildResolver
    ) {}

    /**
     * @OA\Get(
     *     path="/api/v1/profile/photos",
     *     summary="Get profile photos",
     *     tags={"Positive Athlete Profile"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Profile photos retrieved successfully",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/ProfilePhotoData")
     *         )
     *     )
     * )
     */
    public function getPhotos(Request $request): ProfilePhotosData
    {
        try {
            $user = $request->user();
            // For parents, get the linked child's profile
            $effectiveUser = $this->parentChildResolver->getEffectiveUser($user);
            $photos = $effectiveUser->getMedia('profile_photos');

            return ProfilePhotosData::fromMediaCollection($photos);
        } catch (ModelNotFoundException $e) {
            // Handle case where parent has no linked child
            if ($user->profile_type === 'parent') {
                abort(404, 'Your account is not linked to any athlete. Please contact support for assistance.');
            }

            throw $e;
        }
    }

    /**
     * @OA\Post(
     *     path="/api/v1/profile/photos",
     *     summary="Update profile photos",
     *     tags={"Positive Athlete Profile"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(
     *                     property="photos[]",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(
     *                             property="file",
     *                             type="string",
     *                             format="binary",
     *                             description="Photo file (max 5MB, image files only)"
     *                         ),
     *                         @OA\Property(
     *                             property="focal_point",
     *                             type="object",
     *                             @OA\Property(property="x", type="number", format="float", example=0.5),
     *                             @OA\Property(property="y", type="number", format="float", example=0.5)
     *                         )
     *                     )
     *                 ),
     *                 @OA\Property(
     *                     property="delete_photo_ids",
     *                     type="array",
     *                     @OA\Items(type="integer")
     *                 ),
     *                 @OA\Property(property="update_photo_id", type="integer"),
     *                 @OA\Property(
     *                     property="focal_point",
     *                     type="object",
     *                     @OA\Property(property="x", type="number", format="float", example=0.5),
     *                     @OA\Property(property="y", type="number", format="float", example=0.5)
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Profile photos updated successfully",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/ProfilePhotoData")
     *         )
     *     )
     * )
     */
    public function updatePhotos(UpdateProfilePhotosData $data, Request $request): ProfilePhotosData
    {
        try {
            $user = $request->user();
            // For parents, get the linked child's profile
            $effectiveUser = $this->parentChildResolver->getEffectiveUser($user);

            // Delete photos if specified
            if (! empty($data->delete_photo_ids)) {
                $effectiveUser->media()
                    ->whereIn('id', $data->delete_photo_ids)
                    ->where('collection_name', 'profile_photos')
                    ->delete();
            }

            // Update focal point if specified
            if ($data->update_photo_id && $data->focal_point) {
                $photo = $effectiveUser->media()
                    ->where('id', $data->update_photo_id)
                    ->where('collection_name', 'profile_photos')
                    ->first();

                if ($photo) {
                    $photo->setCustomProperty('focal_point', $data->focal_point);
                    $photo->save();
                }
            }

            // Add new photos if provided
            if (! empty($data->photos)) {
                foreach ($data->photos as $photo) {
                    // Get image dimensions before upload
                    $imageSize = getimagesize($photo->file->getPathname());

                    $mediaItem = $effectiveUser->addMedia($photo->file)
                        ->withCustomProperties([
                            'width'  => $imageSize[0],
                            'height' => $imageSize[1],
                        ])
                        ->withResponsiveImages()
                        ->toMediaCollection('profile_photos');

                    // Set focal point if provided
                    if (isset($photo->focal_point)) {
                        $mediaItem->setCustomProperty('focal_point', $photo->focal_point);
                        $mediaItem->save();
                    }
                }
            }

            // Return updated photos
            return ProfilePhotosData::fromMediaCollection($effectiveUser->getMedia('profile_photos'));
        } catch (ModelNotFoundException $e) {
            // Handle case where parent has no linked child
            if ($user->profile_type === 'parent') {
                abort(404, 'Your account is not linked to any athlete. Please contact support for assistance.');
            }

            throw $e;
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/profile/avatar",
     *     summary="Get user avatar",
     *     tags={"Positive Athlete Profile"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Avatar retrieved successfully",
     *         @OA\JsonContent(
     *             nullable=true,
     *             oneOf={
     *                 @OA\Schema(ref="#/components/schemas/AvatarData"),
     *                 @OA\Schema(type="null")
     *             }
     *         )
     *     )
     * )
     */
    public function getAvatar(Request $request): ?AvatarData
    {
        $user   = $request->user();
        $avatar = $user->getFirstMedia('avatar');

        return $avatar ? AvatarData::fromMedia($avatar) : null;
    }

    /**
     * @OA\Post(
     *     path="/api/v1/profile/avatar",
     *     summary="Update user avatar",
     *     tags={"Positive Athlete Profile"},
     *     security={{"sanctum":{}}},
     *     operationId="updateAvatar",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Avatar upload",
     *         content={
     *             @OA\MediaType(
     *                 mediaType="multipart/form-data",
     *                 @OA\Schema(
     *                     type="object",
     *                     required={"file"},
     *                     properties={
     *                         @OA\Property(
     *                             property="file",
     *                             type="string",
     *                             format="binary",
     *                             description="Avatar image file (max 5MB, image files only: jpg, png, gif)"
     *                         )
     *                     }
     *                 )
     *             )
     *         }
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Avatar updated successfully",
     *         @OA\JsonContent(ref="#/components/schemas/AvatarData")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error - invalid file",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\AdditionalProperties(
     *                     type="array",
     *                     @OA\Items(type="string")
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function updateAvatar(UpdateAvatarData $data, Request $request): AvatarData
    {
        $user = $request->user();

        // Clear existing avatar
        $user->clearMediaCollection('avatar');

        // Add new avatar
        $mediaItem = $user->addMedia($data->file)
            ->withResponsiveImages()
            ->toMediaCollection('avatar');

        $user->searchable();

        return AvatarData::fromMedia($mediaItem);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/profile/story",
     *     summary="Get user story",
     *     tags={"Positive Athlete Profile"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Story retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/GetStoryData")
     *     )
     * )
     */
    public function getStory(Request $request): GetStoryData
    {
        $user = $request->user();

        return new GetStoryData(
            content: $user->content ?? '',
        );
    }

    /**
     * @OA\Put(
     *     path="/api/v1/profile/story",
     *     summary="Update user story",
     *     tags={"Positive Athlete Profile"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/UpdateStoryData")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Story updated successfully",
     *         @OA\JsonContent(ref="#/components/schemas/GetStoryData")
     *     )
     * )
     */
    public function updateStory(UpdateStoryData $data, Request $request): GetStoryData
    {
        $user          = $request->user();
        $user->content = $data->content;
        $user->save();

        return new GetStoryData(
            content: $user->content,
        );
    }

    /**
     * @OA\Get(
     *     path="/api/v1/profile",
     *     summary="Get profile details",
     *     tags={"Positive Athlete Profile"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Profile details retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/ProfileDetailsData")
     *     )
     * )
     */
    public function getDetails(Request $request): ProfileDetailsData
    {
        try {
            $user = $request->user();
            // For parents, get the linked child's profile
            $effectiveUser = $this->parentChildResolver->getEffectiveUser($user);

            return $this->positiveAthleteProfileService->getDetails($effectiveUser);
        } catch (ModelNotFoundException $e) {
            // Handle case where parent has no linked child
            if ($user->profile_type === 'parent') {
                abort(404, 'Your account is not linked to any athlete. Please contact support for assistance.');
            }

            throw $e;
        }
    }

    /**
     * @OA\Put(
     *     path="/api/v1/profile",
     *     summary="Update profile details",
     *     tags={"Positive Athlete Profile"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/ProfileDetailsData")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Profile details updated successfully",
     *         @OA\JsonContent(ref="#/components/schemas/ProfileDetailsData")
     *     )
     * )
     */
    public function updateProfile(ProfileDetailsData $data, Request $request): ProfileDetailsData
    {
        try {
            $user = $request->user();
            // For parents, get the linked child's profile
            $effectiveUser = $this->parentChildResolver->getEffectiveUser($user);

            return $this->positiveAthleteProfileService->updateDetails($effectiveUser, $data);
        } catch (ModelNotFoundException $e) {
            // Handle case where parent has no linked child
            if ($user->profile_type === 'parent') {
                abort(404, 'Your account is not linked to any athlete. Please contact support for assistance.');
            }

            throw $e;
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/profile/involvements",
     *     summary="Get community involvements",
     *     tags={"Positive Athlete Profile"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Community involvements retrieved successfully",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/InvolvementData")
     *         )
     *     )
     * )
     */
    public function getInvolvements(Request $request): array
    {
        return $this->positiveAthleteProfileService->getInvolvements($request->user());
    }

    /**
     * @OA\Put(
     *     path="/api/v1/profile/involvements",
     *     summary="Update community involvements",
     *     tags={"Positive Athlete Profile"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/UpdateInvolvementsData")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Community involvements updated successfully",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/InvolvementData")
     *         )
     *     )
     * )
     */
    public function updateInvolvements(UpdateInvolvementsData $data, Request $request): array
    {
        return $this->positiveAthleteProfileService->updateInvolvements(
            $request->user(),
            $data->involvements
        );
    }

    /**
     * @OA\Get(
     *     path="/api/v1/profile/work-experiences",
     *     summary="Get work experiences",
     *     tags={"Positive Athlete Profile"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Work experiences retrieved successfully",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/ProfileWorkExperienceData")
     *         )
     *     )
     * )
     */
    public function getWorkExperiences(Request $request): array
    {
        return $this->positiveAthleteProfileService->getWorkExperiences($request->user());
    }

    /**
     * @OA\Put(
     *     path="/api/v1/profile/work-experiences",
     *     summary="Update work experiences",
     *     tags={"Positive Athlete Profile"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/UpdateWorkExperiencesData")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Work experiences updated successfully",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/ProfileWorkExperienceData")
     *         )
     *     )
     * )
     */
    public function updateWorkExperiences(UpdateWorkExperiencesData $data, Request $request): array
    {
        return $this->positiveAthleteProfileService->updateWorkExperiences(
            $request->user(),
            $data->experiences
        );
    }

    /**
     * @OA\Get(
     *     path="/api/v1/profile/sports",
     *     summary="Get user's sports",
     *     tags={"Positive Athlete Profile"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Sports retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/SportsData")
     *     )
     * )
     */
    public function getSports(Request $request): SportsData
    {
        return $this->positiveAthleteProfileService->getSports($request->user());
    }

    /**
     * @OA\Put(
     *     path="/api/v1/profile/sports",
     *     summary="Update user's sports",
     *     tags={"Positive Athlete Profile"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/SportsData")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Sports updated successfully",
     *         @OA\JsonContent(ref="#/components/schemas/SportsData")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\AdditionalProperties(
     *                     type="array",
     *                     @OA\Items(type="string")
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function updateSports(SportsData $data, Request $request): SportsData
    {
        return $this->positiveAthleteProfileService->updateSports($request->user(), $data);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/profile/sports/search",
     *     summary="Search sports",
     *     tags={"Positive Athlete Profile"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="query",
     *         in="query",
     *         description="Search query string",
     *         required=true,
     *         @OA\Schema(type="string", minLength=2)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Sports search results",
     *         @OA\JsonContent(
     *             type="object",
     *             required={"results"},
     *             @OA\Property(
     *                 property="results",
     *                 type="object",
     *                 required={"platform", "custom"},
     *                 @OA\Property(
     *                     property="platform",
     *                     type="array",
     *                     @OA\Items(ref="#/components/schemas/SportResponse")
     *                 ),
     *                 @OA\Property(
     *                     property="custom",
     *                     type="array",
     *                     @OA\Items(ref="#/components/schemas/SportResponse")
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function searchSports(Request $request): SportSearchResponse
    {
        return SportSearchResponse::fromResults(
            ...array_values($this->positiveAthleteProfileService->searchSports(
                $request->user(),
                $request->get('query')
            ))
        );
    }

    /**
     * @OA\Get(
     *     path="/api/v1/profile/recruiter",
     *     summary="Get recruiter status",
     *     tags={"Positive Athlete Profile"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Recruiter status retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/RecruiterStatusData")
     *     )
     * )
     */
    public function getRecruiterStatus(Request $request): RecruiterStatusData
    {
        return RecruiterStatusData::fromUser($request->user());
    }

    /**
     * @OA\Post(
     *     path="/api/v1/profile/recruiter",
     *     summary="Update recruiter status",
     *     tags={"Positive Athlete Profile"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/UpdateRecruiterStatusData")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Recruiter status updated successfully",
     *         @OA\JsonContent(ref="#/components/schemas/RecruiterStatusData")
     *     )
     * )
     */
    public function updateRecruiterStatus(UpdateRecruiterStatusData $data, Request $request): RecruiterStatusData
    {
        $user                    = $request->user();
        $user->recruiter_enabled = $data->enabled;
        $user->save();

        return RecruiterStatusData::fromUser($user);
    }
}
