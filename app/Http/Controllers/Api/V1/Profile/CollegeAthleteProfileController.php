<?php

namespace App\Http\Controllers\Api\V1\Profile;

use App\Data\Profile\CollegeAthleteDetailsData;
use App\Enums\ProfileType;
use App\Http\Controllers\Controller;
use App\Services\CollegeAthleteProfileService;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="College Athlete Profile",
 *     description="Endpoints for managing college athlete profile details"
 * )
 */
class CollegeAthleteProfileController extends Controller
{
    public function __construct(
        private readonly CollegeAthleteProfileService $collegeAthleteProfileService
    ) {}

    /**
     * @OA\Get(
     *     path="/api/v1/profile/college-athlete/details",
     *     summary="Get college athlete profile details",
     *     tags={"College Athlete Profile"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="College athlete profile details retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/CollegeAthleteDetailsData")
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized"
     *     )
     * )
     */
    public function getDetails(Request $request): CollegeAthleteDetailsData
    {
        $user = $request->user();

        return $this->collegeAthleteProfileService->getDetails($user);
    }

    /**
     * @OA\Put(
     *     path="/api/v1/profile/college-athlete/details",
     *     summary="Update college athlete profile details",
     *     tags={"College Athlete Profile"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/CollegeAthleteDetailsData")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="College athlete profile details updated successfully",
     *         @OA\JsonContent(ref="#/components/schemas/CollegeAthleteDetailsData")
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\AdditionalProperties(
     *                     type="array",
     *                     @OA\Items(type="string")
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function updateDetails(Request $request, CollegeAthleteDetailsData $data): CollegeAthleteDetailsData
    {
        $user = $request->user();

        return $this->collegeAthleteProfileService->updateDetails($user, $data);
    }
}
