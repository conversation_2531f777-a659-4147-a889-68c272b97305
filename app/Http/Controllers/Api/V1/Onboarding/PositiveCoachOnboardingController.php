<?php

namespace App\Http\Controllers\Api\V1\Onboarding;

use App\Data\Onboarding\InvolvementDTO;
use App\Data\Onboarding\OnboardingStepResponse;
use App\Data\Onboarding\PositiveCoach\DetailsDTO;
use App\Data\Onboarding\PositiveCoach\TeamSuccessesDTO;
use App\Data\Onboarding\SportsDTO;
use App\Data\Onboarding\StoryDTO;
use App\Data\Onboarding\AccountInfoWithAddressDTO;
use App\Http\Controllers\Controller;
use App\Models\SystemInvite;
use App\Services\Onboarding\PositiveCoachOnboardingService;
use App\States\Onboarding\States\AccountInfo;
use App\States\Onboarding\States\Completed;
use App\States\Onboarding\States\CommunityInvolvement;
use App\States\Onboarding\States\Details;
use App\States\Onboarding\States\Sports;
use App\States\Onboarding\States\Story;
use App\States\Onboarding\States\TeamSuccesses;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="Positive Coach Onboarding",
 *     description="Endpoints for managing the positive coach onboarding flow"
 * )
 */
class PositiveCoachOnboardingController extends Controller
{
    /**
     * @var PositiveCoachOnboardingService
     */
    protected $onboardingService;

    /**
     * Constructor
     */
    public function __construct(PositiveCoachOnboardingService $onboardingService)
    {
        $this->onboardingService = $onboardingService;
    }

    /**
     * @OA\Post(
     *     path="/api/v1/onboarding/positive-coach/intro",
     *     summary="Start the coach onboarding process",
     *     tags={"Positive Coach Onboarding"},
     *     @OA\RequestBody(
     *         required=true,
     *         description="System invite token required to initiate onboarding.",
     *         @OA\JsonContent(ref="#/components/schemas/OnboardingIntroRequest")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Onboarding started successfully, returns first step and prefill data.",
     *         @OA\JsonContent(ref="#/components/schemas/OnboardingStepResponse")
     *     )
     * )
     */
    public function intro(Request $request): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        $onboarding->state->transitionTo(AccountInfo::class);

        /** @var PositiveCoachInviteData $inviteData */
        $inviteData = $invite->invite_data;

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: Details::identifier(),
            prefill: [
                'email' => $invite->email,
                'first_name' => $inviteData->nomination->first_name,
                'last_name' => $inviteData->nomination->last_name,
            ]
        )->toResponse($request);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/onboarding/positive-coach/account-info",
     *     summary="Submit account information",
     *     tags={"Positive Coach Onboarding"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/AccountInfoWithAddressDTO")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Account information submitted successfully",
     *         @OA\JsonContent(ref="#/components/schemas/OnboardingStepResponse")
     *     )
     * )
     */
    public function submitAccountInfo(Request $request, AccountInfoWithAddressDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        // Use the service to handle account info submission
        $this->onboardingService->submitAccountInfo($invite, $data);

        // Transition to the next state
        $onboarding->state->transitionTo(Details::class);

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: Sports::identifier()
        )->toResponse($request);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/onboarding/positive-coach/details",
     *     summary="Submit personal details for the coach",
     *     tags={"Positive Coach Onboarding"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 @OA\Property(property="token", type="string", maxLength=64, minLength=64, description="The 64-character invite token."),
     *                 @OA\Property(property="state", type="string", example="CA", description="2-letter state code"),
     *                 @OA\Property(property="county", type="string", example="Los Angeles"),
     *                 @OA\Property(property="school_id", type="integer", example=123),
     *                 @OA\Property(property="graduation_year", type="string", format="YYYY", example="2026"),
     *                 @OA\Property(property="current_gpa", type="number", format="float", example=3.8),
     *                 @OA\Property(property="current_class_rank", type="string", example="16/402"),
     *                 @OA\Property(property="gender", type="string", example="male"),
     *                 @OA\Property(property="height", type="string", example="5'11&quot;", description="e.g., 5'11&quot;"),
     *                 @OA\Property(property="weight", type="number", format="float", example=160.0, description="Weight in lbs"),
     *                 @OA\Property(property="career_interests", type="array", @OA\Items(type="integer"), description="Array of interest IDs (deprecated, use interests)"),
     *                 @OA\Property(property="interests", type="array", @OA\Items(type="integer"), description="Array of interest IDs"),
     *                 @OA\Property(property="twitter", type="string", example="username"),
     *                 @OA\Property(property="instagram", type="string", example="username"),
     *                 @OA\Property(property="facebook", type="string", example="username"),
     *                 @OA\Property(property="hudl", type="string", example="username"),
     *                 @OA\Property(property="custom_link", type="string", format="url", example="https://example.com"),
     *                 @OA\Property(property="profile_photo", type="string", format="binary", description="Optional profile photo upload (max 5MB).")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Details submitted successfully",
     *         @OA\JsonContent(ref="#/components/schemas/OnboardingStepResponse")
     *     )
     * )
     */
    public function submitDetails(Request $request, DetailsDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        // Use the service to handle details submission
        $this->onboardingService->submitDetails($invite, $data);

        // Transition to the next state
        $onboarding->state->transitionTo(Sports::class);

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: CommunityInvolvement::identifier()
        )->toResponse($request);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/onboarding/positive-coach/sports",
     *     summary="Submit sports that the coach works with",
     *     tags={"Positive Coach Onboarding"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/SportsDTO")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Sports information submitted successfully",
     *         @OA\JsonContent(ref="#/components/schemas/OnboardingStepResponse")
     *     )
     * )
     */
    public function submitSports(Request $request, SportsDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        // Use the service to handle sports submission
        $this->onboardingService->submitSports($invite, $data);

        // Transition to the next state
        $onboarding->state->transitionTo(CommunityInvolvement::class);

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: TeamSuccesses::identifier()
        )->toResponse($request);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/onboarding/positive-coach/community-involvement",
     *     summary="Submit community involvement information",
     *     tags={"Positive Coach Onboarding"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/InvolvementDTO")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Community involvement information submitted successfully",
     *         @OA\JsonContent(ref="#/components/schemas/OnboardingStepResponse")
     *     )
     * )
     */
    public function submitCommunityInvolvement(Request $request, InvolvementDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        // Use the service to handle community involvement submission
        $this->onboardingService->submitCommunityInvolvement($invite, $data);

        // Transition to the next state
        $onboarding->state->transitionTo(TeamSuccesses::class);

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: Story::identifier()
        )->toResponse($request);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/onboarding/positive-coach/team-successes",
     *     summary="Submit team successes information",
     *     tags={"Positive Coach Onboarding"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/TeamSuccessesDTO")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Team successes submitted successfully",
     *         @OA\JsonContent(ref="#/components/schemas/OnboardingStepResponse")
     *     )
     * )
     */
    public function submitTeamSuccesses(Request $request, TeamSuccessesDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        // Use the service to handle team successes submission
        $this->onboardingService->submitTeamSuccesses($invite, $data);

        // Transition to the next state
        $onboarding->state->transitionTo(Story::class);

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: Story::identifier()
        )->toResponse($request);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/onboarding/positive-coach/story",
     *     summary="Submit personal story",
     *     tags={"Positive Coach Onboarding"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/StoryDTO")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Story submitted successfully",
     *         @OA\JsonContent(ref="#/components/schemas/OnboardingStepResponse")
     *     )
     * )
     */
    public function submitStory(Request $request, StoryDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        // Use the service to handle story submission
        $this->onboardingService->submitStory($invite, $data);

        // Do NOT transition to Completed state here
        // Leave the state as Story until the complete endpoint is called
        // $onboarding->state->transitionTo(Completed::class);

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: null
        )->toResponse($request);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/onboarding/positive-coach/complete",
     *     summary="Complete the onboarding process",
     *     tags={"Positive Coach Onboarding"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/AccountInfoWithAddressDTO")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Onboarding completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="redirect", type="string", example="/login")
     *         )
     *     ),
     *     @OA\Response(
     *         response=409,
     *         description="Account already exists",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Account already exists"),
     *             @OA\Property(property="redirect", type="string", example="/login")
     *         )
     *     )
     * )
     */
    public function complete(Request $request, AccountInfoWithAddressDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        try {
            // Use the service to complete onboarding and create a user
            $user = $this->onboardingService->completeOnboarding($invite, $data);

            // If null was returned, user already exists
            if ($user === null) {
                // Mark the onboarding as completed
                $onboarding->state->transitionTo(Completed::class);

                return response()->json([
                    'message' => 'Account already exists',
                    'redirect' => '/login'
                ], 409); // 409 Conflict
            }

            // Mark the onboarding as completed only AFTER successful user creation
            $onboarding->state->transitionTo(Completed::class);

            return response()->json([
                'redirect' => '/login'
            ]);
        } catch (\Exception $e) {
            // Log the error
            \Illuminate\Support\Facades\Log::error('Onboarding completion failed', [
                'onboarding_id' => $onboarding->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return an error response
            return response()->json([
                'message' => 'An error occurred while completing onboarding: ' . $e->getMessage(),
                'error' => true
            ], 500);
        }
    }
}
