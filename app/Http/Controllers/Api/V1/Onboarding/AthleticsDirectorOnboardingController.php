<?php

namespace App\Http\Controllers\Api\V1\Onboarding;

use App\Data\Onboarding\AccountInfoDTO;
use App\Data\Onboarding\AthleticsDirector\BioDTO;
use App\Data\Onboarding\AthleticsDirector\DetailsDTO;
use App\Data\Onboarding\AthleticsDirector\InvolvementDTO;
use App\Data\Onboarding\AthleticsDirector\SchoolSuccessesDTO;
use App\Data\Onboarding\OnboardingStepResponse;
use App\Data\SystemInvites\AthleticsDirectorInviteData;
use App\Http\Controllers\Controller;
use App\Models\SystemInvite;
use App\Services\Onboarding\AthleticsDirectorOnboardingService;
use App\States\Onboarding\States\AccountInfo;
use App\States\Onboarding\States\Bio;
use App\States\Onboarding\States\Details;
use App\States\Onboarding\States\CommunityInvolvement;
use App\States\Onboarding\States\SchoolSuccesses;
use App\States\Onboarding\States\Completed;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * @OA\Tag(
 *     name="Athletics Director Onboarding",
 *     description="Endpoints for managing the athletics director onboarding flow"
 * )
 */
class AthleticsDirectorOnboardingController extends Controller
{
    public function __construct(
        private readonly AthleticsDirectorOnboardingService $onboardingService
    ) {}

    /**
     * @OA\Post(
     *     path="/api/v1/onboarding/athletics-director/intro",
     *     summary="Start the athletics director onboarding process using an invite token",
     *     tags={"Athletics Director Onboarding"},
     *     @OA\RequestBody(
     *         required=true,
     *         description="System invite token required to initiate onboarding.",
     *         @OA\JsonContent(ref="#/components/schemas/OnboardingIntroRequest")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Onboarding started successfully, returns first step and prefill data.",
     *         @OA\JsonContent(ref="#/components/schemas/OnboardingStepResponse")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Invalid or expired invitation token."
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error (e.g., token missing or incorrect format)."
     *     )
     * )
     */
    public function intro(Request $request): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        $onboarding->state->transitionTo(AccountInfo::class);

        /** @var AthleticsDirectorInviteData $inviteData */
        $inviteData = $invite->invite_data;

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: AccountInfo::identifier(),
            prefill: [
                'email' => $invite->email,
                'first_name' => $inviteData->first_name,
                'last_name' => $inviteData->last_name,
            ]
        )->toResponse($request);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/onboarding/athletics-director/account-info",
     *     summary="Submit account information for athletics director",
     *     tags={"Athletics Director Onboarding"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/AccountInfoDTO")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Account information submitted successfully",
     *         @OA\JsonContent(ref="#/components/schemas/OnboardingStepResponse")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error in submitted data"
     *     )
     * )
     */
    public function submitAccountInfo(Request $request, AccountInfoDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        $onboarding->updateStepData(AccountInfo::identifier(), $data->toArray());
        $onboarding->state->transitionTo(Details::class);

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: Details::identifier()
        )->toResponse($request);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/onboarding/athletics-director/details",
     *     summary="Submit personal details for athletics director",
     *     tags={"Athletics Director Onboarding"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(ref="#/components/schemas/AthleticsDirectorDetailsDTO")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Details submitted successfully",
     *         @OA\JsonContent(ref="#/components/schemas/OnboardingStepResponse")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error in submitted data"
     *     )
     * )
     */
    public function submitDetails(Request $request, DetailsDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        // Use the service to handle details submission
        $stepData = $this->onboardingService->processDetails($invite, $data);

        // Store step data and transition to next state
        $onboarding->updateStepData(Details::identifier(), $stepData);
        $onboarding->state->transitionTo(Bio::class);

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: Bio::identifier()
        )->toResponse($request);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/onboarding/athletics-director/bio",
     *     summary="Submit biographical information for athletics director",
     *     tags={"Athletics Director Onboarding"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/AthleticsDirectorBioDTO")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Bio submitted successfully",
     *         @OA\JsonContent(ref="#/components/schemas/OnboardingStepResponse")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error in submitted data"
     *     )
     * )
     */
    public function submitBio(Request $request, BioDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        $onboarding->updateStepData(Bio::identifier(), $data->toArray());
        $onboarding->state->transitionTo(CommunityInvolvement::class);

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: CommunityInvolvement::identifier()
        )->toResponse($request);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/onboarding/athletics-director/community-involvement",
     *     summary="Submit community involvement information for athletics director",
     *     tags={"Athletics Director Onboarding"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/AthleticsDirectorInvolvementDTO")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Community involvement information submitted successfully",
     *         @OA\JsonContent(ref="#/components/schemas/OnboardingStepResponse")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error in submitted data"
     *     )
     * )
     */
    public function submitCommunityInvolvement(Request $request, InvolvementDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        $onboarding->updateStepData(CommunityInvolvement::identifier(), $data->toArray());
        $onboarding->state->transitionTo(SchoolSuccesses::class);

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: SchoolSuccesses::identifier()
        )->toResponse($request);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/onboarding/athletics-director/school-successes",
     *     summary="Submit school successes information for athletics director",
     *     tags={"Athletics Director Onboarding"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/AthleticsDirectorSchoolSuccessesDTO")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="School successes information submitted successfully",
     *         @OA\JsonContent(ref="#/components/schemas/OnboardingStepResponse")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error in submitted data"
     *     )
     * )
     */
    public function submitSchoolSuccesses(Request $request, SchoolSuccessesDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        $onboarding->updateStepData(SchoolSuccesses::identifier(), $data->toArray());

        // Do NOT transition to Completed state here
        // Leave the state as SchoolSuccesses until the complete endpoint is called

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: null
        )->toResponse($request);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/onboarding/athletics-director/complete",
     *     summary="Complete the athletics director onboarding process",
     *     tags={"Athletics Director Onboarding"},
     *     @OA\Response(
     *         response=200,
     *         description="Onboarding completed successfully",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="current_step", type="string", example="completed"),
     *             @OA\Property(property="next_step", type="string", nullable=true),
     *             @OA\Property(property="redirect", type="string", example="/login")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Server error",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="An error occurred while completing onboarding"),
     *             @OA\Property(property="error", type="boolean", example=true)
     *         )
     *     )
     * )
     */
    public function complete(Request $request): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        try {
            // Create user and complete onboarding using specialized service
            $user = $this->onboardingService->completeOnboarding($invite);

            // Final state transition
            $onboarding->state->transitionTo(Completed::class);

            // Mark the invite as completed
            $invite->markAsCompleted();

            return OnboardingStepResponse::fromState(
                currentStep: $onboarding->state->getIdentifier(),
                nextStep: null,
                redirect: '/login'
            )->toResponse($request);
        } catch (\Exception $e) {
            // Log the error
            Log::error('Athletics Director onboarding completion failed', [
                'onboarding_id' => $onboarding->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return an error response
            return response()->json([
                'message' => 'An error occurred while completing onboarding: ' . $e->getMessage(),
                'error' => true
            ], 500);
        }
    }
}
