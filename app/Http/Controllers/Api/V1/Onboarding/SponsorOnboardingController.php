<?php

namespace App\Http\Controllers\Api\V1\Onboarding;

use App\Data\Onboarding\Sponsor\AccountInfoDTO;
use App\Data\Onboarding\Sponsor\OnboardingStepResponse;
use App\Enums\ProfileType;
use App\Http\Controllers\Controller;
use App\Models\Onboarding;
use App\Models\SystemInvite;
use App\Models\User;
use App\Services\Onboarding\SponsorOnboardingService;
use App\States\Onboarding\States\AccountInfo;
use App\States\Onboarding\States\Completed;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * @OA\Tag(
 *     name="Sponsor Onboarding",
 *     description="API Endpoints for sponsor onboarding process"
 * )
 */
class SponsorOnboardingController extends Controller
{
    public function __construct(
        private readonly SponsorOnboardingService $sponsorOnboardingService
    ) {}

    /**
     * Process the account info step of onboarding and complete the process
     *
     * @OA\Post(
     *     path="/api/v1/onboarding/sponsor/account-info",
     *     summary="Submit account information and complete onboarding",
     *     description="Submit personal account information for sponsor onboarding and complete the process",
     *     tags={"Sponsor Onboarding"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/SponsorAccountInfoDTO")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Success",
     *         @OA\JsonContent(ref="#/components/schemas/SponsorOnboardingStepResponse")
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Created",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="user_id", type="integer", example=123),
     *             @OA\Property(property="redirect", type="string", example="/login")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Not Found",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation Error",
     *         @OA\JsonContent(ref="#/components/schemas/ValidationError")
     *     ),
     *     @OA\Response(
     *         response=409,
     *         description="Conflict - User already exists",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="redirect", type="string", example="/login")
     *         )
     *     )
     * )
     */
    public function accountInfo(AccountInfoDTO $data, Request $request): JsonResponse
    {
        $invite = $request->system_invite;

        $onboarding = $invite->onboarding ?? Onboarding::query()->create([
            'system_invite_id' => $invite->id,
            'profile_type' => ProfileType::SPONSOR,
            'state' => AccountInfo::class,
            'data' => [
                'token' => $invite->token,
                'organization_id' => $invite->invite_data->organization_id, // Store organization ID from invite
                'organization_name' => $invite->invite_data->organization_name ?? null, // Store organization name if available
            ],
        ]);

        // Let the service handle the step data update (but not state transition yet)
        $onboarding = $this->sponsorOnboardingService->saveAccountInfo($onboarding, $data);

        try {
            // Check for existing user before starting transaction
            $accountData = $onboarding->getStepData(AccountInfo::identifier());
            $existingUser = User::query()->where('email', $accountData['email'])->first();

            if ($existingUser) {
                // For existing users, transition to completed state
                $onboarding->state->transitionTo(Completed::class);

                // Mark the invite as completed since the user exists
                $invite->markAsCompleted();

                // Return a 409 Conflict to indicate user already exists
                return response()->json([
                    'redirect' => '/login',
                ], 409); // 409 Conflict
            }

            // Use transaction with Laravel standard approach
            $user = DB::transaction(function() use ($onboarding, $invite) {
                // Create the user and organization association
                $user = $this->sponsorOnboardingService->completeOnboarding($onboarding);

                // Mark the invite as completed
                $invite->markAsCompleted();

                // Now transition to completed state after successful user creation
                $onboarding->state->transitionTo(Completed::class);

                return $user;
            }, 5); // 5 retry attempts

            // Successful creation of new user
            return response()->json([
                'redirect' => '/login',
                'user_id' => $user->id,
            ], 201); // 201 Created

        } catch (\Exception $e) {
            Log::error('Sponsor onboarding completion failed', [
                'error' => $e->getMessage(),
                'onboarding_id' => $onboarding->id,
            ]);

            return response()->json([
                'error' => 'An error occurred during onboarding completion'
            ], 500);
        }
    }

    /**
     * Get the next step for the onboarding process
     */
    private function getNextStep(string $currentStep): ?string
    {
        return match ($currentStep) {
            'account_info' => null, // No next steps since we auto-complete
            'completed' => null,
            default => null,
        };
    }
}
