<?php

namespace App\Http\Controllers\Api\V1\Onboarding;

use App\Data\TeamStudent\TeamStudentAccountInfoDTO;
use App\Data\Onboarding\OnboardingStepResponse;
use App\Http\Controllers\Controller;
use App\Models\SystemInvite;
use App\States\Onboarding\States\AccountInfo;
use App\States\Onboarding\States\Completed;
use App\States\Onboarding\States\Intro;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class TeamStudentOnboardingController extends Controller
{
    public function welcome(Request $request): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        $onboarding->state->transitionTo(Intro::class);

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: AccountInfo::identifier(),
            prefill: [
                'email' => $invite->email,
            ]
        )->toResponse($request);
    }

    public function submitAccountInfo(Request $request, TeamStudentAccountInfoDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        $onboarding->updateStepData(AccountInfo::identifier(), $data->toArray());

        $onboarding->state->transitionTo(Completed::class);

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: null
        )->toResponse($request);
    }
}
