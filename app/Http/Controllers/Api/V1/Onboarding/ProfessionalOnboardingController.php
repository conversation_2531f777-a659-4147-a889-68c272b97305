<?php

namespace App\Http\Controllers\Api\V1\Onboarding;

use App\Data\Onboarding\ProfessionalDetailsDTO;
use App\Data\Onboarding\OnboardingStepResponse;
use App\Data\Onboarding\AccountInfoWithAddressDTO;
use App\Http\Controllers\Controller;
use App\Models\SystemInvite;
use App\States\Onboarding\States\AccountInfo;
use App\States\Onboarding\States\Details;
use App\States\Onboarding\States\Completed;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ProfessionalOnboardingController extends Controller
{
    public function intro(Request $request): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        $onboarding->state->transitionTo(AccountInfo::class);

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: AccountInfo::identifier(),
            prefill: [
                'email' => $invite->email,
            ]
        )->toResponse($request);
    }

    public function submitAccountInfo(Request $request, AccountInfoWithAddressDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        $onboarding->updateStepData(AccountInfo::identifier(), $data->toArray());
        $onboarding->state->transitionTo(Details::class);

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: Details::identifier()
        )->toResponse($request);
    }

    public function submitDetails(Request $request, ProfessionalDetailsDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        $onboarding->updateStepData(Details::identifier(), $data->toArray());
        $onboarding->state->transitionTo(Completed::class);

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: null
        )->toResponse($request);
    }
}
