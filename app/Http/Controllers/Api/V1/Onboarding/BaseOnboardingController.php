<?php

namespace App\Http\Controllers\Api\V1\Onboarding;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

abstract class BaseOnboardingController extends Controller
{
    protected User $user;

    public function __construct(Request $request)
    {
        $this->user = $request->user();
    }

    /**
     * Save onboarding progress
     */
    protected function saveProgress(int $step): void
    {
        $this->user->onboarding_step = $step;
        $this->user->save();
    }

    /**
     * Check if user can access this step
     */
    protected function canAccessStep(int $step): bool
    {
        // Allow moving to next step or revisiting previous steps
        return $step <= ($this->user->onboarding_step + 1);
    }

    /**
     * Get validation rules for a step
     */
    abstract protected function getValidationRules(int $step): array;

    /**
     * Handle the step update
     */
    abstract protected function handleStep(Request $request, int $step): JsonResponse;
}
