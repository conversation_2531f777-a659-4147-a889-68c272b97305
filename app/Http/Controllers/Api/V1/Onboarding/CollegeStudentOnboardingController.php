<?php

namespace App\Http\Controllers\Api\V1\Onboarding;

use App\Data\Onboarding\AccountInfoWithAddressDTO;
use App\Data\Onboarding\CollegeInfoDTO;
use App\Data\Onboarding\NextStepsDTO;
use App\Data\Onboarding\OnboardingStepResponse;
use App\Http\Controllers\Controller;
use App\Models\SystemInvite;
use App\States\Onboarding\States\AccountInfo;
use App\States\Onboarding\States\CollegeInfo;
use App\States\Onboarding\States\Completed;
use App\States\Onboarding\States\NextSteps;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;


class CollegeStudentOnboardingController extends Controller
{

    public function intro(Request $request): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        $onboarding->state->transitionTo(NextSteps::class);

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: AccountInfo::identifier(),
            prefill: [
                'email' => $invite->email,
            ]
        )->toResponse($request);
    }

    public function submitNextSteps(Request $request, NextStepsDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        $onboarding->updateStepData(NextSteps::identifier(), $data->toArray());
        $onboarding->state->transitionTo(AccountInfo::class);

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: CollegeInfo::identifier()
        )->toResponse($request);
    }

    public function submitAccountInfo(Request $request, AccountInfoWithAddressDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        $onboarding->updateStepData(AccountInfo::identifier(), $data->toArray());
        $onboarding->state->transitionTo(CollegeInfo::class);

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: Completed::identifier()
        )->toResponse($request);
    }

    public function submitCollegeInfo(Request $request, CollegeInfoDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        $onboarding->updateStepData(CollegeInfo::identifier(), $data->toArray());
        $onboarding->state->transitionTo(Completed::class);

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: null
        )->toResponse($request);
    }
}
