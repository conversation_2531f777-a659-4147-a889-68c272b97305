<?php

namespace App\Http\Controllers\Api\V1\Onboarding;

use App\Data\Parent\ParentAccountInfoDTO;
use App\Data\Onboarding\OnboardingStepResponse;
use App\Http\Controllers\Controller;
use App\Models\Onboarding;
use App\Models\SystemInvite;
use App\Models\User;
use App\Services\Onboarding\ParentOnboardingService;
use App\States\Onboarding\States\AccountInfo;
use App\States\Onboarding\States\Completed;
use App\Enums\ProfileType;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * @OA\Tag(
 *     name="Parent Onboarding",
 *     description="API Endpoints for parent onboarding process"
 * )
 */
class ParentOnboardingController extends Controller
{
    public function __construct(
        private readonly ParentOnboardingService $parentOnboardingService
    ) {}

/**
     * @OA\Post(
     *     path="/api/v1/onboarding/parent/intro",
     *     summary="Start the onboarding process using an invite token",
     *     tags={"Positive Athlete Onboarding"},
     *     @OA\RequestBody(
     *         required=true,
     *         description="System invite token required to initiate onboarding.",
     *         @OA\JsonContent(ref="#/components/schemas/OnboardingIntroRequest")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Onboarding started successfully, returns first step and prefill data.",
     *         @OA\JsonContent(ref="#/components/schemas/OnboardingStepResponse")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Invalid or expired invitation token."
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error (e.g., token missing or incorrect format)."
     *     )
     * )
     */
    public function intro(Request $request): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;

        $onboarding = $invite->onboarding ?? Onboarding::query()->create([
            'system_invite_id' => $invite->id,
            'profile_type' => ProfileType::PARENT,
            'state' => AccountInfo::class,
            'data' => [
                'token' => $invite->token,
            ],
        ]);

        $onboarding->state->transitionTo(AccountInfo::class);

        return OnboardingStepResponse::fromState(
            $onboarding->state->getIdentifier(),
            AccountInfo::identifier(),
            [
                'email' => $invite->email,
            ]
        )->toResponse($request);
    }

    /**
     * Process the account info step of onboarding and complete the process
     *
     * @OA\Post(
     *     path="/api/v1/onboarding/parent/account-info",
     *     summary="Submit account information and complete onboarding",
     *     description="Submit personal account information for parent onboarding and complete the process",
     *     tags={"Parent Onboarding"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/ParentAccountInfoDTO")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Success",
     *         @OA\JsonContent(ref="#/components/schemas/OnboardingStepResponse")
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Created",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="user_id", type="integer", example=123),
     *             @OA\Property(property="redirect", type="string", example="/login")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Not Found",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation Error",
     *         @OA\JsonContent(ref="#/components/schemas/ValidationError")
     *     ),
     *     @OA\Response(
     *         response=409,
     *         description="Conflict - User already exists",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="redirect", type="string", example="/login")
     *         )
     *     )
     * )
     */
    public function submitAccountInfo(Request $request, ParentAccountInfoDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        // Let the service handle the step data update (but not state transition yet)
        $onboarding = $this->parentOnboardingService->saveAccountInfo($onboarding, $data);

        try {
            // Check for existing user before starting transaction
            $accountData = $onboarding->getStepData(AccountInfo::identifier());
            $existingUser = User::query()->where('email', $accountData['email'])->first();

            if ($existingUser) {
                // For existing users, transition to completed state
                $onboarding->state->transitionTo(Completed::class);

                // Mark the invite as completed since the user exists
                $invite->markAsCompleted();

                // Return a 409 Conflict to indicate user already exists
                return response()->json([
                    'redirect' => '/login',
                ], 409); // 409 Conflict
            }

            // Use transaction with Laravel standard approach
            $user = DB::transaction(function() use ($onboarding, $invite) {
                // Create the user and establish the parent relationship
                $user = $this->parentOnboardingService->completeOnboarding($onboarding);

                // Mark the invite as completed
                $invite->markAsCompleted();

                // Now transition to completed state after successful user creation
                $onboarding->state->transitionTo(Completed::class);

                return $user;
            }, 5); // 5 retry attempts

            // Successful creation of new user
            return response()->json([
                'redirect' => '/login',
                'user_id' => $user->id,
            ], 201); // 201 Created

        } catch (\Exception $e) {
            Log::error('Parent onboarding completion failed', [
                'error' => $e->getMessage(),
                'onboarding_id' => $onboarding->id,
            ]);

            return response()->json([
                'error' => 'An error occurred during onboarding completion'
            ], 500);
        }
    }
}
