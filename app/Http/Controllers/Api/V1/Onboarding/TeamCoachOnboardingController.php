<?php

namespace App\Http\Controllers\Api\V1\Onboarding;

use App\Actions\Onboarding\CompleteOnboardingAction;
use App\Data\Onboarding\AccountInfoDTO;
use App\Data\Onboarding\OnboardingStepResponse;
use App\Data\Onboarding\SportsDTO;
use App\Data\Onboarding\TeamCoach\BuildTeamDTO;
use App\Data\Onboarding\TeamCoach\DetailsDTO;
use App\Http\Controllers\Controller;
use App\Models\SystemInvite;
use App\Services\TeamService;
use App\States\Onboarding\States\AccountInfo;
use App\States\Onboarding\States\BuildTeam;
use App\States\Onboarding\States\Completed;
use App\States\Onboarding\States\Details;
use App\States\Onboarding\States\Intro;
use App\States\Onboarding\States\Sports;
use App\States\Onboarding\States\TeamReview;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class TeamCoachOnboardingController extends Controller
{
    public function __construct(
        private readonly TeamService $teamService,
        private readonly CompleteOnboardingAction $completeOnboarding
    ) {}

    public function intro(Request $request): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        $onboarding->state->transitionTo(AccountInfo::class);

        /** @var TeamCoachInviteData $inviteData */
        $inviteData = $invite->invite_data;

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: AccountInfo::identifier(),
            prefill: [
                'email' => $invite->email,
                'first_name' => $inviteData->first_name,
                'last_name' => $inviteData->last_name,
            ]
        )->toResponse($request);
    }

    public function submitAccountInfo(Request $request, AccountInfoDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        $onboarding->updateStepData(AccountInfo::identifier(), $data->toArray());
        $onboarding->state->transitionTo(Details::class);

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: Details::identifier()
        )->toResponse($request);
    }

    public function submitDetails(Request $request, DetailsDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        $onboarding->updateStepData(Details::identifier(), $data->toArray());
        $onboarding->state->transitionTo(Sports::class);

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: Sports::identifier()
        )->toResponse($request);
    }

    public function submitSports(Request $request, SportsDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        $onboarding->updateStepData(Sports::identifier(), $data->toArray());
        $onboarding->state->transitionTo(BuildTeam::class);

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: BuildTeam::identifier()
        )->toResponse($request);
    }

    public function buildTeam(Request $request, BuildTeamDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        // Store team data for later creation
        $onboarding->updateStepData(BuildTeam::identifier(), [
            'team' => json_encode($data->team->toArray()),
            'students' => json_encode(array_map(fn ($student) => $student->toArray(), $data->students))
        ]);

        $onboarding->state->transitionTo(TeamReview::class);

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: TeamReview::identifier()
        )->toResponse($request);
    }

    public function reviewTeam(Request $request): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboarding = $invite->onboarding;

        // Create user and complete onboarding
        $user = $this->completeOnboarding->execute($onboarding);

        // Now create team and send invites
        $teamData = $onboarding->getStepData(BuildTeam::identifier());
        $team = $this->teamService->createTeam(
            json_decode($teamData['team'], true),
            $user
        );

        if (!empty($teamData['students'])) {
            $this->teamService->inviteStudents(
                $team,
                json_decode($teamData['students'], true)
            );
        }

        $onboarding->state->transitionTo(Completed::class);

        return OnboardingStepResponse::fromState(
            currentStep: $onboarding->state->getIdentifier(),
            nextStep: null
        )->toResponse($request);
    }
}
