<?php

namespace App\Http\Controllers\Api\V1\Onboarding;

use App\Data\Onboarding\Alumni\AccountInfoDTO;
use App\Data\Onboarding\Alumni\CollegeDetailsDTO;
use App\Data\Onboarding\Alumni\LifeStageSelectionDTO;
use App\Data\Onboarding\Alumni\ProfessionalDetailsDTO;
use App\Data\Onboarding\OnboardingStepResponse;
use App\Http\Controllers\Controller;
use App\Models\SystemInvite;
use App\Services\Onboarding\AlumniOnboardingService;
use App\States\Onboarding\States\AccountInfo;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * @OA\Tag(
 *     name="Alumni Onboarding",
 *     description="API endpoints for handling alumni onboarding process"
 * )
 */
class AlumniOnboardingController extends Controller
{
    protected AlumniOnboardingService $onboardingService;

    public function __construct(AlumniOnboardingService $onboardingService)
    {
        $this->onboardingService = $onboardingService;
    }

    /**
     * @OA\Post(
     *     path="/api/v1/onboarding/alumni/life-stage",
     *     operationId="submitLifeStage",
     *     summary="Submit life stage selection for alumni onboarding",
     *     description="Stores the selected life stage and determines the intended profile type",
     *     tags={"Alumni Onboarding"},
     *
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/AlumniLifeStageSelectionDTO")
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Life stage selection submitted successfully",
     *         @OA\JsonContent(ref="#/components/schemas/OnboardingStepResponse")
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="The given data was invalid."),
     *             @OA\Property(property="errors", type="object")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="System invite not found"
     *     )
     * )
     *
     * Submit the life stage selection for alumni onboarding
     *
     * @param Request $request
     * @param LifeStageSelectionDTO $data
     * @return JsonResponse
     */
    public function submitLifeStage(Request $request, LifeStageSelectionDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;

        $this->onboardingService->storeLifeStageSelection($invite, $data);

        // Determine next step based on intended_profile_type
        $nextStep = $data->intended_profile_type === 'college_athlete' ? 'college_details' : 'professional_details';

        return OnboardingStepResponse::fromState(
            currentStep: 'account_info',
            nextStep: $nextStep,
            prefill: $this->onboardingService->getPrefillData($invite)
        )->toResponse($request);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/onboarding/alumni/account-info",
     *     operationId="submitAccountInfo",
     *     summary="Submit account information for alumni onboarding",
     *     description="Stores personal account information and determines the next appropriate step based on life stage",
     *     tags={"Alumni Onboarding"},
     *
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/AlumniAccountInfoDTO")
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Account information submitted successfully",
     *         @OA\JsonContent(ref="#/components/schemas/OnboardingStepResponse")
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Validation error"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="System invite not found"
     *     )
     * )
     *
     * Submit account information for alumni onboarding
     *
     * @param Request $request
     * @param AccountInfoDTO $data
     * @return JsonResponse
     */
    public function submitAccountInfo(Request $request, AccountInfoDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;

        $this->onboardingService->storeAccountInfo($invite, $data);

        // Get the intended profile type from the stored data
        $onboardingData = $invite->onboarding->data;
        $intendedProfileType = $onboardingData['intended_profile_type'] ?? null;

        // Set current step based on the intended profile type
        $currentStep = $intendedProfileType === 'college_athlete' ? 'college_details' : 'professional_details';

        return OnboardingStepResponse::fromState(
            currentStep: $currentStep,
            nextStep: 'completed',
            prefill: $this->onboardingService->getPrefillData($invite)
        )->toResponse($request);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/onboarding/alumni/college-details",
     *     operationId="submitCollegeDetails",
     *     summary="Submit college details for alumni onboarding",
     *     description="Stores college-related information for alumni with college experience",
     *     tags={"Alumni Onboarding"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(ref="#/components/schemas/AlumniCollegeDetailsDTO")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="College details submitted successfully",
     *         @OA\JsonContent(ref="#/components/schemas/OnboardingStepResponse")
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Validation error"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="System invite not found"
     *     )
     * )
     *
     * Submit college details for alumni with college experience
     *
     * @param Request $request
     * @param CollegeDetailsDTO $data
     * @return JsonResponse
     */
    public function submitCollegeDetails(Request $request, CollegeDetailsDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;

        $this->onboardingService->storeCollegeDetails($invite, $data);

        return OnboardingStepResponse::fromState(
            currentStep: 'completed',
            nextStep: null,
            prefill: $this->onboardingService->getPrefillData($invite)
        )->toResponse($request);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/onboarding/alumni/professional-details",
     *     operationId="submitProfessionalDetails",
     *     summary="Submit professional details for alumni onboarding",
     *     description="Stores professional experience information for alumni in professional settings",
     *     tags={"Alumni Onboarding"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(ref="#/components/schemas/AlumniProfessionalDetailsDTO")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Professional details submitted successfully",
     *         @OA\JsonContent(ref="#/components/schemas/OnboardingStepResponse")
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Validation error"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="System invite not found"
     *     )
     * )
     *
     * Submit professional details for alumni in professional settings
     *
     * @param Request $request
     * @param ProfessionalDetailsDTO $data
     * @return JsonResponse
     */
    public function submitProfessionalDetails(Request $request, ProfessionalDetailsDTO $data): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;

        $this->onboardingService->storeProfessionalDetails($invite, $data);

        return OnboardingStepResponse::fromState(
            currentStep: 'completed',
            nextStep: null,
            prefill: $this->onboardingService->getPrefillData($invite)
        )->toResponse($request);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/onboarding/alumni/next-step",
     *     operationId="getNextStep",
     *     summary="Get the next step in the alumni onboarding process",
     *     description="Returns the current step and prefill data for the onboarding process",
     *     tags={"Alumni Onboarding"},
     *     @OA\Parameter(
     *         name="token",
     *         in="path",
     *         required=true,
     *         description="System invite token",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(ref="#/components/schemas/OnboardingStepResponse")
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="System invite not found"
     *     )
     * )
     *
     * Get the next step in the onboarding process
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getNextStep(Request $request): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;
        $onboardingData = $invite->onboarding->data ?? [];

        return OnboardingStepResponse::fromState(
            currentStep: $onboardingData['current_step'] ?? 'life_stage_selection',
            prefill: $this->onboardingService->getPrefillData($invite)
        )->toResponse($request);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/onboarding/alumni/complete",
     *     operationId="completeAlumniOnboarding",
     *     summary="Complete the alumni onboarding process",
     *     description="Finalizes the onboarding process, creates a user account, and redirects to login",
     *     tags={"Alumni Onboarding"},
     *     @OA\Parameter(
     *         name="token",
     *         in="path",
     *         required=true,
     *         description="System invite token",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Onboarding completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="redirect", type="string", example="/login")
     *         )
     *     ),
     *     @OA\Response(
     *         response=409,
     *         description="Account already exists",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Account already exists"),
     *             @OA\Property(property="redirect", type="string", example="/login")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Server error",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="An error occurred while completing onboarding"),
     *             @OA\Property(property="error", type="string"),
     *             @OA\Property(property="file", type="string"),
     *             @OA\Property(property="line", type="integer")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="System invite not found"
     *     )
     * )
     *
     * Complete the onboarding process
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function complete(Request $request): JsonResponse
    {
        /** @var SystemInvite $invite */
        $invite = $request->system_invite;

        try {
            $user = $this->onboardingService->completeOnboarding($invite);

            if ($user === null) {
                return response()->json([
                    'message' => 'Account already exists',
                    'redirect' => '/login'
                ], 409);
            }

            // Update the onboarding data
            $currentData = $invite->onboarding->data;
            $currentData['is_complete'] = true;
            $currentData['completed_at'] = now()->toISOString();
            $invite->onboarding->update(['data' => $currentData]);

            return response()->json([
                'redirect' => '/login',
            ]);
        } catch (\Exception $e) {
            Log::error('Alumni onboarding completion failed', [
                'onboarding_id' => $invite->onboarding->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return more detailed error for debugging
            return response()->json([
                'message' => 'An error occurred while completing onboarding',
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ], 500);
        }
    }
}
