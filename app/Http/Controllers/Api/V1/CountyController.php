<?php

namespace App\Http\Controllers\Api\V1;

use App\Data\Location\CountyData;
use App\Data\Location\CountyDetailsData;
use App\Http\Controllers\Controller;
use App\Models\County;
use Illuminate\Http\Request;
use Spatie\LaravelData\DataCollection;

/**
 * @OA\Tag(
 *     name="Counties",
 *     description="API Endpoints for county information"
 * )
 */
class CountyController extends Controller
{
    /**
     * Search counties by name and state
     *
     * @OA\Get(
     *     path="/api/v1/counties/search",
     *     summary="Search counties by name and state",
     *     description="Returns a list of counties matching the search criteria",
     *     tags={"Counties"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="query",
     *         in="query",
     *         required=true,
     *         description="Search query for county name",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="state",
     *         in="query",
     *         required=false,
     *         description="State code to filter counties",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="List of counties matching search criteria",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/CountyData")
     *         )
     *     )
     * )
     */
    public function search(Request $request): DataCollection
    {
        $query = $request->get('query');
        $state = $request->get('state');

        if (!$query) {
            return new DataCollection(CountyData::class, []);
        }

        $counties = County::query()
            ->when($state, function ($query, $state) {
                return $query->where('state_code', $state);
            })
            ->where('name', 'ilike', "%{$query}%")
            ->with('state') // Eager load state relationship
            ->limit(10)
            ->get();

        return new DataCollection(
            CountyData::class,
            $counties->map(fn ($county) => CountyData::from($county))
        );
    }

    /**
     * Get detailed county information including regional hierarchy
     *
     * @OA\Get(
     *     path="/api/v1/counties/{county}",
     *     summary="Get detailed county information",
     *     description="Returns detailed information about a specific county including regional hierarchy",
     *     tags={"Counties"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="county",
     *         in="path",
     *         required=true,
     *         description="County identifier",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Detailed county information",
     *         @OA\JsonContent(ref="#/components/schemas/CountyDetailsData")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="County not found",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     )
     * )
     */
    public function show(County $county): CountyDetailsData
    {
        return CountyDetailsData::from(
            $county->load(['market.region', 'subRegion', 'state'])
        );
    }
}
