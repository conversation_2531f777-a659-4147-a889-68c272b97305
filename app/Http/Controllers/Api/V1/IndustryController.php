<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\Industry;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use OpenApi\Annotations as OA;

/**
 * @OA\Tag(
 *     name="Industries",
 *     description="API Endpoints for industry-related operations"
 * )
 */


 /**
 * @OA\Schema(
 *     schema="IndustryData",
 *     title="Industry Data",
 *     description="Industry data representation",
 *     @OA\Property(property="id", type="integer", description="The unique identifier of the industry"),
 *     @OA\Property(property="name", type="string", description="The name of the industry"),
 *     @OA\Property(property="icon", type="string", description="The icon of the industry"),
 * )
 */
class IndustryController extends Controller
{
    /**
     * Search for industries
     *
     * @OA\Get(
     *     path="/api/industries/search",
     *     operationId="searchIndustries",
     *     tags={"Industries"},
     *     summary="Search for industries by name",
     *     description="Returns a list of industries matching the search query. This is a public endpoint that does not require authentication.",
     *     @OA\Parameter(
     *         name="query",
     *         in="query",
     *         description="Search term for industry names",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", format="int64", example=1),
     *                 @OA\Property(property="name", type="string", example="Technology")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(ref="#/components/schemas/ValidationError")
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Server error",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     )
     * )
     */
    public function search(Request $request): JsonResponse
    {
        $query = $request->get('query', '');

        $industries = Industry::query()
            ->where('name', 'ilike', "%{$query}%")
            ->orderBy('name')
            ->limit(10)
            ->get(['id', 'name']);

        return response()->json($industries);
    }
}
