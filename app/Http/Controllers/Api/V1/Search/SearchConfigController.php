<?php

namespace App\Http\Controllers\Api\V1\Search;

use App\Http\Controllers\Controller;
use App\Services\Meilisearch\Facades\Meili;
use Illuminate\Http\JsonResponse;

/**
 * @OA\PathItem(path="/api/v1/search/config/{index}/filterable-attributes")
 */
class SearchConfigController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/v1/search/config/{index}/filterable-attributes",
     *     operationId="getFilterableAttributes",
     *     summary="Get filterable attributes for a search index",
     *     tags={"Search Configuration"},
     *     @OA\Parameter(
     *         name="index",
     *         in="path",
     *         required=true,
     *         description="The name of the search index",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful response with filterable attributes",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(type="string")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Index not found",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="error", type="string", example="Index not found")
     *         )
     *     ),
     *     @OA\Response(response=401, description="Unauthenticated")
     * )
     */
    public function filterableAttributes(string $index): JsonResponse
    {
        $client = Meili::client();
        $index = $client->index($index);

        if (!$index) {
            return response()->json(['error' => 'Index not found'], 422);
        }

        return response()->json($index->getFilterableAttributes());
    }
}
