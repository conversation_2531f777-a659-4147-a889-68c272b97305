<?php

namespace App\Http\Controllers\Api\V1;

use App\Data\Opportunity\BookmarkStatusData;
use App\Data\Opportunity\BookmarkedIdsData;
use App\Http\Controllers\Controller;
use App\Http\Resources\OpportunityResource;
use App\Models\Opportunity;
use App\Models\User;
use App\Services\OpportunityService;
use App\Services\ParentChildResolverService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class OpportunityController extends Controller
{
    public function __construct(
        protected OpportunityService $opportunityService,
        protected ParentChildResolverService $parentChildResolver
    ) {}

    /**
     * Get a single opportunity
     *
     * @OA\Get(
     *     path="/api/v1/opportunities/{id}",
     *     operationId="getOpportunityById",
     *     tags={"Opportunities"},
     *     summary="Get a single opportunity by its ID",
     *     description="Returns detailed information about a specific opportunity, including related interests and organization data if available. Note: The 'industries' field is deprecated and will be removed in a future version - use 'interests' instead.",
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="ID of the opportunity to retrieve",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(ref="#/components/schemas/OpportunityResource")
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - User doesn't have access to this opportunity"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Opportunity not found or parent has no linked children"
     *     )
     * )
     *
     * @param string $id
     * @return OpportunityResource
     */
    public function show(string $id, Request $request): OpportunityResource
    {
        try {
            $user = $request->user();

            // For parents, get the linked child
            // For other users, returns the user themselves
            $effectiveUser = $this->parentChildResolver->getEffectiveUser($user);

            // Pass the effective user ID to the service
            $opportunity = $this->opportunityService->show($id, $effectiveUser);

            return new OpportunityResource($opportunity);
        } catch (ModelNotFoundException $e) {
            // Handle case where parent has no linked child
            if ($user->profile_type === 'parent') {
                abort(404, 'Your account is not linked to any athlete. Please contact support for assistance.');
            }

            throw $e;
        }
    }

    /**
     * Toggle bookmark status for an opportunity
     *
     * @OA\Post(
     *      path="/api/v1/opportunities/{opportunity}/bookmark",
     *      operationId="toggleOpportunityBookmark",
     *      tags={"Opportunities"},
     *      summary="Toggle bookmark status for an opportunity",
     *      description="Toggles the bookmark status for a specific opportunity for the authenticated user.",
     *      security={{"sanctum": {}}},
     *      @OA\Parameter(
     *          name="opportunity",
     *          in="path",
     *          required=true,
     *          description="ID of the opportunity to bookmark/unbookmark",
     *          @OA\Schema(type="integer")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Bookmark status updated successfully",
     *          @OA\JsonContent(ref="#/components/schemas/BookmarkStatusData")
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Opportunity not found or parent has no linked children"
     *      )
     * )
     *
     * @param Request $request
     * @param Opportunity $opportunity
     * @return BookmarkStatusData
     */
    public function toggleBookmark(Request $request, Opportunity $opportunity): BookmarkStatusData
    {
        try {
            $user = $request->user();

            // For parents, get the linked child
            // For other users, returns the user themselves
            $effectiveUser = $this->parentChildResolver->getEffectiveUser($user);

            return $this->opportunityService->toggleBookmark($effectiveUser, $opportunity);
        } catch (ModelNotFoundException $e) {
            // Handle case where parent has no linked child
            if ($user->profile_type === 'parent') {
                abort(404, 'Your account is not linked to any athlete. Please contact support for assistance.');
            }

            throw $e;
        }
    }

    /**
     * Get all bookmarked opportunity IDs for the authenticated user
     *
     * @OA\Get(
     *      path="/api/v1/opportunities/bookmarked/ids",
     *      operationId="getBookmarkedOpportunityIds",
     *      tags={"Opportunities"},
     *      summary="Get IDs of bookmarked opportunities",
     *      description="Returns a list of IDs for opportunities bookmarked by the authenticated user.",
     *      security={{"sanctum": {}}},
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\JsonContent(ref="#/components/schemas/BookmarkedIdsData")
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Parent has no linked children"
     *      )
     * )
     *
     * @param Request $request
     * @return BookmarkedIdsData
     */
    public function getBookmarkedIds(Request $request): BookmarkedIdsData
    {
        try {
            $user = $request->user();

            // For parents, get the linked child
            // For other users, returns the user themselves
            $effectiveUser = $this->parentChildResolver->getEffectiveUser($user);

            return $this->opportunityService->getBookmarkedOpportunityIds($effectiveUser);
        } catch (ModelNotFoundException $e) {
            // Handle case where parent has no linked child
            if ($user->profile_type === 'parent') {
                abort(404, 'Your account is not linked to any athlete. Please contact support for assistance.');
            }

            throw $e;
        }
    }
}
