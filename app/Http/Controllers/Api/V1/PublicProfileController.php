<?php
namespace App\Http\Controllers\Api\V1;

use App\Data\Public\PublicAvatarData;
use App\Data\Public\PublicCareerInterestsData;
use App\Data\Public\PublicDetailsData;
use App\Data\Public\PublicInvolvementsData;
use App\Data\Public\PublicPhotosData;
use App\Data\Public\PublicProfileData;
use App\Data\Public\PublicSportsData;
use App\Data\Public\PublicStoryData;
use App\Data\Public\PublicWorkExperiencesData;
use App\Http\Controllers\Controller;
use App\Models\User;
/**
 * @OA\Tag(
 *     name="Public Profile",
 *     description="Endpoints for accessing public profile information"
 * )
 */
class PublicProfileController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/v1/profiles/{userId}",
     *     summary="Get public profile details",
     *     tags={"Public Profile"},
     *     @OA\Parameter(
     *         name="userId",
     *         in="path",
     *         required=true,
     *         description="User ID",
     *         @OA\Schema(type="string", format="uuid")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Public profile details retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/PublicProfileData")
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Access denied - Profile is private or not a public profile type"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User not found or profile not available for public viewing"
     *     )
     * )
     */
    public function show(string $userId)
    {
        $user = User::findOrFail($userId);
        $this->authorize('viewPublicProfile', $user);

        return PublicProfileData::fromModel($user);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/profiles/{userId}/details",
     *     summary="Get public detailed profile information",
     *     tags={"Public Profile"},
     *     @OA\Parameter(
     *         name="userId",
     *         in="path",
     *         required=true,
     *         description="User ID",
     *         @OA\Schema(type="string", format="uuid")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Public detailed profile information retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/PublicDetailsData")
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Access denied - Profile is private or not a public profile type"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User not found or profile not available for public viewing"
     *     )
     * )
     */
    public function details(string $userId)
    {
        $user = User::with(['county', 'state'])->findOrFail($userId);
        $this->authorize('viewPublicProfile', $user);

        return PublicDetailsData::fromModel($user);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/profiles/{userId}/sports",
     *     summary="Get public sports information",
     *     tags={"Public Profile"},
     *     @OA\Parameter(
     *         name="userId",
     *         in="path",
     *         required=true,
     *         description="User ID",
     *         @OA\Schema(type="string", format="uuid")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Public sports information retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/PublicSportsData")
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Access denied - Profile is private or not a public profile type"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User not found or profile not available for public viewing"
     *     )
     * )
     */
    public function sports(string $userId)
    {
        $user = User::with(['sports', 'customSports'])->findOrFail($userId);
        $this->authorize('viewPublicProfile', $user);

        return PublicSportsData::fromModel($user);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/profiles/{userId}/story",
     *     summary="Get public story",
     *     tags={"Public Profile"},
     *     @OA\Parameter(
     *         name="userId",
     *         in="path",
     *         required=true,
     *         description="User ID",
     *         @OA\Schema(type="string", format="uuid")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Public story retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/PublicStoryData")
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Access denied - Profile is private or not a public profile type"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User not found or profile not available for public viewing"
     *     )
     * )
     */
    public function story(string $userId)
    {
        $user = User::findOrFail($userId);
        $this->authorize('viewPublicProfile', $user);

        return PublicStoryData::fromModel($user);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/profiles/{userId}/involvements",
     *     summary="Get public involvements",
     *     tags={"Public Profile"},
     *     @OA\Parameter(
     *         name="userId",
     *         in="path",
     *         required=true,
     *         description="User ID",
     *         @OA\Schema(type="string", format="uuid")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Public involvements retrieved successfully",
     *         @OA\JsonContent(
     *             ref="#/components/schemas/PublicInvolvementsData"
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Access denied - Profile is private or not a public profile type"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User not found or profile not available for public viewing"
     *     )
     * )
     */
    public function involvements(string $userId)
    {
        $user = User::with('communityInvolvements')->findOrFail($userId);
        $this->authorize('viewPublicProfile', $user);

        return PublicInvolvementsData::fromModel($user);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/profiles/{userId}/avatar",
     *     summary="Get public avatar",
     *     tags={"Public Profile"},
     *     @OA\Parameter(
     *         name="userId",
     *         in="path",
     *         required=true,
     *         description="User ID",
     *         @OA\Schema(type="string", format="uuid")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Public avatar retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/PublicAvatarData")
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Access denied - Profile is private or not a public profile type"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User not found or profile not available for public viewing"
     *     )
     * )
     */
    public function avatar(string $userId)
    {
        $user = User::findOrFail($userId);
        $this->authorize('viewPublicProfile', $user);

        $avatar = $user->getFirstMedia('avatar');
        return $avatar ? PublicAvatarData::fromMedia($avatar) : null;
    }

    /**
     * @OA\Get(
     *     path="/api/v1/profiles/{userId}/photos",
     *     summary="Get public photos",
     *     tags={"Public Profile"},
     *     @OA\Parameter(
     *         name="userId",
     *         in="path",
     *         required=true,
     *         description="User ID",
     *         @OA\Schema(type="string", format="uuid")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Public photos retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/PublicPhotosData")
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Access denied - Profile is private or not a public profile type"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User not found or profile not available for public viewing"
     *     )
     * )
     */
    public function photos(string $userId)
    {
        $user = User::findOrFail($userId);
        $this->authorize('viewPublicProfile', $user);

        $photos = $user->getMedia('profile_photos');
        return PublicPhotosData::fromMediaCollection($photos);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/profiles/{userId}/career-interests",
     *     summary="Get public career interests",
     *     tags={"Public Profile"},
     *     @OA\Parameter(
     *         name="userId",
     *         in="path",
     *         required=true,
     *         description="User ID",
     *         @OA\Schema(type="string", format="uuid")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Public career interests retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/PublicCareerInterestsData")
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Access denied - Profile is private or not a public profile type"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User not found or profile not available for public viewing"
     *     )
     * )
     */
    public function careerInterests(string $userId)
    {
        $user = User::with('interests')->findOrFail($userId);
        $this->authorize('viewPublicProfile', $user);

        return PublicCareerInterestsData::fromModel($user);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/profiles/{userId}/work-experiences",
     *     summary="Get public work experiences",
     *     tags={"Public Profile"},
     *     @OA\Parameter(
     *         name="userId",
     *         in="path",
     *         required=true,
     *         description="User ID",
     *         @OA\Schema(type="string", format="uuid")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Public work experiences retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/PublicWorkExperiencesData")
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Access denied - Profile is private or not a public profile type"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User not found or profile not available for public viewing"
     *     )
     * )
     */
    public function workExperiences(string $userId)
    {
        $user = User::with('workExperiences')->findOrFail($userId);
        $this->authorize('viewPublicProfile', $user);

        return PublicWorkExperiencesData::fromModel($user);
    }
}
