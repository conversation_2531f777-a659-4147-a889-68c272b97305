<?php

namespace App\Http\Controllers\Api\V1;

use App\Enums\SystemInviteStatus;
use App\Http\Controllers\Controller;
use App\Models\SystemInvite;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

/**
 * @OA\Tag(
 *     name="System Invites",
 *     description="Endpoints for managing system invitations"
 * )
 *  @OA\Schema(
 *     schema="InviteData",
 *     title="Invite Data",
 *     description="Represents the structure of a pending system invitation, including associated onboarding details.",
 *     required={"email", "token", "created_at"},
 *     @OA\Property(property="nomination", ref="#/components/schemas/NominationData"),
 *     @OA\Property(property="nominator_email", type="string", format="email", example="<EMAIL>"),
 *     @OA\Property(property="nominator_name", type="string", example="Darryl Abshire"),
 *     @OA\Property(property="school_name", type="string", example="Strosin-Pouros High School"),
 *     @OA\Property(property="sport", type="string", example="Baseball"),
 *     @OA\Property(property="relationship", type="string", example="Teacher"),
 *     @OA\Property(property="note", type="string", example="Magni vero est aut quis nisi. Ipsum ducimus magni qui harum. Et blanditiis quos consequuntur. Id est eum dicta aspernatur commodi natus."),
 *     @OA\Property(
 *             property="email",
 *             type="string",
 *             format="email",
 *             description="The email address the invitation was sent to.",
 *             example="<EMAIL>"
 *         ),
 *         @OA\Property(
 *             property="token",
 *             type="string",
 *             description="The token associated with the invite.",
 *             example="1234567890"
 *         ),
 *         @OA\Property(
 *             property="created_at",
 *             type="integer",
 *             description="The timestamp the invite was created.",
 *             example=1713331200
 *         ),
 *         @OA\Property(
 *             property="first_name",
 *             type="string",
 *             description="The first name of the invitee.",
 *             example="John"
 *         ),
 *         @OA\Property(
 *             property="last_name",
 *             type="string",
 *             description="The last name of the invitee.",
 *             example="Doe"
 *         ),
 *         @OA\Property(
 *             property="athlete_name",
 *             type="string",
 *             description="The name of the athlete the invite is for.",
 *             example="John Doe"
 *         ),
 *         @OA\Property(
 *             property="athlete_user_id",
 *             type="integer",
 *             description="The user id of the athlete the invite is for.",
 *             example=1
 *         )
 * )
 *  @OA\Schema(
 *     schema="SystemInvite",
 *     title="System Invite Data",
 *     description="Represents the structure of a pending system invitation, including associated onboarding details.",
 *     required={"type", "email", "data"},
 *       @OA\Property(
 *             property="type",
 *             type="string",
 *             description="The type of profile the invite is for (e.g., student, coach).",
 *             example="student"
 *         ),
 *         @OA\Property(
 *             property="email",
 *             type="string",
 *             format="email",
 *             description="The email address the invitation was sent to.",
 *             example="<EMAIL>"
 *         ),
 *         @OA\Property(
 *             property="data",
 *             type="object",
 *             description="Additional context-specific data associated with the invite (structure may vary based on invite type).",
 *             ref="#/components/schemas/InviteData"
 *         )
 * )
 *  @OA\Schema(
 *     schema="InviteOnboardingData",
 *     title="Invite Onboarding Data",
 *     description="Represents the structure of a pending system invitation, including associated onboarding details.",
 *     required={"current_state", "profile_type"},
 *       @OA\Property(
 *             property="current_state",
 *             type="string",
 *             description="The identifier for the current state of the onboarding workflow.",
 *             example="account_info"
 *         ),
 *         @OA\Property(
 *             property="profile_type",
 *             type="string",
 *             description="The profile type being onboarded (should match invite.type).",
 *             example="student"
 *         )
 * )
 * @OA\Schema(
 *     schema="MobileCodeResponse",
 *     title="Mobile Code Response",
 *     description="Represents the structure of a pending system invitation, including associated onboarding details.",
 *     required={"invite", "onboarding"},
 *     @OA\Property(
 *         property="invite",
 *         ref="#/components/schemas/SystemInvite"
 *     ),
 *     @OA\Property(
 *         property="onboarding",
 *         ref="#/components/schemas/InviteOnboardingData"
 *     )
 * )
 * @OA\Schema(
 *     schema="MobileCodeInvalidResponse",
 *     title="Mobile Code Invalid R esponse",
 *     description="",
 *     required={"status", "message"},
 *     @OA\Property(
 *         property="status",
 *         type="string",
 *         description="Current status of the invite",
 *         example="invalid"
 *     ),
 *     @OA\Property(
 *         property="message",
 *         type="string",
 *         description="Reason why the invite cannot be used",
 *         example="invalid"
 *     )
 * )
 * @OA\Schema(
 *     schema="MobileCodeNotFoundResponse",
 *     title="Mobile Code Not Found Response",
 *     description="",
 *     required={"status", "message"},
 *     @OA\Property(
 *         property="message",
 *         type="string",
 *         description="The message of the error",
 *         example="invalid"
 *     )
 * )
 */


class SystemInviteController extends Controller
{
    public function show(string $token): JsonResponse
    {
        $invite = SystemInvite::query()
            ->with('onboarding')
            ->where('token', $token)
            ->firstOrFail();

        ray($invite, $invite->status);

        if ($invite->status === SystemInviteStatus::PENDING) {
            return response()->json([
                'invite' => [
                    'type' => $invite->type,
                    'email' => $invite->email,
                    'data' => $invite->invite_data,
                ],
                'onboarding' => [
                    'current_state' => $invite->onboarding->state->getIdentifier(),
                    'profile_type' => $invite->type,
                ]
            ]);
        }

        $message = match ($invite->status) {
            SystemInviteStatus::COMPLETED => 'This invitation has already been used',
            SystemInviteStatus::EXPIRED => 'This invitation has expired',
            SystemInviteStatus::REVOKED => 'This invitation has been revoked',
        };

        ray($message);

        return response()->json([
            'status' => $invite->status,
            'message' => $message
        ], 403);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/invites/mobile/{mobileCode}",
     *     summary="Resolve an invite by mobile code",
     *     tags={"Onboarding"},
     *     @OA\Parameter(
     *         name="mobileCode",
     *         in="path",
     *         required=true,
     *         description="The mobile code associated with the invite",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Invite details retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/MobileCodeResponse")
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Invite is not pending (completed, expired, or revoked)",
     *         @OA\JsonContent(ref="#/components/schemas/MobileCodeInvalidResponse")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Invite not found",
     *         @OA\JsonContent(ref="#/components/schemas/MobileCodeNotFoundResponse")
     *     )
     * )
     *
     *
     *
     * Resolve an invite by mobile code
     *
     * @param string $mobileCode
     * @return JsonResponse
     */
    public function showByMobileCode(string $mobileCode): JsonResponse
    {
        $invite = SystemInvite::query()
            ->with('onboarding')
            ->where('mobile_code', $mobileCode)
            ->firstOrFail();

        // Reuse the same logic as the show method to maintain consistency
        if ($invite->status === SystemInviteStatus::PENDING) {
            return response()->json([
                'invite' => [
                    'type' => $invite->type,
                    'email' => $invite->email,
                    'data' => $invite->invite_data,
                ],
                'onboarding' => [
                    'current_state' => $invite->onboarding->state->getIdentifier(),
                    'profile_type' => $invite->type,
                ]
            ]);
        }

        $message = match ($invite->status) {
            SystemInviteStatus::COMPLETED => 'This invitation has already been used',
            SystemInviteStatus::EXPIRED => 'This invitation has expired',
            SystemInviteStatus::REVOKED => 'This invitation has been revoked',
        };

        return response()->json([
            'status' => $invite->status,
            'message' => $message
        ], 403);
    }
}
