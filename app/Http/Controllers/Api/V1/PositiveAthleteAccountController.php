<?php

namespace App\Http\Controllers\Api\V1;

use App\Data\Account\LinkParentData;
use App\Data\Account\ParentAccountData;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\PositiveAthleteAccountService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="Athlete Account Management",
 *     description="API Endpoints for positive athlete specific account management"
 * )
 */
class PositiveAthleteAccountController extends Controller
{
    public function __construct(
        private readonly PositiveAthleteAccountService $accountService
    ) {}

    /**
     * Get all parent accounts linked to the authenticated athlete.
     * Only accessible by authenticated users with an athlete profile.
     *
     * @OA\Get(
     *     path="/api/v1/athlete/account/parents",
     *     summary="Get all parent accounts linked to the athlete",
     *     description="Retrieves a list of parent accounts linked to the currently authenticated athlete user. Requires the user to have an active athlete profile.",
     *     tags={"Athlete Account Management"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="List of parent accounts retrieved successfully",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/ParentAccountData")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden (User is not an athlete or does not have permission)"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal Server Error"
     *     )
     * )
     *
     * @return array<ParentAccountData>
     */
    public function getParents(Request $request): array
    {
        /** @var User $user */
        $user = $request->user();

        $parents = $this->accountService->getParentsForAthlete($user);

        return ParentAccountData::collection($parents);
    }

    /**
     * Link a parent account to the authenticated athlete.
     * Only accessible by authenticated users with an athlete profile.
     *
     * @OA\Post(
     *     path="/api/v1/athlete/account/parents",
     *     summary="Link a parent account to the athlete",
     *     description="Links a parent account (identified by data in the request body) to the currently authenticated athlete user. Requires the user to have an active athlete profile.",
     *     tags={"Athlete Account Management"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Data containing parent identifier (e.g., email)",
     *         @OA\JsonContent(ref="#/components/schemas/LinkParentData")
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Parent account linked successfully",
     *         @OA\JsonContent(ref="#/components/schemas/ParentAccountData")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Parent account not found to link"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error (e.g., invalid email, parent already linked)",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="The email has already been taken.")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden (User is not an athlete)"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal Server Error"
     *     )
     * )
     *
     * @param LinkParentData $data
     * @return JsonResponse
     */
    public function linkParent(LinkParentData $data, Request $request): JsonResponse
    {
        /** @var User $user */
        $user = $request->user();

        $parent = $this->accountService->linkParentToAthlete(
            $user,
            $data->toArray()
        );

        return response()->json(ParentAccountData::fromModel($parent), 201);
    }

    /**
     * Unlink a parent account from the authenticated athlete by email.
     * Only accessible by authenticated users with an athlete profile.
     *
     * @OA\Delete(
     *     path="/api/v1/athlete/account/parents/unlink",
     *     summary="Unlink a parent account from the athlete by email",
     *     description="Unlinks the specified parent account (by email) from the currently authenticated athlete user. Requires the user to have an active athlete profile.",
     *     tags={"Athlete Account Management"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Email address of the parent account to unlink",
     *         @OA\JsonContent(
     *             required={"email"},
     *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>")
     *         )
     *     ),
     *     @OA\Response(
     *         response=204,
     *         description="Parent account unlinked successfully"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Parent account not found or not linked to this athlete"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation Error (e.g., invalid email format)"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden (User is not an athlete)"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal Server Error"
     *     )
     * )
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function unlinkParent(Request $request): JsonResponse
    {
        /** @var User $user */
        $user = $request->user();

        $request->validate([
            'email' => 'required|email',
        ]);

        $this->accountService->unlinkParent(
            $user,
            $request->input('email')
        );

        return response()->json(null, 204);
    }
}
