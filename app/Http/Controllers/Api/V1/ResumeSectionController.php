<?php

namespace App\Http\Controllers\Api\V1;

use App\Data\Resume\ResumeSectionData;
use App\Data\Resume\UpdateResumeSectionData;
use App\Http\Controllers\Controller;
use App\Models\Resume;
use App\Services\ResumeSectionService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;

class ResumeSectionController extends Controller
{
    use AuthorizesRequests;

    public function __construct(
        protected ResumeSectionService $sectionService
    ) {}

    /**
     * @OA\Put(
     *     path="/api/v1/resumes/{resumeId}/sections/{type}",
     *     summary="Update a resume section with optional avatar for profile section",
     *     description="Updates the content of a resume section. For profile sections, also allows updating the avatar.",
     *     tags={"Resumes"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="resumeId",
     *         in="path",
     *         required=true,
     *         description="ID of the resume",
     *         @OA\Schema(type="integer", format="int64")
     *     ),
     *     @OA\Parameter(
     *         name="type",
     *         in="path",
     *         required=true,
     *         description="Type of resume section",
     *         @OA\Schema(
     *             type="string",
     *             enum={"profile", "contact", "education", "involvement", "experience", "sports"}
     *         )
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 required={"content"},
     *                 @OA\Property(
     *                     property="content",
     *                     type="object",
     *                     description="Section content object. Structure varies by section type."
     *                 ),
     *                 @OA\Property(
     *                     property="is_enabled",
     *                     type="boolean",
     *                     description="Whether the section is enabled"
     *                 ),
     *                 @OA\Property(
     *                     property="avatar",
     *                     type="string",
     *                     format="binary",
     *                     description="Avatar image file (only for profile section)"
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Section updated successfully",
     *         @OA\JsonContent(ref="#/components/schemas/ResumeSectionData")
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized to manage this resume section"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error in the request data"
     *     )
     * )
     */
    public function update(UpdateResumeSectionData $data, Resume $resume, string $type): JsonResponse
    {
        $this->authorize('manageSections', $resume);

        $section = $resume->sections()
            ->where('section_type', $type)
            ->firstOrCreate(['section_type' => $type], [
                'is_enabled' => true,
                'content' => [],
            ]);

        // Update section content
        $section = $this->sectionService->update($section, $data);

        // Handle avatar if provided (only for profile section)
        if ($type === 'profile' && $data->avatar) {
            $resume->clearMediaCollection('avatar');
            $resume->addMedia($data->avatar)
                ->withResponsiveImages()
                ->toMediaCollection('avatar');
        }

        return response()->json(ResumeSectionData::from($section));
    }

    /**
     * @OA\Put(
     *     path="/api/v1/resumes/{resumeId}/sections/{type}/toggle",
     *     summary="Toggle visibility of a resume section",
     *     description="Toggles the enabled/disabled state of a resume section",
     *     tags={"Resumes"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="resumeId",
     *         in="path",
     *         required=true,
     *         description="ID of the resume",
     *         @OA\Schema(type="integer", format="int64")
     *     ),
     *     @OA\Parameter(
     *         name="type",
     *         in="path",
     *         required=true,
     *         description="Type of resume section to toggle",
     *         @OA\Schema(type="string", enum={"profile", "work", "academic"})
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Section visibility toggled successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="integer"),
     *             @OA\Property(property="section_type", type="string"),
     *             @OA\Property(property="content", type="object"),
     *             @OA\Property(property="is_enabled", type="boolean"),
     *             @OA\Property(property="created_at", type="string", format="date-time"),
     *             @OA\Property(property="updated_at", type="string", format="date-time")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized to manage this resume section"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Resume section not found"
     *     )
     * )
     */
    public function toggle(Resume $resume, string $type): JsonResponse
    {
        $this->authorize('manageSections', $resume);

        $section = $resume->sections()
            ->where('section_type', $type)
            ->firstOrCreate(['section_type' => $type], [
                'id' => (string) Str::uuid(),
                'is_enabled' => true,
                'content' => [],
            ]);

        $section = $this->sectionService->update($section, UpdateResumeSectionData::from([
            'content' => $section->content,
            'is_enabled' => !$section->is_enabled,
        ]));

        return response()->json(ResumeSectionData::from($section));
    }
}
