<?php

namespace App\Http\Controllers\Api\V1;

use App\Data\Nomination\NominationData;
use App\Data\Nomination\UserNominationData;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\Nomination\NominationService;
use Illuminate\Http\JsonResponse;
use OpenApi\Attributes as OA;
use Spatie\LaravelData\DataCollection;

/**
 * @OA\Tag(
 *     name="Nominations",
 *     description="API Endpoints for managing nominations"
 * )
 */
class NominationController extends Controller
{
    public function __construct(
        private readonly NominationService $nominationService,
    ) {}

    /**
     * @OA\Get(
     *     path="/api/v1/users/{userId}/nominations",
     *     summary="Get nominations for a user",
     *     description="Returns all nominations received by a specific user",
     *     tags={"Nominations"},
     *     @OA\Parameter(
     *         name="userId",
     *         description="User ID of the nominated user",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="User nominations",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/UserNominationData")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User not found",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="User not found")
     *         )
     *     )
     * )
     *
     * Get nominations for a user.
     *
     * @param int $userId
     * @return DataCollection|JsonResponse
     */
    public function getUserNominations(int $userId): DataCollection|JsonResponse
    {
        $user = User::query()->find($userId);

        if (!$user) {
            return response()->json([
                'message' => 'User not found',
            ], 404);
        }

        return $this->nominationService->getUserNominations($user);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/nominations",
     *     summary="Submit a new nomination",
     *     description="Creates a new nomination for an athlete or coach",
     *     tags={"Nominations"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/NominationData")
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Nomination created successfully",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="id", type="integer", example=1),
     *             @OA\Property(property="email", type="string", example="<EMAIL>"),
     *             @OA\Property(property="first_name", type="string", example="John"),
     *             @OA\Property(property="last_name", type="string", example="Doe"),
     *             @OA\Property(property="created_at", type="string", format="date-time")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="The given data was invalid"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(property="email", type="array", @OA\Items(type="string", example="The email field is required")),
     *                 @OA\Property(property="first_name", type="array", @OA\Items(type="string", example="The first name field is required")),
     *                 @OA\Property(property="last_name", type="array", @OA\Items(type="string", example="The last name field is required"))
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=429,
     *         description="Too many requests",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Too Many Attempts.")
     *         )
     *     )
     * )
     *
     * Submit a new nomination.
     *
     * @param NominationData $data
     * @return JsonResponse
     */
    public function store(NominationData $data): JsonResponse
    {
        $nomination = $this->nominationService->handleSingleNomination($data);

        return response()->json($nomination, 201);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/nominations/bulk",
     *     summary="Submit multiple nominations at once",
     *     description="Creates multiple nominations in a single request",
     *     tags={"Nominations"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/NominationData")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Nominations created successfully",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="email", type="string", example="<EMAIL>"),
     *                 @OA\Property(property="first_name", type="string", example="John"),
     *                 @OA\Property(property="last_name", type="string", example="Doe"),
     *                 @OA\Property(property="created_at", type="string", format="date-time")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="The given data was invalid"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(property="0.email", type="array", @OA\Items(type="string", example="The email field is required")),
     *                 @OA\Property(property="0.first_name", type="array", @OA\Items(type="string", example="The first name field is required"))
     *             )
     *         )
     *     )
     * )
     *
     * Submit multiple nominations at once.
     *
     * @param array $nominations
     * @return JsonResponse
     */
    public function bulkStore(array $nominations): JsonResponse
    {
        $nominations = $this->nominationService->handleBulkNomination($nominations);

        return response()->json($nominations, 201);
    }
}
