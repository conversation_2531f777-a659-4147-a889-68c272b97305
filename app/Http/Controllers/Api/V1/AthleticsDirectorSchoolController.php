<?php

namespace App\Http\Controllers\Api\V1;

use App\Data\School\SchoolNomineePaginatedData;
use App\Data\School\SchoolNomineesRequestData;
use App\Http\Controllers\Controller;
use App\Services\SchoolNomineeService;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class AthleticsDirectorSchoolController extends Controller
{
    protected $schoolNomineeService;

    public function __construct(SchoolNomineeService $schoolNomineeService)
    {
        $this->schoolNomineeService = $schoolNomineeService;
        $this->middleware(['auth:sanctum', 'verified']);
    }

    /**
     * Get nominees from the athletics director's school
     *
     * @param SchoolNomineesRequestData $request
     * @return JsonResponse
     * @throws AuthorizationException
     */
    public function nominees(SchoolNomineesRequestData $request): JsonResponse
    {
        try {
            // Get nominees from the service
            $nominees = $this->schoolNomineeService->getNominees(request()->user(), $request->all());

            // Return the nominees with 200 OK status
            return response()->json($nominees, 200);
        } catch (AuthorizationException $e) {
            // Return authorization error with 403 Forbidden status
            return response()->json(['message' => $e->getMessage()], 403);
        } catch (\Exception $e) {
            // Log the error for debugging
            Log::error('Error fetching nominees: ' . $e->getMessage(), [
                'exception' => $e,
                'user_id' => request()->user()->id ?? null
            ]);

            // Return generic server error with 500 status
            return response()->json(['message' => 'An error occurred while fetching nominees'], 500);
        }
    }
}
