<?php

namespace App\Http\Controllers\Api\V1;

use App\Data\Endorsement\PublicEndorsementRequest;
use App\Data\Endorsement\PublicEndorsementResponse;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\Endorsement\EndorsementService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use OpenApi\Attributes as OA;
use Spatie\LaravelData\DataCollection;

/**
 * @OA\Tag(
 *     name="Endorsements",
 *     description="API Endpoints for managing user endorsements"
 * )
 */
class EndorsementController extends Controller
{
    public function __construct(
        private EndorsementService $endorsementService
    ) {
    }

    /**
     * @OA\Get(
     *     path="/api/v1/endorsements",
     *     summary="Get all endorsement categories",
     *     description="Returns all available endorsement categories",
     *     tags={"Endorsements"},
     *     @OA\Response(
     *         response=200,
     *         description="List of all endorsement categories",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/EndorsementData")
     *         )
     *     )
     * )
     *
     * Get all endorsement categories.
     *
     * @return DataCollection
     */
    public function index(): DataCollection
    {
        return $this->endorsementService->getAllEndorsements();
    }

    /**
     * @OA\Post(
     *     path="/api/v1/endorsements",
     *     summary="Submit public endorsements for a user",
     *     description="Submit one or more endorsements for a specific user. Can be used by authenticated or non-authenticated users.",
     *     tags={"Endorsements"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/PublicEndorsementRequest")
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Endorsements submitted successfully",
     *         @OA\JsonContent(ref="#/components/schemas/PublicEndorsementResponse")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Request processed but no endorsements were created",
     *         @OA\JsonContent(ref="#/components/schemas/PublicEndorsementResponse")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="The given data was invalid."),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 example={"userId": {"The user id field is required."}}
     *             )
     *         )
     *     )
     * )
     *
     * Submit public endorsements for a user.
     *
     * @param PublicEndorsementRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(PublicEndorsementRequest $request): \Illuminate\Http\JsonResponse
    {
        $response = $this->endorsementService->handlePublicEndorsements($request);

        // Return 201 Created if endorsements were successfully created
        // Return 200 OK if request was valid but no endorsements were created
        $statusCode = $response->success && $response->endorsementCount > 0 ? 201 : 200;

        return response()->json($response, $statusCode);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/users/{userId}/endorsements",
     *     summary="Get endorsements for a user",
     *     description="Returns endorsements for a specific user, grouped by category with counts and endorser details",
     *     tags={"Endorsements"},
     *     @OA\Parameter(
     *         name="userId",
     *         description="User ID",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="User endorsements grouped by category",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/EndorsementSummaryData")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User not found",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="User not found")
     *         )
     *     )
     * )
     *
     * Get endorsements for a user.
     *
     * @param int $userId
     * @return DataCollection|JsonResponse
     */
    public function getUserEndorsements(int $userId): DataCollection|JsonResponse
    {
        $user = User::query()->find($userId);

        if (!$user) {
            return response()->json([
                'message' => 'User not found',
            ], 404);
        }

        return $this->endorsementService->getUserEndorsements($user);
    }
}
