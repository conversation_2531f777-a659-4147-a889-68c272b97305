<?php

namespace App\Http\Controllers\Api\V1;

use App\Enums\ProfileType;
use App\Http\Controllers\Controller;
use App\Services\NetworkingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use OpenApi\Attributes as OA;

/**
 * @OA\Tag(
 *     name="Networking Configuration",
 *     description="API endpoints for networking configuration and connection rules"
 * )
 */
class NetworkingConfigController extends Controller
{
    /**
     * @var NetworkingService
     */
    protected $networkingService;

    /**
     * Create a new controller instance.
     *
     * @param NetworkingService $networkingService
     */
    public function __construct(NetworkingService $networkingService)
    {
        $this->networkingService = $networkingService;
    }

    /**
     * @OA\Get(
     *     path="/api/v1/networking/config",
     *     summary="Get the networking connection rules configuration",
     *     description="Retrieves the full connection map and searchable profile types for client-side use",
     *     tags={"Networking Configuration"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Connection rules configuration",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connection_map",
     *                 type="object",
     *                 description="Map of which profile types can connect with which other profile types",
     *                 example={"POSITIVE_ATHLETE": {"POSITIVE_ATHLETE"}, "POSITIVE_COACH": {"POSITIVE_COACH", "COLLEGE_ATHLETE"}}
     *             ),
     *             @OA\Property(
     *                 property="searchable_types",
     *                 type="array",
     *                 description="Profile types that should be included in the search index",
     *                 @OA\Items(type="string", example="POSITIVE_ATHLETE")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     )
     * )
     *
     * Get the connection rules configuration for client-side use
     *
     * @return JsonResponse
     */
    public function getConnectionRules(): JsonResponse
    {
        return response()->json([
            'connection_map' => $this->networkingService->getConnectionMap(),
            'searchable_types' => $this->networkingService::SEARCHABLE_TYPES,
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/networking/config/can-connect",
     *     summary="Check if a profile type can connect with another profile type",
     *     tags={"Networking Configuration"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="requester_type",
     *         in="query",
     *         required=true,
     *         description="The profile type of the requester",
     *         @OA\Schema(type="string", example="POSITIVE_COACH")
     *     ),
     *     @OA\Parameter(
     *         name="target_type",
     *         in="query",
     *         required=true,
     *         description="The profile type of the target",
     *         @OA\Schema(type="string", example="COLLEGE_ATHLETE")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Connection possibility check result",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="can_connect", type="boolean", example=true)
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Invalid profile type provided",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="error", type="string", example="Invalid profile type provided")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(ref="#/components/schemas/ValidationError")
     *     )
     * )
     *
     * Check if a profile type can connect with another profile type
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function canProfileTypesConnect(Request $request): JsonResponse
    {
        $request->validate([
            'requester_type' => 'required|string',
            'target_type' => 'required|string',
        ]);

        try {
            $requesterType = ProfileType::from($request->input('requester_type'));
            $targetType = ProfileType::from($request->input('target_type'));

            return response()->json([
                'can_connect' => $this->networkingService->canProfileTypesConnect($requesterType, $targetType),
            ]);
        } catch (\ValueError $e) {
            return response()->json([
                'error' => 'Invalid profile type provided',
            ], 400);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/networking/config/connectable-by",
     *     summary="Get all profile types that can connect with a specified profile type",
     *     description="Returns a list of profile types that can initiate connections with the specified profile type",
     *     tags={"Networking Configuration"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="profile_type",
     *         in="query",
     *         required=true,
     *         description="The profile type to check against",
     *         @OA\Schema(type="string", example="POSITIVE_ATHLETE")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="List of connectable profile types",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="profile_type", type="string", example="POSITIVE_ATHLETE"),
     *             @OA\Property(
     *                 property="connectable_by_types",
     *                 type="array",
     *                 description="Profile types that can connect with the specified profile type",
     *                 @OA\Items(type="string", example="POSITIVE_COACH")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Invalid profile type provided",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="error", type="string", example="Invalid profile type provided")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(ref="#/components/schemas/ValidationError")
     *     )
     * )
     *
     * Get all profile types that can connect with a specified profile type
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getConnectableByTypes(Request $request): JsonResponse
    {
        $request->validate([
            'profile_type' => 'required|string',
        ]);

        try {
            $profileType = ProfileType::from($request->input('profile_type'));

            return response()->json([
                'profile_type' => $profileType->value,
                'connectable_by_types' => $this->networkingService->getConnectableByTypes($profileType),
            ]);
        } catch (\ValueError $e) {
            return response()->json([
                'error' => 'Invalid profile type provided',
            ], 400);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/networking/config/connectable-to",
     *     summary="Get all profile types that a specified profile type can connect with",
     *     description="Returns a list of profile types that the specified profile type can initiate connections with",
     *     tags={"Networking Configuration"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="profile_type",
     *         in="query",
     *         required=true,
     *         description="The profile type to check from",
     *         @OA\Schema(type="string", example="POSITIVE_COACH")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="List of connectable profile types",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="profile_type", type="string", example="POSITIVE_COACH"),
     *             @OA\Property(
     *                 property="connectable_to_types",
     *                 type="array",
     *                 description="Profile types that the specified profile type can connect with",
     *                 @OA\Items(type="string", example="COLLEGE_ATHLETE")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Invalid profile type provided",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="error", type="string", example="Invalid profile type provided")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(ref="#/components/schemas/ValidationError")
     *     )
     * )
     *
     * Get all profile types that a specified profile type can connect with
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getConnectableToTypes(Request $request): JsonResponse
    {
        $request->validate([
            'profile_type' => 'required|string',
        ]);

        try {
            $profileType = ProfileType::from($request->input('profile_type'));

            return response()->json([
                'profile_type' => $profileType->value,
                'connectable_to_types' => $this->networkingService->getConnectableToTypes($profileType),
            ]);
        } catch (\ValueError $e) {
            return response()->json([
                'error' => 'Invalid profile type provided',
            ], 400);
        }
    }
}
