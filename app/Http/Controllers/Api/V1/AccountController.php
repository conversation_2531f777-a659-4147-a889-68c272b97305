<?php

namespace App\Http\Controllers\Api\V1;

use App\Data\Account\ProfileData;
use App\Data\Account\AddressData;
use App\Data\Account\DeleteAccountData;
use App\Data\Account\UpdateAddressData;
use App\Data\Account\UpdatePasswordData;
use App\Data\Account\UpdateProfileData;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\AccountManagementService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

/**
 * @OA\Tag(
 *     name="Account Management",
 *     description="API Endpoints for general account management"
 * )
 */
class AccountController extends Controller
{
    /**
     * Constructor
     *
     * @param AccountManagementService $accountService
     */
    public function __construct(
        private readonly AccountManagementService $accountService
    ) {}

    /**
     * Get the user's profile information.
     *
     * @OA\Get(
     *     path="/api/v1/account/profile",
     *     summary="Get user profile information",
     *     tags={"Account Management"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="User profile retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/ProfileData")
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal Server Error"
     *     )
     * )
     */
    public function getProfile(): ProfileData
    {
        /** @var User $user */
        $user = request()->user();

        // Ensure we're getting fresh data from the database
        $user = User::query()->find($user->id);

        return ProfileData::fromModel($user);
    }

    /**
     * Update the user's profile information.
     *
     * @OA\Patch(
     *     path="/api/v1/account/profile",
     *     summary="Update user profile information",
     *     tags={"Account Management"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Data to update user profile",
     *         @OA\JsonContent(ref="#/components/schemas/UpdateProfileData")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Profile updated successfully",
     *         @OA\JsonContent(ref="#/components/schemas/ProfileData")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation Error"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal Server Error"
     *     )
     * )
     */
    public function updateProfile(UpdateProfileData $data): ProfileData
    {
        /** @var User $user */
        $user = request()->user();

        // Log the incoming data for debugging
        Log::info('Profile update request received', [
            'user_id' => $user->id,
            'data' => $data->toArray(),
        ]);

        $updatedUser = $this->accountService->updateUserProfile(
            $user,
            $data->toArray()
        );

        return ProfileData::fromModel($updatedUser);
    }

    /**
     * Get the user's address information.
     *
     * @OA\Get(
     *     path="/api/v1/account/address",
     *     summary="Get user address information",
     *     tags={"Account Management"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="User address retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/AddressData")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Address information not found for the user"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal Server Error"
     *     )
     * )
     */
    public function getAddress(): AddressData
    {
        /** @var User $user */
        $user = request()->user();

        // Ensure we're getting fresh data from the database
        $user = User::query()->find($user->id);

        return AddressData::fromModel($user);
    }

    /**
     * Update the user's address information.
     *
     * @OA\Patch(
     *     path="/api/v1/account/address",
     *     summary="Update user address information",
     *     tags={"Account Management"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Data to update user address",
     *         @OA\JsonContent(ref="#/components/schemas/UpdateAddressData")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Address updated successfully",
     *         @OA\JsonContent(ref="#/components/schemas/AddressData")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation Error"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal Server Error"
     *     )
     * )
     */
    public function updateAddress(UpdateAddressData $data): AddressData
    {
        /** @var User $user */
        $user = request()->user();

        // Log the incoming data for debugging
        Log::info('Address update request received', [
            'user_id' => $user->id,
            'data' => $data->toArray(),
        ]);

        $updatedUser = $this->accountService->updateAddress(
            $user,
            $data->toArray()
        );

        return AddressData::fromModel($updatedUser);
    }

    /**
     * Update the user's password.
     *
     * @OA\Patch(
     *     path="/api/v1/account/password",
     *     summary="Update user password",
     *     tags={"Account Management"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Current and new password data",
     *         @OA\JsonContent(ref="#/components/schemas/UpdatePasswordData")
     *     ),
     *     @OA\Response(
     *         response=204,
     *         description="Password updated successfully"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="The current password is incorrect.")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal Server Error"
     *     )
     * )
     */
    public function updatePassword(UpdatePasswordData $data): JsonResponse
    {
        /** @var User $user */
        $user = request()->user();

        $this->accountService->updatePassword(
            $user,
            $data->current_password,
            $data->new_password
        );

        return response()->json(null, 204);
    }

    /**
     * Delete the user's account.
     *
     * @OA\Delete(
     *     path="/api/v1/account",
     *     summary="Delete user account",
     *     tags={"Account Management"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Password confirmation for account deletion",
     *         @OA\JsonContent(ref="#/components/schemas/DeleteAccountData")
     *     ),
     *     @OA\Response(
     *         response=204,
     *         description="Account deleted successfully"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="The password is incorrect.")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal Server Error"
     *     )
     * )
     */
    public function deleteAccount(DeleteAccountData $data): JsonResponse
    {
        /** @var User $user */
        $user = request()->user();

        $this->accountService->deleteAccount(
            $user,
            $data->password
        );

        return response()->json(null, 204);
    }
}
