<?php

namespace App\Http\Controllers\Api\V1\Sponsor;

use App\Data\Sponsor\SponsorOrganizationData;
use App\Http\Controllers\Controller;
use App\Services\SponsorService;
use Illuminate\Http\Request;

class SponsorController extends Controller
{
    public function __construct(
        private readonly SponsorService $sponsorService,
    ) {
        // Authorization is handled by the 'ensure.sponsor' middleware in routes/api.php
    }

    /**
     * Get the active organization for the authenticated sponsor
     *
     * @param Request $request
     * @return SponsorOrganizationData
     */
    public function organization(Request $request): SponsorOrganizationData
    {
        return $this->sponsorService->getActiveOrganizationForSponsor(
            user: $request->user()
        );
    }

    /**
     * Get a specific organization for the authenticated sponsor
     *
     * @param Request $request
     * @param int $organizationId
     * @return SponsorOrganizationData
     */
    public function specificOrganization(Request $request, int $organizationId): SponsorOrganizationData
    {
        return $this->sponsorService->getOrganizationForSponsor(
            user: $request->user(),
            organizationId: $organizationId
        );
    }
}
