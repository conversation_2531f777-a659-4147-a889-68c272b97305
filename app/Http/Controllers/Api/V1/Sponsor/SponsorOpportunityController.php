<?php

namespace App\Http\Controllers\Api\V1\Sponsor;

use App\Data\Sponsor\CreateOpportunityRequest;
use App\Data\Sponsor\OpportunityData;
use App\Data\Sponsor\UpdateOpportunityRequest;
use App\Http\Controllers\Controller;
use App\Models\Opportunity;
use App\Services\SponsorOpportunityService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Spatie\LaravelData\DataCollection;

class SponsorOpportunityController extends Controller
{
    public function __construct(
        private readonly SponsorOpportunityService $opportunityService,
    ) {
        $this->authorizeResource(Opportunity::class, 'opportunity');
    }

    /**
     * Display a listing of the opportunities.
     *
     * @param Request $request
     * @return DataCollection
     */
    public function index(Request $request): DataCollection
    {
        return $this->opportunityService->getOpportunitiesForSponsor(
            user: $request->user(),
            search: $request->input('search'),
            status: $request->input('status'),
            industries: $request->input('industry'),
            perPage: $request->input('per_page', 15)
        );
    }

    /**
     * Display the specified opportunity.
     *
     * @param Opportunity $opportunity
     * @return OpportunityData
     */
    public function show(Opportunity $opportunity): OpportunityData
    {
        return $this->opportunityService->getOpportunity(
            id: $opportunity->id,
            user: request()->user()
        );
    }

    /**
     * Store a newly created opportunity.
     *
     * @param CreateOpportunityRequest $request
     * @return OpportunityData
     */
    public function store(CreateOpportunityRequest $request): OpportunityData
    {
        return $this->opportunityService->createOpportunity(
            request: $request,
            user: request()->user()
        );
    }

    /**
     * Update the specified opportunity.
     *
     * @param UpdateOpportunityRequest $request
     * @param Opportunity $opportunity
     * @return OpportunityData
     */
    public function update(UpdateOpportunityRequest $request, Opportunity $opportunity): OpportunityData
    {
        return $this->opportunityService->updateOpportunity(
            id: $opportunity->id,
            request: $request,
            user: request()->user()
        );
    }

    /**
     * Remove the specified opportunity.
     *
     * @param Opportunity $opportunity
     * @return Response
     */
    public function destroy(Opportunity $opportunity): Response
    {
        $this->opportunityService->deleteOpportunity(
            id: $opportunity->id,
            user: request()->user()
        );

        return response()->noContent();
    }

    /**
     * Duplicate the specified opportunity.
     *
     * @param Opportunity $opportunity
     * @return OpportunityData
     */
    public function duplicate(Opportunity $opportunity): OpportunityData
    {
        return $this->opportunityService->duplicateOpportunity(
            id: $opportunity->id,
            user: request()->user()
        );
    }

    /**
     * Toggle the status of the specified opportunity.
     *
     * @param Opportunity $opportunity
     * @return OpportunityData
     */
    public function toggleStatus(Opportunity $opportunity): OpportunityData
    {
        return $this->opportunityService->toggleOpportunityStatus(
            id: $opportunity->id,
            user: request()->user()
        );
    }
}
