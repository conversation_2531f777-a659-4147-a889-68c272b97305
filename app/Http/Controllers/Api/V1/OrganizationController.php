<?php

namespace App\Http\Controllers\Api\V1;

use App\Data\Organization\OrganizationData;
use App\Data\Organization\UpdateOrganizationData;
use App\Http\Controllers\Controller;
use App\Services\OrganizationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;

class OrganizationController extends Controller
{
    public function __construct(
        private readonly OrganizationService $organizationService
    ) {
    }

    /**
     * Display the sponsor's organization details
     */
    public function show(): OrganizationData
    {
        $organization = $this->organizationService->getSponsorOrganization(Auth::user());

        return OrganizationData::from($organization);
    }

    /**
     * Update the sponsor's organization details
     */
    public function update(UpdateOrganizationData $request): JsonResponse
    {
        $organization = $this->organizationService->updateSponsorOrganization(
            Auth::user(),
            $request
        );

        return response()->json(
            OrganizationData::from($organization),
            Response::HTTP_OK
        );
    }
}
