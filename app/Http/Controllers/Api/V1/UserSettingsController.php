<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Scout\Jobs\MakeSearchable;
use OpenApi\Attributes as OA;

/**
 * @OA\Tag(
 *     name="User Settings",
 *     description="API endpoints for managing user settings"
 * )
 */
class UserSettingsController extends Controller
{
    /**
     * @OA\Post(
     *     path="/api/v1/user/settings/profile-visibility",
     *     summary="Update the user's profile visibility settings",
     *     tags={"User Settings"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             type="object",
     *             required={"public_profile"},
     *             @OA\Property(property="public_profile", type="boolean", description="Whether the user's profile is public and visible in search results")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Profile visibility settings updated successfully",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Profile visibility settings updated successfully"),
     *             @OA\Property(property="public_profile", type="boolean", example=true)
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(ref="#/components/schemas/ValidationError")
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     )
     * )
     *
     * Update the user's profile visibility settings
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function updateProfileVisibility(Request $request): JsonResponse
    {
        $request->validate([
            'public_profile' => 'required|boolean',
        ]);

        /** @var User $user */
        $user = $request->user();

        $user->update([
            'public_profile' => $request->boolean('public_profile')
        ]);

        // Scout will automatically handle the indexing

        return response()->json([
            'message' => 'Profile visibility settings updated successfully',
            'public_profile' => $user->public_profile,
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/user/settings/profile-visibility",
     *     summary="Get the user's profile visibility settings",
     *     tags={"User Settings"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Profile visibility settings",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="public_profile", type="boolean", example=true, description="Whether the user's profile is public and visible in search results")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     )
     * )
     *
     * Get the user's profile visibility settings
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getProfileVisibility(Request $request): JsonResponse
    {
        /** @var User $user */
        $user = $request->user();

        return response()->json([
            'public_profile' => $user->public_profile,
        ]);
    }
}
