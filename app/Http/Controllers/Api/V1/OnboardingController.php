<?php

namespace App\Http\Controllers\Api\V1;

use App\Data\Onboarding\OnboardingData;
use App\Http\Controllers\Controller;
use App\Models\SystemInvite;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Http\JsonResponse;

/**
 * @OA\Tag(
 *     name="Onboarding",
 *     description="API Endpoints for user onboarding process"
 * )
 *
 *   @OA\Schema(
 *     schema="OnboardingIntroRequest",
 *     title="Onboarding Intro Request",
 *     description="",
 *     required={"token"},
 *       @OA\Property(
 *             property="token",
 *             type="string",
 *             description="The token associated with the invite.",
 *             example="1234567890"
 *         )
 * )
 *
 * @OA\Schema(
 *     schema="OnboardingStateData",
 *     title="OnboardingStateData",
 *     description="Response object containing onboarding and invite information",
 *     @OA\Property(
 *         property="onboarding",
 *         type="object",
 *         @OA\Property(property="id", type="integer", example=1),
 *         @OA\Property(property="system_invite_id", type="integer", example=42),
 *         @OA\Property(property="profile_type", type="string", example="positive_athlete"),
 *         @OA\Property(property="state", type="string", example="account_info"),
 *         @OA\Property(
 *             property="data",
 *             type="object",
 *             description="Dynamic JSON data structure that varies based on the onboarding process and current state",
 *             example={"steps": {"account_info": {"first_name": "John", "last_name": "Doe"}}}
 *         ),
 *     ),
 *     @OA\Property(
 *         property="invite",
 *         type="object",
 *         ref="#/components/schemas/SystemInvite"
 *     )
 * )

 */


class OnboardingController extends Controller
{
    /**
     * Complete the onboarding process for a new user
     *
     * @OA\Post(
     *     path="/api/v1/onboarding",
     *     summary="Complete user onboarding",
     *     description="Processes the onboarding data for a new user, creating their account and profile",
     *     tags={"Onboarding"},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Onboarding data including user information, profile details, and optional activities",
     *         @OA\JsonContent(ref="#/components/schemas/OnboardingData")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="User successfully onboarded",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Onboarding completed successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="user_id", type="integer", example=1),
     *                 @OA\Property(property="profile_id", type="integer", example=1)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Invalid or expired invite token",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(ref="#/components/schemas/ValidationError")
     *     )
     * )
     */
    public function store(OnboardingData $request)
    {
        return DB::transaction(function () use ($request) {
            $invite = SystemInvite::query()
                ->whereToken($request->token)
                ->pending()
                ->firstOrFail();

            $user = User::query()->create([
                'email' => $invite->email,
                'password' => Hash::make($request->password),
                'name' => $request->name,
                'phone' => $request->phone,
            ]);

            $profile = $user->athleteProfile()->create([
                'school_id' => $request->school_id,
                'graduation_year' => $request->graduation_year,
                'sports' => $request->sports,
                'gpa' => $request->gpa,
                'class_rank' => $request->class_rank,
            ]);

            $invite->markAsCompleted();

            if ($request->activities) {
                $user->activities()->createMany(
                    $request->activities->toArray()
                );
            }

            if ($request->work_experience) {
                $user->workExperience()->createMany(
                    $request->work_experience->toArray()
                );
            }

            return $user->load(['athleteProfile', 'activities', 'workExperience'])
                ->getData();
        });
    }

    /**
     * Resolve an onboarding record using either a system invite token or mobile code
     *
     * @param string $token The token to check (can be a full token or mobile code)
     * @return JsonResponse
     *
     * @OA\Get(
     *     path="/api/v1/onboarding/resolve/{token}",
     *     summary="Resolve an onboarding record by token or mobile code",
     *     description="Finds an onboarding record associated with either a system invite token or mobile code",
     *     tags={"Onboarding"},
     *     @OA\Parameter(
     *         name="token",
     *         in="path",
     *         required=true,
     *         description="The system invite token or mobile code to check",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Onboarding record found",
     *         @OA\JsonContent(ref="#/components/schemas/OnboardingStateData")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="No invite found with the provided token",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="No invite found with the provided token")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Invite found but no onboarding record exists",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="No onboarding record found for this invite")
     *         )
     *     )
     * )
     */
    public function resolveByToken(string $token): JsonResponse
    {
        // Search for the token in both token and mobile_code fields
        $invite = SystemInvite::query()
            ->with('onboarding')
            ->where('token', $token)
            ->orWhere('mobile_code', $token)
            ->first();

        if (!$invite) {
            return response()->json([
                'message' => 'No invite found with the provided token'
            ], 404);
        }

        // Check if the invite has an associated onboarding record
        if (!$invite->onboarding) {
            return response()->json([
                'message' => 'No onboarding record found for this invite'
            ], 422);
        }

        // Return the onboarding record
        return response()->json([
            'onboarding' => [
                'id' => $invite->onboarding->id,
                'system_invite_id' => $invite->onboarding->system_invite_id,
                'profile_type' => $invite->onboarding->profile_type,
                'state' => $invite->onboarding->state->getIdentifier(),
                'data' => $invite->onboarding->data
            ],
            'invite' => [
                'id' => $invite->id,
                'type' => $invite->type,
                'email' => $invite->email,
                'status' => $invite->status,
                'expires_at' => $invite->expires_at,
                'data' => $invite->invite_data,
            ]
        ]);
    }
}
