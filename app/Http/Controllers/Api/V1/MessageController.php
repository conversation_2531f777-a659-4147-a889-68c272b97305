<?php

namespace App\Http\Controllers\Api\V1;

use App\Data\ConversationData;
use App\Data\MessageData;
use App\Data\SendMessageRequest;
use App\Data\EditMessageData;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\MessageService;
use App\Services\NetworkingService;
use App\Services\ParentChildResolverService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Spatie\LaravelData\DataCollection;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;

/**
 * @OA\Tag(
 *     name="Messages",
 *     description="Endpoints for managing user messages and conversations"
 * )
 */
class MessageController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct(
        private readonly MessageService $messageService,
        private readonly NetworkingService $networkingService,
        private readonly ParentChildResolverService $parentChildResolver
    ) {
    }

    /**
     * @OA\Get(
     *     path="/api/v1/messages/conversations",
     *     summary="Get all conversations for the authenticated user",
     *     tags={"Messages"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         required=false,
     *         description="Number of conversations per page",
     *         @OA\Schema(type="integer", default=15)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="List of conversations",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/ConversationData")
     *         )
     *     )
     * )
     *
     * Get all conversations for the authenticated user.
     *
     * @param Request $request
     * @return DataCollection
     */
    public function conversations(Request $request): DataCollection
    {
        /** @var User $user */
        $user = $request->user();
        $perPage = $request->input('per_page', 15);

        try {
            // Use the ParentChildResolverService instead of duplicating the logic
            try {
                $effectiveUser = $this->parentChildResolver->getEffectiveUser($user);
                $userId = $effectiveUser->id;

                if ($user->isParent()) {
                    Log::info('Parent viewing child conversations', [
                        'parent_id' => $user->id,
                        'child_id' => $effectiveUser->id
                    ]);
                }
            } catch (ModelNotFoundException $e) {
                // If no child found, use parent's ID (will likely return empty results)
                $userId = $user->id;

                Log::warning('Parent attempted to view conversations but has no linked child', [
                    'parent_id' => $user->id
                ]);
            }

            $conversations = $this->messageService->getConversations($userId, $perPage);

            // Get the items from the paginator and ensure it's a properly indexed array
            $items = array_values($conversations->items());

            // Check if there are any items
            if (empty($items)) {
                // Return an empty collection if there are no conversations
                return new DataCollection(ConversationData::class, collect([]));
            }

            // Map each item to a ConversationData object
            $data = collect($items)->map(function ($item) {
                try {
                    return ConversationData::fromStdClass($item);
                } catch (\Throwable $e) {
                    Log::error('Error creating ConversationData', [
                        'error' => $e->getMessage(),
                        'file' => $e->getFile(),
                        'line' => $e->getLine()
                    ]);
                    throw $e;
                }
            });

            // Return a DataCollection
            return new DataCollection(ConversationData::class, $data);
        } catch (\Throwable $e) {
            Log::error('MessageController::conversations - Error retrieving conversations', [
                'userId' => $user->id,
                'error' => $e->getMessage()
            ]);

            // Return an empty collection in case of error
            return new DataCollection(ConversationData::class, collect([]));
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/messages/conversation/{userId}",
     *     summary="Get a conversation between the authenticated user and another user",
     *     tags={"Messages"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="userId",
     *         in="path",
     *         required=true,
     *         description="ID of the user to get conversation with",
     *         @OA\Schema(type="integer", format="int64")
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         required=false,
     *         description="Number of messages per page",
     *         @OA\Schema(type="integer", default=15)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Conversation messages and connection data",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="messages",
     *                 type="array",
     *                 @OA\Items(ref="#/components/schemas/MessageData")
     *             ),
     *             @OA\Property(
     *                 property="connection",
     *                 type="object",
     *                 nullable=true,
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="status", type="string"),
     *                 @OA\Property(property="requesterId", type="integer"),
     *                 @OA\Property(property="recipientId", type="integer"),
     *                 @OA\Property(property="isBlocked", type="boolean")
     *             )
     *         )
     *     )
     * )
     *
     * Get a conversation between the authenticated user and another user.
     *
     * @param Request $request
     * @param int $userId
     * @return array
     */
    public function getConversation(Request $request, int $userId): array
    {
        /** @var User $user */
        $user = $request->user();
        $perPage = $request->input('per_page', 15);

        // Use the ParentChildResolverService instead of duplicating the logic
        try {
            $effectiveUser = $this->parentChildResolver->getEffectiveUser($user);
            $effectiveUserId = $effectiveUser->id;

            if ($user->isParent()) {
                Log::info('Parent viewing child conversation', [
                    'parent_id' => $user->id,
                    'child_id' => $effectiveUser->id,
                    'conversation_with' => $userId
                ]);
            }
        } catch (ModelNotFoundException $e) {
            // If no child found, use parent's ID
            $effectiveUserId = $user->id;

            Log::warning('Parent attempted to view conversations but has no linked child', [
                'parent_id' => $user->id
            ]);
        }

        // Get the messages - note we're using the effective user ID that might be the child's ID
        $messages = $this->messageService->getConversation($effectiveUserId, $userId, $perPage);

        // Get connection information using the effective user ID
        $connection = $this->networkingService->getConnection($effectiveUserId, $userId);

        $connectionData = null;
        if ($connection) {
            $connectionData = [
                'id' => $connection->id,
                'status' => $connection->status->value,
                'requesterId' => $connection->requester_id,
                'recipientId' => $connection->recipient_id,
                'isBlocked' => $connection->trashed(),
            ];
        }

        // Transform messages using our custom fromMessage method
        $messageData = $messages->map(function ($message) {
            return MessageData::fromMessage($message);
        });

        return [
            'messages' => $messageData,
            'connection' => $connectionData,
        ];
    }

    /**
     * @OA\Post(
     *     path="/api/v1/messages",
     *     summary="Send a new message",
     *     tags={"Messages"},
     *     security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             type="object",
     *             required={"recipientId", "content"},
     *             @OA\Property(
     *                 property="recipientId",
     *                 type="integer",
     *                 description="ID of message recipient"
     *             ),
     *             @OA\Property(
     *                 property="content",
     *                 type="string",
     *                 description="Message content"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Message sent successfully",
     *         @OA\JsonContent(ref="#/components/schemas/MessageData")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\AdditionalProperties(
     *                     type="array",
     *                     @OA\Items(type="string")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - parent users cannot send messages",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Parent users cannot send messages")
     *         )
     *     )
     * )
     *
     * Send a new message.
     *
     * @param SendMessageRequest $request
     * @return JsonResponse
     */
    public function store(SendMessageRequest $request): JsonResponse
    {
        try {
            /** @var User $user */
            $user = request()->user();

            // Check if the user is a parent - parents cannot send messages
            if ($user->isParent()) {
                return response()->json([
                    'message' => 'Parent users cannot send messages'
                ], Response::HTTP_FORBIDDEN);
            }

            $message = $this->messageService->sendMessage(
                $user->id,
                $request->recipientId,
                $request->content,
                null
            );

            $messageData = MessageData::fromMessage($message);

            // Return a JSON response with the exact structure expected by the front-end
            return response()->json($messageData);
        } catch (Exception $e) {
            Log::error('Error sending message', [
                'sender_id' => request()->user()->id,
                'recipient_id' => $request->recipientId,
                'error' => $e->getMessage()
            ]);

            // Return appropriate error response based on exception message
            $errorData = match (true) {
                str_contains($e->getMessage(), 'blocking restrictions') => [
                    'message' => 'Cannot send message because you have been blocked by this user or you have blocked them.',
                    'status' => Response::HTTP_FORBIDDEN,
                ],
                str_contains($e->getMessage(), 'connection restrictions') => [
                    'message' => 'You do not have permission to message this user due to user type restrictions.',
                    'status' => Response::HTTP_FORBIDDEN,
                ],
                str_contains($e->getMessage(), 'connection request is accepted') => [
                    'message' => 'Cannot send more messages until your connection request is accepted.',
                    'status' => Response::HTTP_FORBIDDEN,
                ],
                default => (function() {
                    return [
                        'message' => 'An error occurred while sending the message.',
                        'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                    ];
                })(),
            };

            return response()->json(['message' => $errorData['message']], $errorData['status']);
        }
    }

    /**
     * @OA\Patch(
     *     path="/api/v1/messages/{id}/read",
     *     summary="Mark a message as read",
     *     tags={"Messages"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="X-HTTP-Method-Override",
     *         in="header",
     *         required=true,
     *         description="Method override for clients that don't support PATCH requests",
     *         @OA\Schema(type="string", example="PATCH")
     *     ),
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="ID of the message to mark as read",
     *         @OA\Schema(type="integer", format="int64")
     *     ),
     *     @OA\Response(
     *         response=204,
     *         description="Message marked as read"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Message not found"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - parent users cannot mark messages as read",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Parent users cannot mark messages as read")
     *         )
     *     )
     * )
     *
     * Mark a message as read.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function markAsRead(Request $request, int $id): JsonResponse
    {
        /** @var User $user */
        $user = $request->user();

        // Check if the user is a parent - parents cannot mark messages as read
        if ($user->isParent()) {
            return response()->json([
                'message' => 'Parent users cannot mark messages as read'
            ], Response::HTTP_FORBIDDEN);
        }

        $this->messageService->markAsRead($id, $user->id);

        return response()->json(null, Response::HTTP_NO_CONTENT);
    }

    /**
     * @OA\Delete(
     *     path="/api/v1/messages/{id}",
     *     summary="Delete a message",
     *     tags={"Messages"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="X-HTTP-Method-Override",
     *         in="header",
     *         required=true,
     *         description="Method override for clients that don't support DELETE requests",
     *         @OA\Schema(type="string", example="DELETE")
     *     ),
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="ID of the message to delete",
     *         @OA\Schema(type="integer", format="int64")
     *     ),
     *     @OA\Response(
     *         response=204,
     *         description="Message deleted successfully"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Message not found"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - cannot delete message or parent users cannot delete messages",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string")
     *         )
     *     )
     * )
     *
     * Delete a message.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(Request $request, int $id): JsonResponse
    {
        /** @var User $user */
        $user = $request->user();

        // Check if the user is a parent - parents cannot delete messages
        if ($user->isParent()) {
            return response()->json([
                'message' => 'Parent users cannot delete messages'
            ], Response::HTTP_FORBIDDEN);
        }

        $this->messageService->deleteMessage($id, $user->id);

        return response()->json(null, Response::HTTP_NO_CONTENT);
    }

    /**
     * @OA\Patch(
     *     path="/api/v1/messages/conversation/{userId}/pin",
     *     summary="Pin a conversation",
     *     tags={"Messages"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="X-HTTP-Method-Override",
     *         in="header",
     *         required=true,
     *         description="Method override for clients that don't support PATCH requests",
     *         @OA\Schema(type="string", example="PATCH")
     *     ),
     *     @OA\Parameter(
     *         name="userId",
     *         in="path",
     *         required=true,
     *         description="ID of the user whose conversation to pin",
     *         @OA\Schema(type="integer", format="int64")
     *     ),
     *     @OA\Response(
     *         response=204,
     *         description="Conversation pinned successfully"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Conversation not found"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - parent users cannot pin conversations",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Parent users cannot pin conversations")
     *         )
     *     )
     * )
     *
     * Pin a conversation.
     *
     * @param Request $request
     * @param int $userId
     * @return JsonResponse
     */
    public function pinConversation(Request $request, int $userId): JsonResponse
    {
        /** @var User $user */
        $user = $request->user();

        // Check if the user is a parent - parents cannot pin conversations
        if ($user->isParent()) {
            return response()->json([
                'message' => 'Parent users cannot pin conversations'
            ], Response::HTTP_FORBIDDEN);
        }

        $this->messageService->pinConversation($userId, $user->id);

        return response()->json(null, Response::HTTP_NO_CONTENT);
    }

    /**
     * @OA\Patch(
     *     path="/api/v1/messages/conversation/{userId}/unpin",
     *     summary="Unpin a conversation",
     *     tags={"Messages"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="X-HTTP-Method-Override",
     *         in="header",
     *         required=true,
     *         description="Method override for clients that don't support PATCH requests",
     *         @OA\Schema(type="string", example="PATCH")
     *     ),
     *     @OA\Parameter(
     *         name="userId",
     *         in="path",
     *         required=true,
     *         description="ID of the user whose conversation to unpin",
     *         @OA\Schema(type="integer", format="int64")
     *     ),
     *     @OA\Response(
     *         response=204,
     *         description="Conversation unpinned successfully"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Conversation not found"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - parent users cannot unpin conversations",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Parent users cannot unpin conversations")
     *         )
     *     )
     * )
     *
     * Unpin a conversation.
     *
     * @param Request $request
     * @param int $userId
     * @return JsonResponse
     */
    public function unpinConversation(Request $request, int $userId): JsonResponse
    {
        /** @var User $user */
        $user = $request->user();

        // Check if the user is a parent - parents cannot unpin conversations
        if ($user->isParent()) {
            return response()->json([
                'message' => 'Parent users cannot unpin conversations'
            ], Response::HTTP_FORBIDDEN);
        }

        $this->messageService->unpinConversation($userId, $user->id);

        return response()->json(null, Response::HTTP_NO_CONTENT);
    }

    /**
     * @OA\Patch(
     *     path="/api/v1/messages/{id}",
     *     summary="Edit an existing message",
     *     tags={"Messages"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="ID of the message to edit",
     *         @OA\Schema(type="integer", format="int64")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/EditMessageData")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Message updated successfully",
     *         @OA\JsonContent(ref="#/components/schemas/MessageData")
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - User does not have permission to edit this message or parent users cannot edit messages",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Message not found or you do not have permission to edit it")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Message not found",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Message not found")
     *         )
     *     )
     * )
     *
     * Edit an existing message.
     *
     * @param EditMessageData $data
     * @param int $id
     * @return MessageData|JsonResponse
     */
    public function update(EditMessageData $data, int $id): MessageData|JsonResponse
    {
        try {
            /** @var User $user */
            $user = request()->user();

            // Check if the user is a parent - parents cannot edit messages
            if ($user->isParent()) {
                return response()->json([
                    'message' => 'Parent users cannot edit messages'
                ], Response::HTTP_FORBIDDEN);
            }

            $message = $this->messageService->editMessage(
                $id,
                $user->id,
                $data->content
            );

            return MessageData::fromMessage($message);
        } catch (Exception $e) {
            $statusCode = $e->getMessage() === 'Message not found or you do not have permission to edit it' ? 403 : 500;

            return response()->json([
                'message' => $e->getMessage()
            ], $statusCode);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/messages/organization/colleagues/{colleagueId}/conversations/{otherUserId}",
     *     summary="Get conversation between a colleague and another user (for sponsor organizational view)",
     *     tags={"Messages"},
     *     security={{"sanctum":{}}},
     *     @OA\Parameter(
     *         name="colleagueId",
     *         in="path",
     *         required=true,
     *         description="ID of the colleague",
     *         @OA\Schema(type="integer", format="int64")
     *     ),
     *     @OA\Parameter(
     *         name="otherUserId",
     *         in="path",
     *         required=true,
     *         description="ID of the other user in the conversation",
     *         @OA\Schema(type="integer", format="int64")
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         required=false,
     *         description="Number of messages per page",
     *         @OA\Schema(type="integer", default=15)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Colleague conversation messages and connection data",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="messages",
     *                 type="array",
     *                 @OA\Items(ref="#/components/schemas/MessageData")
     *             ),
     *             @OA\Property(
     *                 property="connection",
     *                 type="object",
     *                 nullable=true,
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="status", type="string"),
     *                 @OA\Property(property="requesterId", type="integer"),
     *                 @OA\Property(property="recipientId", type="integer"),
     *                 @OA\Property(property="isBlocked", type="boolean")
     *             ),
     *             @OA\Property(
     *                 property="is_readonly",
     *                 type="boolean",
     *                 example=true,
     *                 description="Flag indicating this is a read-only conversation view"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Not authorized to view this conversation",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="You are not authorized to view this conversation")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Conversation not found",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Conversation not found")
     *         )
     *     )
     * )
     *
     * Get conversation between a colleague and another user (for sponsor organizational view).
     *
     * @param Request $request
     * @param int $colleagueId
     * @param int $otherUserId
     * @return array|JsonResponse
     */
    public function sponsorConversation(Request $request, int $sponsorId, int $otherUserId): array|JsonResponse
    {
        try {
            /** @var User $user */
            $user = $request->user();
            $perPage = $request->input('per_page', 15);

            // Check if the authenticated user is a sponsor
            if (!$user->isSponsor()) {
                return response()->json([
                    'message' => 'Only sponsors can access this endpoint'
                ], Response::HTTP_FORBIDDEN);
            }

            // Check if the colleague is in the same organization as the authenticated user
            if (!$this->messageService->isColleagueInSameOrganization($user->id, $sponsorId)) {
                return response()->json([
                    'message' => 'You can only view conversations of colleagues in your organization'
                ], Response::HTTP_FORBIDDEN);
            }

            // Get the conversation between the colleague and the other user
            $messages = $this->messageService->getColleagueConversation($user->id, $sponsorId, $otherUserId, $perPage);

            // Get connection information between the colleague and the other user
            $connection = $this->networkingService->getConnection($sponsorId, $otherUserId);

            $connectionData = null;
            if ($connection) {
                $connectionData = [
                    'id' => $connection->id,
                    'status' => $connection->status->value,
                    'requesterId' => $connection->requester_id,
                    'recipientId' => $connection->recipient_id,
                    'isBlocked' => $connection->trashed(),
                ];
            }

            // Transform messages using our custom fromMessage method
            $messageData = $messages->map(function ($message) {
                return MessageData::fromMessage($message);
            });

            return [
                'messages' => $messageData,
                'connection' => $connectionData,
                'is_readonly' => true, // Always true for this endpoint
            ];
        } catch (Exception $e) {
            Log::error('Error fetching sponsor conversation', [
                'viewer_id' => $request->user()->id,
                'sponsor_id' => $sponsorId,
                'other_user_id' => $otherUserId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            $statusCode = match (true) {
                str_contains($e->getMessage(), 'not authorized') => Response::HTTP_FORBIDDEN,
                str_contains($e->getMessage(), 'not found') => Response::HTTP_NOT_FOUND,
                default => Response::HTTP_INTERNAL_SERVER_ERROR,
            };

            return response()->json([
                'message' => $e->getMessage()
            ], $statusCode);
        }
    }
}
