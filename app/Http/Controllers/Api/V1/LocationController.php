<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\LocationCoordinate;
use Illuminate\Http\Request;
use OpenApi\Annotations as OA;

/**
 * @OA\Tag(
 *     name="Locations",
 *     description="API Endpoints for location-related operations"
 * )
 */
class LocationController extends Controller
{
    /**
     * Search for cities by name
     *
     * @OA\Get(
     *     path="/api/v1/locations/search",
     *     operationId="searchLocations",
     *     tags={"Locations"},
     *     summary="Search for cities by name",
     *     description="Returns a list of cities matching the search query (limited to 20 results). This is a public endpoint that does not require authentication.",
     *     @OA\Parameter(
     *         name="query",
     *         in="query",
     *         description="Search term for city names",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="string", format="uuid", example="9e98a071-69ca-459c-90ee-bdd86436ecec"),
     *                     @OA\Property(property="value", type="string", format="uuid", example="9e98a071-69ca-459c-90ee-bdd86436ecec"),
     *                     @OA\Property(property="label", type="string", example="Atlanta, GA"),
     *                     @OA\Property(property="city", type="string", example="Atlanta"),
     *                     @OA\Property(property="state_code", type="string", example="GA")
     *                 )
     *             ),
     *             @OA\Property(property="message", type="string", nullable=true, example=null)
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Server error",
     *         @OA\JsonContent(ref="#/components/schemas/Error")
     *     )
     * )
     */
    public function searchCities(Request $request)
    {
        $query = $request->get('query', '');

        // Search for cities that match the query string
        // For empty or very short queries, we'll still return results but limit them
        $cities = LocationCoordinate::query();

        if (!empty($query)) {
            $cities->where('city', 'ilike', $query . '%');
        }

        $results = $cities->orderBy('city')
            ->orderBy('state_code')
            ->limit(20)
            ->get(['id', 'city', 'state_code'])
            ->map(function ($city) {
                return [
                    'id' => $city->id,
                    'value' => $city->id,
                    'label' => "{$city->city}, {$city->state_code}",
                    'city' => $city->city,
                    'state_code' => $city->state_code
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $results,
            'message' => null
        ]);
    }

    /**
     * Get a specific location by ID
     *
     * @OA\Get(
     *     path="/api/v1/locations/{id}",
     *     operationId="getLocation",
     *     tags={"Locations"},
     *     summary="Get location details by ID",
     *     description="Returns details for a specific location by its UUID. This is a public endpoint that does not require authentication.",
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Location UUID",
     *         required=true,
     *         @OA\Schema(type="string", format="uuid")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="id", type="string", format="uuid", example="9e98a071-69ca-459c-90ee-bdd86436ecec"),
     *                 @OA\Property(property="value", type="string", format="uuid", example="9e98a071-69ca-459c-90ee-bdd86436ecec"),
     *                 @OA\Property(property="label", type="string", example="Atlanta, GA"),
     *                 @OA\Property(property="city", type="string", example="Atlanta"),
     *                 @OA\Property(property="state_code", type="string", example="GA"),
     *                 @OA\Property(property="latitude", type="number", format="float", example=33.7490),
     *                 @OA\Property(property="longitude", type="number", format="float", example=-84.3880)
     *             ),
     *             @OA\Property(property="message", type="string", nullable=true, example=null)
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Location not found",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Location not found")
     *         )
     *     )
     * )
     */
    public function getLocation(string $id)
    {
        $location = LocationCoordinate::find($id);

        if (!$location) {
            return response()->json([
                'success' => false,
                'message' => 'Location not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $location->id,
                'value' => $location->id,
                'label' => "{$location->city}, {$location->state_code}",
                'city' => $location->city,
                'state_code' => $location->state_code,
                'latitude' => $location->latitude,
                'longitude' => $location->longitude
            ],
            'message' => null
        ]);
    }
}
