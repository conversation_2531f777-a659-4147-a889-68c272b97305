<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Winner;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use OpenApi\Attributes as OA;

/**
 * @OA\Schema(
 *     schema="ScholarshipAchievement",
 *     title="Scholarship Achievement",
 *     description="Details of a scholarship won by a user",
 *     @OA\Property(property="id", type="integer", description="Scholarship ID", example=1),
 *     @OA\Property(property="name", type="string", description="Name of the scholarship", example="Positive Athlete Scholarship"),
 *     @OA\Property(property="details", type="string", description="Details about the scholarship", example="Awarded for outstanding character."),
 *     @OA\Property(property="year", type="integer", description="Year the scholarship was won", example=2023),
 *     @OA\Property(property="amount", type="number", format="float", description="Amount of the scholarship", example=1000.00),
 *     @OA\Property(property="region", type="string", nullable=true, description="Region of the scholarship", example="Georgia North"),
 *     @OA\Property(property="state", type="string", nullable=true, description="State of the scholarship", example="GA")
 * )
 *
 * @OA\Schema(
 *     schema="AwardAchievement",
 *     title="Award Achievement",
 *     description="Details of an award won by a user",
 *     @OA\Property(property="id", type="integer", description="Award ID", example=101),
 *     @OA\Property(property="name", type="string", description="Name of the award", example="Most Improved Player"),
 *     @OA\Property(property="details", type="string", description="Details about the award", example="Recognizing significant progress."),
 *     @OA\Property(property="year", type="integer", description="Year the award was won", example=2023),
 *     @OA\Property(property="type", type="string", description="Type of the award", example="Athletic"),
 *     @OA\Property(property="region", type="string", nullable=true, description="Region of the award", example="Georgia North"),
 *     @OA\Property(property="state", type="string", nullable=true, description="State of the award", example="GA")
 * )
 *
 * @OA\Tag(
 *     name="User Achievements",
 *     description="API endpoints for retrieving user achievements"
 * )
 */
class UserAchievementsController extends Controller
{
    /**
     * Get all scholarships and awards won by a user
     *
     * @OA\Get(
     *     path="/api/v1/users/{userId}/achievements",
     *     summary="Get scholarships and awards won by a user",
     *     tags={"User Achievements"},
     *     @OA\Parameter(
     *         name="userId",
     *         in="path",
     *         required=true,
     *         description="ID of the user",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful response with list of achievements",
     *         @OA\JsonContent(
     *             @OA\Property(property="scholarships", type="array", @OA\Items(ref="#/components/schemas/ScholarshipAchievement")),
     *             @OA\Property(property="awards", type="array", @OA\Items(ref="#/components/schemas/AwardAchievement"))
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User not found"
     *     )
     * )
     */
    public function index(string $userId): JsonResponse
    {
        $user = User::query()->find($userId);

        if (!$user) {
            return response()->json(['message' => 'User not found'], 404);
        }

        // Get all scholarships won by the user
        $scholarships = Winner::query()
            ->where('user_id', $user->id)
            ->where('is_winner', true)
            ->whereNotNull('scholarship_id')
            ->with('scholarship')
            ->get()
            ->map(function ($winner) {
                return [
                    'id' => $winner->scholarship->id,
                    'name' => $winner->scholarship->name,
                    'details' => $winner->scholarship->details,
                    'year' => $winner->year,
                    'amount' => $winner->scholarship->amount,
                    'region' => $winner->scholarship->region ? $winner->scholarship->region->name : null,
                    'state' => $winner->scholarship->state ? $winner->scholarship->state->name : null,
                ];
            });

        // Get all awards won by the user
        $awards = Winner::query()
            ->where('user_id', $user->id)
            ->where('is_winner', true)
            ->whereNotNull('award_id')
            ->with('award')
            ->get()
            ->map(function ($winner) {
                return [
                    'id' => $winner->award->id,
                    'name' => $winner->award->name,
                    'details' => $winner->award->details,
                    'year' => $winner->year,
                    'type' => $winner->award->type,
                    'region' => $winner->award->region ? $winner->award->region->name : null,
                    'state' => $winner->award->state ? $winner->award->state->name : null,
                ];
            });

        return response()->json([
            'scholarships' => $scholarships,
            'awards' => $awards
        ]);
    }
}
