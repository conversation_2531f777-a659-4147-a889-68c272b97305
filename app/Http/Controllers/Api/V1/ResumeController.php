<?php

namespace App\Http\Controllers\Api\V1;

use App\Data\Resume\ResumeData;
use App\Data\Resume\ResumeAvatarData;
use App\Data\Resume\UpdateResumeAvatarData;
use App\Http\Controllers\Controller;
use App\Models\Resume;
use App\Services\ResumeService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;

/**
 * @OA\Tag(
 *     name="Resumes",
 *     description="API Endpoints for managing resumes"
 * )
 * @OA\Schema(
 *     schema="ResumePdfResponse",
 *     required={"pdf_url"},
 *     @OA\Property(
 *         property="pdf_url",
 *         type="string",
 *         format="url",
 *         example="http://localhost/storage/1/resume.pdf"
 *     )
 * )
 */
class ResumeController extends Controller
{
    use AuthorizesRequests;

    public function __construct(
        protected ResumeService $resumeService
    ) {
        $this->authorizeResource(Resume::class, 'resume');
    }

    /**
     * @OA\Get(
     *     path="/api/v1/resumes",
     *     summary="Get the authenticated user's resume",
     *     tags={"Resumes"},
     *     @OA\Response(
     *         response=200,
     *         description="User's resume",
     *         @OA\JsonContent(ref="#/components/schemas/ResumeData")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="No resume found"
     *     ),
     *     security={{"sanctum": {}}}
     * )
     */
    public function index(): JsonResponse
    {
        $resume = $this->resumeService->getUserResume();

        if (!$resume) {
            return response()->json(null, Response::HTTP_NOT_FOUND);
        }

        return response()->json(ResumeData::from($resume));
    }

    /**
     * @OA\Post(
     *     path="/api/v1/resumes",
     *     summary="Create a new resume",
     *     tags={"Resumes"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/ResumeData")
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Resume created successfully",
     *         @OA\JsonContent(ref="#/components/schemas/ResumeData")
     *     ),
     *     security={{"sanctum": {}}}
     * )
     */
    public function store(ResumeData $data): JsonResponse
    {
        $resume = $this->resumeService->create($data);

        return response()->json(
            ResumeData::from($resume->load('sections')),
            Response::HTTP_CREATED
        );
    }

    /**
     * @OA\Get(
     *     path="/api/v1/resumes/{resume}",
     *     summary="Get a specific resume",
     *     tags={"Resumes"},
     *     @OA\Parameter(
     *         name="resume",
     *         in="path",
     *         required=true,
     *         description="Resume ID",
     *         @OA\Schema(type="integer", format="int64")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Resume details",
     *         @OA\JsonContent(ref="#/components/schemas/ResumeData")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Resume not found"
     *     ),
     *     security={{"sanctum": {}}}
     * )
     */
    public function show(Resume $resume): JsonResponse
    {
        return response()->json(ResumeData::from($resume->load('sections')));
    }

    /**
     * @OA\Put(
     *     path="/api/v1/resumes/{resume}",
     *     summary="Update a resume",
     *     tags={"Resumes"},
     *     @OA\Parameter(
     *         name="resume",
     *         in="path",
     *         required=true,
     *         description="Resume ID",
     *         @OA\Schema(type="integer", format="int64")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/ResumeData")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Resume updated successfully",
     *         @OA\JsonContent(ref="#/components/schemas/ResumeData")
     *     ),
     *     security={{"sanctum": {}}}
     * )
     */
    public function update(ResumeData $data, Resume $resume): JsonResponse
    {
        $resume = $this->resumeService->update($resume, $data);

        return response()->json(ResumeData::from($resume->load('sections')));
    }

    /**
     * @OA\Delete(
     *     path="/api/v1/resumes/{resume}",
     *     summary="Delete a resume",
     *     tags={"Resumes"},
     *     @OA\Parameter(
     *         name="resume",
     *         in="path",
     *         required=true,
     *         description="Resume ID",
     *         @OA\Schema(type="integer", format="int64")
     *     ),
     *     @OA\Response(
     *         response=204,
     *         description="Resume deleted successfully"
     *     ),
     *     security={{"sanctum": {}}}
     * )
     */
    public function destroy(Resume $resume): JsonResponse
    {
        $this->resumeService->delete($resume);

        return response()->json(null, Response::HTTP_NO_CONTENT);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/resumes/{resume}/avatar",
     *     summary="Get a resume's avatar",
     *     tags={"Resumes"},
     *     @OA\Parameter(
     *         name="resume",
     *         in="path",
     *         required=true,
     *         description="Resume ID",
     *         @OA\Schema(type="integer", format="int64")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Resume avatar details",
     *         @OA\JsonContent(ref="#/components/schemas/ResumeAvatarData")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Avatar not found"
     *     ),
     *     security={{"sanctum": {}}}
     * )
     */
    public function getAvatar(Resume $resume): JsonResponse
    {
        $avatar = $resume->getFirstMedia('avatar');

        if (!$avatar) {
            return response()->json(null, Response::HTTP_NOT_FOUND);
        }

        return response()->json(ResumeAvatarData::fromMedia($avatar));
    }

    /**
     * @OA\Post(
     *     path="/api/v1/resumes/{resume}/avatar",
     *     summary="Update a resume's avatar",
     *     tags={"Resumes"},
     *     @OA\Parameter(
     *         name="resume",
     *         in="path",
     *         required=true,
     *         description="Resume ID",
     *         @OA\Schema(type="integer", format="int64")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/UpdateResumeAvatarData")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Avatar updated successfully",
     *         @OA\JsonContent(ref="#/components/schemas/ResumeAvatarData")
     *     ),
     *     security={{"sanctum": {}}}
     * )
     */
    public function updateAvatar(UpdateResumeAvatarData $data, Resume $resume): JsonResponse
    {
        $avatar = $this->resumeService->updateAvatar($resume, $data);

        return response()->json(ResumeAvatarData::fromMedia($avatar));
    }

    /**
     * @OA\Post(
     *     path="/api/v1/resumes/{resume}/avatar/reset",
     *     summary="Reset a resume's avatar to default",
     *     tags={"Resumes"},
     *     @OA\Parameter(
     *         name="resume",
     *         in="path",
     *         required=true,
     *         description="Resume ID",
     *         @OA\Schema(type="integer", format="int64")
     *     ),
     *     @OA\Response(
     *         response=204,
     *         description="Avatar reset successfully"
     *     ),
     *     security={{"sanctum": {}}}
     * )
     */
    public function resetAvatar(Resume $resume): JsonResponse
    {
        $this->resumeService->resetAvatar($resume);

        $avatar = $resume->getFirstMedia('avatar');
        if (!$avatar) {
            return response()->json(['message' => 'Avatar reset, no default avatar set.'], Response::HTTP_OK);
        }

        return response()->json(ResumeAvatarData::fromMedia($avatar));
    }

    /**
     * @OA\Post(
     *     path="/api/v1/resumes/pdf",
     *     summary="Upload a PDF for the authenticated user's resume",
     *     tags={"Resumes"},
     *     @OA\RequestBody(
     *         required=true,
     *         description="PDF file to upload",
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 @OA\Property(
     *                     property="pdf_file",
     *                     description="The PDF file.",
     *                     type="string",
     *                     format="binary"
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="PDF uploaded successfully",
     *         @OA\JsonContent(type="object",
     *              @OA\Property(property="message", type="string", example="PDF uploaded successfully."),
     *              @OA\Property(property="file_url", type="string", format="url"),
     *              @OA\Property(property="resume_id", type="integer")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *      @OA\Response(
     *         response=422,
     *         description="Validation Error"
     *     ),
     *     security={{"sanctum": {}}}
     * )
     */
    public function storePdf(Request $request): JsonResponse
    {
        $request->validate([
            'pdf_file' => 'required|file|mimes:pdf|max:10240',
        ]);

        try {
            // Upload PDF via service (which will handle resume creation if needed)
            $result = $this->resumeService->uploadResumePdf($request->file('pdf_file'));

            // Ensure we have authorization to update this resume
            $this->authorize('update', $result['resume']);

            return response()->json([
                'message' => 'PDF uploaded successfully.',
                'file_url' => $result['media']->getFullUrl(),
                'resume_id' => $result['resume']->id
            ], Response::HTTP_OK);

        } catch (\Exception $e) {
            return response()->json(['message' => 'Failed to upload PDF: ' . $e->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/resumes/pdf",
     *     summary="Get the URL of the stored PDF for the authenticated user's resume",
     *     tags={"Resumes"},
     *     @OA\Response(
     *         response=200,
     *         description="PDF URL retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/ResumePdfResponse")
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="PDF not found for this resume"
     *     ),
     *     security={{"sanctum": {}}}
     * )
     */
    public function showPdf(): JsonResponse
    {
        // Get the user's resume
        $resume = $this->resumeService->getUserResume();

        if (!$resume) {
            return response()->json(['message' => 'No resume found for this user.'], Response::HTTP_NOT_FOUND);
        }

        $this->authorize('view', $resume);

        $mediaItem = $resume->getFirstMedia('resume_pdf');

        if (!$mediaItem) {
            return response()->json(['message' => 'PDF not found for this resume.'], Response::HTTP_NOT_FOUND);
        }

        return response()->json([
            'pdf_url' => $mediaItem->getFullUrl(),
        ], Response::HTTP_OK);
    }

    /**
     * @OA\Delete(
     *     path="/api/v1/resumes/pdf",
     *     summary="Delete the PDF for the authenticated user's resume",
     *     tags={"Resumes"},
     *     @OA\Response(
     *         response=204,
     *         description="PDF deleted successfully"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Resume not found"
     *     ),
     *     security={{"sanctum": {}}}
     * )
     */
    public function deletePdf(): JsonResponse
    {
        // Get the user's resume
        $resume = $this->resumeService->getUserResume();

        if (!$resume) {
            return response()->json(['message' => 'No resume found for this user.'], Response::HTTP_NOT_FOUND);
        }

        $this->authorize('update', $resume);

        // Clear the resume_pdf media collection
        $resume->clearMediaCollection('resume_pdf');

        return response()->json(null, Response::HTTP_NO_CONTENT);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/users/{userId}/resume/pdf",
     *     summary="Get the PDF of another user's resume if authorized",
     *     description="Download a user's PDF resume if the logged in user is a sponsor or parent of the user",
     *     tags={"Resumes"},
     *     @OA\Parameter(
     *         name="userId",
     *         in="path",
     *         required=true,
     *         description="ID of the user whose resume PDF should be downloaded",
     *         @OA\Schema(type="integer", format="int64")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="PDF URL retrieved successfully",
     *         @OA\JsonContent(type="object",
     *              @OA\Property(property="pdf_url", type="string", format="url", example="http://localhost/storage/1/resume.pdf"))
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Not authorized to access this resume"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="PDF not found or user not found"
     *     ),
     *     security={{"sanctum": {}}}
     * )
     */
    public function downloadUserPdf(int $userId): JsonResponse
    {
        // Get the current logged in user
        /** @var \App\Models\User $currentUser */
        $currentUser = Auth::user();

        // Get the target user
        $targetUser = \App\Models\User::find($userId);

        if (!$targetUser) {
            return response()->json(['message' => 'User not found.'], Response::HTTP_NOT_FOUND);
        }

        // Authorization check:
        // 1. Check if the current user is a parent of the target user
        $isParent = $targetUser->parent_id === $currentUser->id;

        // 2. Check if the current user is a sponsor
        $isSponsor = $currentUser->profile_type === \App\Enums\ProfileType::SPONSOR;

        // If neither condition is true, deny access
        if (!$isParent && !$isSponsor) {
            return response()->json(['message' => 'You are not authorized to access this resume.'], Response::HTTP_FORBIDDEN);
        }

        // Get the resume for the target user
        $resume = \App\Models\Resume::where('user_id', $targetUser->id)->first();

        if (!$resume) {
            return response()->json(['message' => 'No resume found for this user.'], Response::HTTP_NOT_FOUND);
        }

        $mediaItem = $resume->getFirstMedia('resume_pdf');

        if (!$mediaItem) {
            return response()->json(['message' => 'PDF not found for this resume.'], Response::HTTP_NOT_FOUND);
        }

        return response()->json([
            'pdf_url' => $mediaItem->getFullUrl(),
        ], Response::HTTP_OK);
    }
}
