<?php

namespace App\Http\Requests\Nomination;

use App\Data\Nomination\NominationData;
use Illuminate\Foundation\Http\FormRequest;

class BulkNominationRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'nominations' => ['required', 'array', 'min:1', 'max:100'],
            'nominations.*.email' => ['required', 'email'],
            'nominations.*.first_name' => ['required', 'string', 'max:100'],
            'nominations.*.last_name' => ['required', 'string', 'max:100'],
            'nominations.*.nominator_email' => ['required', 'email'],
            'nominations.*.nominator_first_name' => ['required', 'string', 'max:100'],
            'nominations.*.nominator_last_name' => ['required', 'string', 'max:100'],
            'nominations.*.school_name' => ['required', 'string', 'max:200'],
            'nominations.*.sport' => ['required', 'string', 'max:100'],
            'nominations.*.relationship' => ['required', 'string', 'max:50'],
            'nominations.*.note' => ['nullable', 'string'],
        ];
    }

    public function toData(): array
    {
        return collect($this->validated('nominations'))
            ->map(fn (array $nomination) => NominationData::from($nomination))
            ->all();
    }
}
