<?php

namespace App\Http\Requests\Nomination;

use Illuminate\Foundation\Http\FormRequest;

class PublicNominationRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'email' => 'required|email|unique:users,email',
            'nominator_id' => 'required|exists:users,id',
            'source' => 'required|in:public_form,coach_nomination',
            'metadata' => 'sometimes|array',
        ];
    }
}
