<?php

namespace App\Http\Requests\School;

use Illuminate\Foundation\Http\FormRequest;
use Spa<PERSON>\LaravelData\Data;

class SearchSchoolRequest extends Data
{
    public function __construct(
        public readonly ?string $query = null,
        public readonly ?int $county_id = null,
        public readonly ?string $state_code = null,
    ) {}

    public static function rules(): array
    {
        return [
            'query' => ['nullable', 'string', 'max:100'],
            'county_id' => ['nullable', 'integer', 'exists:counties,id'],
            'state_code' => ['nullable', 'string', 'exists:states,code'],
        ];
    }
}
