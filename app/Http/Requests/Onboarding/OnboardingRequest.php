<?php

namespace App\Http\Requests\Onboarding;

use Illuminate\Foundation\Http\FormRequest;

class OnboardingRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'token' => 'required|string|exists:system_invites,token',
            'password' => 'required|min:8|confirmed',
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'school_id' => 'required|exists:schools,id',
            'graduation_year' => 'required|date_format:Y|after:now',
            'sports' => 'required|array|min:1',
            'sports.*.type' => 'required|exists:sports,id',
            'gpa' => 'required|numeric|between:0,5.0',
            'class_rank' => 'required|string',
            'activities' => 'array',
            'activities.*.title' => 'required|string',
            'activities.*.date_range' => 'required|string',
            'activities.*.description' => 'nullable|string',
            'work_experience' => 'array',
            'work_experience.*.title' => 'required|string',
            'work_experience.*.company' => 'required|string',
            'work_experience.*.date_range' => 'required|string',
            'work_experience.*.description' => 'nullable|string',
        ];
    }
}
