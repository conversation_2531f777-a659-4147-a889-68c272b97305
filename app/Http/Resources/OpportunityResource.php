<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @OA\Schema(
 *     schema="OpportunityResource",
 *     title="Opportunity Resource",
 *     description="Opportunity resource representation",
 *     @OA\Property(property="data", type="object", ref="#/components/schemas/OpportunityResourceData")
 * )
 *
 * @OA\Schema(
 *     schema="OpportunityResourceData",
 *     title="Opportunity Resource Data",
 *     description="Opportunity resource data representation",
 *     @OA\Property(property="id", type="integer", example=770),
 *     @OA\Property(property="title", type="string", example="Certificate in Digital Marketing"),
 *     @OA\Property(property="description", type="string", nullable=true),
 *     @OA\Property(property="details", type="string", nullable=true),
 *     @OA\Property(property="apply_url", type="string", format="uri", nullable=true),
 *     @OA\Property(property="qualifications", type="string", nullable=true),
 *     @OA\Property(property="responsibilities", type="string", nullable=true),
 *     @OA\Property(property="benefits", type="string", nullable=true),
 *     @OA\Property(property="location", type="integer", nullable=true),
 *     @OA\Property(property="location_display", type="string", nullable=true, example="Miami, FL"),
 *     @OA\Property(property="city", type="string", nullable=true, example="Miami"),
 *     @OA\Property(property="state_code", type="string", nullable=true, example="FL"),
 *     @OA\Property(property="state_name", type="string", nullable=true),
 *     @OA\Property(property="organization_name", type="string", nullable=true),
 *     @OA\Property(property="organization_logo", type="string", format="uri", nullable=true),
 *     @OA\Property(
 *         property="industries",
 *         type="array",
 *         description="DEPRECATED - Use interests instead. This field will be removed in a future version.",
 *         deprecated=true,
 *         @OA\Items(type="object")
 *     ),
 *     @OA\Property(
 *         property="interests",
 *         type="array",
 *         description="User interests related to this opportunity. Use this field instead of the deprecated industries field.",
 *         @OA\Items(ref="#/components/schemas/OpportunityInterest")
 *     ),
 *     @OA\Property(property="type", type="string", example="education", enum={"education", "job", "internship", "volunteer"}),
 *     @OA\Property(property="subtype", type="string", example="degree_program", nullable=true),
 *     @OA\Property(property="status", type="string", example="listed", enum={"draft", "listed", "unlisted", "archived"}),
 *     @OA\Property(property="location_type", type="string", example="onsite", enum={"onsite", "remote", "hybrid"}),
 *     @OA\Property(property="term", type="string", nullable=true, example="indefinite"),
 *     @OA\Property(property="is_featured", type="boolean"),
 *     @OA\Property(property="visible_start_date", type="string", format="date-time", nullable=true),
 *     @OA\Property(property="visible_end_date", type="string", format="date-time", nullable=true),
 *     @OA\Property(property="created_at", type="string", format="date-time"),
 *     @OA\Property(property="updated_at", type="string", format="date-time"),
 *     @OA\Property(
 *         property="organization",
 *         nullable=true,
 *         allOf={
 *             @OA\Schema(ref="#/components/schemas/OrganizationSimple")
 *         }
 *     )
 *
 * )
 *
 * @OA\Schema(
 *     schema="OpportunityInterest",
 *     title="Opportunity Interest",
 *     description="Interest data specific to opportunities, including pivot data",
 *     @OA\Property(property="id", type="integer"),
 *     @OA\Property(property="name", type="string"),
 *     @OA\Property(property="created_at", type="string", format="date-time"),
 *     @OA\Property(property="updated_at", type="string", format="date-time"),
 *     @OA\Property(property="icon", type="string", nullable=true),
 *     @OA\Property(
 *         property="pivot",
 *         type="object",
 *         @OA\Property(property="opportunity_id", type="integer"),
 *         @OA\Property(property="interest_id", type="integer"),
 *         @OA\Property(property="created_at", type="string", format="date-time"),
 *         @OA\Property(property="updated_at", type="string", format="date-time")
 *     )
 * )
 *
 * @OA\Schema(
 *     schema="OrganizationSimple",
 *     title="Organization Simple",
 *     description="Simplified organization data included in opportunity response",
 *     @OA\Property(property="id", type="integer"),
 *     @OA\Property(property="name", type="string"),
 *     @OA\Property(property="website", type="string", format="uri", nullable=true),
 *     @OA\Property(property="about", type="string", nullable=true)
 * )
 */
class OpportunityResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'details' => $this->details,
            'apply_url' => $this->apply_url,
            'qualifications' => $this->qualifications,
            'responsibilities' => $this->responsibilities,
            'benefits' => $this->benefits,
            'location' => $this->location_coordinate_id ?? null,
            'location_display' => $this->city . ', ' . $this->state_code ?? null,
            'city' => $this->city,
            'state_code' => $this->state_code,
            'state_name' => $this->state_name,
            'organization_name' => $this->organization_name,
            'organization_logo' => $this->organization?->getFirstMediaUrl('logo', 'thumbnail'),
            'industries' => $this->industries,
            'interests' => $this->interests,
            'type' => $this->type,
            'subtype' => $this->subtype,
            'status' => $this->status,
            'location_type' => $this->location_type,
            'term' => $this->term?->value,
            'is_featured' => $this->is_featured,
            'visible_start_date' => $this->visible_start_date,
            'visible_end_date' => $this->visible_end_date,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,

            // Include relationships when available
            'organization' => $this->when($this->relationLoaded('organization'), fn() => [
                'id' => $this->organization->id,
                'name' => $this->organization->name,
                'website' => $this->organization->website,
                'about' => $this->organization->about,
                // Add other organization fields as needed
            ]),
        ];
    }
}
