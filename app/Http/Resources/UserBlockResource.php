<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserBlockResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'blocker_id' => $this->blocker_id,
            'blocked_id' => $this->blocked_id,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'blocked' => [
                'id' => $this->when(isset($this->blocked), $this->blocked->id ?? null),
                'first_name' => $this->when(isset($this->blocked), $this->blocked->first_name ?? null),
                'last_name' => $this->when(isset($this->blocked), $this->blocked->last_name ?? null),
                'profile_image_url' => $this->when(isset($this->blocked), $this->blocked->profile_image_url ?? null),
            ],
        ];
    }
}
