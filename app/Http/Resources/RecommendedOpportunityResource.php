<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @OA\Schema(
 *     schema="RecommendedOpportunityResource",
 *     title="Recommended Opportunity Resource",
 *     description="Resource representing a recommended opportunity for dashboard display",
 *     @OA\Property(property="id", type="integer", description="Opportunity ID"),
 *     @OA\Property(property="title", type="string", description="Opportunity title"),
 *     @OA\Property(property="description", type="string", description="Short description of the opportunity"),
 *     @OA\Property(property="details", type="string", description="Detailed description of the opportunity"),
 *     @OA\Property(property="location", type="string", description="Location of the opportunity (City, State)"),
 *     @OA\Property(property="state_code", type="string", description="State code (e.g., CA, NY)"),
 *     @OA\Property(property="location_type", type="string", enum={"onsite", "remote", "hybrid"}, description="Location type"),
 *     @OA\Property(property="subtype", type="string", description="Opportunity subtype"),
 *     @OA\Property(property="organization_name", type="string", description="Name of the organization"),
 *     @OA\Property(property="organization_logo", type="string", format="uri", description="URL to organization logo"),
 *     @OA\Property(property="interests", type="array", @OA\Items(ref="#/components/schemas/Interest"), description="List of related interest names"),
 *     @OA\Property(property="term", type="string", description="Time commitment term")
 * )
 */
class RecommendedOpportunityResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'details' => $this->details,
            'location' => $this->location,
            'state_code' => $this->state_code,
            'location_type' => $this->location_type?->value,
            'subtype' => $this->subtype?->value,
            'organization_name' => $this->organization?->name,
            'organization_logo' => $this->organization?->getFirstMediaUrl('logo', 'thumbnail'),
            'interests' => $this->whenLoaded('interests', function () {
                return $this->interests->toArray();
            }, []),
            'term' => $this->term?->value,
        ];
    }
}
