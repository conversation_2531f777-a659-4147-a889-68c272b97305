<?php

namespace App\Http\Middleware;

use App\Enums\ProfileType;
use Closure;
use Illuminate\Auth\AuthenticationException;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;

class EnsureSponsorUser
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     *
     * @throws \Illuminate\Auth\AuthenticationException
     * @throws \Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException
     */
    public function handle($request, Closure $next)
    {
        $user = $request->user();

        // Check if the user has a sponsor profile
        if ($user->profile_type !== ProfileType::SPONSOR) {
            throw new AccessDeniedHttpException('Access denied. Sponsor profile required.');
        }

        return $next($request);
    }
}
