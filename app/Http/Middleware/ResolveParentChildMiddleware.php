<?php

namespace App\Http\Middleware;

use App\Models\User;
use App\Services\ParentChildResolverService;
use Closure;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class ResolveParentChildMiddleware
{
    /**
     * Create a new middleware instance.
     *
     * @param ParentChildResolverService $parentChildResolver
     */
    public function __construct(
        private readonly ParentChildResolverService $parentChildResolver
    ) {}

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $abilityGate
     * @return mixed
     */
    public function handle(Request $request, Closure $next, ?string $abilityGate = null)
    {
        /** @var User $user */
        $user = $request->user();

        try {
            // Get the effective user
            if ($user && $user->profile_type === \App\Enums\ProfileType::PARENT) {
                try {
                    $effectiveUser = $this->parentChildResolver->getEffectiveUser($user);
                    $request->attributes->set('effectiveUser', $effectiveUser);
                } catch (ModelNotFoundException $e) {
                    // Log that a parent without a linked child attempted to access a protected resource
                    Log::warning('Parent without linked child attempted to access resource', [
                        'parent_id' => $user->id,
                        'path' => $request->path()
                    ]);

                    // For routes like profile, network, etc. where child content is expected but missing,
                    // we could redirect to an onboarding flow or return an appropriate error
                    if ($abilityGate) {
                        return response()->json([
                            'message' => 'You need to link your account to your child to access this resource',
                            'error' => 'MISSING_CHILD_LINK'
                        ], Response::HTTP_FORBIDDEN);
                    }
                }
            }

            return $next($request);
        } catch (\Throwable $e) {
            Log::error('Error in ResolveParentChildMiddleware', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw $e;
        }
    }
}
