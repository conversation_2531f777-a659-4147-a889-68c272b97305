<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class VerifyWebhookToken
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $token = $request->query('token') ?? $request->header('X-Webhook-Token');
        $expectedToken = config('services.jotform.webhook_token');

        if (empty($expectedToken)) {
            return response()->json([
                'error' => 'Webhook token not configured'
            ], 500);
        }

        if (empty($token) || !hash_equals($expectedToken, $token)) {
            return response()->json([
                'error' => 'Invalid webhook token'
            ], 401);
        }

        return $next($request);
    }
}
