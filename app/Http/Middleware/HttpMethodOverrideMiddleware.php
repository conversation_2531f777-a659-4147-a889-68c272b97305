<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class HttpMethodOverrideMiddleware
{
    /**
     * The list of acceptable method overrides
     *
     * @var array<string>
     */
    protected $acceptableMethods = ['PUT', 'PATCH', 'DELETE'];

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): mixed
    {
        Log::info('HTTP Method Override Middleware', [
            'request_headers' => $request->headers->all()
        ]);

        // Check if the request has the X-HTTP-Method-Override header
        if ($request->hasHeader('X-HTTP-Method-Override')) {
            $method = strtoupper($request->header('X-HTTP-Method-Override'));

            // Only allow PUT, PATCH or DELETE overrides
            if (in_array($method, $this->acceptableMethods)) {
                // Store the original method for logging purposes
                $originalMethod = $request->method();

                // Override the request method
                $request->setMethod($method);

                // Log the method override (optional, can be removed in production)
                Log::info('HTTP Method Override', [
                    'original' => $originalMethod,
                    'override' => $method,
                    'uri' => $request->getRequestUri(),
                    'user_id' => $request->user()?->id ?? 'guest'
                ]);
            }
        }

        Log::info('Raw request information', [
            'all_headers' => getallheaders(),
            'server_vars' => $_SERVER
        ]);

        return $next($request);
    }
}
