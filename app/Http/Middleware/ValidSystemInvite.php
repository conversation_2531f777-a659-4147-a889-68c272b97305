<?php

namespace App\Http\Middleware;

use App\Models\SystemInvite;
use Closure;
use Illuminate\Http\Request;

class ValidSystemInvite
{
    public function handle(Request $request, Closure $next)
    {
        $request->validate([
            'token' => ['required', 'string', 'size:64'],
        ]);

        $invite = SystemInvite::query()
            ->where('token', $request->input('token'))
            ->where('status', 'pending')
            ->first();

        if (!$invite) {
            abort(404, 'Invalid or expired invitation.');
        }

        $request->merge(['system_invite' => $invite]);

        return $next($request);
    }
}
