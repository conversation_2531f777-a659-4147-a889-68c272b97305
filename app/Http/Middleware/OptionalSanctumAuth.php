<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Lara<PERSON>\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful;
use Symfony\Component\HttpFoundation\Response;

class OptionalSanctumAuth
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // First, handle Sanctum's frontend request detection
        $ensureFrontendStateful = new EnsureFrontendRequestsAreStateful();

        return $ensureFrontendStateful->handle($request, function ($request) use ($next) {
            // Try to authenticate with Sanct<PERSON>, but don't fail if no token
            $guards = ['sanctum'];

            foreach ($guards as $guard) {
                if (auth()->guard($guard)->check()) {
                    auth()->shouldUse($guard);
                    break;
                }
            }

            // Continue regardless of authentication status
            return $next($request);
        });
    }
}
