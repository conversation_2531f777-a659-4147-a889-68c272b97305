<?php

namespace App\Repositories;

use App\Models\UserBlock;
use Illuminate\Pagination\LengthAwarePaginator;

class UserBlockRepository
{
    /**
     * Block a user.
     *
     * @param int $blockerId
     * @param int $blockedId
     * @return UserBlock
     */
    public function blockUser(int $blockerId, int $blockedId): UserBlock
    {
        return UserBlock::firstOrCreate([
            'blocker_id' => $blockerId,
            'blocked_id' => $blockedId,
        ]);
    }

    /**
     * Unblock a user.
     *
     * @param int $blockerId
     * @param int $blockedId
     * @return bool
     */
    public function unblockUser(int $blockerId, int $blockedId): bool
    {
        return (bool) UserBlock::query()
            ->where('blocker_id', $blockerId)
            ->where('blocked_id', $blockedId)
            ->delete();
    }

    /**
     * Check if a user is blocked.
     *
     * @param int $userId
     * @param int $otherUserId
     * @return bool
     */
    public function isBlocked(int $userId, int $otherUserId): bool
    {
        return UserBlock::query()
            ->where(function ($query) use ($userId, $otherUserId) {
                $query->where('blocker_id', $userId)
                      ->where('blocked_id', $otherUserId);
            })
            ->orWhere(function ($query) use ($userId, $otherUserId) {
                $query->where('blocker_id', $otherUserId)
                      ->where('blocked_id', $userId);
            })
            ->exists();
    }

    /**
     * Get all users blocked by a user.
     *
     * @param int $userId
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getBlockedUsers(int $userId, int $perPage = 15): LengthAwarePaginator
    {
        return UserBlock::query()
            ->where('blocker_id', $userId)
            ->with('blocked')
            ->paginate($perPage);
    }
}
