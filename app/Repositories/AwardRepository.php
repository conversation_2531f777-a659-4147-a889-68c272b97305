<?php

namespace App\Repositories;

use App\Enums\AwardType;
use App\Models\Award;
use App\Models\User;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class AwardRepository
{
    /**
     * Get a paginated list of awards with optional filtering.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAwards(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Award::query();

        // Apply filters
        if (!empty($filters['name'])) {
            $query->where('name', 'ilike', "%{$filters['name']}%");
        }

        if (isset($filters['year'])) {
            $query->where('year', $filters['year']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        if (isset($filters['region_id'])) {
            $query->where('region_id', $filters['region_id']);
        }

        if (isset($filters['market_id'])) {
            $query->where('market_id', $filters['market_id']);
        }

        if (isset($filters['state_id'])) {
            $query->where('state_id', $filters['state_id']);
        }

        if (isset($filters['subregion_id'])) {
            $query->where('subregion_id', $filters['subregion_id']);
        }

        return $query->orderBy('year', 'desc')
            ->orderBy('name', 'asc')
            ->paginate($perPage);
    }

    /**
     * Find an award by ID.
     *
     * @param int $id
     * @return Award|null
     */
    public function findById(int $id): ?Award
    {
        return Award::query()->findOrFail($id);
    }

    /**
     * Create a new award.
     *
     * @param array $data
     * @return Award
     */
    public function create(array $data): Award
    {
        return Award::query()->create($data);
    }

    /**
     * Update an existing award.
     *
     * @param Award $award
     * @param array $data
     * @return Award
     */
    public function update(Award $award, array $data): Award
    {
        $award->update($data);
        return $award;
    }

    /**
     * Soft delete an award.
     *
     * @param Award $award
     * @return bool
     */
    public function delete(Award $award): bool
    {
        return $award->delete();
    }

    /**
     * Get awards for a specific user.
     *
     * @param User $user
     * @param array $filters
     * @return Collection
     */
    public function getAwardsForUser(User $user, array $filters = []): Collection
    {
        $query = Award::query()
            ->whereHas('winners', function (Builder $query) use ($user) {
                $query->where('user_id', $user->id);
            });

        // Apply filters
        if (isset($filters['is_finalist'])) {
            $query->whereHas('winners', function (Builder $query) use ($user, $filters) {
                $query->where('user_id', $user->id)
                    ->where('is_finalist', $filters['is_finalist']);
            });
        }

        if (isset($filters['is_winner'])) {
            $query->whereHas('winners', function (Builder $query) use ($user, $filters) {
                $query->where('user_id', $user->id)
                    ->where('is_winner', $filters['is_winner']);
            });
        }

        if (isset($filters['year'])) {
            $query->where('year', $filters['year']);
        }

        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        if (isset($filters['region_id'])) {
            $query->where('region_id', $filters['region_id']);
        }

        if (isset($filters['market_id'])) {
            $query->where('market_id', $filters['market_id']);
        }

        if (isset($filters['state_id'])) {
            $query->where('state_id', $filters['state_id']);
        }

        if (isset($filters['subregion_id'])) {
            $query->where('subregion_id', $filters['subregion_id']);
        }

        return $query->orderBy('year', 'desc')
            ->orderBy('name', 'asc')
            ->get();
    }

    /**
     * Check if an award with the same name exists in the given year and region/market/subregion.
     *
     * @param string $name
     * @param int $year
     * @param string $type
     * @param array $geographicalData
     * @param int|null $excludeId
     * @return bool
     */
    public function isDuplicate(
        string $name,
        int $year,
        string $type,
        array $geographicalData,
        ?int $excludeId = null
    ): bool {
        $query = Award::query()
            ->where('name', $name)
            ->where('year', $year)
            ->where('type', $type);

        // Add appropriate geographical constraints based on type
        switch ($type) {
            case AwardType::REGIONAL->value:
                if (isset($geographicalData['region_id'])) {
                    $query->where('region_id', $geographicalData['region_id']);
                }
                break;
            case AwardType::MARKET->value:
                if (isset($geographicalData['market_id'])) {
                    $query->where('market_id', $geographicalData['market_id']);
                }
                break;
            case AwardType::SUBREGIONAL->value:
                if (isset($geographicalData['subregion_id'])) {
                    $query->where('subregion_id', $geographicalData['subregion_id']);
                }
                break;
        }

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Activate an award.
     *
     * @param Award $award
     * @return Award
     */
    public function activate(Award $award): Award
    {
        $award->is_active = true;
        $award->save();

        return $award;
    }

    /**
     * Deactivate an award.
     *
     * @param Award $award
     * @return Award
     */
    public function deactivate(Award $award): Award
    {
        $award->is_active = false;
        $award->save();

        return $award;
    }

    /**
     * Get awards by geographic area.
     *
     * @param string $areaType
     * @param int|string $areaId
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAwardsByGeographicArea(
        string $areaType,
        $areaId,
        array $filters = [],
        int $perPage = 15
    ): LengthAwarePaginator {
        $query = Award::query();

        switch ($areaType) {
            case 'region':
                $query->where('region_id', $areaId);
                break;
            case 'market':
                $query->where('market_id', $areaId);
                break;
            case 'subregion':
                $query->where('subregion_id', $areaId);
                break;
            case 'state':
                $query->where('state_id', $areaId);
                break;
            default:
                break;
        }

        // Apply additional filters
        if (!empty($filters['name'])) {
            $query->where('name', 'ilike', "%{$filters['name']}%");
        }

        if (isset($filters['year'])) {
            $query->where('year', $filters['year']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        return $query->orderBy('year', 'desc')
            ->orderBy('name', 'asc')
            ->paginate($perPage);
    }
}
