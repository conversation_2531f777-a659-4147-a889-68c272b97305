<?php

namespace App\Repositories;

use App\Data\XFactor\ModuleBrowseRequest;
use App\Models\Module;
use App\Models\Topic;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class XFactorModuleRepository
{
    public function getModulesByTopic(Topic $topic, ?User $user = null): array
    {
        $modules = $topic->modules()
            ->published()
            ->with([
                'course',
                'topics',
                'tests.questions' => function ($query) {
                    $query->select('id', 'test_id');
                },
                'tests.attempts' => function ($query) use ($user) {
                    $query->where('user_id', $user?->id)
                        ->select('id', 'test_id', 'user_id', 'completed_at')
                        ->with(['questionResponses' => function ($query) {
                            $query->select('id', 'test_attempt_id');
                        }]);
                }
            ])
            ->get();

        return [
            'topic' => $topic,
            'modules' => $modules,
        ];
    }

    public function getAllModulesByTopics(User $user): Collection
    {
        $topics = Topic::with([
            'modules' => function ($query) use ($user) {
                $query
                    ->with([
                        'topics',
                        'test.lastCompletedAttempt' => fn($q) => $q->where('user_id', $user->id)
                    ])
                    ->whereDoesntHave('courses')
                    ->published()
                    ->limit(10);
            },
        ])->get();

        return $topics;
    }

    public function searchModules(ModuleBrowseRequest $request, ?User $user = null): LengthAwarePaginator
    {
        $query = Module::query()
            ->published()
            ->with(['course', 'topics']);

        if ($request->search) {
            $query->where(function (Builder $query) use ($request) {
                $query->where('name', 'ilike', "%{$request->search}%")
                    ->orWhereHas('topics', function (Builder $query) use ($request) {
                        $query->where('name', 'ilike', "%{$request->search}%");
                    });
            });
        }

        if ($request->topics) {
            $query->whereHas('topics', function (Builder $query) use ($request) {
                $query->whereIn('id', $request->topics);
            });
        }

        if ($request->standalone !== null) {
            if ($request->standalone) {
                $query->whereNull('course_id');
            } else {
                $query->whereNotNull('course_id');
            }
        }

        $query->when($request->sort === 'newest', fn($q) => $q->latest())
            ->when($request->sort === 'title', fn($q) => $q->orderBy('name'))
            ->when($request->sort === 'duration', fn($q) => $q->orderBy('duration_minutes'));

        return $query->paginate(
            perPage: $request->perPage,
            page: $request->page,
        );
    }

    public function getModuleProgressData(User $user, array $moduleIds): array
    {
        $progress = $user->moduleProgress()
            ->whereIn('module_id', $moduleIds)
            ->get();

        $data = [];
        foreach ($progress as $item) {
            $data[$item->module_id] = [
                'status' => $item->status,
                'progress' => $item->progress,
                'completed_at' => $item->completed_at?->toISOString(),
            ];
        }

        return $data;
    }
}
