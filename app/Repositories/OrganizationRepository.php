<?php

namespace App\Repositories;

use App\Models\Organization;

class OrganizationRepository
{
    public function __construct(
        private readonly Organization $model
    ) {
    }

    /**
     * Find an organization by its ID
     */
    public function find(int $id): ?Organization
    {
        return $this->model::query()->find($id);
    }

    /**
     * Update an organization's details
     */
    public function update(int $id, array $data): Organization
    {
        $organization = $this->model::query()->findOrFail($id);
        $organization->update($data);
        return $organization->fresh();
    }
}
