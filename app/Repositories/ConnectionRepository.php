<?php

namespace App\Repositories;

use App\Enums\ConnectionStatus;
use App\Models\Connection;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Log;

class ConnectionRepository
{
    /**
     * Create a new connection request.
     *
     * @param int $requesterId
     * @param int $recipientId
     * @return Connection
     */
    public function createRequest(int $requesterId, int $recipientId): Connection
    {
        return Connection::firstOrCreate(
            [
                'requester_id' => $requesterId,
                'recipient_id' => $recipientId,
            ],
            [
                'status' => ConnectionStatus::PENDING,
            ]
        );
    }

    /**
     * Accept a connection request.
     *
     * @param int $connectionId
     * @param int $userId
     * @return bool
     */
    public function acceptRequest(int $connectionId, int $userId): bool
    {
        $connection = Connection::where('id', $connectionId)
            ->where('recipient_id', $userId)
            ->where('status', ConnectionStatus::PENDING)
            ->first();

        if (!$connection) {
            return false;
        }

        $connection->status = ConnectionStatus::ACCEPTED;
        return $connection->save();
    }

    /**
     * Reject a connection request.
     *
     * @param int $connectionId
     * @param int $userId
     * @return bool
     */
    public function rejectRequest(int $connectionId, int $userId): bool
    {
        $connection = Connection::where('id', $connectionId)
            ->where('recipient_id', $userId)
            ->where('status', ConnectionStatus::PENDING)
            ->first();

        if (!$connection) {
            return false;
        }

        $connection->status = ConnectionStatus::REJECTED;
        return $connection->save();
    }

    /**
     * Block a connection (soft delete).
     *
     * @param int $userId
     * @param int $blockedUserId
     * @return bool
     */
    public function blockConnection(int $userId, int $blockedUserId): bool
    {
        $connection = Connection::betweenUsers($userId, $blockedUserId)->first();

        if (!$connection) {
            // Create a rejected connection that is immediately soft deleted
            $connection = Connection::create([
                'requester_id' => $userId,
                'recipient_id' => $blockedUserId,
                'status' => ConnectionStatus::REJECTED,
            ]);
        } else {
            // Update the status to rejected before soft-deleting
            $connection->status = ConnectionStatus::REJECTED;
            $connection->save();
        }

        return $connection->delete();
    }

    /**
     * Unblock a connection (restore from soft delete).
     *
     * @param int $userId
     * @param int $blockedUserId
     * @return bool
     */
    public function unblockConnection(int $userId, int $blockedUserId): bool
    {
        $connection = Connection::betweenUsers($userId, $blockedUserId)
            ->withTrashed()
            ->first();

        if (!$connection) {
            Log::warning('ConnectionRepository::unblockConnection - Connection not found', [
                'userId' => $userId,
                'blockedUserId' => $blockedUserId
            ]);
            return false;
        }

        // When unblocking, set the status to PENDING
        // This requires the users to re-establish their connection
        $connection->status = ConnectionStatus::PENDING;
        $connection->save();

        return $connection->restore();
    }

    /**
     * Check if a connection exists between two users.
     *
     * @param int $userOneId
     * @param int $userTwoId
     * @return bool
     */
    public function connectionExists(int $userOneId, int $userTwoId): bool
    {
        return Connection::betweenUsers($userOneId, $userTwoId)->exists();
    }

    /**
     * Check if users are connected.
     *
     * @param int $userOneId
     * @param int $userTwoId
     * @return bool
     */
    public function areConnected(int $userOneId, int $userTwoId): bool
    {
        return Connection::betweenUsers($userOneId, $userTwoId)
            ->accepted()
            ->exists();
    }

    /**
     * Check if a connection is pending between two users.
     *
     * @param int $userOneId
     * @param int $userTwoId
     * @return bool
     */
    public function isPending(int $userOneId, int $userTwoId): bool
    {
        return Connection::betweenUsers($userOneId, $userTwoId)
            ->pending()
            ->exists();
    }

    /**
     * Check if a user is blocked.
     *
     * @param int $userOneId
     * @param int $userTwoId
     * @return bool
     */
    public function isBlocked(int $userOneId, int $userTwoId): bool
    {
        return Connection::betweenUsers($userOneId, $userTwoId)
            ->withTrashed()
            ->whereNotNull('deleted_at')
            ->exists();
    }

    /**
     * Get all connections for a user.
     *
     * @param int $userId
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getConnections(int $userId, int $perPage = 15): LengthAwarePaginator
    {
        return Connection::forUser($userId)
            ->with(['requester', 'recipient'])
            ->paginate($perPage);
    }

    /**
     * Get all accepted connections for a user.
     *
     * @param int $userId
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAcceptedConnections(int $userId, int $perPage = 15): LengthAwarePaginator
    {
        return Connection::forUser($userId)
            ->accepted()
            ->with(['requester', 'recipient'])
            ->paginate($perPage);
    }

    /**
     * Get all pending connection requests for a user.
     *
     * @param int $userId
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getPendingRequests(int $userId, int $perPage = 15): LengthAwarePaginator
    {
        return Connection::where('recipient_id', $userId)
            ->pending()
            ->with('requester')
            ->paginate($perPage);
    }

    /**
     * Get a specific connection between two users.
     *
     * @param int $userOneId
     * @param int $userTwoId
     * @return Connection|null
     */
    public function getConnection(int $userOneId, int $userTwoId): ?Connection
    {
        return Connection::betweenUsers($userOneId, $userTwoId)
            ->with(['requester', 'recipient'])
            ->first();
    }
}
