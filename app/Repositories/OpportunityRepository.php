<?php

namespace App\Repositories;

use App\Enums\OpportunityStatus;
use App\Models\LocationCoordinate;
use App\Models\Opportunity;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;

class OpportunityRepository
{
    /**
     * Get opportunities for a specific organization with optional filtering.
     *
     * @param int $organizationId
     * @param string|null $search
     * @param string|null $status
     * @param array|null $industries
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getOpportunitiesForOrganization(
        int $organizationId,
        ?string $search = null,
        ?string $status = null,
        ?array $industries = null,
        int $perPage = 15
    ): LengthAwarePaginator {
        $query = Opportunity::query()
            ->where('organization_id', $organizationId)
            ->with(['industries', 'organization', 'locationCoordinate']);

        if ($search) {
            $query->where(function (Builder $query) use ($search) {
                $query->where('title', 'ilike', "%{$search}%")
                    ->orWhere('description', 'ilike', "%{$search}%");
            });
        }

        if ($status) {
            $query->where('status', $status);
        }

        if ($industries && count($industries) > 0) {
            $query->whereHas('industries', function (Builder $query) use ($industries) {
                $query->whereIn('industries.id', $industries);
            });
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Find an opportunity by ID.
     *
     * @param int $id
     * @return Opportunity|null
     */
    public function findById(int $id): ?Opportunity
    {
        return Opportunity::query()
            ->with(['industries', 'organization', 'locationCoordinate'])
            ->find($id);
    }

    /**
     * Process location data in the opportunity data array.
     * If location_coordinate_id is provided, populate city and state_code from the LocationCoordinate.
     *
     * @param array $data
     * @return array
     */
    protected function processLocationData(array $data): array
    {
        // If location_coordinate_id is provided, look up the location and set city/state
        if (!empty($data['location_coordinate_id'])) {
            $locationCoordinate = LocationCoordinate::find($data['location_coordinate_id']);

            if ($locationCoordinate) {
                $data['city'] = $locationCoordinate->city;
                $data['state_code'] = $locationCoordinate->state_code;
            } else {
                \Illuminate\Support\Facades\Log::warning("Could not find location coordinate with ID: {$data['location_coordinate_id']}");
            }
        }

        return $data;
    }

    /**
     * Create a new opportunity.
     *
     * @param array $data
     * @return Opportunity
     */
    public function create(array $data): Opportunity
    {
        // Process location data
        $data = $this->processLocationData($data);

        // Process location data
        $data = $this->processLocationData($data);

        $opportunity = Opportunity::query()->create($data);
        return $opportunity;
    }

    /**
     * Update an existing opportunity.
     *
     * @param Opportunity $opportunity
     * @param array $data
     * @return Opportunity
     */
    public function update(Opportunity $opportunity, array $data): Opportunity
    {
        // Process location data
        $data = $this->processLocationData($data);

        $opportunity->update($data);
        return $opportunity;
    }

    /**
     * Delete an opportunity.
     *
     * @param Opportunity $opportunity
     * @return bool
     */
    public function delete(Opportunity $opportunity): bool
    {
        return $opportunity->delete();
    }

    /**
     * Duplicate an opportunity.
     *
     * @param Opportunity $opportunity
     * @return Opportunity
     */
    public function duplicate(Opportunity $opportunity): Opportunity
    {
        $newOpportunity = $opportunity->replicate();
        $newOpportunity->title = "{$opportunity->title} (Copy)";
        $newOpportunity->status = OpportunityStatus::UNLISTED;
        $newOpportunity->save();

        // Copy industries
        $industryIds = $opportunity->industries->pluck('id')->toArray();
        $this->attachIndustries($newOpportunity, $industryIds);

        // Copy interests
        $interestIds = $opportunity->interests->pluck('id')->toArray();
        $this->attachInterests($newOpportunity, $interestIds);

        return $newOpportunity;
    }

    /**
     * Toggle the status of an opportunity between published and draft.
     *
     * @param Opportunity $opportunity
     * @return Opportunity
     */
    public function toggleStatus(Opportunity $opportunity): Opportunity
    {
        $newStatus = $opportunity->status === OpportunityStatus::LISTED
            ? OpportunityStatus::UNLISTED
            : OpportunityStatus::LISTED;

        $opportunity->status = $newStatus;
        $opportunity->save();

        // Refresh the model to ensure we have the latest data
        $opportunity->refresh();

        return $opportunity;
    }

    /**
     * Attach industries to an opportunity.
     *
     * @param Opportunity $opportunity
     * @param array $industryIds
     * @return void
     */
    public function attachIndustries(Opportunity $opportunity, array $industryIds): void
    {
        $opportunity->industries()->sync($industryIds);
    }

    /**
     * Attach interests to an opportunity.
     *
     * @param Opportunity $opportunity
     * @param array $interestIds
     * @return void
     */
    public function attachInterests(Opportunity $opportunity, array $interestIds): void
    {
        $opportunity->interests()->sync($interestIds);
    }
}
