<?php

namespace App\Repositories;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class XFactorLeaderboardRepository
{
    /**
     * Get the X-Factor leaderboard data with filters
     *
     * @param string|null $regionId Filter by region ID
     * @param string|null $stateCode Filter by state code
     * @param int|null $graduationYear Filter by graduation year
     * @param Carbon|null $startDate Start date for module completion range
     * @param Carbon|null $endDate End date for module completion range
     * @param int $limit Number of results to return
     * @param int $offset Offset for pagination
     * @return Collection
     */
    public function getLeaderboard(
        ?string $regionId = null,
        ?string $stateCode = null,
        ?int $graduationYear = null,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
        int $limit = 50,
        int $offset = 0
    ): Collection {
        $query = DB::table('users as u')
            ->join('schools as s', 'u.school_id', '=', 's.id')
            ->join('module_user as mu', 'u.id', '=', 'mu.user_id')
            ->leftJoin('badge_user as bu', 'u.id', '=', 'bu.user_id')
            ->leftJoin('badges as b', 'bu.badge_id', '=', 'b.id')
            ->whereNotNull('mu.completed_at');

        // Apply date range filter if provided
        if ($startDate && $endDate) {
            $query->whereBetween('mu.completed_at', [$startDate, $endDate]);
        }

        // Apply filters using denormalized fields
        if ($regionId) {
            $query->where('u.region_id', $regionId);
        }

        if ($stateCode) {
            $query->where('u.state_code', $stateCode);
        }

        if ($graduationYear) {
            $query->where('u.graduation_year', $graduationYear);
        }

        // Get the ranked users with completed modules count
        $rankedUsers = $query->select([
                'u.id',
                'u.first_name',
                'u.last_name',
                'u.graduation_year',
                'u.school_id',
                's.name as school_name',
                'u.region_id',
                'u.state_code',
                DB::raw('COUNT(DISTINCT mu.module_id) as completed_modules_count'),
                DB::raw('MAX(b.name) FILTER (WHERE bu.is_achieved = true) as badge_name')
            ])
            ->groupBy([
                'u.id',
                'u.first_name',
                'u.last_name',
                'u.graduation_year',
                'u.school_id',
                's.name',
                'u.region_id',
                'u.state_code'
            ])
            ->orderByDesc('completed_modules_count')
            ->offset($offset)
            ->limit($limit)
            ->get();

        // Add ranking to the results
        $rank = $offset + 1;
        return $rankedUsers->map(function ($user) use (&$rank) {
            $user->rank = $rank++;
            return $user;
        });
    }

    /**
     * Get total count of users for pagination
     *
     * @param string|null $regionId Filter by region ID
     * @param string|null $stateCode Filter by state code
     * @param int|null $graduationYear Filter by graduation year
     * @param Carbon|null $startDate Start date for module completion range
     * @param Carbon|null $endDate End date for module completion range
     * @return int
     */
    public function getTotalUsers(
        ?string $regionId = null,
        ?string $stateCode = null,
        ?int $graduationYear = null,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null
    ): int {
        $query = DB::table('users as u')
            ->join('module_user as mu', 'u.id', '=', 'mu.user_id')
            ->whereNotNull('mu.completed_at');

        // Apply date range filter if provided
        if ($startDate && $endDate) {
            $query->whereBetween('mu.completed_at', [$startDate, $endDate]);
        }

        // Apply filters using denormalized fields
        if ($regionId) {
            $query->where('u.region_id', $regionId);
        }

        if ($stateCode) {
            $query->where('u.state_code', $stateCode);
        }

        if ($graduationYear) {
            $query->where('u.graduation_year', $graduationYear);
        }

        // Count distinct users who have completed modules
        return $query->select('u.id')
            ->groupBy('u.id')
            ->get()
            ->count();
    }

    /**
     * Get a specific user's rank on the leaderboard
     *
     * @param int $userId The user ID to find the rank for
     * @param string|null $regionId Filter by region ID
     * @param string|null $stateCode Filter by state code
     * @param int|null $graduationYear Filter by graduation year
     * @param Carbon|null $startDate Start date for module completion range
     * @param Carbon|null $endDate End date for module completion range
     * @return int|null The user's rank or null if not found
     */
    public function getUserRank(
        int $userId,
        ?string $regionId = null,
        ?string $stateCode = null,
        ?int $graduationYear = null,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null
    ): ?int {
        // First, get the user's module count
        $userQuery = DB::table('users as u')
            ->join('module_user as mu', 'u.id', '=', 'mu.user_id')
            ->where('u.id', $userId)
            ->whereNotNull('mu.completed_at');

        // Apply date range filter if provided
        if ($startDate && $endDate) {
            $userQuery->whereBetween('mu.completed_at', [$startDate, $endDate]);
        }

        $moduleCount = $userQuery->count(DB::raw('DISTINCT mu.module_id'));

        if ($moduleCount === 0) {
            return null; // User has no completed modules
        }

        // Now count users with more completed modules
        $rankQuery = DB::table('users as u')
            ->join('module_user as mu', 'u.id', '=', 'mu.user_id')
            ->whereNotNull('mu.completed_at');

        // Apply date range filter if provided
        if ($startDate && $endDate) {
            $rankQuery->whereBetween('mu.completed_at', [$startDate, $endDate]);
        }

        // Apply filters using denormalized fields
        if ($regionId) {
            $rankQuery->where('u.region_id', $regionId);
        }

        if ($stateCode) {
            $rankQuery->where('u.state_code', $stateCode);
        }

        if ($graduationYear) {
            $rankQuery->where('u.graduation_year', $graduationYear);
        }

        $usersWithMoreModules = $rankQuery
            ->select('u.id')
            ->groupBy('u.id')
            ->havingRaw('COUNT(DISTINCT mu.module_id) > ?', [$moduleCount])
            ->get()
            ->count();

        // Rank is users with more modules + 1
        return $usersWithMoreModules + 1;
    }

    /**
     * Get user details for the leaderboard
     *
     * @param int $userId The user ID to get details for
     * @param Carbon|null $startDate Start date for module completion range
     * @param Carbon|null $endDate End date for module completion range
     * @return object|null The user details or null if not found
     */
    public function getUserDetails(
        int $userId,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null
    ): ?object {
        $query = DB::table('users as u')
            ->leftJoin('schools as s', 'u.school_id', '=', 's.id')
            ->join('module_user as mu', 'u.id', '=', 'mu.user_id')
            ->leftJoin('badge_user as bu', 'u.id', '=', 'bu.user_id')
            ->leftJoin('badges as b', 'bu.badge_id', '=', 'b.id')
            ->where('u.id', $userId)
            ->whereNotNull('mu.completed_at');

        // Apply date range filter if provided
        if ($startDate && $endDate) {
            $query->whereBetween('mu.completed_at', [$startDate, $endDate]);
        }

        $user = $query->select([
                'u.id',
                'u.first_name',
                'u.last_name',
                'u.graduation_year',
                'u.school_id',
                's.name as school_name',
                'u.region_id',
                'u.state_code',
                DB::raw('COUNT(DISTINCT mu.module_id) as completed_modules_count'),
                DB::raw('MAX(b.name) FILTER (WHERE bu.is_achieved = true) as badge_name')
            ])
            ->groupBy([
                'u.id',
                'u.first_name',
                'u.last_name',
                'u.graduation_year',
                'u.school_id',
                's.name',
                'u.region_id',
                'u.state_code'
            ])
            ->first();

        return $user;
    }
}
