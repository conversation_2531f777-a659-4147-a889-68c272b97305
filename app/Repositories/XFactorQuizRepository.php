<?php

namespace App\Repositories;

use App\Enums\TestStatus;
use App\Enums\TestType;
use App\Models\Answer;
use App\Models\Module;
use App\Models\Question;
use App\Models\QuestionResponse;
use App\Models\Test;
use App\Models\TestAttempt;

class XFactorQuizRepository
{
    public function getQuizByModuleId(string $moduleId, ?string $userId = null): ?Test
    {
        $query = Test::query()
            ->where('testable_type', Module::class)
            ->where('testable_id', $moduleId)
            ->where('type', TestType::Quiz)
            ->with(['questions.answers']);

        if ($userId) {
            $query->with([
                'latestAttempt' => function ($query) use ($userId) {
                    $query->where('user_id', $userId);
                },
                'lastCompletedAttempt' => function ($query) use ($userId) {
                    $query->where('user_id', $userId);
                }
            ]);
        }

        return $query->first();
    }

    public function createQuizAttempt(string $testId, string $userId): TestAttempt
    {
        $attempt = new TestAttempt();
        $attempt->test_id = $testId;
        $attempt->user_id = $userId;
        $attempt->started_at = now();
        $attempt->ends_at = now()->addMinutes(60); // Default to 60 minutes if not specified
        $attempt->status = TestStatus::InProgress;
        $attempt->save();

        return $attempt;
    }

    public function getTestAttempt(string $attemptId): ?TestAttempt
    {
        return TestAttempt::query()
            ->with(['test.questions.answers', 'questionResponses'])
            ->find($attemptId);
    }

    public function createQuestionResponse(
        string $attemptId,
        string $questionId,
        string $userId,
        string $response
    ): QuestionResponse {
        $question = Question::with('answers')->find($questionId);
        $isCorrect = $question->answers->where('answer', $response)->where('is_correct', true)->isNotEmpty();

        $questionResponse = new QuestionResponse();
        $questionResponse->test_attempt_id = $attemptId;
        $questionResponse->question_id = $questionId;
        $questionResponse->user_id = $userId;
        $questionResponse->response = $response;
        $questionResponse->correct = $isCorrect;
        $questionResponse->save();

        return $questionResponse;
    }

    public function completeQuizAttempt(int $attemptId, array $responses): TestAttempt
    {
        $attempt = TestAttempt::with(['test.questions.answers'])->find($attemptId);
        // Create question responses for each answer
        $questionResponses = collect();
        foreach ($responses as $responseData) {
            $answer = Answer::query()->find($responseData['answerId']);
            $isCorrect = $answer->is_correct ?? false;

            $questionResponse = QuestionResponse::query()->create([
                'test_attempt_id' => $attempt->id,
                'question_id' => $responseData['questionId'],
                'user_id' => $attempt->user_id,
                'response' => $answer->answer,
                'correct' => $isCorrect,
            ]);

            $questionResponses->push($questionResponse);
        }

        // Calculate score based on correct responses
        $totalQuestions = $attempt->test->questions->count();
        $correctAnswers = $questionResponses->where('correct', true)->count();

        // Calculate percentage and round to nearest whole number
        $score = (int) round(($correctAnswers / $totalQuestions) * 100);

        $attempt->completed_at = now();
        $attempt->score = $score;
        $attempt->status = TestStatus::Complete;
        $attempt->save();

        return $attempt->fresh(['questionResponses', 'test.questions']);
    }

    public function getUserAttempts(string $testId, string $userId): int
    {
        return TestAttempt::query()
            ->where('test_id', $testId)
            ->where('user_id', $userId)
            ->count();
    }

    public function findValidAttempt(int $testId, string $userId): ?TestAttempt
    {
        return TestAttempt::query()
            ->where('test_id', $testId)
            ->where('user_id', $userId)
            ->where('completed_at', null)
            ->where('ends_at', '>', now())
            ->orderBy('created_at', 'desc')
            ->first();
    }
}
