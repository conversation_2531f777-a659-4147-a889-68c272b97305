<?php

namespace App\Repositories;

use App\Models\Import;
use App\Models\ImportRecord;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class ImportRepository
{
    /**
     * Get all imports with optional filtering.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getImports(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Import::query();

        // Apply type filter
        if (!empty($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        // Apply status filter
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        // Apply date range filter
        if (!empty($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        // Apply search filter
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function (Builder $query) use ($search) {
                $query->where('file_name', 'like', "%{$search}%")
                    ->orWhere('batch_id', 'like', "%{$search}%");
            });
        }

        // Order by created_at desc by default
        $query->orderBy('created_at', 'desc');

        return $query->paginate($perPage);
    }

    /**
     * Get a specific import by ID.
     *
     * @param int $id
     * @return Import|null
     */
    public function getImportById(int $id): ?Import
    {
        return Import::find($id);
    }

    /**
     * Get a specific import by batch ID.
     *
     * @param string $batchId
     * @return Import|null
     */
    public function getImportByBatchId(string $batchId): ?Import
    {
        return Import::where('batch_id', $batchId)->first();
    }

    /**
     * Get records for a specific import with optional status filtering.
     *
     * @param int $importId
     * @param string|null $status
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getImportRecords(int $importId, ?string $status = null, int $perPage = 15): LengthAwarePaginator
    {
        $query = ImportRecord::where('import_id', $importId);

        if ($status) {
            $query->where('status', $status);
        }

        return $query->paginate($perPage);
    }

    /**
     * Get summary statistics for imports.
     *
     * @param int $limit
     * @return array
     */
    public function getImportStats(int $limit = 30): array
    {
        // Get counts by type
        $typeStats = Import::query()
            ->selectRaw('type, count(*) as count')
            ->groupBy('type')
            ->get()
            ->pluck('count', 'type')
            ->toArray();

        // Get counts by status
        $statusStats = Import::query()
            ->selectRaw('status, count(*) as count')
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status')
            ->toArray();

        // Get recent imports
        $recentImports = Import::query()
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();

        // Get total counts
        $totalImports = Import::count();
        $totalRecords = ImportRecord::count();
        $totalCreated = ImportRecord::where('status', 'created')->count();
        $totalUpdated = ImportRecord::where('status', 'updated')->count();
        $totalFailed = ImportRecord::where('status', 'failed')->count();

        return [
            'type_stats' => $typeStats,
            'status_stats' => $statusStats,
            'recent_imports' => $recentImports,
            'total_imports' => $totalImports,
            'total_records' => $totalRecords,
            'total_created' => $totalCreated,
            'total_updated' => $totalUpdated,
            'total_failed' => $totalFailed,
        ];
    }

    /**
     * Get imports by type.
     *
     * @param string $type
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getImportsByType(string $type, int $perPage = 15): LengthAwarePaginator
    {
        return Import::where('type', $type)
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * Get imports by status.
     *
     * @param string $status
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getImportsByStatus(string $status, int $perPage = 15): LengthAwarePaginator
    {
        return Import::where('status', $status)
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * Get recent imports.
     *
     * @param int $limit
     * @return Collection
     */
    public function getRecentImports(int $limit = 10): Collection
    {
        return Import::orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }
}
