<?php

namespace App\Repositories;

use App\Models\Contact;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class ContactRepository
{
    /**
     * Get all contacts.
     *
     * @return Collection
     */
    public function all(): Collection
    {
        return Contact::query()->get();
    }

    /**
     * Get a paginated list of contacts.
     *
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function paginate(int $perPage = 15): LengthAwarePaginator
    {
        return Contact::query()->paginate($perPage);
    }

    /**
     * Find a contact by ID.
     *
     * @param int $id
     * @return Contact|null
     */
    public function find(int $id): ?Contact
    {
        return Contact::query()->find($id);
    }

    /**
     * Find a contact by email.
     *
     * @param string $email
     * @return Contact|null
     */
    public function findByEmail(string $email): ?Contact
    {
        return Contact::query()->where('email', $email)->first();
    }

    /**
     * Create a new contact.
     *
     * @param array $data
     * @return Contact
     */
    public function create(array $data): Contact
    {
        return Contact::query()->create($data);
    }

    /**
     * Update a contact.
     *
     * @param Contact $contact
     * @param array $data
     * @return Contact
     */
    public function update(Contact $contact, array $data): Contact
    {
        $contact->update($data);
        return $contact->fresh();
    }

    /**
     * Delete a contact.
     *
     * @param Contact $contact
     * @return bool
     */
    public function delete(Contact $contact): bool
    {
        return $contact->delete();
    }

    /**
     * Get contacts by type.
     *
     * @param string $type
     * @return Collection
     */
    public function getByType(string $type): Collection
    {
        return Contact::query()->ofType($type)->get();
    }

    /**
     * Get contacts by region.
     *
     * @param int $regionId
     * @return Collection
     */
    public function getByRegion(int $regionId): Collection
    {
        return Contact::query()->where('region_id', $regionId)->get();
    }

    /**
     * Get contacts by market.
     *
     * @param int $marketId
     * @return Collection
     */
    public function getByMarket(int $marketId): Collection
    {
        return Contact::query()->where('market_id', $marketId)->get();
    }

    /**
     * Get contacts by county.
     *
     * @param int $countyId
     * @return Collection
     */
    public function getByCounty(int $countyId): Collection
    {
        return Contact::query()->where('county_id', $countyId)->get();
    }

    /**
     * Get contacts by state.
     *
     * @param int $stateId
     * @return Collection
     */
    public function getByState(int $stateId): Collection
    {
        return Contact::query()->where('state_id', $stateId)->get();
    }

    /**
     * Get contacts by school.
     *
     * @param int $schoolId
     * @return Collection
     */
    public function getBySchool(int $schoolId): Collection
    {
        return Contact::query()->where('school_id', $schoolId)->get();
    }

    /**
     * Get contacts by sport.
     *
     * @param string $sport
     * @return Collection
     */
    public function getBySport(string $sport): Collection
    {
        return Contact::query()->hasSport($sport)->get();
    }

    /**
     * Get contacts by tag.
     *
     * @param string $tag
     * @return Collection
     */
    public function getByTag(string $tag): Collection
    {
        return Contact::query()->hasTag($tag)->get();
    }

    /**
     * Get contacts by graduation year.
     *
     * @param string $year
     * @return Collection
     */
    public function getByGraduationYear(string $year): Collection
    {
        return Contact::query()->graduatingIn($year)->get();
    }

    /**
     * Get contacts by gender.
     *
     * @param string $gender
     * @return Collection
     */
    public function getByGender(string $gender): Collection
    {
        return Contact::query()->ofGender($gender)->get();
    }

    /**
     * Get a filtered query builder for contacts.
     *
     * @param array $filters
     * @return Builder
     */
    public function getFilteredQuery(array $filters): Builder
    {
        $query = Contact::query();

        if (isset($filters['type'])) {
            $query->ofType($filters['type']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['region_id'])) {
            $query->where('region_id', $filters['region_id']);
        }

        if (isset($filters['market_id'])) {
            $query->where('market_id', $filters['market_id']);
        }

        if (isset($filters['sub_region_id'])) {
            $query->where('sub_region_id', $filters['sub_region_id']);
        }

        if (isset($filters['county_id'])) {
            $query->where('county_id', $filters['county_id']);
        }

        if (isset($filters['state_id'])) {
            $query->where('state_id', $filters['state_id']);
        }

        if (isset($filters['school_id'])) {
            $query->where('school_id', $filters['school_id']);
        }

        if (isset($filters['sport'])) {
            $query->hasSport($filters['sport']);
        }

        if (isset($filters['tag'])) {
            $query->hasTag($filters['tag']);
        }

        if (isset($filters['graduation_year'])) {
            $query->graduatingIn($filters['graduation_year']);
        }

        if (isset($filters['gender'])) {
            $query->ofGender($filters['gender']);
        }

        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                    ->orWhere('last_name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        return $query;
    }

    /**
     * Find a contact by ID
     *
     * @param string $id
     * @return Contact|null
     */
    public function findById(string $id): ?Contact
    {
        return Contact::query()->find($id);
    }

    /**
     * Get all contacts
     *
     * @return Collection
     */
    public function getAll(): Collection
    {
        return Contact::query()->get();
    }

    /**
     * Get paginated contacts
     *
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getPaginated(int $perPage = 15): LengthAwarePaginator
    {
        return Contact::query()->paginate($perPage);
    }

    /**
     * Find contacts by phone number
     *
     * @param string $phoneNumber
     * @return Collection
     */
    public function findByPhoneNumber(string $phoneNumber): Collection
    {
        return Contact::query()->where('phone_number', $phoneNumber)->get();
    }

    /**
     * Find contacts by type
     *
     * @param string $type
     * @return Collection
     */
    public function findByType(string $type): Collection
    {
        return Contact::query()->where('type', $type)->get();
    }

    /**
     * Search contacts by name
     *
     * @param string $name
     * @return Collection
     */
    public function searchByName(string $name): Collection
    {
        return Contact::query()->where('name', 'LIKE', "%{$name}%")->get();
    }

    /**
     * Find contacts by metadata field
     *
     * @param string $key
     * @param mixed $value
     * @return Collection
     */
    public function findByMetadata(string $key, $value): Collection
    {
        return Contact::query()->whereJsonContains("metadata->{$key}", $value)->get();
    }

    /**
     * Bulk create contacts
     *
     * @param array $contacts
     * @return Collection
     */
    public function bulkCreate(array $contacts): Collection
    {
        $createdContacts = new Collection();

        foreach ($contacts as $contactData) {
            $createdContacts->push($this->create($contactData));
        }

        return $createdContacts;
    }
}
