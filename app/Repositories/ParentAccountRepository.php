<?php

namespace App\Repositories;

use App\Data\Account\ParentInviteData;
use App\Data\SystemInvites\ParentSystemInviteData;
use App\Enums\ProfileType;
use App\Enums\SystemInviteStatus;
use App\Models\SystemInvite;
use App\Models\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Illuminate\Support\Str;

class ParentAccountRepository
{
    /**
     * Get all parent accounts linked to an athlete
     *
     * @param int $athleteId The athlete user ID
     * @return Collection The collection of parent users and system invites
     */
    public function getForAthlete(int $athleteId): Collection
    {
        // Get the athlete
        $athlete = User::find($athleteId);
        if (!$athlete) {
            return collect([]);
        }

        $results = collect();

        // 1. Check for direct parent relationship via parent_id
        if ($athlete->parent_id) {
            $parent = User::query()
                ->where('id', $athlete->parent_id)
                ->where('profile_type', ProfileType::PARENT)
                ->first();

            if ($parent) {
                $results->push($parent);
            }
        }

        // 2. Check for pending system invites - using PostgreSQL's JSON querying capabilities
        $pendingInvites = SystemInvite::query()
            ->where('status', SystemInviteStatus::PENDING)
            ->where('type', ProfileType::PARENT)
            ->where('expires_at', '>', now())
            ->whereRaw("(invite_data->>'athlete_user_id')::int = ?", [$athleteId])
            ->get();

        // Convert system invites to parent invite data objects
        if ($pendingInvites->isNotEmpty()) {
            $pendingInvites = $pendingInvites->map(function ($invite) {
                $inviteDataJson = json_decode($invite->getRawOriginal('invite_data'), true);
                return ParentInviteData::fromSystemInvite($invite);
            });

            $results = $results->concat($pendingInvites);
        }

        return $results;
    }

    /**
     * Link a parent account to an athlete
     *
     * @param User $athlete The athlete user
     * @param array $parentData The parent data (first_name, last_name, email, phone)
     * @return User|ParentInviteData Either the parent user or a system invite representation
     */
    public function linkParentToAthlete(User $athlete, array $parentData)
    {
        // Check if a parent with this email already exists
        $existingParent = User::query()
            ->where('email', 'ILIKE', $parentData['email'])
            ->first();

        if ($existingParent) {
            // Ensure the user is a parent type
            if ($existingParent->profile_type !== ProfileType::PARENT) {
                // Optionally: Convert user to parent type or handle differently
                Log::warning('Attempted to link non-parent user as parent', [
                    'athlete_id' => $athlete->id,
                    'email' => $parentData['email'],
                    'current_profile_type' => $existingParent->profile_type,
                ]);

                // Handle according to business rules - either throw exception or convert profile
                throw new \Exception('A user with this email already exists but is not a parent account.');
            }

            // Link to existing parent account via parent_id
            $athlete->parent_id = $existingParent->id;
            $athlete->save();

            Log::info('Parent account linked to athlete', [
                'athlete_id' => $athlete->id,
                'parent_id' => $existingParent->id,
                'parent_email' => $parentData['email'],
            ]);

            return $existingParent;
        } else {
            // Create a system invite for the parent
            $inviteData = new ParentSystemInviteData(
                first_name: $parentData['first_name'],
                last_name: $parentData['last_name'],
                email: $parentData['email'],
                token: Str::random(64),
                created_at: time(),
                athlete_user_id: $athlete->id,
                athlete_name: $athlete->first_name . ' ' . $athlete->last_name,
            );

            // Use the system invite service to create the invite
            $systemInviteService = app(\App\Services\Invite\SystemInviteService::class);
            $invite = $systemInviteService->createForParent($inviteData);

            Log::info('Parent invitation created', [
                'athlete_id' => $athlete->id,
                'parent_email' => $parentData['email'],
                'invite_id' => $invite->id,
            ]);

            return ParentInviteData::fromSystemInvite($invite);
        }
    }

    /**
     * Unlink a parent from an athlete
     *
     * @param int $athleteId The athlete user ID
     * @param string $parentEmail The parent email address
     * @return bool Whether the operation was successful
     */
    public function unlinkParent(int $athleteId, string $parentEmail): bool
    {
        // Get the athlete
        $athlete = User::find($athleteId);
        if (!$athlete) {
            return false;
        }

        $result = false;

        // 1. Check if there is a direct parent link via parent_id - always unlink regardless of email
        if ($athlete->parent_id) {
            // Log the email mismatch if applicable but still unlink
            if ($athlete->parent && $athlete->parent->email !== $parentEmail) {
                Log::info('Unlinking parent with email mismatch', [
                    'athlete_id' => $athleteId,
                    'parent_id' => $athlete->parent_id,
                    'parent_email' => $athlete->parent ? $athlete->parent->email : 'unknown',
                    'requested_email' => $parentEmail
                ]);
            }

            $parentId = $athlete->parent_id; // Store for logging
            $athlete->parent_id = null;
            $result = $athlete->save();

            if ($result) {
                Log::info('Removed direct parent-athlete link', [
                    'athlete_id' => $athleteId,
                    'previous_parent_id' => $parentId,
                    'requested_email' => $parentEmail,
                ]);
            }

            return $result;
        }

        // 2. Check for and cancel any pending system invites - using PostgreSQL's JSON querying
        $pendingInvites = SystemInvite::query()
            ->where('email', 'ILIKE', $parentEmail)
            ->where('status', SystemInviteStatus::PENDING)
            ->where('type', ProfileType::PARENT)
            ->where('expires_at', '>', now())
            ->whereRaw("(invite_data->>'athlete_user_id')::int = ?", [$athleteId])
            ->get();

        if ($pendingInvites->isNotEmpty()) {
            foreach ($pendingInvites as $invite) {
                // For tests, handle the case where we might be using a plain DB record
                if (method_exists($invite, 'markAsRevoked')) {
                    $invite->markAsRevoked();
                } else {
                    // Direct DB update for tests
                    DB::table('system_invites')
                        ->where('id', $invite->id)
                        ->update(['status' => SystemInviteStatus::REVOKED->value]);
                }
                $result = true;

                Log::info('Cancelled parent system invite', [
                    'athlete_id' => $athleteId,
                    'parent_email' => $parentEmail,
                    'invite_id' => $invite->id,
                ]);
            }
        }

        return $result;
    }

    /**
     * Find or create a parent user
     *
     * @param array $data The parent data
     * @return User The parent user
     */
    public function findOrCreateParentUser(array $data): User
    {
        // Try to find an existing parent user with the same email
        $parent = User::query()
            ->where('email', 'ILIKE', $data['email'])
            ->first();

        if ($parent) {
            // If the user exists but is not a parent, we don't want to change their profile type
            // This is a business decision - we might want to change this behavior
            if ($parent->profile_type !== ProfileType::PARENT) {
                Log::warning('Attempted to use non-parent user as parent', [
                    'email' => $data['email'],
                    'current_profile_type' => $parent->profile_type,
                ]);

                throw new \Exception('A user with this email already exists but is not a parent account.');
            }

            return $parent;
        }

        // Create a new parent user
        $parent = User::create([
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'email' => $data['email'],
            'phone' => $data['phone'] ?? null,
            'profile_type' => ProfileType::PARENT,
            'password' => \Illuminate\Support\Facades\Hash::make(Str::random(16)),
        ]);

        Log::info('Created new parent user', [
            'parent_id' => $parent->id,
            'email' => $parent->email,
        ]);

        return $parent;
    }
}
