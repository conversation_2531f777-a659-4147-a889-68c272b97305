<?php

namespace App\Repositories;

use App\Enums\TestStatus;
use App\Enums\TestType;
use App\Enums\QuestionType;
use App\Models\Answer;
use App\Models\Module;
use App\Models\Question;
use App\Models\QuestionResponse;
use App\Models\Test;
use App\Models\TestAttempt;

class XFactorExamRepository
{
    public function getExamByModuleId(int $moduleId, ?int $userId = null): ?Test
    {
        $query = Test::query()
            ->where('testable_type', Module::class)
            ->where('testable_id', $moduleId)
            ->where('type', TestType::Exam)
            ->with(['questions.answers']);

        if ($userId) {
            $query->with([
                'latestAttempt' => function ($query) use ($userId) {
                    $query->where('user_id', $userId);
                },
                'lastCompletedAttempt' => function ($query) use ($userId) {
                    $query->where('user_id', $userId)
                        ->whereNotNull('completed_at');
                }
            ]);
        }

        return $query->first();
    }

    public function createExamAttempt(int $testId, int $userId, int $timeLimit): TestAttempt
    {
        $attempt = new TestAttempt();
        $attempt->test_id = $testId;
        $attempt->user_id = $userId;
        $attempt->started_at = now();
        $attempt->ends_at = now()->addSeconds($timeLimit);
        $attempt->status = TestStatus::InProgress;
        $attempt->save();

        return $attempt;
    }

    public function findValidAttempt(int $testId, int $userId): ?TestAttempt
    {
        return TestAttempt::query()
            ->where('test_id', $testId)
            ->where('user_id', $userId)
            ->whereNull('completed_at')
            ->where('ends_at', '>', now())
            ->orderBy('created_at', 'desc')
            ->first();
    }

    public function findActiveAttempt(int $moduleId, int $userId): ?TestAttempt
    {
        return TestAttempt::query()
            ->whereHas('test', function ($query) use ($moduleId) {
                $query->where('testable_id', $moduleId)
                    ->where('type', TestType::Exam);
            })
            ->where('user_id', $userId)
            ->whereNull('completed_at')
            ->latest()
            ->first();
    }

    public function getUserAttempts(int $testId, int $userId): int
    {
        return TestAttempt::query()
            ->where('test_id', $testId)
            ->where('user_id', $userId)
            ->count();
    }

    public function submitExamResponses(int $attemptId, array $responses): TestAttempt
    {
        $attempt = TestAttempt::with(['test.questions.answers'])->find($attemptId);
        $correctCount = 0;
        $totalMultipleChoice = 0;

        foreach ($responses as $responseData) {
            $question = Question::find($responseData['questionId']);

            // For multiple choice questions, check correctness
            if ($question->type === QuestionType::MultipleChoice) {
                $totalMultipleChoice++;
                $answer = Answer::find($responseData['response'] ?? null);
                $isCorrect = $answer?->is_correct ?? false;
                if ($isCorrect) {
                    $correctCount++;
                }
            }

            QuestionResponse::query()->create([
                'test_attempt_id' => $attempt->id,
                'question_id' => $responseData['questionId'],
                'user_id' => $attempt->user_id,
                'response' => $responseData['response'],
                'response_html' => $responseData['responseHtml'] ?? null,
                'correct' => $question->type === QuestionType::MultipleChoice ? ($answer?->is_correct ?? false) : null,
            ]);
        }

        // Calculate score based on multiple choice questions only
        if ($totalMultipleChoice > 0) {
            $attempt->score = (int) round(($correctCount / $totalMultipleChoice) * 100);
            $attempt->save();
        }

        return $attempt->fresh(['questionResponses', 'test.questions']);
    }

    public function updateAttemptStatus(int $attemptId, TestStatus $status): TestAttempt
    {
        $attempt = TestAttempt::find($attemptId);

        $attempt->status = $status;
        $attempt->completed_at = now();

        // If time expired, set score to 0
        if ($status === TestStatus::Expired) {
            $attempt->score = 0;
        }

        $attempt->save();

        return $attempt->fresh(['questionResponses', 'test.questions']);
    }
}
