<?php

namespace App\Repositories;

use App\Enums\ProfileType;
use App\Models\Message;
use App\Models\User;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class MessageRepository
{
    /**
     * Create a new message.
     *
     * @param array $data
     * @param array|null $media
     * @return Message
     */
    public function createMessage(array $data, ?array $media = null): Message
    {
        // Create the message
        $message = Message::query()->create([
            'sender_id' => $data['sender_id'],
            'recipient_id' => $data['recipient_id'],
            'content' => $data['content'] ?? null,
            'is_flagged' => $data['is_flagged'] ?? false,
            'moderation_result' => $data['moderation_result'] ?? null
        ]);

        // If media is provided, attach it to the message
        if ($media && !empty($media)) {
            // Process media attachments
            // ...
        }

        return $message;
    }

    /**
     * Get conversation between two users.
     *
     * @param int $userId
     * @param int $otherUserId
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getConversation(int $userId, int $otherUserId, int $perPage = 15): LengthAwarePaginator
    {
        // Query in descending order to get the most recent messages first
        $messages = Message::query()
            // Messages sent by the user to the other user (including flagged ones)
            ->where(function ($q) use ($userId, $otherUserId) {
                $q->where('sender_id', $userId)
                  ->where('recipient_id', $otherUserId);
            })
            // Messages received by the user from the other user (excluding flagged ones)
            ->orWhere(function ($q) use ($userId, $otherUserId) {
                $q->where('sender_id', $otherUserId)
                  ->where('recipient_id', $userId)
                  ->where('is_flagged', false);
            })
            ->with([
                'sender' => function ($query) {
                    $query->with(['activeOrganization' => function ($q) {
                        $q->with('media');
                    }, 'media']);
                },
                'recipient' => function ($query) {
                    $query->with(['activeOrganization' => function ($q) {
                        $q->with('media');
                    }, 'media']);
                },
                'media'
            ])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);

        // Sort the results in ascending order in the application layer
        $sortedItems = collect($messages->items())->sortBy('created_at')->values();

        // Process messages and add organization data for sponsor users
        $processedItems = $sortedItems->map(function ($message) {
            // Process sender
            if ($message->sender) {
                // Add avatar URL if available
                if ($message->sender->hasMedia('avatar')) {
                    $message->sender->avatar_url = $message->sender->getFirstMediaUrl('avatar', 'thumb');
                }

                if ($message->sender->profile_type === ProfileType::SPONSOR &&
                    $message->sender->relationLoaded('activeOrganization') &&
                    $message->sender->activeOrganization->isNotEmpty()) {

                    $org = $message->sender->activeOrganization->first();
                    $message->sender->organization_id = $org->id;
                    $message->sender->organization_name = $org->name;
                    $message->sender->organization_logo_url = $org->hasMedia('logo') ?
                        $org->getFirstMediaUrl('logo', 'thumb') : null;
                }
            }

            // Process recipient
            if ($message->recipient) {
                // Add avatar URL if available
                if ($message->recipient->hasMedia('avatar')) {
                    $message->recipient->avatar_url = $message->recipient->getFirstMediaUrl('avatar', 'thumb');
                }

                if ($message->recipient->profile_type === ProfileType::SPONSOR &&
                    $message->recipient->relationLoaded('activeOrganization') &&
                    $message->recipient->activeOrganization->isNotEmpty()) {

                    $org = $message->recipient->activeOrganization->first();
                    $message->recipient->organization_id = $org->id;
                    $message->recipient->organization_name = $org->name;
                    $message->recipient->organization_logo_url = $org->hasMedia('logo') ?
                        $org->getFirstMediaUrl('logo', 'thumb') : null;
                }
            }

            return $message;
        });

        // Create a new paginator with the sorted and processed items
        return new LengthAwarePaginator(
            $processedItems,
            $messages->total(),
            $messages->perPage(),
            $messages->currentPage(),
            ['path' => request()->url(), 'query' => request()->query()]
        );
    }

    /**
     * Get all conversations for a user.
     *
     * @param int $userId
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getConversations(int $userId, int $perPage = 15): LengthAwarePaginator
    {
        try {
            // Get the user's pinned conversations
            $user = User::query()->find($userId);
            $pinnedConversations = is_string($user->pinned_conversations)
                ? json_decode($user->pinned_conversations, true)
                : ($user->pinned_conversations ?? []);

            // Check if there are any conversations in the database for this user
            $conversationsExist = DB::table('conversations')
                ->where('user_one_id', $userId)
                ->orWhere('user_two_id', $userId)
                ->exists();

            // Using the conversations view with a join to get the latest message
            $latestMessages = DB::table('conversations')
                ->where(function ($query) use ($userId) {
                    $query->where('user_one_id', $userId)
                        ->orWhere('user_two_id', $userId);
                })
                ->join('messages as m', function ($join) {
                    $join->on(DB::raw('LEAST(m.sender_id, m.recipient_id)'), '=', 'conversations.user_one_id')
                        ->on(DB::raw('GREATEST(m.sender_id, m.recipient_id)'), '=', 'conversations.user_two_id')
                        ->on('m.created_at', '=', 'conversations.last_message_at');
                    // No filter for flagged messages here - we'll handle that in the results
                })
                ->leftJoin('connections as c', function ($join) use ($userId) {
                    $join->whereRaw("(
                        LEAST(c.requester_id, c.recipient_id) = LEAST({$userId}, CASE WHEN conversations.user_one_id = {$userId} THEN conversations.user_two_id ELSE conversations.user_one_id END)
                        AND
                        GREATEST(c.requester_id, c.recipient_id) = GREATEST({$userId}, CASE WHEN conversations.user_one_id = {$userId} THEN conversations.user_two_id ELSE conversations.user_one_id END)
                    )");
                })
                ->select([
                    'conversations.*',
                    'm.id as last_message_id',
                    'm.content as last_message',
                    'm.read_at as last_message_read_at',
                    'm.is_flagged as last_message_is_flagged',
                    'm.sender_id as last_message_sender_id',
                    'm.recipient_id as last_message_recipient_id',
                    DB::raw("false as is_pinned"), // Default to false, we'll update this later
                    DB::raw("CASE WHEN conversations.user_one_id = {$userId} THEN conversations.user_two_id ELSE conversations.user_one_id END as other_user_id"),
                    'c.id as connection_id',
                    'c.status as connection_status',
                    'c.requester_id as connection_requester_id',
                    'c.deleted_at as connection_blocked_at',
                ]);

            $latestMessages = $latestMessages->orderBy('conversations.last_message_at', 'desc')
                ->paginate($perPage);

            if (count($latestMessages->items()) === 0) {
                return $latestMessages; // Return empty paginator if no conversations
            }

            // Load other user details
            $otherUserIds = collect($latestMessages->items())->pluck('other_user_id');
            $users = User::query()
                ->with(['activeOrganization.media', 'media'])
                ->whereIn('id', $otherUserIds)
                ->get()
                ->keyBy('id');

            // Attach user details to results and set is_pinned based on pinned_conversations
            $items = collect($latestMessages->items())
                ->map(function ($item) use ($users, $pinnedConversations, $userId) {
                    // Check if the user exists in the collection
                    if (!isset($users[$item->other_user_id])) {
                        // Handle user not found if necessary
                    }

                    // Replace flagged message preview with the latest non-flagged message or default message
                    if ($item->last_message_is_flagged && $item->last_message_recipient_id == $userId) {
                        $nonFlaggedMessage = $this->findNonFlaggedMessageForConversation($userId, $item->other_user_id);

                        if ($nonFlaggedMessage) {
                            $item->last_message = $nonFlaggedMessage->content;
                            $item->last_message_read_at = $nonFlaggedMessage->read_at;
                            $item->last_message_sender_id = $nonFlaggedMessage->sender_id;
                            $item->last_message_recipient_id = $nonFlaggedMessage->recipient_id;
                            $item->last_message_is_flagged = false;
                        } else {
                            // No valid messages found, use a non-descriptive placeholder
                            $item->last_message = 'New conversation';
                        }
                    }

                    // Set is_pinned based on whether the other_user_id is in the pinned_conversations array
                    $item->is_pinned = is_array($pinnedConversations) && in_array($item->other_user_id, $pinnedConversations);

                    // Add user data
                    if (isset($users[$item->other_user_id])) {
                        $otherUser = $users[$item->other_user_id];
                        $item->other_user = $otherUser;

                        // Get avatar URL from Media Library if available
                        $avatarUrl = null;
                        if ($otherUser->hasMedia('avatar')) {
                            $avatarUrl = $otherUser->getFirstMediaUrl('avatar', 'thumb');
                        }

                        // Add user data to the conversation
                        $item->other_user = (object)[
                            'id' => $otherUser->id,
                            'first_name' => $otherUser->first_name,
                            'last_name' => $otherUser->last_name,
                            'handle' => $otherUser->handle,
                            'profile_type' => $otherUser->profile_type,
                            'organization_id' => null,
                            'organization_name' => null,
                            'organization_logo_url' => null,
                            'avatar_url' => $avatarUrl,
                        ];

                        // Add organization data if the user is a sponsor
                        if ($otherUser->profile_type === ProfileType::SPONSOR &&
                            $otherUser->activeOrganization->count() > 0) {

                            $org = $otherUser->activeOrganization->first();
                            $item->other_user->organization_id = $org->id;
                            $item->other_user->organization_name = $org->name;

                            // Add logo URL if available
                            if ($org->hasMedia('logo')) {
                                $item->other_user->organization_logo_url = $org->getFirstMediaUrl('logo', 'thumb');
                            }
                        }
                    }

                    return $item;
                });

            // Sort by pinned status first, then by last_message_at
            $items = $items->sort(function ($a, $b) {
                // First sort by pinned status (pinned conversations first)
                if ($a->is_pinned && !$b->is_pinned) {
                    return -1;
                }
                if (!$a->is_pinned && $b->is_pinned) {
                    return 1;
                }

                // Then sort by last_message_at (most recent first)
                return strtotime($b->last_message_at) - strtotime($a->last_message_at);
            });

            return new LengthAwarePaginator(
                $items,
                $latestMessages->total(),
                $latestMessages->perPage(),
                $latestMessages->currentPage(),
                ['path' => request()->url(), 'query' => request()->query()]
            );
        } catch (\Throwable $e) {
            // Return an empty paginator in case of error
            return new LengthAwarePaginator(
                [],
                0,
                $perPage,
                1,
                ['path' => request()->url(), 'query' => request()->query()]
            );
        }
    }

    /**
     * Mark a message as read.
     *
     * @param int $messageId
     * @param int $userId
     * @return bool
     */
    public function markAsRead(int $messageId, int $userId): bool
    {
        return (bool) Message::query()
            ->where('id', $messageId)
            ->where('recipient_id', $userId)
            ->whereNull('read_at')
            ->update(['read_at' => now()]);
    }

    /**
     * Delete a message for the sender.
     *
     * @param int $messageId
     * @param int $senderId
     * @return bool
     */
    public function deleteMessageForSender(int $messageId, int $senderId): bool
    {
        return (bool) Message::query()
            ->where('id', $messageId)
            ->where('sender_id', $senderId)
            ->update(['deleted_by_sender_at' => now()]);
    }

    /**
     * Delete a message for the recipient.
     *
     * @param int $messageId
     * @param int $recipientId
     * @return bool
     */
    public function deleteMessageForRecipient(int $messageId, int $recipientId): bool
    {
        return (bool) Message::query()
            ->where('id', $messageId)
            ->where('recipient_id', $recipientId)
            ->update(['deleted_by_recipient_at' => now()]);
    }

    /**
     * Pin a conversation.
     *
     * @param int $otherUserId
     * @param int $userId
     * @param bool $pinned
     * @return bool
     */
    public function pinConversation(int $otherUserId, int $userId, bool $pinned = true): bool
    {
        try {
            // Get the user
            $user = User::query()->find($userId);
            if (!$user) {
                return false;
            }

            // Get current pinned conversations
            $pinnedConversations = is_string($user->pinned_conversations)
                ? json_decode($user->pinned_conversations, true)
                : ($user->pinned_conversations ?? []);

            // Ensure it's an array
            if (!is_array($pinnedConversations)) {
                $pinnedConversations = [];
            }

            if ($pinned) {
                // Add the other user ID to pinned conversations if not already there
                if (!in_array($otherUserId, $pinnedConversations)) {
                    $pinnedConversations[] = $otherUserId;
                }
            } else {
                // Remove the other user ID from pinned conversations
                $pinnedConversations = array_values(array_filter($pinnedConversations, function ($id) use ($otherUserId) {
                    return $id != $otherUserId;
                }));
            }

            // Update the user's pinned_conversations
            return (bool) $user->update(['pinned_conversations' => json_encode($pinnedConversations)]);
        } catch (\Throwable $e) {
            return false;
        }
    }

    /**
     * Unpin a conversation.
     *
     * @param int $otherUserId
     * @param int $userId
     * @return bool
     */
    public function unpinConversation(int $otherUserId, int $userId): bool
    {
        return $this->pinConversation($otherUserId, $userId, false);
    }

    /**
     * Check if a conversation exists between two users.
     *
     * @param int $userOneId
     * @param int $userTwoId
     * @return bool
     */
    public function getConversationBetweenUsers(int $userOneId, int $userTwoId): bool
    {
        return Message::query()
            ->where(function ($query) use ($userOneId, $userTwoId) {
                $query->where('sender_id', $userOneId)
                    ->where('recipient_id', $userTwoId);
            })
            ->orWhere(function ($query) use ($userOneId, $userTwoId) {
                $query->where('sender_id', $userTwoId)
                    ->where('recipient_id', $userOneId);
            })
            ->exists();
    }

    /**
     * Update an existing message.
     *
     * @param int $messageId
     * @param int $senderId
     * @param array $data
     * @return Message|null
     */
    public function updateMessage(int $messageId, int $senderId, array $data): ?Message
    {
        // Find the message and ensure it belongs to the sender
        $message = Message::query()
            ->where('id', $messageId)
            ->where('sender_id', $senderId)
            ->first();

        if (!$message) {
            return null;
        }

        // Update the message with new data
        $message->update($data);

        return $message;
    }

    /**
     * Get conversations for a sponsor user, including their own conversations
     * and conversations from other sponsors in the same organization.
     *
     * @param int $userId
     * @param int $organizationId
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getSponsorConversations(int $userId, int $organizationId, int $perPage = 15): LengthAwarePaginator
    {
        try {
            // 1. Get the sponsor user with pinned conversations
            $user = User::query()
                ->with(['activeOrganization.media'])
                ->find($userId);

            $pinnedConversations = is_string($user->pinned_conversations)
                ? json_decode($user->pinned_conversations, true)
                : ($user->pinned_conversations ?? []);

            // 2. Find all other sponsor users from the same organization
            $sponsorColleagues = User::query()
                ->with(['activeOrganization.media', 'media'])
                ->whereHas('activeOrganization', function ($query) use ($organizationId) {
                    $query->where('organizations.id', $organizationId);
                })
                ->where('id', '!=', $userId)
                ->where('profile_type', ProfileType::SPONSOR)
                ->get();

            $sponsorColleagueIds = $sponsorColleagues->pluck('id')->toArray();
            $allOrgSponsorIds = array_merge([$userId], $sponsorColleagueIds);

            // 3. Get direct conversations for the user
            $userConversations = $this->getUserConversations($userId);

            // Filter out conversations with colleagues
            $userConversations = $userConversations->filter(function ($conversation) use ($sponsorColleagueIds) {
                return !in_array($conversation->other_user_id, $sponsorColleagueIds);
            });

            // 4. Get colleague conversations if there are other sponsors
            $colleagueConversations = collect();
            if (count($sponsorColleagueIds) > 0) {
                // Map of sponsor ID to user object for easy lookup
                $sponsorColleaguesMap = $sponsorColleagues->keyBy('id');

                // Get conversations for all sponsor colleagues (only with non-sponsor users)
                foreach ($sponsorColleagueIds as $sponsorId) {
                    // Get conversations where colleague is one of the participants
                    $conversations = $this->getColleagueConversations($sponsorId, $allOrgSponsorIds);

                    // Process each conversation to add the colleague sponsor's info
                    foreach ($conversations as $conversation) {
                        // Set the colleague sponsor user object
                        $conversation->sponsor_user = $sponsorColleaguesMap[$sponsorId];

                        // Mark as read-only since these are colleague conversations
                        $conversation->is_readonly = true;

                        // Add to the collection
                        $colleagueConversations->push($conversation);
                    }
                }
            }

            // 5. Merge direct and colleague conversations
            $allConversations = $userConversations->concat($colleagueConversations);

            // If no conversations, return empty paginator
            if ($allConversations->isEmpty()) {
                return new LengthAwarePaginator(
                    [],
                    0,
                    $perPage,
                    1,
                    ['path' => request()->url(), 'query' => request()->query()]
                );
            }

            // 6. Load Connection Data
            $allOtherUserIds = $allConversations->pluck('other_user_id')->unique()->values();
            $connectionsMap = collect();
            if ($allOtherUserIds->isNotEmpty()) {
                $connectionsMap = DB::table('connections')
                    ->where(function ($query) use ($userId, $allOtherUserIds) {
                        $query->where('requester_id', $userId)
                            ->whereIn('recipient_id', $allOtherUserIds);
                    })
                    ->orWhere(function ($query) use ($userId, $allOtherUserIds) {
                        $query->whereIn('requester_id', $allOtherUserIds)
                            ->where('recipient_id', $userId);
                    })
                    ->get()
                    ->keyBy(function ($connection) use ($userId) {
                        return $connection->requester_id == $userId ? $connection->recipient_id : $connection->requester_id;
                    });
            }

            // Add connection data to all conversations before further processing
            $allConversations = $allConversations->map(function($conversation) use ($connectionsMap) {
                $otherUserId = $conversation->other_user_id;
                $connection = $connectionsMap->get($otherUserId);
                $conversation->connection_id = $connection->id ?? null;
                $conversation->connection_status = $connection->status ?? null;
                $conversation->connection_requester_id = $connection->requester_id ?? null;
                $conversation->connection_blocked_at = $connection->deleted_at ?? null;
                return $conversation;
            });

            // 7. Load all other users (conversation participants)
            $otherUserIds = $allConversations->pluck('other_user_id')->unique()->values();
            $otherUsers = User::query()
                ->with(['activeOrganization.media'])
                ->whereIn('id', $otherUserIds)
                ->get()
                ->keyBy('id');

            // 8. Process conversations to add user data and organization info
            $processedConversations = $allConversations->map(function($conversation) use ($otherUsers, $pinnedConversations, $userId) {
                // Set pinned status
                $conversation->is_pinned = in_array($conversation->other_user_id, $pinnedConversations);

                // Add other user data if it exists
                if (isset($otherUsers[$conversation->other_user_id])) {
                    $otherUser = $otherUsers[$conversation->other_user_id];

                    // Get avatar URL from Media Library if available
                    $avatarUrl = null;
                    if ($otherUser->hasMedia('avatar')) {
                        $avatarUrl = $otherUser->getFirstMediaUrl('avatar', 'thumb');
                    }

                    // Add user data to the conversation
                    $conversation->other_user = (object)[
                        'id' => $otherUser->id,
                        'first_name' => $otherUser->first_name,
                        'last_name' => $otherUser->last_name,
                        'handle' => $otherUser->handle,
                        'profile_type' => $otherUser->profile_type,
                        'organization_id' => null,
                        'organization_name' => null,
                        'organization_logo_url' => null,
                        'avatar_url' => $avatarUrl,
                    ];

                    // Add organization data if the user is a sponsor
                    if ($otherUser->profile_type === ProfileType::SPONSOR &&
                        $otherUser->activeOrganization->count() > 0) {

                        $org = $otherUser->activeOrganization->first();
                        $conversation->other_user->organization_id = $org->id;
                        $conversation->other_user->organization_name = $org->name;

                        // Add logo URL if available
                        if ($org->hasMedia('logo')) {
                            $conversation->other_user->organization_logo_url = $org->getFirstMediaUrl('logo', 'thumb');
                        }
                    }
                }

                // For read-only conversations from colleagues, add sponsor user data
                if (isset($conversation->is_readonly) &&
                    $conversation->is_readonly &&
                    isset($conversation->sponsor_user)) {

                    $sponsorUser = $conversation->sponsor_user;
                    $conversation->sponsor_user = (object)[
                        'id' => $sponsorUser->id,
                        'first_name' => $sponsorUser->first_name,
                        'last_name' => $sponsorUser->last_name,
                        'handle' => $sponsorUser->handle,
                        'profile_type' => $sponsorUser->profile_type,
                        'organization_id' => null,
                        'organization_name' => null,
                        'organization_logo_url' => null,
                        'avatar_url' => $sponsorUser->hasMedia('avatar') ? $sponsorUser->getFirstMediaUrl('avatar', 'thumb') : null,
                    ];

                    // Add organization data
                    if ($sponsorUser->activeOrganization->count() > 0) {
                        $org = $sponsorUser->activeOrganization->first();
                        $conversation->sponsor_user->organization_id = $org->id;
                        $conversation->sponsor_user->organization_name = $org->name;

                        // Add logo URL if available
                        if ($org->hasMedia('logo')) {
                            $conversation->sponsor_user->organization_logo_url = $org->getFirstMediaUrl('logo', 'thumb');
                        }
                    }
                }

                // Handle flagged messages (find non-flagged content for preview)
                if ($conversation->last_message_is_flagged && $conversation->last_message_recipient_id == $userId) {
                    $nonFlaggedMessage = $this->findNonFlaggedMessageForConversation(
                        $userId,
                        $conversation->other_user_id
                    );

                    if ($nonFlaggedMessage) {
                        $conversation->last_message = $nonFlaggedMessage->content;
                        $conversation->last_message_read_at = $nonFlaggedMessage->read_at;
                        $conversation->last_message_sender_id = $nonFlaggedMessage->sender_id;
                        $conversation->last_message_recipient_id = $nonFlaggedMessage->recipient_id;
                        $conversation->last_message_is_flagged = false;
                    } else {
                        // No valid messages found, use placeholder
                        $conversation->last_message = 'New conversation';
                    }
                }

                return $conversation;
            });

            // 9. Sort: pinned first, then by last message timestamp
            $sortedConversations = $processedConversations->sortByDesc(function ($item) {
                return [
                    (int) $item->is_pinned,
                    $item->last_message_at
                ];
            })->values();

            // 10. Create paginator from the collection
            $paginatedResults = $this->paginateCollection(
                $sortedConversations,
                $perPage,
                request()->query('page', 1)
            );

            return $paginatedResults;

        } catch (\Throwable $e) {
            // Return an empty paginator in case of error
            return new LengthAwarePaginator(
                [],
                0,
                $perPage,
                1,
                ['path' => request()->url(), 'query' => request()->query()]
            );
        }
    }

    /**
     * Get direct conversations for a user.
     *
     * @param int $userId
     * @return \Illuminate\Support\Collection
     */
    private function getUserConversations(int $userId): \Illuminate\Support\Collection
    {
        // Get information about all conversations
        $query = DB::table('conversations')
            ->where(function ($query) use ($userId) {
                $query->where('user_one_id', $userId)
                    ->orWhere('user_two_id', $userId);
            })
            ->join('messages as m', function ($join) {
                $join->on(DB::raw('LEAST(m.sender_id, m.recipient_id)'), '=', 'conversations.user_one_id')
                    ->on(DB::raw('GREATEST(m.sender_id, m.recipient_id)'), '=', 'conversations.user_two_id')
                    ->on('m.created_at', '=', 'conversations.last_message_at');
            });

        // Add select clause
        $query->select([
            'conversations.*',
            'm.id as last_message_id',
            'm.content as last_message',
            'm.read_at as last_message_read_at',
            'm.is_flagged as last_message_is_flagged',
            'm.sender_id as last_message_sender_id',
            'm.recipient_id as last_message_recipient_id',
            DB::raw("CASE WHEN conversations.user_one_id = {$userId} THEN conversations.user_two_id ELSE conversations.user_one_id END as other_user_id"),
            'conversations.message_count',
        ]);

        $conversations = $query->get();

        // Get connection information for these conversations
        if ($conversations->isNotEmpty()) {
            $otherUserIds = $conversations->pluck('other_user_id');

            // Get all connections between user and other users in one query
            $connections = DB::table('connections')
                ->where(function ($query) use ($userId, $otherUserIds) {
                    $query->where('requester_id', $userId)
                        ->whereIn('recipient_id', $otherUserIds);
                })
                ->orWhere(function ($query) use ($userId, $otherUserIds) {
                    $query->whereIn('requester_id', $otherUserIds)
                        ->where('recipient_id', $userId);
                })
                ->get()
                ->keyBy(function ($connection) use ($userId) {
                    // Create a key based on the other user ID
                    return $connection->requester_id == $userId
                        ? $connection->recipient_id
                        : $connection->requester_id;
                });

            // Add connection information to the conversations
            $conversations = $conversations->map(function ($conversation) use ($connections) {
                if (isset($connections[$conversation->other_user_id])) {
                    $connection = $connections[$conversation->other_user_id];
                    $conversation->connection_id = $connection->id;
                    $conversation->connection_status = $connection->status;
                    $conversation->connection_requester_id = $connection->requester_id;
                    $conversation->connection_blocked_at = $connection->deleted_at;
                } else {
                    $conversation->connection_id = null;
                    $conversation->connection_status = null;
                    $conversation->connection_requester_id = null;
                    $conversation->connection_blocked_at = null;
                }

                // Set is_readonly to false for user's own conversations
                $conversation->is_readonly = false;

                return $conversation;
            });
        }

        return $conversations;
    }

    /**
     * Get conversations for a colleague sponsor, excluding conversations with other sponsors.
     *
     * @param int $sponsorId
     * @param array $allSponsorIds
     * @return \Illuminate\Support\Collection
     */
    private function getColleagueConversations(int $sponsorId, array $allSponsorIds): \Illuminate\Support\Collection
    {
        $query = DB::table('conversations')
            ->where(function ($query) use ($sponsorId) {
                $query->where('user_one_id', $sponsorId)
                    ->orWhere('user_two_id', $sponsorId);
            })
            ->join('messages as m', function ($join) {
                $join->on(DB::raw('LEAST(m.sender_id, m.recipient_id)'), '=', 'conversations.user_one_id')
                    ->on(DB::raw('GREATEST(m.sender_id, m.recipient_id)'), '=', 'conversations.user_two_id')
                    ->on('m.created_at', '=', 'conversations.last_message_at');
            })
            ->join('users as u1', 'conversations.user_one_id', '=', 'u1.id')
            ->join('users as u2', 'conversations.user_two_id', '=', 'u2.id')
            ->where(function ($query) use ($sponsorId, $allSponsorIds) {
                // Only include conversations with non-sponsor users
                $query->where(function ($q) use ($sponsorId, $allSponsorIds) {
                    $q->where('conversations.user_one_id', $sponsorId)
                      ->whereNotIn('conversations.user_two_id', $allSponsorIds);
                })
                ->orWhere(function ($q) use ($sponsorId, $allSponsorIds) {
                    $q->where('conversations.user_two_id', $sponsorId)
                      ->whereNotIn('conversations.user_one_id', $allSponsorIds);
                });
            })
            ->select([
                'conversations.*',
                'm.id as last_message_id',
                'm.content as last_message',
                'm.read_at as last_message_read_at',
                'm.is_flagged as last_message_is_flagged',
                'm.sender_id as last_message_sender_id',
                'm.recipient_id as last_message_recipient_id',
                DB::raw("CASE WHEN conversations.user_one_id = {$sponsorId} THEN conversations.user_two_id ELSE conversations.user_one_id END as other_user_id"),
                'conversations.message_count',
            ]);

        return $query->get();
    }

    /**
     * Find a non-flagged message for a conversation preview.
     *
     * @param int $userId
     * @param int $otherUserId
     * @return Message|null
     */
    private function findNonFlaggedMessageForConversation(int $userId, int $otherUserId): ?Message
    {
        return Message::query()
            ->where(function ($query) use ($userId, $otherUserId) {
                // Get messages between these two users
                $query->where(function ($q) use ($userId, $otherUserId) {
                    $q->where('sender_id', $userId)
                      ->where('recipient_id', $otherUserId);
                })
                ->orWhere(function ($q) use ($userId, $otherUserId) {
                    $q->where('sender_id', $otherUserId)
                      ->where('recipient_id', $userId);
                });
            })
            ->where(function ($query) use ($userId) {
                // Only include messages that are safe for the user to see:
                // 1. Messages sent by the user (can see even if flagged)
                $query->where('sender_id', $userId)
                // 2. OR messages received by the user that are explicitly not flagged
                      ->orWhere(function ($q) use ($userId) {
                         $q->where('recipient_id', $userId)
                           ->where('is_flagged', false);
                      });
            })
            ->whereNull('deleted_by_sender_at')
            ->whereNull('deleted_by_recipient_at')
            ->orderBy('created_at', 'desc')
            ->first();
    }

    /**
     * Create a paginator from a collection.
     *
     * @param \Illuminate\Support\Collection $collection
     * @param int $perPage
     * @param int $page
     * @return LengthAwarePaginator
     */
    private function paginateCollection($collection, $perPage, $page)
    {
        $offset = ($page - 1) * $perPage;
        $paginatedItems = $collection->slice($offset, $perPage)->values();

        return new LengthAwarePaginator(
            $paginatedItems,
            $collection->count(),
            $perPage,
            $page,
            ['path' => request()->url(), 'query' => request()->query()]
        );
    }

    /**
     * Count messages sent from a specific sender to a specific recipient.
     *
     * @param int $senderId
     * @param int $recipientId
     * @return int
     */
    public function countMessagesBetweenUsers(int $senderId, int $recipientId): int
    {
        return Message::query()
            ->where('sender_id', $senderId)
            ->where('recipient_id', $recipientId)
            ->count();
    }
}
