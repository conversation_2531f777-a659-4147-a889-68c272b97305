<?php

namespace App\Repositories;

use App\Models\User;
use App\Models\Winner;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SchoolNomineeRepository
{
    /**
     * Get nominees for a specific school
     *
     * @param int $schoolId
     * @param array $filters
     * @return LengthAwarePaginator
     */
    public function getNomineesForSchool(int $schoolId, array $filters = []): LengthAwarePaginator
    {
        Log::info('Repository called with school_id: ' . $schoolId . ' and filters: ' . json_encode($filters));

        // Start with users who have nominations for this school (nominations are linked by email)
        $query = User::query()
            ->whereIn('email', function ($subQuery) use ($schoolId) {
                $subQuery->select('email')
                    ->from('nominations')
                    ->where('school_id', $schoolId);
            })
            ->where(function ($query) {
                $query->where('profile_type', 'positive_athlete')
                    ->orWhere('profile_type', 'positive_coach');
            });

        Log::info('Base query SQL: ' . $query->toSql());

        // Apply search filter if provided
        if (!empty($filters['search'])) {
            $searchTerm = '%' . $filters['search'] . '%';
            $query->where(function ($query) use ($searchTerm) {
                $query->where('first_name', 'ilike', $searchTerm)
                    ->orWhere('last_name', 'ilike', $searchTerm)
                    ->orWhereRaw("CONCAT(first_name, ' ', last_name) ilike ?", [$searchTerm]);
            });
        }

        // Apply graduation year filter if provided
        if (!empty($filters['graduation_year'])) {
            $query->where('graduation_year', $filters['graduation_year']);
        }

        // Apply gender filter if provided (case-insensitive)
        if (!empty($filters['gender'])) {
            $query->whereRaw('LOWER(gender) = ?', [strtolower($filters['gender'])]);
        }

        // Apply profile type filter if provided
        if (!empty($filters['profile_type'])) {
            $query->where('profile_type', $filters['profile_type']);
        }

        // Apply sport filter if provided
        if (!empty($filters['sport'])) {
            $query->whereHas('sports', function ($query) use ($filters) {
                $query->where('name', $filters['sport']);
            });
        }

        // Filter to only include winners if requested
        // Only apply this filter when explicitly requested to show winners only
        if (isset($filters['winners_only']) && filter_var($filters['winners_only'], FILTER_VALIDATE_BOOLEAN)) {
            $query->whereHas('winners', function ($query) {
                $query->where('is_winner', true);
            });
        }

        // Apply date filters if provided
        if (!empty($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        // Apply sorting
        $sortField = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';

        // Map frontend sort fields to database fields if needed
        if ($sortField === 'name') {
            $query->orderBy('first_name', $sortDirection)
                ->orderBy('last_name', $sortDirection);
        } else {
            $query->orderBy($sortField, $sortDirection);
        }

        // Set pagination parameters
        $perPage = $filters['per_page'] ?? 10;
        $page = $filters['page'] ?? 1;

        // Get paginated results with eager loaded relationships
        $results = $query->with([
            'sports',
            'nominations',
            'winners' => function ($query) {
                $query->with(['award', 'scholarship']);
            }
        ])->paginate($perPage, ['*'], 'page', $page);

        Log::info('Final query results count: ' . $results->total());

        return $results;
    }
}
