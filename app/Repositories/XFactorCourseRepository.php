<?php

namespace App\Repositories;

use App\Models\Course;
use App\Models\Topic;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Pagination\LengthAwarePaginator;

class XFactorCourseRepository
{
    /**
     * Get featured courses for a user
     */
    public function getFeaturedCourses(User $user, int $limit): Collection
    {
        return Course::query()
            ->with(['media', 'topics', 'users' => function($query) use ($user) {
                $query->where('user_id', $user->id);
            }])
            ->select('courses.*')
            ->withTotalRuntime()
            ->where('featured', true)
            ->where('published', true)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get courses for a specific topic
     */
    public function getCoursesByTopic(User $user, string $topic, int $page, int $perPage): LengthAwarePaginator
    {
        return Course::query()
            ->with(['media', 'topics'])
            ->leftJoin('course_user', function ($join) use ($user) {
                $join->on('courses.id', '=', 'course_user.course_id')
                    ->where('course_user.user_id', '=', $user->id);
            })
            ->select([
                'courses.*',
                'course_user.completed_at',
                'course_user.last_calculated_completion_percentage as progress'
            ])
            ->withTotalRuntime()
            ->whereHas('topics', function ($query) use ($topic) {
                $query->where('name', $topic);
            })
            ->where('published', true)
            ->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);
    }

    /**
     * Get total count of courses for a topic
     */
    public function getTopicCourseCount(string $topic): int
    {
        return Course::query()
            ->whereHas('topics', function ($query) use ($topic) {
                $query->where('name', $topic);
            })
            ->where('published', true)
            ->count();
    }

    /**
     * Get all available topics
     */
    public function getAllTopics(): Collection
    {
        return Topic::query()
            ->whereHas('courses', function ($query) {
                $query->where('published', true);
            })
            ->orderBy('name')
            ->get();
    }

    /**
     * Search courses with filters
     */
    public function searchCourses(
        User $user,
        ?string $query = null,
        ?array $topics = null,
        ?string $sort = null,
        ?string $status = null,
        int $page = 1,
        int $perPage = 10
    ): LengthAwarePaginator {
        $coursesQuery = Course::query()
            ->with(['media', 'topics'])
            ->leftJoin('course_user', function ($join) use ($user) {
                $join->on('courses.id', '=', 'course_user.course_id')
                    ->where('course_user.user_id', '=', $user->id);
            })
            ->select([
                'courses.*',
                'course_user.completed_at',
                'course_user.last_calculated_completion_percentage as progress',
            ])
            ->withTotalRuntime()
            ->where('published', true);

        // Apply search query
        if ($query) {
            $coursesQuery->where(function ($q) use ($query) {
                $q->where('title', 'ilike', "%{$query}%")
                    ->orWhereHas('topics', function ($q) use ($query) {
                        $q->where('name', 'ilike', "%{$query}%");
                    });
            });
        }

        // Filter by topics
        if ($topics) {
            $coursesQuery->whereHas('topics', function ($q) use ($topics) {
                $q->whereIn('name', $topics);
            });
        }

        // Filter by status
        if ($status) {
            switch ($status) {
                case 'not_started':
                    $coursesQuery->whereNull('course_user.id');
                    break;
                case 'in_progress':
                    $coursesQuery->whereNotNull('course_user.id')
                        ->whereNull('course_user.completed_at');
                    break;
                case 'completed':
                    $coursesQuery->whereNotNull('course_user.completed_at');
                    break;
            }
        }

        // Apply sorting
        switch ($sort) {
            case 'recent':
                $coursesQuery->orderBy('course_user.updated_at', 'desc');
                break;
            case 'progress':
                $coursesQuery->orderBy('course_user.last_calculated_completion_percentage', 'desc');
                break;
            case 'alphabetical':
                $coursesQuery->orderBy('title');
                break;
            default:
                $coursesQuery->orderBy('created_at', 'desc');
        }

        return $coursesQuery->paginate($perPage, ['*'], 'page', $page);
    }

    /**
     * Get a course with all its details including modules, tests, and user progress
     *
     * @throws ModelNotFoundException
     */
    public function getCourseWithDetails(User $user, string $courseId): Course
    {
        return Course::query()
            ->with([
                'media',
                'topics',
                'modules' => function ($query) use ($user) {
                    $query->where('published', true)
                        ->orderBy('course_module.order');
                },
                'modules.users' => function ($query) use ($user) {
                    $query->where('user_id', $user->id)
                        ->withPivot(['completed_at', 'started_at', 'completion_metadata', 'last_position']);
                },
                'modules.media',
                'modules.topics',
                'modules.test' => function ($query) use ($user) {
                    $query->with([
                        'questions' => fn($q) => $q->select('id', 'test_id', 'type', 'question'),
                        'attempts' => fn($q) => $q->where('user_id', $user->id)
                            ->orderBy('completed_at', 'desc')
                    ]);
                },
                'users' => function ($query) use ($user) {
                    $query->where('user_id', $user->id);
                }
            ])
            ->withTotalRuntime()
            ->where('published', true)
            ->findOrFail($courseId);
    }
}
