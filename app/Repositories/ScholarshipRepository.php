<?php

namespace App\Repositories;

use App\Models\Scholarship;
use App\Models\User;
use App\Models\Winner;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ScholarshipRepository
{
    /**
     * Get a paginated list of scholarships with optional filtering.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getScholarships(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Scholarship::query();

        // Apply filters
        if (!empty($filters['name'])) {
            $query->where('name', 'ilike', "%{$filters['name']}%");
        }

        if (isset($filters['year'])) {
            $query->where('year', $filters['year']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        return $query->orderBy('year', 'desc')
            ->orderBy('name', 'asc')
            ->paginate($perPage);
    }

    /**
     * Find a scholarship by ID.
     *
     * @param int $id
     * @return Scholarship|null
     */
    public function findById(int $id): ?Scholarship
    {
        return Scholarship::query()->findOrFail($id);
    }

    /**
     * Create a new scholarship.
     *
     * @param array $data
     * @return Scholarship
     */
    public function create(array $data): Scholarship
    {
        return Scholarship::query()->create($data);
    }

    /**
     * Update an existing scholarship.
     *
     * @param Scholarship $scholarship
     * @param array $data
     * @return Scholarship
     */
    public function update(Scholarship $scholarship, array $data): Scholarship
    {
        $scholarship->update($data);
        return $scholarship;
    }

    /**
     * Soft delete a scholarship.
     *
     * @param Scholarship $scholarship
     * @return bool
     */
    public function delete(Scholarship $scholarship): bool
    {
        return $scholarship->delete();
    }

    /**
     * Get scholarships for a specific user.
     *
     * @param User $user
     * @param array $filters
     * @return Collection
     */
    public function getScholarshipsForUser(User $user, array $filters = []): Collection
    {
        $query = Scholarship::query()
            ->whereHas('winners', function (Builder $query) use ($user) {
                $query->where('user_id', $user->id);
            });

        // Apply filters
        if (isset($filters['is_finalist'])) {
            $query->whereHas('winners', function (Builder $query) use ($user, $filters) {
                $query->where('user_id', $user->id)
                    ->where('is_finalist', $filters['is_finalist']);
            });
        }

        if (isset($filters['is_winner'])) {
            $query->whereHas('winners', function (Builder $query) use ($user, $filters) {
                $query->where('user_id', $user->id)
                    ->where('is_winner', $filters['is_winner']);
            });
        }

        if (isset($filters['year'])) {
            $query->where('year', $filters['year']);
        }

        return $query->orderBy('year', 'desc')
            ->orderBy('name', 'asc')
            ->get();
    }

    /**
     * Check if a scholarship with the same name exists in the given year.
     *
     * @param string $name
     * @param int $year
     * @param int|null $excludeId
     * @return bool
     */
    public function isDuplicate(string $name, int $year, ?int $excludeId = null): bool
    {
        $query = Scholarship::query()
            ->where('name', $name)
            ->where('year', $year);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Activate a scholarship.
     *
     * @param Scholarship $scholarship
     * @return Scholarship
     */
    public function activate(Scholarship $scholarship): Scholarship
    {
        $scholarship->is_active = true;
        $scholarship->save();

        return $scholarship;
    }

    /**
     * Deactivate a scholarship.
     *
     * @param Scholarship $scholarship
     * @return Scholarship
     */
    public function deactivate(Scholarship $scholarship): Scholarship
    {
        $scholarship->is_active = false;
        $scholarship->save();

        return $scholarship;
    }
}
