<?php

namespace App\Traits;

trait SanitizesImportData
{
    /**
     * Sanitize record data to ensure valid UTF-8
     *
     * @param array $record
     * @return array
     */
    protected function sanitizeRecordData(array $record): array
    {
        $sanitized = [];

        foreach ($record as $key => $value) {
            // Skip null values
            if ($value === null) {
                $sanitized[$key] = null;
                continue;
            }

            // Convert to string if not already
            $strValue = (string) $value;

            // Check if the string is valid UTF-8
            if (!mb_check_encoding($strValue, 'UTF-8')) {
                // Try to convert from common encodings to UTF-8
                $convertedValue = mb_convert_encoding($strValue, 'UTF-8', ['ISO-8859-1', 'Windows-1252']);

                // If conversion fails, replace invalid characters with a placeholder
                if (!mb_check_encoding($convertedValue, 'UTF-8')) {
                    $convertedValue = preg_replace('/[\x00-\x1F\x7F-\xFF]/', '', $strValue);
                }

                $sanitized[$key] = $convertedValue;
            } else {
                $sanitized[$key] = $strValue;
            }
        }

        return $sanitized;
    }

    /**
     * Sanitize a string for logging to prevent UTF-8 issues
     *
     * @param string|null $value
     * @return string|null
     */
    protected function sanitizeForLogging(?string $value): ?string
    {
        if ($value === null) {
            return null;
        }

        // Check if the string is valid UTF-8
        if (!mb_check_encoding($value, 'UTF-8')) {
            // Try to convert from common encodings to UTF-8
            $convertedValue = mb_convert_encoding($value, 'UTF-8', ['ISO-8859-1', 'Windows-1252']);

            // If conversion fails, replace invalid characters with a placeholder
            if (!mb_check_encoding($convertedValue, 'UTF-8')) {
                return preg_replace('/[\x00-\x1F\x7F-\xFF]/', '', $value);
            }

            return $convertedValue;
        }

        return $value;
    }

    /**
     * Sanitize results array for logging
     *
     * @param array $results
     * @return array
     */
    protected function sanitizeResultsForLogging(array $results): array
    {
        $sanitized = [];

        foreach ($results as $key => $value) {
            if (is_array($value)) {
                $sanitized[$key] = $this->sanitizeResultsForLogging($value);
            } elseif (is_string($value)) {
                $sanitized[$key] = $this->sanitizeForLogging($value);
            } else {
                $sanitized[$key] = $value;
            }
        }

        return $sanitized;
    }
}
