<?php

namespace App\Traits;

use Illuminate\Support\Facades\Response;

trait HasCsvTemplateDownload
{
    /**
     * Generate CSV content with headers and optional sample data
     */
    protected function generateCsvContent(array $headers, array $sampleData = []): string
    {
        $headers = array_map(function($header) {
            return '"' . str_replace('"', '""', $header) . '"';
        }, $headers);

        $csvContent = implode(',', $headers) . "\n";

        // Add sample data if provided
        if (!empty($sampleData)) {
            $sampleData = array_map(function($value) {
                return '"' . str_replace('"', '""', $value ?? '') . '"';
            }, $sampleData);
            $csvContent .= implode(',', $sampleData) . "\n";
        }

        return $csvContent;
    }

    /**
     * Get the template filename (should be implemented by the using class)
     */
    abstract protected function getTemplateFilename(): string;

    /**
     * Get template headers (should be implemented by the using class)
     */
    abstract protected function getTemplateHeaders(): array;

    /**
     * Get sample data (should be implemented by the using class)
     */
    abstract protected function getTemplateSampleData(): array;

    /**
     * Download the CSV template - Livewire compatible
     */
    public function downloadCsvTemplate()
    {
        $csvContent = $this->generateCsvContent(
            $this->getTemplateHeaders(),
            $this->getTemplateSampleData()
        );

        return Response::streamDownload(
            fn() => print($csvContent),
            $this->getTemplateFilename(),
            ['Content-Type' => 'text/csv']
        );
    }
}
