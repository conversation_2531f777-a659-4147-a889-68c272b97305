<?php

namespace App\Traits;

use Mews\Purifier\Facades\Purifier;

trait SanitizesHtml
{
    /**
     * Sanitize HTML content while preserving valid formatting and structure
     *
     * @param string|null $content
     * @param string $config Configuration to use for HTML Purifier
     * @return string|null
     */
    protected function sanitizeHtml(?string $content, string $config = 'tiptap'): ?string
    {
        if (empty($content)) {
            return $content;
        }

        // Use HTML Purifier with the specified configuration
        return Purifier::clean($content, $config);
    }

    /**
     * Sanitize a specific field in the current object
     *
     * @param string $field The field name to sanitize
     * @param string $config Configuration to use for HTML Purifier
     * @return void
     */
    protected function sanitizeField(string $field, string $config = 'tiptap'): void
    {
        if (property_exists($this, $field) && isset($this->{$field})) {
            $this->{$field} = $this->sanitizeHtml($this->{$field}, $config);
        }
    }

    /**
     * Sanitize multiple HTML rich text fields
     *
     * @param array $fields Array of field names to sanitize
     * @param string $config Configuration to use for HTML Purifier
     * @return void
     */
    protected function sanitizeFields(array $fields, string $config = 'tiptap'): void
    {
        foreach ($fields as $field) {
            $this->sanitizeField($field, $config);
        }
    }
}
