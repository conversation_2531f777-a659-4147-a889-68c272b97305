<?php

namespace App\Actions\Onboarding;

use App\Models\User;
use App\Models\Onboarding;
use App\Services\UserService;
use App\Services\SportService;
use App\Services\AchievementService;
use App\Services\WorkExperienceService;
use App\States\Onboarding\States\AccountInfo;
use Illuminate\Support\Facades\DB;

class CompleteOnboardingAction
{
    public function __construct(
        private readonly UserService $userService,
        private readonly SportService $sportService,
        private readonly AchievementService $achievementService,
        private readonly WorkExperienceService $workExperienceService,
    ) {}

    public function execute(Onboarding $onboarding): User
    {
        // Check if user already exists from this onboarding
        $accountInfo = $onboarding->getStepData(AccountInfo::identifier());
        $existingUser = User::where('email', $accountInfo['email'])->first();

        if ($existingUser) {
            return $existingUser;
        }

        return DB::transaction(function () use ($onboarding) {
            // Create user from account_info (required step)
            $user = $this->userService->createFromOnboarding(
                $onboarding->getStepData('account_info')
            );

            // Update optional user details if present
            $details = $onboarding->getStepData('details');
            if ($details && !empty($details)) {
                $this->userService->updateDetails($user, $details);
            }

            // Attach sports if present
            $sports = $onboarding->getStepData('sports');
            if ($sports && !empty($sports)) {
                $this->sportService->attachUserSports($user, $sports);
            }

            // Create achievements if school involvement data present
            $schoolData = $onboarding->getStepData('school');
            if ($schoolData && !empty($schoolData)) {
                $this->achievementService->createFromOnboarding($user, $schoolData);
            }

            // Create work experience if present
            $workExperience = $onboarding->getStepData('work_experience');
            if ($workExperience && !empty($workExperience)) {
                $this->workExperienceService->createFromOnboarding($user, $workExperience);
            }

            // Update story/bio if present
            $story = $onboarding->getStepData('story');
            if ($story && !empty($story)) {
                $this->userService->updateStory($user, $story);
            }

            return $user;
        });
    }
}
