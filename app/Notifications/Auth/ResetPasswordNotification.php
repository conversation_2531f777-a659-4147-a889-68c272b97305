<?php

namespace App\Notifications\Auth;

use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Notifications\Messages\MailMessage;

class ResetPasswordNotification extends ResetPassword
{
    /**
     * Get the mail representation of the notification.
     */
    public function toMail(mixed $notifiable): MailMessage
    {
        // Generate URL pointing to Next.js frontend instead of Laravel backend
        $frontendUrl = config('app.frontend_url');
        $url = $frontendUrl . '/reset-password/confirm?' . http_build_query([
            'token' => $this->token,
            'email' => $notifiable->getEmailForPasswordReset(),
        ]);

        return (new MailMessage)
            ->markdown('emails.auth.reset-password', [
                'url' => $url,
                'user' => $notifiable,
            ]);
    }
}
