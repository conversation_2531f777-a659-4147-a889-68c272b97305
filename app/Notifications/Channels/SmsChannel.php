<?php

namespace App\Notifications\Channels;

use App\Models\OutboundMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class SmsChannel
{
    /**
     * Send the given notification.
     *
     * @param mixed $notifiable
     * @param Notification $notification The notification should implement toSms() method
     */
    public function send($notifiable, Notification $notification)
    {
        if (!method_exists($notification, 'toSms')) {
            Log::warning('Notification does not implement toSms method', [
                'notification' => get_class($notification),
            ]);
            return;
        }

        $message = $notification->toSms($notifiable);

        if (!$message) {
            return;
        }

        // Get the phone number
        $to = null;

        // Try routeNotificationFor method first (if it exists)
        if (method_exists($notifiable, 'routeNotificationFor')) {
            $to = $notifiable->routeNotificationFor('sms');
        }

        // Fallback to phone/mobile properties
        if (!$to) {
            try {
                // For Eloquent models, try to access phone attributes
                if (method_exists($notifiable, 'getAttribute')) {
                    $to = $notifiable->getAttribute('phone')
                        ?? $notifiable->getAttribute('mobile')
                        ?? $notifiable->getAttribute('nominee_phone');
                }

                // Fallback to direct property access
                if (!$to) {
                    if (property_exists($notifiable, 'phone') && $notifiable->phone) {
                        $to = $notifiable->phone;
                    } elseif (property_exists($notifiable, 'mobile') && $notifiable->mobile) {
                        $to = $notifiable->mobile;
                    } elseif (property_exists($notifiable, 'nominee_phone') && $notifiable->nominee_phone) {
                        $to = $notifiable->nominee_phone;
                    }
                }

                // Final fallback using isset
                if (!$to) {
                    if (isset($notifiable->phone)) {
                        $to = $notifiable->phone;
                    } elseif (isset($notifiable->mobile)) {
                        $to = $notifiable->mobile;
                    } elseif (isset($notifiable->nominee_phone)) {
                        $to = $notifiable->nominee_phone;
                    }
                }
            } catch (\Exception $e) {
                // Ignore exceptions and continue
            }
        }

        if (!$to) {
            Log::warning('No phone number found for notifiable', [
                'notifiable_id' => $notifiable->id,
                'notifiable_type' => get_class($notifiable),
                'notification' => get_class($notification),
            ]);
            return;
        }

        // Extract content (SMS doesn't have subject)
        $content = $this->extractSmsContent($message);

        // Get template information if available
        $templateId = null;
        $templateData = [];

        if (method_exists($notification, 'getTemplateId')) {
            $templateId = $notification->getTemplateId();
        }

        if (method_exists($notification, 'getTemplateData')) {
            $templateData = $notification->getTemplateData();
        }

        // Determine the user_id for the OutboundMessage
        // The user_id field has a foreign key constraint to users table
        $userId = $this->extractUserId($notifiable);

        if (!$userId) {
            Log::warning('Unable to determine user_id for outbound message', [
                'notifiable_id' => $notifiable->id,
                'notifiable_type' => get_class($notifiable),
                'notification' => get_class($notification),
            ]);
            return;
        }

        // Always log to OutboundMessage
        $outboundMessage = OutboundMessage::create([
            'user_id' => $userId,
            'channel' => OutboundMessage::CHANNEL_SMS,
            'to_address' => $to,
            'subject' => null, // SMS doesn't have subject
            'content' => $content,
            'template_id' => $templateId,
            'template_data' => $templateData,
            'status' => OutboundMessage::STATUS_LOGGED,
            'environment' => config('app.env'),
        ]);

        // Send via external service if enabled
        if (config('messaging.send_external_messages') && config('messaging.sms.enabled')) {
            try {
                // Override recipient in development if configured
                $devOverride = config('messaging.environment.dev_recipient_override');
                if ($devOverride && config('app.env') !== 'production') {
                    $to = $devOverride;
                }

                // Send via Twilio
                $this->sendViaTwilio($to, $content, $outboundMessage);

            } catch (\Exception $e) {
                // Mark as failed
                $outboundMessage->markAsFailed($e->getMessage());

                Log::error('Failed to send SMS via external service', [
                    'outbound_message_id' => $outboundMessage->id,
                    'to' => $to,
                    'content' => $content,
                    'error' => $e->getMessage(),
                ]);

                // Re-throw to ensure notification system handles the error
                throw $e;
            }
        } else {
            // Development mode - just log
            Log::info('SMS logged (not sent - external sending disabled)', [
                'outbound_message_id' => $outboundMessage->id,
                'to' => $to,
                'content' => $content,
                'environment' => config('app.env'),
            ]);
        }

        return $outboundMessage;
    }

    /**
     * Send SMS via Twilio service.
     */
    protected function sendViaTwilio(string $to, string $content, OutboundMessage $outboundMessage): void
    {
        // TODO: Implement actual Twilio integration in Phase 3
        // For now, simulate successful sending

        $sid = config('messaging.sms.twilio.account_sid');
        $token = config('messaging.sms.twilio.auth_token');
        $from = config('messaging.sms.twilio.from_number');

        if (!$sid || !$token || !$from) {
            throw new \Exception('Twilio configuration missing: account_sid, auth_token, or from_number');
        }

        // Simulate Twilio API call
        // In Phase 3, this will be replaced with actual Twilio SDK integration
        $externalId = 'SM' . uniqid(); // Simulate Twilio message SID

        $outboundMessage->markAsSent($externalId);

        Log::info('SMS sent successfully via Twilio (simulated)', [
            'outbound_message_id' => $outboundMessage->id,
            'to' => $to,
            'content' => $content,
            'external_id' => $externalId,
        ]);
    }

    /**
     * Extract content from SMS message.
     */
    protected function extractSmsContent($message): string
    {
        if (is_string($message)) {
            return $message;
        }

        if (is_array($message) && isset($message['content'])) {
            return $message['content'];
        }

        if (is_object($message) && method_exists($message, 'getContent')) {
            return $message->getContent();
        }

        if (method_exists($message, 'toArray')) {
            $array = $message->toArray();
            return $array['content'] ?? json_encode($array);
        }

        return json_encode($message);
    }

    /**
     * Extract user ID from notifiable.
     */
    protected function extractUserId($notifiable): ?int
    {
        // If the notifiable is already a User, return its ID
        if ($notifiable instanceof \App\Models\User) {
            return $notifiable->id;
        }

        // If the notifiable has a user relationship, use that
        if (method_exists($notifiable, 'user') && $notifiable->user) {
            return $notifiable->user->id;
        }

        // If the notifiable has a user_id field, use that (already linked)
        if (property_exists($notifiable, 'user_id') && $notifiable->user_id) {
            return $notifiable->user_id;
        }

        // For Nomination model, try to get the associated user via email
        if ($notifiable instanceof \App\Models\Nomination) {
            // Try to find user by the nomination's email
            $user = \App\Models\User::query()->where('email', $notifiable->email)->first();
            if ($user) {
                return $user->id;
            }
        }

        return null;
    }
}
