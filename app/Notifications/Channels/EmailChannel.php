<?php

namespace App\Notifications\Channels;

use App\Models\OutboundMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class EmailChannel
{
    /**
     * Send the given notification.
     */
    public function send($notifiable, Notification $notification)
    {
        $message = $notification->toMail($notifiable);

        if (!$message) {
            return;
        }

        // Get the email address
        $to = null;

        // Try routeNotificationFor method first (if it exists)
        if (method_exists($notifiable, 'routeNotificationFor')) {
            $to = $notifiable->routeNotificationFor('mail');
        }

        // Fallback to email attribute/property
        if (!$to) {
            try {
                // For Eloquent models, try to access the email attribute
                if (method_exists($notifiable, 'getAttribute') && $notifiable->getAttribute('email')) {
                    $to = $notifiable->getAttribute('email');
                } elseif (property_exists($notifiable, 'email') && $notifiable->email) {
                    $to = $notifiable->email;
                } elseif (isset($notifiable->email)) {
                    $to = $notifiable->email;
                }
            } catch (\Exception $e) {
                // Ignore exceptions and continue
            }
        }

        if (!$to) {
            Log::warning('No email address found for notifiable', [
                'notifiable_id' => $notifiable->id,
                'notifiable_type' => get_class($notifiable),
                'notification' => get_class($notification),
            ]);
            return;
        }

        // Prepare content for logging
        $subject = $message->subject ?? '';
        $content = $this->extractEmailContent($message);

        // Get template information if available
        $templateId = null;
        $templateData = [];

        if (method_exists($notification, 'getTemplateId')) {
            $templateId = $notification->getTemplateId();
        }

        if (method_exists($notification, 'getTemplateData')) {
            $templateData = $notification->getTemplateData();
        }

        // Determine the user_id for the OutboundMessage
        // The user_id field has a foreign key constraint to users table
        $userId = $this->extractUserId($notifiable);

        if (!$userId) {
            Log::warning('Unable to determine user_id for outbound message', [
                'notifiable_id' => $notifiable->id,
                'notifiable_type' => get_class($notifiable),
                'notification' => get_class($notification),
            ]);
            return;
        }

        // Always log to OutboundMessage
        $outboundMessage = OutboundMessage::create([
            'user_id' => $userId,
            'channel' => OutboundMessage::CHANNEL_EMAIL,
            'to_address' => $to,
            'subject' => $subject,
            'content' => $content,
            'template_id' => $templateId,
            'template_data' => $templateData,
            'status' => config('messaging.send_external_messages')
                ? OutboundMessage::STATUS_LOGGED
                : OutboundMessage::STATUS_LOGGED,
            'environment' => config('app.env'),
        ]);

        // Send via external service if enabled
        if (config('messaging.send_external_messages') && config('messaging.email.enabled')) {
            try {
                // Override recipient in development if configured
                $devOverride = config('messaging.environment.dev_recipient_override');
                if ($devOverride && config('app.env') !== 'production') {
                    $to = $devOverride;
                }

                // Send the email using the proper Laravel Mail API
                // Convert MailMessage to sendable format
                if (method_exists($message, 'render')) {
                    // If the message can be rendered to HTML, use Mail::send() with a closure
                    Mail::send([], [], function ($mailMessage) use ($message, $to) {
                        $mailMessage->to($to)
                                   ->subject($message->subject ?? 'Notification')
                                   ->html($message->render());
                    });
                } else {
                    // Fallback: extract content and use Mail::raw()
                    $content = $this->extractEmailContent($message);
                    $subject = $message->subject ?? 'Notification';

                    Mail::raw($content, function ($mailMessage) use ($to, $subject) {
                        $mailMessage->to($to)->subject($subject);
                    });
                }

                // Update status to sent
                $outboundMessage->markAsSent();

                Log::info('Email sent successfully via external service', [
                    'outbound_message_id' => $outboundMessage->id,
                    'to' => $to,
                    'subject' => $subject,
                ]);

            } catch (\Exception $e) {
                // Mark as failed
                $outboundMessage->markAsFailed($e->getMessage());

                Log::error('Failed to send email via external service', [
                    'outbound_message_id' => $outboundMessage->id,
                    'to' => $to,
                    'subject' => $subject,
                    'error' => $e->getMessage(),
                ]);

                // Re-throw to ensure notification system handles the error
                throw $e;
            }
        } else {
            // Development mode - just log
            Log::info('Email logged (not sent - external sending disabled)', [
                'outbound_message_id' => $outboundMessage->id,
                'to' => $to,
                'subject' => $subject,
                'environment' => config('app.env'),
            ]);
        }

        return $outboundMessage;
    }

    /**
     * Extract content from email message object.
     */
    protected function extractEmailContent($message): string
    {
        // Handle different message types
        if (method_exists($message, 'render')) {
            return $message->render();
        }

        if (method_exists($message, 'toArray')) {
            $array = $message->toArray();
            return $array['body'] ?? $array['content'] ?? json_encode($array);
        }

        if (is_string($message)) {
            return $message;
        }

        return json_encode($message);
    }

    /**
     * Extract user ID from notifiable.
     */
    protected function extractUserId($notifiable): ?int
    {
        // If the notifiable is already a User, return its ID
        if ($notifiable instanceof \App\Models\User) {
            return $notifiable->id;
        }

        // If the notifiable has a user relationship, use that
        if (method_exists($notifiable, 'user') && $notifiable->user) {
            return $notifiable->user->id;
        }

        // If the notifiable has a user_id field, use that (already linked)
        if (property_exists($notifiable, 'user_id') && $notifiable->user_id) {
            return $notifiable->user_id;
        }

        // For Nomination model, try to get the associated user via email
        if ($notifiable instanceof \App\Models\Nomination) {
            // Try to find user by the nomination's email
            $user = \App\Models\User::query()->where('email', $notifiable->email)->first();
            if ($user) {
                return $user->id;
            }
        }

        return null;
    }
}
