<?php

namespace App\Notifications;

use App\Models\EmailTemplate;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

abstract class MultiChannelNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The email template to use for this notification.
     */
    protected ?EmailTemplate $emailTemplate = null;

    /**
     * The data to use for template variable substitution.
     */
    protected array $templateData = [];

    /**
     * Create a new notification instance.
     */
    public function __construct(?EmailTemplate $emailTemplate = null, array $templateData = [])
    {
        $this->emailTemplate = $emailTemplate;
        $this->templateData = $templateData;

        // Set queue configuration from messaging config
        $this->onQueue(config('messaging.queue.email_queue', 'emails'));
        $this->onConnection(config('messaging.queue.connection'));
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        $channels = ['database']; // Always log to database for in-app notifications

        // Add custom channels based on configuration
        if ($this->shouldSendEmail($notifiable)) {
            $channels[] = \App\Notifications\Channels\EmailChannel::class;
        }

        if ($this->shouldSendSms($notifiable)) {
            $channels[] = \App\Notifications\Channels\SmsChannel::class;
        }

        return $channels;
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): ?MailMessage
    {
        if (!$this->emailTemplate) {
            return null;
        }

        // Process template with context-aware data
        $processed = $this->emailTemplate->processTemplateWithContext(
            $this->getContextualTemplateData($notifiable),
            $this->templateData
        );

        return (new MailMessage)
            ->subject($processed['subject'])
            ->line($processed['preview_text'])
            ->line($processed['body']);
    }

    /**
     * Get the SMS representation of the notification.
     */
    public function toSms($notifiable): ?string
    {
        if (!$this->emailTemplate) {
            return null;
        }

        // Process template with context-aware data
        $processed = $this->emailTemplate->processTemplateWithContext(
            $this->getContextualTemplateData($notifiable),
            $this->templateData
        );

        // For SMS, we'll use the body content (subject would be too short)
        $content = strip_tags($processed['body']);
        return strlen($content) > 160 ? substr($content, 0, 157) . '...' : $content;
    }

    /**
     * Get the array representation of the notification for database storage.
     */
    public function toArray($notifiable): array
    {
        return [
            'template_id' => $this->emailTemplate?->id,
            'template_data' => $this->templateData,
            'message' => 'Multi-channel notification processed',
            'channels' => $this->via($notifiable),
        ];
    }

    /**
     * Get the template ID for logging purposes.
     */
    public function getTemplateId(): ?int
    {
        return $this->emailTemplate?->id;
    }

    /**
     * Get the template data for logging purposes.
     */
    public function getTemplateData(): array
    {
        return $this->templateData;
    }

    /**
     * Determine if email should be sent to the notifiable.
     */
    protected function shouldSendEmail($notifiable): bool
    {
        return $this->emailTemplate !== null
            && config('messaging.email.enabled', true)
            && !empty($notifiable->email);
    }

    /**
     * Determine if SMS should be sent to the notifiable.
     */
    protected function shouldSendSms($notifiable): bool
    {
        return config('messaging.sms.enabled', true)
            && !empty($notifiable->phone ?? $notifiable->mobile);
    }

    /**
     * Get contextual template data based on notification type and recipient.
     * This method provides context-aware data sources for the VariableSubstitutionService.
     */
    protected function getContextualTemplateData($notifiable): array
    {
        $data = [];

        // Always include user context if notifiable is a user
        if ($notifiable) {
            $data['user'] = $this->prepareUserContext($notifiable);
        }

        // Include nominee context if available in template data
        if (isset($this->templateData['nominee'])) {
            $data['nominee'] = $this->prepareNomineeContext($this->templateData['nominee']);
        }

        // Include nominator context if available in template data
        if (isset($this->templateData['nominator'])) {
            $data['nominator'] = $this->prepareNominatorContext($this->templateData['nominator']);
        }

        return $data;
    }

    /**
     * Prepare user context data.
     */
    protected function prepareUserContext($notifiable): array
    {
        if (is_array($notifiable)) {
            return $notifiable;
        }

        return [
            'id' => $notifiable->id ?? '',
            'firstname' => $notifiable->firstname ?? $notifiable->first_name ?? '',
            'lastname' => $notifiable->lastname ?? $notifiable->last_name ?? '',
            'name' => $notifiable->name ?? trim(($notifiable->firstname ?? $notifiable->first_name ?? '') . ' ' . ($notifiable->lastname ?? $notifiable->last_name ?? '')),
            'email' => $notifiable->email ?? '',
            'phone' => $notifiable->phone ?? $notifiable->mobile ?? '',
            'graduation_year' => $notifiable->graduation_year ?? '',
            'created_at' => optional($notifiable->created_at)->format('F j, Y') ?? '',
            'updated_at' => optional($notifiable->updated_at)->format('F j, Y') ?? '',
        ];
    }

    /**
     * Prepare nominee context data.
     */
    protected function prepareNomineeContext($nominee): array
    {
        if (is_array($nominee)) {
            return $nominee;
        }

        return [
            'id' => $nominee->id ?? '',
            'firstname' => $nominee->firstname ?? $nominee->first_name ?? '',
            'lastname' => $nominee->lastname ?? $nominee->last_name ?? '',
            'name' => $nominee->name ?? trim(($nominee->firstname ?? $nominee->first_name ?? '') . ' ' . ($nominee->lastname ?? $nominee->last_name ?? '')),
            'email' => $nominee->email ?? '',
            'phone' => $nominee->phone ?? '',
            'grade' => $nominee->grade ?? '',
            'gender' => $nominee->gender ?? '',
            'sport1' => $nominee->sport1 ?? '',
            'sport2' => $nominee->sport2 ?? '',
            'sport3' => $nominee->sport3 ?? '',
            'school' => $nominee->school ?? '',
            'nominator_name' => $nominee->nominator_name ?? '',
            'nomination_type' => $nominee->nomination_type ?? '',
            'created_at' => optional($nominee->created_at)->format('F j, Y') ?? '',
        ];
    }

    /**
     * Prepare nominator context data.
     */
    protected function prepareNominatorContext($nominator): array
    {
        if (is_array($nominator)) {
            return $nominator;
        }

        return [
            'id' => $nominator->id ?? '',
            'firstname' => $nominator->firstname ?? $nominator->first_name ?? '',
            'lastname' => $nominator->lastname ?? $nominator->last_name ?? '',
            'name' => $nominator->name ?? trim(($nominator->firstname ?? $nominator->first_name ?? '') . ' ' . ($nominator->lastname ?? $nominator->last_name ?? '')),
            'email' => $nominator->email ?? '',
            'phone' => $nominator->phone ?? '',
            'title' => $nominator->title ?? '',
            'organization' => $nominator->organization ?? '',
            'created_at' => optional($nominator->created_at)->format('F j, Y') ?? '',
        ];
    }

    /**
     * Get variables for template processing.
     * This method maintains backward compatibility while leveraging the new service.
     */
    protected function getTemplateVariables($notifiable): array
    {
        // For backward compatibility, merge contextual data into flat structure
        $contextualData = $this->getContextualTemplateData($notifiable);
        $flatData = [];

        foreach ($contextualData as $context => $data) {
            foreach ($data as $key => $value) {
                $flatData["{$context}.{$key}"] = $value;
            }
        }

        // Also include flat user data for existing templates
        if (isset($contextualData['user'])) {
            $flatData = array_merge($flatData, $contextualData['user']);
        }

        // Merge with any additional template data
        return array_merge($flatData, $this->templateData);
    }

    /**
     * Get the number of times the job may be attempted.
     */
    public function tries(): int
    {
        return config('messaging.queue.retry_attempts', 3);
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoffDelay(): int
    {
        return config('messaging.queue.retry_delay', 60);
    }
}
