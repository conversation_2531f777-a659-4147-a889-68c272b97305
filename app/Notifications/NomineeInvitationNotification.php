<?php

namespace App\Notifications;

use App\Models\EmailTemplate;

class NomineeInvitationNotification extends MultiChannelNotification
{
    protected $nominee;
    protected $nominator;

    /**
     * Create a new notification instance.
     *
     * @param EmailTemplate|null $emailTemplate The template to use
     * @param mixed $nominee The nominee data/model
     * @param mixed $nominator The nominator data/model
     * @param array $additionalData Any additional template data
     */
    public function __construct(
        ?EmailTemplate $emailTemplate = null,
        $nominee = null,
        $nominator = null,
        array $additionalData = []
    ) {
        $this->nominee = $nominee;
        $this->nominator = $nominator;

        // Prepare template data with nominee and nominator contexts
        $templateData = array_merge($additionalData, [
            'nominee' => $nominee,
            'nominator' => $nominator,
        ]);

        parent::__construct($emailTemplate, $templateData);
    }

    /**
     * Get contextual template data specific to nominee invitations.
     */
    protected function getContextualTemplateData($notifiable): array
    {
        $data = parent::getContextualTemplateData($notifiable);

        // Override nominee and nominator contexts with our specific data
        if ($this->nominee) {
            $data['nominee'] = $this->prepareNomineeContext($this->nominee);
        }

        if ($this->nominator) {
            $data['nominator'] = $this->prepareNominatorContext($this->nominator);
        }

        return $data;
    }

    /**
     * Determine if this notification should send via email.
     */
    protected function shouldSendEmail($notifiable): bool
    {
        // For nominee invitations, we can send to the nominee's email even if notifiable is different
        $hasNomineeEmail = !empty($this->nominee['email'] ?? $this->nominee->email ?? null);
        $hasNotifiableEmail = !empty($notifiable->email ?? null);

        return $this->emailTemplate !== null
            && config('messaging.email.enabled', true)
            && ($hasNotifiableEmail || $hasNomineeEmail);
    }

    /**
     * Determine if this notification should send via SMS.
     */
    protected function shouldSendSms($notifiable): bool
    {
        // For nominee invitations, we can send to the nominee's phone even if notifiable is different
        $hasNomineePhone = !empty($this->nominee['phone'] ?? $this->nominee->phone ?? null);
        $hasNotifiablePhone = !empty($notifiable->phone ?? $notifiable->mobile ?? null);

        return config('messaging.sms.enabled', true)
            && ($hasNotifiablePhone || $hasNomineePhone);
    }
}
