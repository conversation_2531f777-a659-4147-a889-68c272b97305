<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\ValidationRule;

class VideoTimeRange implements ValidationRule, DataAwareRule
{
    /**
     * All of the data under validation.
     *
     * @var array<string, mixed>
     */
    protected $data = [];

    /**
     * Set the data under validation.
     *
     * @param  array<string, mixed>  $data
     */
    public function setData(array $data): static
    {
        $this->data = $data;

        return $this;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // If this is the end_time field and both start and end times are provided
        if ($attribute === 'video_end_time' &&
            isset($this->data['video_start_time']) &&
            $this->data['video_start_time'] !== null &&
            $value !== null) {

            $startTime = (int) $this->data['video_start_time'];
            $endTime = (int) $value;

            // Ensure end time is greater than start time
            if ($endTime <= $startTime) {
                $fail('The video end time must be greater than the start time.');
            }
        }
    }
}
