<?php

namespace App\Rules;

use App\Models\Nomination;
use Closure;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\ValidationRule;

class UniqueNominatorNominee implements ValidationRule, DataAwareRule
{
    protected array $data = [];

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $exists = Nomination::query()
            ->where('email', $this->data['email'])
            ->where('nominator_email', $this->data['nominator_email'])
            ->exists();

        if ($exists) {
            $fail('You have already nominated this person.');
        }
    }

    public function setData(array $data): static
    {
        $this->data = $data;
        return $this;
    }
}
