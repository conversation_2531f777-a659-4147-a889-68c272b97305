<?php

namespace App\Observers;

use App\Models\User;
use App\Models\Nomination;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UserObserver
{
    /**
     * Handle the User "saving" event.
     */
    public function saving(User $user): void
    {
        // Only proceed if email is being updated, the original email exists, and the new email is not null
        if ($user->isDirty('email') && $user->getOriginal('email') && $user->email !== null) {
            // Get the original email before the change
            $originalEmail = $user->getOriginal('email');

            Log::info('Updating nominations with new email', [
                'user_id' => $user->id,
                'original_email' => $originalEmail,
                'new_email' => $user->email
            ]);

            // Find all nominations with case-insensitive matching and update them to match the user's email case
            Nomination::query()
                ->where(DB::raw('LOWER(email)'), '=', strtolower($originalEmail))
                ->update(['email' => $user->email]);

            // Update all outgoing nominations that reference the old email
            Nomination::query()
                ->where(DB::raw('LOWER(nominator_email)'), '=', strtolower($originalEmail))
                ->update(['nominator_email' => $user->email]);
        }
    }
}
