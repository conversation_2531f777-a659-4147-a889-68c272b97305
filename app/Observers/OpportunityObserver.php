<?php

namespace App\Observers;

use App\Models\LocationCoordinate;
use App\Models\Opportunity;
use App\Services\GeocodingService;
use Illuminate\Support\Facades\Log;

class OpportunityObserver
{
    /**
     * Handle the Opportunity "saving" event.
     */
    public function saving(Opportunity $opportunity): void
    {
        // Skip if city or state code is missing
        if (empty($opportunity->city) || empty($opportunity->state_code)) {
            return;
        }

        // Check if coordinates already exist for this location
        $exists = LocationCoordinate::query()
            ->where('city', $opportunity->city)
            ->where('state_code', $opportunity->state_code)
            ->exists();

        // If coordinates don't exist, create a placeholder record
        if (!$exists) {
            LocationCoordinate::create([
                'city' => $opportunity->city,
                'state_code' => $opportunity->state_code,
                'latitude' => null,
                'longitude' => null,
            ]);

            Log::info("Created placeholder location record for: {$opportunity->city}, {$opportunity->state_code}");
        }
    }
}
