<?php

namespace App\Mail\SystemInvites;

use App\Data\SystemInvites\PositiveCoachInviteData;

class PositiveCoachInviteMail extends BaseSystemInviteMail
{
    protected function getSubject(): string
    {
        return 'You\'ve Been Nominated as a Positive Coach!';
    }

    protected function getIntroText(): string
    {
        /** @var PositiveCoachInviteData $inviteData */
        $inviteData = $this->invite->invite_data;
        $nomination = $inviteData->nomination;

        return "Congratulations! You've been nominated as a Positive Coach by {$nomination->nominator_first_name} {$nomination->nominator_last_name}. " .
            "This recognition celebrates your positive impact on student-athletes and your dedication to fostering a positive sports environment.";
    }

    protected function getCallToAction(): string
    {
        return 'Create Your Positive Coach Profile';
    }
}
