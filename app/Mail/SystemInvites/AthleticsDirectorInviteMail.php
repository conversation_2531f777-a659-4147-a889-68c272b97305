<?php

namespace App\Mail\SystemInvites;

use App\Models\SystemInvite;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class AthleticsDirectorInviteMail extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        private readonly SystemInvite $invite,
        private readonly string $registrationUrl
    ) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Welcome to Positive Athlete - Complete Your Athletics Director Profile',
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'emails.system-invites.athletics-director',
            with: [
                'invite' => $this->invite,
                'registrationUrl' => $this->registrationUrl,
                'mobileCode' => $this->invite->mobile_code,
            ],
        );
    }
}
