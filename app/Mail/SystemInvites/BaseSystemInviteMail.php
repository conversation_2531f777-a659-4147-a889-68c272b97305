<?php

namespace App\Mail\SystemInvites;

use App\Models\SystemInvite;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

abstract class BaseSystemInviteMail extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        protected SystemInvite $invite,
        protected string $registrationUrl
    ) {}

    abstract protected function getSubject(): string;
    abstract protected function getIntroText(): string;
    abstract protected function getCallToAction(): string;

    public function build(): self
    {
        return $this
            ->subject($this->getSubject())
            ->markdown('emails.system-invite', [
                'invite' => $this->invite,
                'registrationUrl' => $this->registrationUrl,
                'introText' => $this->getIntroText(),
                'callToAction' => $this->getCallToAction(),
                'mobileCode' => $this->invite->mobile_code,
            ]);
    }
}
