<?php

namespace App\Mail\SystemInvites;

class PositiveAthleteInviteMail extends BaseSystemInviteMail
{
    protected function getSubject(): string
    {
        return 'You\'ve Been Nominated as a Positive Athlete!';
    }

    protected function getIntroText(): string
    {
        /** @var PositiveAthleteInviteData $inviteData */
        $inviteData = $this->invite->invite_data;
        $nomination = $inviteData->nomination;

        return "Congratulations! You've been nominated as a Positive Athlete by {$nomination->first_name} {$nomination->last_name}. " .
            "This recognition celebrates your positive impact both in sports and in your community.";
    }

    protected function getCallToAction(): string
    {
        return 'Create Your Positive Athlete Profile';
    }
}
