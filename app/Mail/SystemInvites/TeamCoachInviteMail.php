<?php

namespace App\Mail\SystemInvites;

use App\Models\SystemInvite;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class TeamCoachInviteMail extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        private readonly SystemInvite $invite,
        private readonly string $registrationUrl
    ) {}

    public function build(): self
    {
        /** @var TeamCoachInviteData $inviteData */
        $inviteData = $this->invite->invite_data;
        return $this->markdown('emails.system-invites.team-coach')
            ->subject('Complete Your Team Coach Account Setup')
            ->with([
                'data' => $inviteData->toArray(),
                'registrationUrl' => $this->registrationUrl,
                'mobileCode' => $this->invite->mobile_code,
            ]);
    }
}
