<?php

namespace App\Mail\SystemInvites;

use App\Models\SystemInvite;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class AlumniInviteMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(protected SystemInvite $invite)
    {
        //
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'You\'ve Been Invited to Join Positive Athlete as an Alumni',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        $data = $this->invite->invite_data;

        return new Content(
            view: 'emails.system-invites.alumni',
            with: [
                'inviteUrl' => url(route('onboarding.start', ['token' => $this->invite->token])),
                'mobileCode' => $this->invite->mobile_code,
                'first_name' => $data->first_name,
                'last_name' => $data->last_name,
                'expires_at' => $this->invite->expires_at->format('F j, Y'),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
