<?php

namespace App\Mail\SystemInvites;

use App\Models\SystemInvite;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class TeamStudentInviteMail extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        public SystemInvite $invite,
        public string $registrationUrl,
    ) {
    }

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Welcome to Your Team on Positive Athlete',
        );
    }

    public function content(): Content
    {
        return new Content(
            markdown: 'emails.system-invites.team-student',
            with: [
                'invite' => $this->invite,
                'registrationUrl' => $this->registrationUrl,
                'mobileCode' => $this->invite->mobile_code,
            ],
        );
    }
}
