<?php

namespace App\Mail\SystemInvites;

use App\Models\SystemInvite;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class SponsorInviteMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public function __construct(
        public SystemInvite $invite,
        public string $registrationUrl
    ) {
    }

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'You\'re Invited to Join Positive Athlete as a Sponsor!',
        );
    }

    public function content(): Content
    {
        return new Content(
            markdown: 'emails.system-invites.sponsor-invite',
            with: [
                'invite' => $this->invite,
                'registrationUrl' => $this->registrationUrl,
                'mobileCode' => $this->invite->mobile_code,
            ],
        );
    }

    public function attachments(): array
    {
        return [];
    }
}
