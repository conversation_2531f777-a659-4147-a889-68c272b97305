<?php

namespace App\Policies;

use App\Models\User;
use App\Enums\ProfileType;
use Illuminate\Auth\Access\Response;
use Illuminate\Support\Facades\Log;

class PublicProfilePolicy
{
    /**
     * Profile types that are allowed to have public profiles
     */
    private const ALLOWED_PUBLIC_PROFILE_TYPES = [
        ProfileType::POSITIVE_ATHLETE,
        ProfileType::COLLEGE_ATHLETE,
        ProfileType::PROFESSIONAL,
        ProfileType::POSITIVE_COACH,
    ];

    /**
     * Determine whether the user can view the model publicly.
     */
    public function viewPublicProfile(?User $user, User $model): Response
    {
        // Log debug information for troubleshooting
        Log::info('PublicProfilePolicy::viewPublicProfile', [
            'authenticated_user_id' => $user?->id,
            'authenticated_user_profile_type' => $user?->profile_type?->value,
            'target_user_id' => $model->id,
            'target_user_profile_type' => $model->profile_type?->value,
            'target_user_parent_id' => $model->parent_id,
            'target_user_public_profile' => $model->public_profile,
            'is_auth_user_null' => is_null($user),
        ]);

        // First check: Is this profile type allowed to have public profiles?
        if (!in_array($model->profile_type, self::ALLOWED_PUBLIC_PROFILE_TYPES)) {
            Log::info('Profile type not allowed for public viewing', [
                'profile_type' => $model->profile_type?->value
            ]);
            return Response::denyWithStatus(404, 'User not found or profile not available for public viewing');
        }

        // Second check: Parent accessing their child's profile
        if ($user &&
            $user->profile_type === ProfileType::PARENT &&
            $model->parent_id &&
            $model->parent_id === $user->id) {

            Log::info('Parent accessing child profile - ALLOWED', [
                'parent_id' => $user->id,
                'child_parent_id' => $model->parent_id,
            ]);
            return Response::allow();
        }

        // Third check: Profile is marked as public
        if ($model->public_profile) {
            Log::info('Public profile access - ALLOWED');
            return Response::allow();
        }

        // Default: Deny access with 404 to avoid leaking information about private profiles
        Log::info('Access denied - private profile with no parent relationship');
        return Response::denyWithStatus(404, 'User not found or profile not available for public viewing');
    }

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(?User $user): bool
    {
        return true; // Anyone can attempt to view public profiles
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(?User $user, User $model): Response
    {
        return $this->viewPublicProfile($user, $model);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return false; // Public profiles cannot be created via this endpoint
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, User $model): bool
    {
        return false; // Public profiles cannot be updated via this endpoint
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, User $model): bool
    {
        return false; // Public profiles cannot be deleted via this endpoint
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, User $model): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, User $model): bool
    {
        return false;
    }
}
