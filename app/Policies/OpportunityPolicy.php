<?php

namespace App\Policies;

use App\Enums\ProfileType;
use App\Models\Opportunity;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class OpportunityPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any opportunities.
     *
     * Automatically filtered to only show opportunities from the user's organizations
     * in the repository layer.
     *
     * @param User $user
     * @return bool
     */
    public function viewAny(User $user): bool
    {
        return $user->profile_type === ProfileType::SPONSOR;
    }

    /**
     * Determine whether the user can view the opportunity.
     *
     * @param User $user
     * @param Opportunity $opportunity
     * @return bool
     */
    public function view(User $user, Opportunity $opportunity): bool
    {
        // Check if user belongs to the organization
        return $user->organizations()
            ->where('organization_id', $opportunity->organization_id)
            ->wherePivotNull('deactivated_at')
            ->exists();
    }

    /**
     * Determine whether the user can create opportunities.
     *
     * @param User $user
     * @return bool
     */
    public function create(User $user): bool
    {
        // Any sponsor user can create opportunities for their organizations
        return $user->profile_type === ProfileType::SPONSOR;
    }

    /**
     * Determine whether the user can update the opportunity.
     *
     * @param User $user
     * @param Opportunity $opportunity
     * @return bool
     */
    public function update(User $user, Opportunity $opportunity): bool
    {
        // Check if user belongs to the organization
        return $user->organizations()
            ->where('organization_id', $opportunity->organization_id)
            ->wherePivotNull('deactivated_at')
            ->exists();
    }

    /**
     * Determine whether the user can delete the opportunity.
     *
     * @param User $user
     * @param Opportunity $opportunity
     * @return bool
     */
    public function delete(User $user, Opportunity $opportunity): bool
    {
        // First check if user belongs to the organization
        $hasOrgAccess = $user->organizations()
            ->where('organization_id', $opportunity->organization_id)
            ->wherePivotNull('deactivated_at')
            ->exists();

        if (!$hasOrgAccess) {
            return false;
        }

        // If opportunity has a user_id set, only allow deletion by that user
        if ($opportunity->user_id && $opportunity->user_id !== $user->id) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can duplicate the opportunity.
     *
     * @param User $user
     * @param Opportunity $opportunity
     * @return bool
     */
    public function duplicate(User $user, Opportunity $opportunity): bool
    {
        // Check if user belongs to the organization
        return $user->organizations()
            ->where('organization_id', $opportunity->organization_id)
            ->wherePivotNull('deactivated_at')
            ->exists();
    }

    /**
     * Determine whether the user can toggle the status of the opportunity.
     *
     * @param User $user
     * @param Opportunity $opportunity
     * @return bool
     */
    public function toggleStatus(User $user, Opportunity $opportunity): bool
    {
        // Check if user belongs to the organization
        return $user->organizations()
            ->where('organization_id', $opportunity->organization_id)
            ->wherePivotNull('deactivated_at')
            ->exists();
    }

    /**
     * Determine whether the user can admin disable/enable the opportunity.
     *
     * @param User $user
     * @param Opportunity $opportunity
     * @return bool
     */
    public function adminToggle(User $user, Opportunity $opportunity): bool
    {
        // Only admin users can use admin override
        return $user->hasRole('admin');
    }
}
