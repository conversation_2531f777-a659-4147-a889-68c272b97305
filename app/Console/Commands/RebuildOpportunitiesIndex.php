<?php

namespace App\Console\Commands;

use App\Models\Opportunity;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class RebuildOpportunitiesIndex extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'opportunities:rebuild-index';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Rebuild the opportunities index';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Rebuilding opportunities index...');

        Artisan::call("scout:sync-index-settings", [], $this->getOutput());
        Artisan::call("scout:flush", ['model' => Opportunity::class], $this->getOutput());
        Artisan::call('scout:import', ['model' => Opportunity::class], $this->getOutput());

        $this->info('Opportunities index rebuilt successfully.');
    }
}
