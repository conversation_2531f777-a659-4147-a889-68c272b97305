<?php

namespace App\Console\Commands;

use App\Models\LocationCoordinate;
use Illuminate\Console\Command;
use League\Csv\Reader;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class ImportCityCoordinates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'locations:import-csv
                            {--chunk=100 : Number of records to process in each chunk}
                            {--force : Force import even if records already exist}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import city coordinates from the uscities.csv file';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting import of city coordinates from CSV...');

        $csvPath = database_path('data/uscities.csv');

        if (!file_exists($csvPath)) {
            $this->error('CSV file not found at: ' . $csvPath);
            return Command::FAILURE;
        }

        // Create CSV reader
        $csv = Reader::createFromPath($csvPath, 'r');
        $csv->setHeaderOffset(0);

        $totalRecords = count($csv);
        $this->info("Found {$totalRecords} records in CSV file");

        // Check if we should force import
        $force = $this->option('force');

        if (!$force) {
            // Check if we already have records
            $existingCount = LocationCoordinate::count();

            if ($existingCount > 0) {
                if (!$this->confirm("There are already {$existingCount} records in the database. Do you want to continue?")) {
                    $this->info('Import cancelled.');
                    return Command::SUCCESS;
                }
            }
        }

        $chunkSize = (int) $this->option('chunk');
        $records = $csv->getRecords();

        $bar = $this->output->createProgressBar($totalRecords);
        $bar->start();

        $processed = 0;
        $created = 0;
        $updated = 0;
        $skipped = 0;
        $errors = 0;

        $chunk = [];

        foreach ($records as $record) {
            // Process each record
            try {
                $city = trim($record['city']);
                $stateCode = trim($record['state_id']);
                $latitude = !empty($record['lat']) ? (float) $record['lat'] : null;
                $longitude = !empty($record['lng']) ? (float) $record['lng'] : null;

                // Skip if missing essential data
                if (empty($city) || empty($stateCode) || $latitude === null || $longitude === null) {
                    $skipped++;
                    $bar->advance();
                    continue;
                }

                $chunk[] = [
                    'id' => Str::uuid()->toString(),
                    'city' => $city,
                    'state_code' => $stateCode,
                    'latitude' => $latitude,
                    'longitude' => $longitude,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                $processed++;

                // Process in chunks for better performance
                if (count($chunk) >= $chunkSize) {
                    $this->processChunk($chunk, $created, $updated, $skipped);
                    $chunk = [];
                }

                $bar->advance();
            } catch (\Exception $e) {
                $errors++;
                $this->error("Error processing record: " . $e->getMessage());
                // Log more detailed error information
                $this->line("Record data: " . json_encode($record));
                $this->line("Exception trace: " . $e->getTraceAsString());
                $bar->advance();
            }
        }

        // Process any remaining records
        if (count($chunk) > 0) {
            $this->processChunk($chunk, $created, $updated, $skipped);
        }

        $bar->finish();
        $this->newLine(2);

        $this->info("Import completed!");
        $this->info("Processed: {$processed} records");
        $this->info("Created: {$created} new records");
        $this->info("Updated: {$updated} existing records");
        $this->info("Skipped: {$skipped} records");
        $this->info("Errors: {$errors} records");

        return Command::SUCCESS;
    }

    /**
     * Process a chunk of records using upsert
     */
    protected function processChunk(array &$chunk, int &$created, int &$updated, int &$skipped): void
    {
        // Deduplicate records by city and state_code to avoid PostgreSQL error
        $uniqueRecords = [];
        $uniqueKeys = [];

        foreach ($chunk as $record) {
            $key = $record['city'] . '|' . $record['state_code'];

            // If we've already seen this city/state combination, skip it
            if (in_array($key, $uniqueKeys)) {
                $skipped++;
                continue;
            }

            $uniqueKeys[] = $key;
            $uniqueRecords[] = $record;
        }

        // If no unique records after deduplication, return early
        if (empty($uniqueRecords)) {
            return;
        }

        // Wrap in a transaction for data integrity
        DB::beginTransaction();

        try {
            // Use upsert to efficiently handle duplicates
            $result = LocationCoordinate::upsert(
                $uniqueRecords,
                ['city', 'state_code'], // Unique key
                ['latitude', 'longitude', 'updated_at'] // Fields to update if record exists
            );

            DB::commit();

            // Update counters based on the result
            // In Laravel's upsert, the return value is the number of rows affected
            // For created records, this is the number of new records
            // For updated records, this is the number of records that were actually changed
            $created += $result;

            // Count how many records were matched but not updated (values were the same)
            $matched = count($uniqueRecords) - $result;

            // These are technically "updated" in the sense that they were matched
            // but not actually changed because the values were the same
            $updated += $matched;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e; // Re-throw to be caught by the main try-catch block
        }
    }
}
