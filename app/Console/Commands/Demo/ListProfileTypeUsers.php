<?php

namespace App\Console\Commands\Demo;

use App\Enums\ProfileType;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;

class ListProfileTypeUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'demo:list-profile-users {--random : Randomly select one user per profile type}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'List one user per profile type as a table';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Ensure we're in a safe environment (optional, but good practice)
        if (!App::environment(['local', 'staging', 'development'])) {
            $this->error('This command can only be run in development, local or staging environments.');
            return 1;
        }

        $this->info('Fetching one user for each profile type...');

        // Get all available profile types from the enum
        $profileTypes = ProfileType::cases();

        // Array to store the results
        $tableData = [];

        // For each profile type, try to find one user
        foreach ($profileTypes as $profileType) {
            $query = User::query()
                ->where('profile_type', $profileType->value);

            if ($this->option('random')) {
                $query->inRandomOrder();
            }

            $user = $query->first();

            $tableData[] = [
                'Profile Type' => $profileType->value,
                'User ID' => $user?->id ?? 'N/A',
                'Name' => $user ? $user->full_name : 'No user found',
                'Email' => $user?->email ?? 'N/A',
            ];
        }

        // Sort the table data by profile type for better readability
        usort($tableData, function ($a, $b) {
            return $a['Profile Type'] <=> $b['Profile Type'];
        });

        // Output as a table
        $this->table(
            ['Profile Type', 'User ID', 'Name', 'Email'],
            $tableData
        );

        return 0;
    }
}
