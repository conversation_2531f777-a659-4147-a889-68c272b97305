<?php

namespace App\Console\Commands\Demo;

use App\Enums\TestStatus;
use App\Models\Course;
use App\Models\TestAttempt;
use App\Models\QuestionResponse;
use App\Models\User;
use App\Enums\QuestionType;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;

class CompleteCourse extends Command
{
    protected $signature = 'demo:complete-course {courseId} {userId} {--partial} {--stop-at-module-order=}';

    protected $description = 'Complete a course for a demo user. Use --partial to complete only half of the modules, or --stop-at-module-order to stop at a specific module.';

    public function handle()
    {
        // Ensure we're in local environment
        if (!App::environment('local') && !App::environment('staging')) {
            $this->error('This command can only be run in the local or staging environment.');
            return 1;
        }

        $courseId = $this->argument('courseId');
        $userId = $this->argument('userId');
        $isPartial = $this->option('partial');
        $stopAtModuleOrder = $this->option('stop-at-module-order');
        $user = User::query()->findOrFail($userId);

        // Get the course with its modules and tests
        $course = Course::query()->with([
            'modules.test.questions.answers',
            'modules.users' => function ($query) use ($userId) {
                $query->where('user_id', $userId);
            }
        ])->findOrFail($courseId);

        // Get modules in order from the relationship
        $modules = $course->modules->sortBy('pivot.order');

        // For partial completion, only complete the first half
        if ($isPartial && !$stopAtModuleOrder) {
            $modulesToComplete = ceil($modules->count() / 2);
            $modules = $modules->take($modulesToComplete);
            $this->info("Partial completion requested. Will complete {$modulesToComplete} modules.");
        }

        // For stop-at-module-order, complete up to that module
        if ($stopAtModuleOrder) {
            $modules = $modules->filter(function ($module) use ($stopAtModuleOrder) {
                return $module->pivot->order <= $stopAtModuleOrder;
            });
            $this->info("Will complete modules up to order {$stopAtModuleOrder}.");
        }

        // Complete each module in sequence
        foreach ($modules as $module) {
            $this->info("Completing module: {$module->name}");

            // Create or update module_user record
            $module->users()->syncWithoutDetaching([
                $user->id => [
                    'started_at' => now()->subMinutes(30),
                    'completed_at' => now(),
                    'completion_metadata' => [
                        'demo_completed' => true,
                        'partial_completion' => $isPartial || $stopAtModuleOrder !== null,
                        'completion_type' => $module->test ? 'test' : 'view',
                    ],
                ]
            ]);

            // If module has a test, complete it with perfect score
            if ($module->test) {
                // Create test attempt
                $attempt = new TestAttempt([
                    'user_id' => $user->id,
                    'test_id' => $module->test->id,
                    'started_at' => now()->subMinutes(15),
                    'ends_at' => now()->addMinutes(45),
                    'completed_at' => now(),
                    'score' => 100,
                    'status' => TestStatus::Complete,
                ]);
                $attempt->save();

                // Create responses for all questions
                foreach ($module->test->questions as $question) {
                    if ($question->type === QuestionType::MultipleChoice) {
                        // Find the correct answer for multiple choice
                        $correctAnswer = $question->answers->where('is_correct', true)->first();

                        // Create the response
                        QuestionResponse::query()->create([
                            'user_id' => $user->id,
                            'question_id' => $question->id,
                            'test_attempt_id' => $attempt->id,
                            'response' => $correctAnswer->answer,
                            'correct' => true,
                        ]);
                    }
                    if ($question->type === QuestionType::LongText) {
                        // For free response questions, create a detailed response
                        $responses = [
                            "Based on the module content, I've developed a comprehensive understanding of this topic. Here's my detailed analysis: First, the key principles we learned include strategic thinking, adaptability, and effective communication. I've seen these principles in action through various real-world examples presented in the course. For instance, the case study about team dynamics demonstrated how these concepts can be practically applied. Furthermore, I can connect these ideas to my own experiences in athletics, where I've had to demonstrate similar skills. Looking ahead, I plan to implement these strategies by: 1) Setting clear, measurable goals, 2) Developing action plans that incorporate feedback loops, and 3) Regularly assessing and adjusting my approach based on outcomes.",
                            "This module has significantly impacted my understanding of leadership and team dynamics. The concepts presented have challenged my previous assumptions and provided new frameworks for decision-making. I can now see how these principles apply in various contexts, from sports teams to organizational management. The practical exercises and real-world examples have helped me develop a more nuanced approach to problem-solving and team building. Moving forward, I intend to apply these insights by fostering more inclusive communication, building stronger team relationships, and creating environments that promote both individual growth and collective success.",
                            "The material presented in this module has given me valuable tools for addressing complex challenges. I've learned that effective leadership requires a balance of technical knowledge and emotional intelligence. The frameworks we studied provide a structured approach to decision-making while remaining flexible enough to adapt to different situations. I particularly appreciated how the content connected theoretical concepts with practical applications. In my future role, I plan to implement these strategies by: 1) Actively listening to team members, 2) Creating opportunities for collaborative problem-solving, and 3) Maintaining a focus on both short-term goals and long-term development."
                        ];

                        QuestionResponse::query()->create([
                            'user_id' => $user->id,
                            'question_id' => $question->id,
                            'test_attempt_id' => $attempt->id,
                            'response' => $responses[array_rand($responses)],
                            'correct' => null,
                        ]);
                    }
                }
            }

            $this->info("✓ Module completed successfully");
        }

        // Update course completion
        $course->calculateCompletionForUser($user);

        $this->info(sprintf(
            "Course %s completed successfully!",
            $isPartial || $stopAtModuleOrder ? "partially" : "fully"
        ));

        return 0;
    }
}
