<?php

namespace App\Console\Commands\Demo;

use App\Data\SystemInvites\AlumniInviteData;
use App\Data\SystemInvites\CollegeAthleteInviteData;
use App\Data\SystemInvites\ParentSystemInviteData;
use App\Data\SystemInvites\ProfessionalInviteData;
use App\Data\SystemInvites\TeamStudentInviteData;
use App\Data\SystemInvites\TeamCoachInviteData;
use App\Data\SystemInvites\AthleticsDirectorInviteData;
use App\Data\SystemInvites\SponsorInviteData;
use App\Data\Nomination\NominationData;
use App\Enums\LifeStage;
use App\Enums\NominationType;
use App\Enums\ProfileType;
use App\Models\Nomination;
use App\Models\Organization;
use App\Services\Invite\SystemInviteService;
use App\Services\Nomination\NominationService;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class CreateTestInvite extends Command
{
    protected $signature = 'invite:test
                          {type : The profile type (parent, coach, etc.)}
                          {--email= : Optional email address. If not provided, will generate fake email}';

    protected $description = 'Creates a test system invite for any profile type';

    public function __construct(
        private readonly SystemInviteService $inviteService,
        private readonly NominationService $nominationService
    ) {
        parent::__construct();
    }

    public function handle(): int
    {
        $this->info('Creating test invite...');
        $type = $this->argument('type');
        $profileType = ProfileType::tryFrom($type);
        if (!$profileType) {
            $this->error("Invalid profile type: {$type}");
            return 1;
        }

        $email = $this->option('email') ?? fake()->safeEmail();

        // For sponsor type, select a random organization
        $organization = null;
        if ($profileType === ProfileType::SPONSOR) {
            // Try to get a random organization
            $organization = Organization::query()->inRandomOrder()->first();

            if (!$organization) {
                $this->error('No organizations found in the database. Please create an organization first.');
                return 1;
            }

            $this->info("Selected organization: {$organization->name} (ID: {$organization->id})");
        }

        $invite = match ($profileType) {
            ProfileType::PARENT => $this->createParentInvite($email),
            ProfileType::COLLEGE_ATHLETE => $this->inviteService->createForCollegeAthlete(CollegeAthleteInviteData::from([
                'first_name' => fake()->firstName(),
                'last_name' => fake()->lastName(),
                'email' => $email,
                'college_name' => fake()->company() . ' University',
                'sport' => fake()->randomElement(['Basketball', 'Football', 'Soccer', 'Baseball', 'Track & Field']),
                'token' => Str::random(64),
                'created_at' => now()->timestamp,
            ])),
            ProfileType::PROFESSIONAL => $this->inviteService->createForProfessional(ProfessionalInviteData::from([
                'first_name' => fake()->firstName(),
                'last_name' => fake()->lastName(),
                'email' => $email,
                'company' => fake()->company(),
                'token' => Str::random(64),
                'created_at' => now()->timestamp,
            ])),
            ProfileType::ALUMNI => $this->inviteService->createForAlumni(AlumniInviteData::from([
                'first_name' => fake()->firstName(),
                'last_name' => fake()->lastName(),
                'email' => $email,
                'life_stage' => fake()->randomElement(LifeStage::cases()),
                'college_name' => fake()->company() . ' University',
                'company' => fake()->company(),
                'sport' => fake()->randomElement(['Basketball', 'Football', 'Soccer', 'Baseball', 'Track & Field']),
                'token' => Str::random(64),
                'created_at' => now()->timestamp,
            ])),
            ProfileType::TEAM_STUDENT => $this->inviteService->createForTeamStudent(TeamStudentInviteData::from([
                'first_name' => fake()->firstName(),
                'last_name' => fake()->lastName(),
                'email' => $email,
                'team_name' => fake()->randomElement(['Varsity Basketball', 'JV Football', 'Soccer', 'Track Team', 'Baseball']),
                'school_name' => fake()->company() . ' High School',
                'token' => Str::random(64),
                'created_at' => now()->timestamp,
            ])),
            ProfileType::TEAM_COACH => $this->inviteService->createForTeamCoach(TeamCoachInviteData::from([
                'first_name' => fake()->firstName(),
                'last_name' => fake()->lastName(),
                'email' => $email,
                'school_name' => fake()->company() . ' High School',
                'token' => Str::random(64),
                'created_at' => now()->timestamp,
            ])),
            ProfileType::ATHLETICS_DIRECTOR => $this->inviteService->createForAthleticsDirector(AthleticsDirectorInviteData::from([
                'first_name' => fake()->firstName(),
                'last_name' => fake()->lastName(),
                'email' => $email,
                'school_name' => fake()->company() . ' High School',
                'school_district' => fake()->company() . ' School District',
                'token' => Str::random(64),
                'created_at' => now()->timestamp,
            ])),
            ProfileType::SPONSOR => $this->inviteService->createForSponsor(SponsorInviteData::from([
                'first_name' => fake()->firstName(),
                'last_name' => fake()->lastName(),
                'email' => $email,
                'company_name' => fake()->company(),
                'organization_id' => $organization->id,
                'token' => Str::random(64),
                'created_at' => now()->timestamp,
            ])),
            ProfileType::POSITIVE_ATHLETE => $this->createPositiveAthleteInvite($email),
            ProfileType::POSITIVE_COACH => $this->createPositiveCoachInvite($email),
            default => throw new \InvalidArgumentException("Unsupported profile type: {$type}")
        };

        $this->info('Test invite created successfully!');
        $this->info("Registration URL: " . config('app.frontend_url') . "/invite/{$invite->token}");
        $this->newLine();
        $this->info("Invite Token: " . $invite->token);
        $this->info("Mobile Code: " . $invite->mobile_code);

        if ($profileType === ProfileType::SPONSOR) {
            $this->info("Organization: {$organization->name} (ID: {$organization->id})");
        }

        if ($profileType === ProfileType::PARENT) {
            $inviteData = $invite->invite_data;
            $this->info("Linked to Athlete: {$inviteData->athlete_name} (ID: {$inviteData->athlete_user_id})");
        }

        if ($profileType === ProfileType::ALUMNI) {
            $inviteData = $invite->invite_data;
            $this->info("Life Stage: {$inviteData->life_stage->value}");
            $this->info("College: {$inviteData->college_name}");
            $this->info("Company: {$inviteData->company}");
        }

        return 0;
    }

    /**
     * Create a parent invite linked to a random positive athlete
     */
    private function createParentInvite(string $email): object
    {
        // Find a random positive athlete
        $athlete = \App\Models\User::query()
            ->where('profile_type', ProfileType::POSITIVE_ATHLETE->value)
            ->inRandomOrder()
            ->first();

        if (!$athlete) {
            throw new \RuntimeException('No positive athletes found in the database. Please create a positive athlete first.');
        }

        $this->info("Selected athlete: {$athlete->full_name} (ID: {$athlete->id})");

        $invite = $this->inviteService->createForParent(ParentSystemInviteData::from([
            'first_name' => fake()->firstName(),
            'last_name' => fake()->lastName(),
            'email' => $email,
            'token' => Str::random(64),
            'created_at' => now()->timestamp,
            'athlete_user_id' => $athlete->id,
            'athlete_name' => $athlete->full_name,
        ]));

        return $invite;
    }

    /**
     * Create a nomination and invite for a Positive Athlete
     */
    private function createPositiveAthleteInvite(string $email): object
    {
        $nomination = $this->createNomination($email, NominationType::ATHLETE);
        $invite = $nomination->systemInvite;

        if (!$invite) {
            throw new \RuntimeException('Failed to create system invite for positive athlete');
        }

        return $invite;
    }

    /**
     * Create a nomination and invite for a Positive Coach
     */
    private function createPositiveCoachInvite(string $email): object
    {
        $nomination = $this->createNomination($email, NominationType::COACH);
        $invite = $nomination->systemInvite;

        if (!$invite) {
            throw new \RuntimeException('Failed to create system invite for positive coach');
        }

        return $invite;
    }

    /**
     * Create a nomination with the given email and type
     */
    private function createNomination(string $email, NominationType $type): Nomination
    {
        $firstName = fake()->firstName();
        $lastName = fake()->lastName();
        $nominatorFirstName = fake()->firstName();
        $nominatorLastName = fake()->lastName();
        $schoolName = fake()->company() . ' High School';
        $sport = fake()->randomElement(['Basketball', 'Football', 'Soccer', 'Baseball', 'Track & Field']);

        $nominationData = new NominationData(
            email: $email,
            first_name: $firstName,
            last_name: $lastName,
            nominator_email: fake()->safeEmail(),
            nominator_first_name: $nominatorFirstName,
            nominator_last_name: $nominatorLastName,
            school_name: $schoolName,
            school_id: null,
            sport: $sport,
            relationship: fake()->randomElement(['Coach', 'Teacher', 'Athletic Director', 'Parent']),
            type: $type,
            note: fake()->paragraph()
        );

        return $this->nominationService->handleSingleNomination($nominationData);
    }
}
