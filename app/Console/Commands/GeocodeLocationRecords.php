<?php

namespace App\Console\Commands;

use App\Models\LocationCoordinate;
use App\Services\GeocodingService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class GeocodeLocationRecords extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'locations:geocode {--limit=10 : Maximum number of records to process}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Geocode location records that are missing latitude and longitude';

    /**
     * Execute the console command.
     */
    public function handle(GeocodingService $geocodingService): int
    {
        $limit = (int) $this->option('limit');

        // Find records that need geocoding
        $pendingLocations = LocationCoordinate::query()
            ->whereNull('latitude')
            ->whereNull('longitude')
            ->limit($limit)
            ->get();

        $count = $pendingLocations->count();

        if ($count === 0) {
            $this->info('No pending locations to geocode.');
            return Command::SUCCESS;
        }

        $this->info("Found {$count} locations to geocode.");

        $processed = 0;
        $successful = 0;
        $failed = 0;
        $errors = 0;

        foreach ($pendingLocations as $location) {
            $this->line("Geocoding: {$location->city}, {$location->state_code}");

            try {
                // Use a transaction for each geocoding operation
                DB::beginTransaction();

                $result = $geocodingService->geocodeLocation($location->city, $location->state_code);

                if ($result) {
                    $location->latitude = $result['latitude'];
                    $location->longitude = $result['longitude'];
                    $location->save();

                    $this->info("✓ Successfully geocoded: {$location->city}, {$location->state_code}");
                    $successful++;

                    DB::commit();
                } else {
                    // No error, just no results found
                    DB::rollBack();
                    $this->warn("✗ Failed to geocode: {$location->city}, {$location->state_code} - No results found");
                    $failed++;
                }
            } catch (\Exception $e) {
                DB::rollBack();
                $this->error("✗ Error geocoding {$location->city}, {$location->state_code}: " . $e->getMessage());
                $errors++;
            }

            $processed++;

            // No need to add delay here as the geocodeLocation method already includes a delay
        }

        $this->newLine();
        $this->info("Geocoding complete!");
        $this->info("Processed: {$processed} locations");
        $this->info("Successful: {$successful}");
        $this->info("Failed: {$failed}");

        if ($errors > 0) {
            $this->warn("Errors: {$errors}");
        }

        if ($successful > 0) {
            $this->info('You may want to rebuild the opportunities index to include the new coordinates:');
            $this->line('  php artisan opportunities:rebuild-index');
        }

        return Command::SUCCESS;
    }
}
