# X-Factor Leaderboard Testing Command

This command allows you to test and verify the X-Factor Leaderboard implementation against the requirements defined in the Architecture Decision Record (ADR) and design screenshots.

## Command Information

- **Name**: `test:xfactor-leaderboard`
- **Description**: Test the X-Factor leaderboard functionality and verify it matches requirements

## Usage

```
php artisan test:xfactor-leaderboard [options]
```

## Available Options

| Option | Description |
|--------|-------------|
| `--region=[REGION]` | Filter by region ID (e.g., 1 for Georgia) |
| `--state=[STATE]` | Filter by state code (e.g., GA) |
| `--graduation-year=[YEAR]` | Filter by graduation year (e.g., 2025) |
| `--academic-year=[YEAR]` | Filter by academic year (e.g., 2024-25) |
| `--all-time` | Show all-time results instead of specific timeframe |
| `--user-id=[ID]` | Test specific user position in leaderboard |
| `--detailed` | Show more detailed output including performance metrics |

## Command Output

The command generates a detailed report that includes:

1. **Filter Information**
   - Region/state selected
   - Academic year or date range
   - Total students in the ranking

2. **Leaderboard Data Visualization**
   - Top 10 student rankings in a table format
   - Rank, name, school, graduation year, badge, and modules completed for each student

3. **Ranking Structure Verification**
   - Checks if rankings are sorted by completed modules
   - Verifies all required fields are present
   - Checks rank sequence continuity

4. **User Position Information**
   - Displays current user's rank and module completion count
   - Indicates if the user is in the top 50

5. **ADR Requirements Validation**
   - Verifies leaderboard structure requirements
   - Validates filtering and display options
   - Confirms sorting and ranking logic is correct

6. **Performance Data** (with `--detailed` option)
   - Number of regions/states available for filtering
   - Graduation year range in the database
   - Total module completions tracked

## Examples

Test the leaderboard with all-time data:
```
php artisan test:xfactor-leaderboard --all-time
```

Test with a specific academic year:
```
php artisan test:xfactor-leaderboard --academic-year=2023-24
```

Test with a specific user:
```
php artisan test:xfactor-leaderboard --user-id=123 --detailed
```

Filter by region and graduation year:
```
php artisan test:xfactor-leaderboard --region=1 --graduation-year=2025
```

## Requirements Verification

The command automatically verifies the implementation against key requirements:

1. **Leaderboard Structure Requirements**
   - Student ranking display
   - Class/graduation year information
   - Student name and school
   - Badge display
   - Completed modules count

2. **Filtering Requirements**
   - Region/state filtering
   - Academic year filtering
   - All-time data option
   - Default July-June academic year range
   - Top 50 display limit
   - User position highlighting

3. **Sorting and Ranking Requirements**
   - Sorting by number of completed modules
   - Proper ranking calculation
   - User position detection

## Purpose

This command serves multiple purposes:

1. **Testing**: Verifies that the leaderboard functionality works correctly with different filter combinations.

2. **Validation**: Confirms that the implementation meets the requirements specified in the ADR.

3. **Debugging**: Helps identify any issues with data retrieval or presentation.

4. **Documentation**: Provides a working example of how to use the leaderboard API.

5. **Performance Checking**: With the `--detailed` flag, monitors key performance metrics.

## Notes

- The command will randomly select a user if none is specified with `--user-id`.
- The command verifies data structure but doesn't test the frontend rendering.
- Requirements validation is based on the ADR and design screenshots for the X-Factor Leaderboard feature. 
