<?php

namespace App\Console\Commands;

use App\Services\EngagementService;
use Illuminate\Console\Command;

class CleanupEngagementsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'engagements:cleanup {--days=30 : Number of days of detailed engagement data to retain}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up detailed engagement records older than the specified number of days';

    /**
     * Execute the console command.
     */
    public function handle(EngagementService $engagementService)
    {
        $days = (int)$this->option('days');

        if ($days < 7) {
            $this->error('Minimum retention period is 7 days.');
            return 1;
        }

        $this->info("Cleaning up engagement records older than {$days} days...");

        $count = $engagementService->cleanupOldEngagements($days);

        $this->info("Deleted {$count} old engagement records.");

        return 0;
    }
}
