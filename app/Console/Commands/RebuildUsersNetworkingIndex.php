<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class RebuildUsersNetworkingIndex extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'networking:rebuild-index';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Rebuild the users networking index';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Rebuilding users networking index...');

        Artisan::call("scout:sync-index-settings", [], $this->getOutput());
        Artisan::call("scout:flush", ['model' => User::class], $this->getOutput());
        Artisan::call('scout:import', ['model' => User::class], $this->getOutput());

        $this->info('Users networking index rebuilt successfully.');
    }
}
