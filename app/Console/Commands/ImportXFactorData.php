<?php

namespace App\Console\Commands;

use App\Enums\ModuleType;
use App\Enums\QuestionType;
use App\Models\Answer;
use App\Models\Module;
use App\Models\Question;
use App\Models\Tag;
use App\Models\Test;
use App\Models\Topic;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ImportXFactorData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:xfactor
                            {--path=database/data/legacy-xfactor : Path to the migration files}
                            {--json=xfactor_data.json : JSON fixture filename}
                            {--dry-run : Run without making actual changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import X-Factor data from JSON fixture';

    /**
     * Mapping between original IDs and new IDs
     */
    protected array $moduleMap = [];
    protected array $testMap = [];
    protected array $questionMap = [];
    protected array $topicMap = [];
    protected array $tagMap = [];

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $path = $this->option('path');
        $jsonFile = $this->option('json');
        $dryRun = $this->option('dry-run');

        $fullPath = base_path($path . '/' . $jsonFile);

        if (!file_exists($fullPath)) {
            $this->error("JSON file not found: {$fullPath}");
            return 1;
        }

        $this->info("Reading JSON data from {$fullPath}");
        $data = json_decode(file_get_contents($fullPath), true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->error("Failed to parse JSON: " . json_last_error_msg());
            return 1;
        }

        $this->info("Found {$data['module_count']} modules to import");

        if ($dryRun) {
            $this->info("Running in dry-run mode - no changes will be made");
        }

        // Begin transaction for atomic operations
        if (!$dryRun) {
            DB::beginTransaction();
        }

        try {
            // Import modules
            $bar = $this->output->createProgressBar(count($data['modules']));
            $bar->start();

            foreach ($data['modules'] as $moduleData) {
                $this->processModule($moduleData, $path, $dryRun);
                $bar->advance();
            }

            $bar->finish();
            $this->newLine();

            if (!$dryRun) {
                DB::commit();
                $this->info('Import completed successfully!');
            } else {
                $this->info('Dry run completed. No changes were made.');
            }

            return 0;
        } catch (\Exception $e) {
            if (!$dryRun) {
                DB::rollBack();
            }
            $this->error('Import failed: ' . $e->getMessage());
            $this->error($e->getTraceAsString());
            return 1;
        }
    }

    /**
     * Process a single module
     */
    protected function processModule(array $moduleData, string $basePath, bool $dryRun): void
    {
        if ($dryRun) {
            $this->line("Would import module: {$moduleData['name']} (ID: {$moduleData['source_id']})");
            return;
        }

        // Create module
        $module = new Module();
        $module->name = $moduleData['name'];
        $module->description = $moduleData['description'];
        $module->slug = $moduleData['slug'];
        $module->type = ModuleType::from($moduleData['type']);
        $module->video_url = $moduleData['video_url'] ?? null;
        $module->video_start_time = $moduleData['video_start_time'] ?? null;
        $module->video_end_time = $moduleData['video_end_time'] ?? null;
        $module->minutes = $moduleData['minutes'] ?? null;
        $module->created_at = $moduleData['created_at'];
        $module->updated_at = $moduleData['updated_at'];
        $module->published = $moduleData['published'] ?? true;
        $module->save();

        $this->moduleMap[$moduleData['source_id']] = $module->id;

        // Process images
        $this->processImages($module, $moduleData, $basePath);

        // Process test and questions if exists
        if (!empty($moduleData['test'])) {
            $this->processTest($module, $moduleData['test']);
        }

        // Process taxonomy data (core traits as topics, sports as tags)
        $this->processTaxonomy($module, $moduleData);
    }

    /**
     * Process module images
     */
    protected function processImages(Module $module, array $moduleData, string $basePath): void
    {
        if (empty($moduleData['images'])) {
            return;
        }

        foreach ($moduleData['images'] as $imageData) {
            $imagePath = $basePath . '/images/' . $imageData['filename'];
            $fullImagePath = base_path($imagePath);

            if (file_exists($fullImagePath)) {
                // Add image to media library
                $module->addMedia($fullImagePath)
                       ->preservingOriginal()
                       ->withCustomProperties(['source_id' => $imageData['source_id']])
                       ->toMediaCollection('cover');
            } else {
                $this->warn("Image file not found: {$fullImagePath}");
            }
        }
    }

    /**
     * Process test data
     */
    protected function processTest(Module $module, array $testData): void
    {
        // Create test
        $test = new Test();
        $test->type = $testData['type'];
        $test->testable_type = Module::class;
        $test->testable_id = $module->id;
        $test->attempts_allowed = $testData['attempts_allowed'] ?? 3;
        $test->wait_period = $testData['wait_period'] ?? 0;
        $test->passing_score = $testData['passing_score'] ?? 60;
        $test->time_limit = $testData['time_limit'];
        $test->created_at = $testData['created_at'];
        $test->updated_at = $testData['updated_at'];
        $test->save();

        // Process questions
        if (empty($testData['questions'])) {
            return;
        }

        foreach ($testData['questions'] as $questionData) {
            $question = new Question();
            $question->test_id = $test->id;
            $question->question = $questionData['question'];
            $question->type = QuestionType::from($questionData['type']);
            $question->created_at = $questionData['created_at'];
            $question->updated_at = $questionData['updated_at'];
            $question->save();

            $this->questionMap[$questionData['source_id']] = $question->id;

            // Process answers
            if (empty($questionData['answers'])) {
                continue;
            }

            foreach ($questionData['answers'] as $answerData) {
                $answer = new Answer();
                $answer->question_id = $question->id;
                $answer->answer = $answerData['answer'];
                $answer->is_correct = $answerData['is_correct'];
                $answer->created_at = $answerData['created_at'];
                $answer->updated_at = $answerData['updated_at'];
                $answer->save();
            }
        }
    }

    /**
     * Process taxonomy data for a module
     */
    protected function processTaxonomy(Module $module, array $moduleData): void
    {
        // Process core trait as topic
        if (!empty($moduleData['taxonomy']['core_trait'])) {
            $topicName = $moduleData['taxonomy']['core_trait'];
            $topic = $this->findOrCreateTopic($topicName);

            // Associate the topic with the module
            $module->topics()->attach($topic->id);
        }

        // Process sport as tag
        if (!empty($moduleData['taxonomy']['sport']) && $moduleData['taxonomy']['sport'] !== 'N/A') {
            $tagName = $moduleData['taxonomy']['sport'];
            $tag = $this->findOrCreateTag($tagName);

            // Associate the tag with the module
            $module->tags()->attach($tag->id);
        }
    }

    /**
     * Find or create a topic by name
     */
    protected function findOrCreateTopic(string $name): Topic
    {
        if (isset($this->topicMap[$name])) {
            return Topic::find($this->topicMap[$name]);
        }

        $topic = Topic::firstOrCreate(['name' => $name]);
        $this->topicMap[$name] = $topic->id;

        return $topic;
    }

    /**
     * Find or create a tag by name
     */
    protected function findOrCreateTag(string $name): Tag
    {
        $normalizedName = strtolower($name);

        if (isset($this->tagMap[$normalizedName])) {
            return Tag::find($this->tagMap[$normalizedName]);
        }

        $tag = Tag::firstOrCreate(['name' => $normalizedName]);
        $this->tagMap[$normalizedName] = $tag->id;

        return $tag;
    }
}
