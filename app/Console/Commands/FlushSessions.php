<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Redis;
use Laravel\Sanctum\PersonalAccessToken;

class FlushSessions extends Command
{
    protected $signature = 'sessions:flush {--all : Flush both sessions and API tokens}';
    protected $description = 'Flush active sessions and/or API tokens';

    public function handle()
    {
        $flushAll = $this->option('all');
        $count = 0;

        // Handle Redis sessions
        if (config('session.driver') === 'redis') {
            // Connect to the correct Redis database
            $redis = Redis::connection('session');

            // Get count of sessions before flushing
            $sessionCount = $redis->dbsize();
            $this->info("Found {$sessionCount} active sessions");

            // Flush the dedicated sessions database
            $redis->flushdb();
            $count += $sessionCount;
        }

        // Handle Sanctum tokens if --all flag is used
        if ($flushAll) {
            $tokensCount = PersonalAccessToken::count();
            PersonalAccessToken::truncate();
            $this->info("Flushed {$tokensCount} API tokens.");
            $count += $tokensCount;
        }

        $this->info("Total items flushed: {$count}");
        return 0;
    }
}
