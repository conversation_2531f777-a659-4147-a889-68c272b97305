<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Storage;
use SplQueue;

class ClearStoragePublic extends Command
{
    protected $signature = 'storage:clear-public';
    protected $description = 'Clear all files in storage/app/public directory';

    public function handle()
    {
        if (!App::isLocal()) {
            $this->error('This command can only be run in local environment.');
            return;
        }

        $disk = Storage::disk('public');

        $queue = new SplQueue();
        $queue->enqueue('');

        $directories = [];

        while (!$queue->isEmpty()) {
            $currentPath = $queue->dequeue();

            $files = $disk->files($currentPath);
            $subDirs = $disk->directories($currentPath);

            foreach ($files as $file) {
                if ($file !== '.gitignore') {
                    $disk->delete($file);
                }
            }

            foreach ($subDirs as $subDir) {
                $queue->enqueue($subDir);
            }

            if ($currentPath !== '') {
                $directories[] = $currentPath;
            }
        }

        foreach (array_reverse($directories) as $dir) {
            $disk->deleteDirectory($dir);
        }

        $this->info('All files and empty directories in storage/app/public have been cleared, except .gitignore.');
    }
}
