<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use PDO;

class ExportXFactorData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:export-x-factor-data
                            {--host=127.0.0.1 : MySQL host}
                            {--port=3306 : MySQL port}
                            {--database=legacy_xfactor : Source database name}
                            {--username=root : Database username}
                            {--password= : Database password}
                            {--output-dir=database/data/legacy-xfactor : Output directory for JSON and images}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Export X-Factor data from MySQL to JSON fixture for migration';

    /**
     * PDO database connection
     */
    protected PDO $sourceDB;

    /**
     * Output directories
     */
    protected string $exportDir;
    protected string $mediaOutputDir;
    protected string $jsonOutputFile;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->setupConnection();
        $this->setupOutputDirectories();

        // Container for all exported data
        $exportData = [
            'metadata' => [
                'export_date' => date('Y-m-d H:i:s'),
                'source_database' => $this->option('database'),
                'version' => '1.0'
            ],
            'modules' => [],
            'module_count' => 0,
            'question_count' => 0,
            'answer_count' => 0,
            'image_count' => 0
        ];

        // Step 1: Get content types for reference
        $this->info('Fetching content types...');
        $contentTypes = $this->fetchContentTypes();

        // Step 2: Export content to modules
        $this->info('Exporting content to modules...');
        $exportData = $this->exportModules($exportData, $contentTypes);

        // Update metadata with counts
        $exportData['metadata']['total_modules'] = $exportData['module_count'];
        $exportData['metadata']['total_questions'] = $exportData['question_count'];
        $exportData['metadata']['total_answers'] = $exportData['answer_count'];
        $exportData['metadata']['total_images'] = $exportData['image_count'];

        // Save JSON data
        file_put_contents($this->jsonOutputFile, json_encode($exportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        $this->info('Migration export completed successfully!');
        $this->info("Total modules exported: {$exportData['module_count']}");
        $this->info("Total questions exported: {$exportData['question_count']}");
        $this->info("Total answers exported: {$exportData['answer_count']}");
        $this->info("Total images exported: {$exportData['image_count']}");
        $this->info("JSON file saved to: {$this->jsonOutputFile}");
        $this->info("Media files saved to: {$this->mediaOutputDir}");
    }

    /**
     * Setup database connection
     */
    protected function setupConnection(): void
    {
        $host = $this->option('host');
        $port = $this->option('port');
        $database = $this->option('database');
        $username = $this->option('username');
        $password = $this->option('password');

        $this->info("Connecting to MySQL database: {$database} on {$host}:{$port}");

        try {
            $this->sourceDB = new PDO(
                "mysql:host={$host};port={$port};dbname={$database}",
                $username,
                $password,
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );

            $this->info('Database connection established.');
        } catch (\PDOException $e) {
            $this->error("Database connection failed: {$e->getMessage()}");
            exit(1);
        }
    }

    /**
     * Setup output directories
     */
    protected function setupOutputDirectories(): void
    {
        $this->exportDir = base_path($this->option('output-dir'));
        $this->mediaOutputDir = $this->exportDir . '/images';
        $this->jsonOutputFile = $this->exportDir . '/xfactor_data.json';

        if (!file_exists($this->exportDir)) {
            mkdir($this->exportDir, 0755, true);
        }

        if (!file_exists($this->mediaOutputDir)) {
            mkdir($this->mediaOutputDir, 0755, true);
        }

        $this->info("Output directory: {$this->exportDir}");
        $this->info("Media output directory: {$this->mediaOutputDir}");
    }

    /**
     * Fetch content types from database
     */
    protected function fetchContentTypes(): array
    {
        $contentTypes = [];
        $contentTypeQuery = $this->sourceDB->query("SELECT Id, Name FROM contenttype WHERE Deleted = 0");

        while ($type = $contentTypeQuery->fetch(PDO::FETCH_ASSOC)) {
            $contentTypes[$type['Id']] = $type['Name'];
        }

        $this->info('Fetched ' . count($contentTypes) . ' content types.');
        return $contentTypes;
    }

    /**
     * Export modules with related data
     */
    protected function exportModules(array $exportData, array $contentTypes): array
    {
        // Fetch core traits and sports for reference
        $coreTraits = $this->fetchCoreTraits();
        $sports = $this->fetchSports();

        $contentQuery = $this->sourceDB->query("
            SELECT
                c.Id, c.Title, c.Description, c.Link, c.TypeId,
                c.StartTime, c.EndTime, c.Length, c.CreatedDate, c.LastModifiedDate,
                c.Speaker, c.CoreTraitId, c.SportId
            FROM content c
            WHERE c.Deleted = 0
            ORDER BY c.Id
        ");

        $totalModules = $contentQuery->rowCount();
        $this->info("Found {$totalModules} content items to export.");
        $bar = $this->output->createProgressBar($totalModules);
        $bar->start();

        while ($content = $contentQuery->fetch(PDO::FETCH_ASSOC)) {
            $moduleData = [
                'source_id' => $content['Id'],
                'name' => $content['Title'],
                'description' => $content['Description'] ?? '',
                'slug' => Str::slug($content['Title']),
                'type' => 'video', // Default to video
                'video_url' => $content['Link'],
                'video_start_time' => $content['StartTime'],
                'video_end_time' => $content['EndTime'],
                'minutes' => $content['Length'] ? (int)$content['Length'] : null,
                'created_at' => $content['CreatedDate'],
                'updated_at' => $content['LastModifiedDate'],
                'published' => true,
                'speaker' => $content['Speaker'],
                'metadata' => [
                    'core_trait_id' => $content['CoreTraitId'],
                    'sport_id' => $content['SportId'],
                    'content_type' => $contentTypes[$content['TypeId']] ?? 'Unknown'
                ],
                'images' => [],
                'test' => null,
                'taxonomy' => [
                    'core_trait' => isset($content['CoreTraitId']) && isset($coreTraits[$content['CoreTraitId']])
                        ? $coreTraits[$content['CoreTraitId']]
                        : null,
                    'sport' => isset($content['SportId']) && isset($sports[$content['SportId']])
                        ? $sports[$content['SportId']]
                        : null
                ]
            ];

            // Map content type if available
            if (isset($contentTypes[$content['TypeId']])) {
                // Map type based on content type name
                if (stripos($contentTypes[$content['TypeId']], 'video') !== false) {
                    $moduleData['type'] = 'video';
                } elseif (stripos($contentTypes[$content['TypeId']], 'podcast') !== false) {
                    $moduleData['type'] = 'audio';
                }
            }

            // Process content images
            $moduleData = $this->processContentImages($moduleData, $content['Id'], $exportData);
            $exportData['image_count'] = $moduleData['_image_count'];
            unset($moduleData['_image_count']);

            // Process questions and tests
            $result = $this->processQuestionsAndTests($moduleData, $content['Id'], $exportData);
            $moduleData = $result['moduleData'];
            $exportData['question_count'] = $result['questionCount'];
            $exportData['answer_count'] = $result['answerCount'];

            $exportData['modules'][] = $moduleData;
            $exportData['module_count']++;

            $bar->advance();
        }

        $bar->finish();
        $this->newLine();

        return $exportData;
    }

    /**
     * Process content images
     */
    protected function processContentImages(array $moduleData, int $contentId, array $exportData): array
    {
        $imagesQuery = $this->sourceDB->prepare("
            SELECT
                ci.Id, ci.ImageType, ci.ImageContent, ci.CreatedDate, ci.LastModifiedDate
            FROM contentimage ci
            WHERE ci.ContentId = ? AND ci.Deleted = 0
        ");
        $imagesQuery->execute([$contentId]);

        $imageCount = $exportData['image_count'];

        while ($image = $imagesQuery->fetch(PDO::FETCH_ASSOC)) {
            $extension = str_replace('image/', '', $image['ImageType']);
            if (empty($extension)) {
                $extension = 'jpg'; // Default extension if missing
            }

            $filename = "content_{$contentId}_{$image['Id']}.{$extension}";
            $filepath = "{$this->mediaOutputDir}/{$filename}";

            // Decode and save the image to file
            $imageData = $image['ImageContent'];
            file_put_contents($filepath, $imageData);

            $moduleData['images'][] = [
                'source_id' => $image['Id'],
                'filename' => $filename,
                'mime_type' => $image['ImageType'] ?: 'image/jpeg',
                'created_at' => $image['CreatedDate'],
                'updated_at' => $image['LastModifiedDate']
            ];

            $imageCount++;
        }

        $moduleData['_image_count'] = $imageCount;
        return $moduleData;
    }

    /**
     * Process questions and tests
     */
    protected function processQuestionsAndTests(array $moduleData, int $contentId, array $exportData): array
    {
        $questionCount = $exportData['question_count'];
        $answerCount = $exportData['answer_count'];

        // Check if this content has questions
        $questionCountQuery = $this->sourceDB->prepare("SELECT COUNT(*) FROM question WHERE ContentId = ? AND Deleted = 0");
        $questionCountQuery->execute([$contentId]);
        $hasQuestions = (int)$questionCountQuery->fetchColumn() > 0;

        if ($hasQuestions) {
            $testData = [
                'type' => 'quiz',
                'attempts_allowed' => 3,
                'wait_period' => 0,
                'passing_score' => 60,
                'time_limit' => null,
                'created_at' => $moduleData['created_at'],
                'updated_at' => $moduleData['updated_at'],
                'questions' => []
            ];

            // Export questions
            $questionsQuery = $this->sourceDB->prepare("
                SELECT
                    q.Id, q.Description, q.CreatedDate, q.LastModifiedDate
                FROM question q
                WHERE q.ContentId = ? AND q.Deleted = 0
                ORDER BY q.Id
            ");
            $questionsQuery->execute([$contentId]);

            while ($question = $questionsQuery->fetch(PDO::FETCH_ASSOC)) {
                $questionData = [
                    'source_id' => $question['Id'],
                    'question' => $question['Description'],
                    'type' => 'multiple_choice',
                    'created_at' => $question['CreatedDate'],
                    'updated_at' => $question['LastModifiedDate'],
                    'answers' => []
                ];

                // Export question options
                $optionsQuery = $this->sourceDB->prepare("
                    SELECT
                        qo.Id, qo.Description, qo.IsCorrect, qo.CreatedDate, qo.LastModifiedDate
                    FROM questionoption qo
                    WHERE qo.QuestionId = ? AND qo.Deleted = 0
                    ORDER BY qo.Id
                ");
                $optionsQuery->execute([$question['Id']]);

                while ($option = $optionsQuery->fetch(PDO::FETCH_ASSOC)) {
                    $questionData['answers'][] = [
                        'source_id' => $option['Id'],
                        'answer' => $option['Description'],
                        'is_correct' => (bool)$option['IsCorrect'],
                        'created_at' => $option['CreatedDate'],
                        'updated_at' => $option['LastModifiedDate']
                    ];

                    $answerCount++;
                }

                $testData['questions'][] = $questionData;
                $questionCount++;
            }

            $moduleData['test'] = $testData;
        }

        return [
            'moduleData' => $moduleData,
            'questionCount' => $questionCount,
            'answerCount' => $answerCount
        ];
    }

    /**
     * Fetch core traits from database
     */
    protected function fetchCoreTraits(): array
    {
        $coreTraits = [];
        $query = $this->sourceDB->query("SELECT Id, Name FROM coretrait WHERE Deleted = 0");

        while ($trait = $query->fetch(PDO::FETCH_ASSOC)) {
            $coreTraits[$trait['Id']] = $trait['Name'];
        }

        $this->info('Fetched ' . count($coreTraits) . ' core traits.');
        return $coreTraits;
    }

    /**
     * Fetch sports from database
     */
    protected function fetchSports(): array
    {
        $sports = [];
        $query = $this->sourceDB->query("SELECT Id, Name FROM sport WHERE Deleted = 0");

        while ($sport = $query->fetch(PDO::FETCH_ASSOC)) {
            $sports[$sport['Id']] = $sport['Name'];
        }

        $this->info('Fetched ' . count($sports) . ' sports.');
        return $sports;
    }
}
