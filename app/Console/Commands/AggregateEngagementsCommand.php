<?php

namespace App\Console\Commands;

use App\Services\EngagementService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class AggregateEngagementsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'engagements:aggregate {--date= : The date to aggregate (YYYY-MM-DD format). Defaults to yesterday.}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Aggregate engagement data for a specific date';

    /**
     * Execute the console command.
     */
    public function handle(EngagementService $engagementService)
    {
        $dateString = $this->option('date');

        if ($dateString) {
            try {
                $date = Carbon::createFromFormat('Y-m-d', $dateString);
            } catch (\Exception $e) {
                $this->error('Invalid date format. Please use YYYY-MM-DD.');
                return 1;
            }
        } else {
            $date = Carbon::yesterday();
        }

        $this->info("Aggregating engagements for {$date->format('Y-m-d')}...");

        $count = $engagementService->aggregateEngagementsForDate($date);

        $this->info("Aggregated {$count} unique engagement combinations.");

        return 0;
    }
}
