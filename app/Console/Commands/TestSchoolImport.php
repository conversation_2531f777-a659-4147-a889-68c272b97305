<?php

namespace App\Console\Commands;

use App\Services\Import\SchoolImportService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class TestSchoolImport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-school-import {file : Path to the CSV file}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the school import functionality with a CSV file';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $filePath = $this->argument('file');

        if (!file_exists($filePath)) {
            $this->error("File not found: {$filePath}");
            return 1;
        }

        $this->info("Testing school import with file: {$filePath}");

        $service = app(SchoolImportService::class);

        // Validate the CSV structure
        $this->info('Validating CSV structure...');
        $validationResult = $service->validateCsvStructure($filePath);

        if (!$validationResult['valid']) {
            $this->error('CSV validation failed:');
            foreach ($validationResult['errors'] as $error) {
                $this->error("- {$error}");
            }
            return 1;
        }

        $this->info("CSV validation successful. Found {$validationResult['total_rows']} rows to process.");

        // Process the CSV file
        if ($this->confirm('Do you want to proceed with the import?', true)) {
            $this->info('Processing CSV file...');
            $progressBar = $this->output->createProgressBar($validationResult['total_rows']);
            $progressBar->start();

            $result = $service->processCsvFile($filePath, function ($rowNumber, $result) use ($progressBar) {
                $progressBar->advance();
            });

            $progressBar->finish();
            $this->newLine(2);

            if ($result['success']) {
                $this->info("Import completed successfully!");
                $this->info("Successfully imported {$result['success_count']} schools.");

                if ($result['error_count'] > 0) {
                    $this->warn("Encountered {$result['error_count']} errors during import:");
                    foreach ($result['errors'] as $error) {
                        $this->warn("- {$error}");
                    }
                }
            } else {
                $this->error("Import failed: {$result['message']}");
                return 1;
            }
        }

        return 0;
    }
}
