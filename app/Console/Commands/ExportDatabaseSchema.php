<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;
use Symfony\Component\Yaml\Yaml;
use Illuminate\Support\Str;

class ExportDatabaseSchema extends Command
{
    protected $signature = 'db:export-schema {output : The relative path and filename for the output YAML file}';
    protected $description = 'Export the database schema to a YAML file';

    public function handle()
    {
        $outputPath = $this->argument('output');
        $schema = $this->getSchema();
        $yaml = Yaml::dump($schema, 4, 2);

        file_put_contents(base_path($outputPath), $yaml);

        $this->info("Schema exported to {$outputPath}");
    }

    private function getSchema()
    {
        $schema = [];
        $tables = Schema::getTables();

        foreach ($tables as $tableInfo) {
            $tableName = $tableInfo['name'];

            // Split table name into schema and table parts
            $parts = str_contains($tableName, '.')
                ? explode('.', $tableName)
                : ['public', $tableName];

            $schemaName = $parts[0];
            $table = $parts[1];

            $schema[$tableName] = [
                'columns' => $this->getColumns($schemaName, $table),
                'indexes' => $this->getIndexes($tableName),
                'foreign_keys' => $this->getForeignKeys($tableName),
            ];
        }

        return $schema;
    }

    private function getColumns($schema, $table)
    {
        $connection = Schema::getConnection();
        $driver = $connection->getDriverName();
        $columns = Schema::getColumns($schema . '.' . $table);
        $columnDetails = [];

        foreach ($columns as $column) {
            // Handle type differences between databases
            $type = $this->normalizeType($column['type'], $column['type_name'], $driver);

            $details = [
                'type' => $type,
                'nullable' => $column['nullable'],
            ];

            // Handle auto-increment across different databases
            if ($this->isAutoIncrement($column, $driver)) {
                $details['auto_increment'] = true;
            }

            // Handle default values across different databases
            if ($column['default'] !== null) {
                $details['default'] = $this->normalizeDefault($column['default'], $driver);
            }

            $columnDetails[$column['name']] = $details;
        }

        return $columnDetails;
    }

    private function normalizeType(string $type, string $typeName, string $driver): string
    {
        return match ($driver) {
            'pgsql' => $typeName === 'varchar' ? $type : $typeName,
            'mysql' => str_replace(['unsigned', 'signed'], '', $type),
            'sqlite' => strtolower($type), // SQLite types are case-insensitive
            default => $type,
        };
    }

    private function isAutoIncrement(array $column, string $driver): bool
    {
        return match ($driver) {
            'pgsql' => $column['auto_increment'] ?? false,
            'mysql' => str_contains(strtolower($column['extra'] ?? ''), 'auto_increment'),
            'sqlite' => $column['auto_increment'] ?? false,
            default => false,
        };
    }

    private function normalizeDefault($default, string $driver): string
    {
        if ($driver === 'pgsql' && str_contains($default, '::')) {
            $parts = explode('::', $default);
            return trim($parts[0], "'");
        }

        if ($driver === 'mysql' && str_contains(strtolower($default), 'current_timestamp')) {
            return 'CURRENT_TIMESTAMP';
        }

        return $default;
    }

    private function getIndexes($tableName)
    {
        $connection = Schema::getConnection();
        $driver = $connection->getDriverName();
        $indexes = Schema::getIndexes($tableName);
        $indexDetails = [];

        foreach ($indexes as $index) {
            // Skip primary key indexes if they follow standard naming
            if ($this->isStandardPrimaryKey($index, $driver)) {
                continue;
            }

            $details = [
                'columns' => $index['columns'],
            ];

            if ($index['unique']) {
                $details['unique'] = true;
            }

            if ($index['primary']) {
                $details['primary'] = true;
            }

            // Only include type for PostgreSQL as other DBs mainly use btree
            if ($driver === 'pgsql' && $index['type'] !== 'btree') {
                $details['type'] = $index['type'];
            }

            if (!$this->isStandardIndexName($index['name'], $index['columns'])) {
                $details['name'] = $index['name'];
            }

            $indexDetails[] = $details;
        }

        return $indexDetails;
    }

    private function isStandardPrimaryKey(array $index, string $driver): bool
    {
        if (!$index['primary']) {
            return false;
        }

        return match ($driver) {
            'pgsql' => str_ends_with($index['name'], '_pkey'),
            'mysql' => $index['name'] === 'PRIMARY',
            'sqlite' => str_starts_with($index['name'], 'sqlite_autoindex_'),
            default => false,
        };
    }

    private function getForeignKeys($tableName)
    {
        $foreignKeys = Schema::getForeignKeys($tableName);
        $fkDetails = [];

        foreach ($foreignKeys as $fk) {
            $details = [
                'columns' => $fk['columns'],
                'foreign_table' => $fk['foreign_table'],
                'foreign_columns' => $fk['foreign_columns'],
            ];

            // Only add non-cascade actions
            if ($fk['on_delete'] !== 'cascade') {
                $details['on_delete'] = $fk['on_delete'];
            }
            if ($fk['on_update'] !== 'cascade') {
                $details['on_update'] = $fk['on_update'];
            }

            // Only add name if it exists and it's not a standard name
            if (isset($fk['name']) && !$this->isStandardForeignKeyName($fk['name'], $fk['columns'], $fk['foreign_table'])) {
                $details['name'] = $fk['name'];
            }

            $fkDetails[] = $details;
        }

        return $fkDetails;
    }

    private function isStandardIndexName(string $name, array $columns): bool
    {
        $standardName = implode('_', $columns) . '_index';
        return $name === $standardName;
    }

    private function isStandardForeignKeyName(?string $name, array $columns, string $foreignTable): bool
    {
        if ($name === null) {
            return true; // Treat null names as standard
        }

        $standardName = sprintf(
            '%s_%s_foreign',
            Str::singular($foreignTable),
            implode('_', $columns)
        );
        return $name === $standardName;
    }
}
