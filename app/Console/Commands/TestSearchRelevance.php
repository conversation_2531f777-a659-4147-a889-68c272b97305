<?php

namespace App\Console\Commands;

use App\Services\Meilisearch\Facades\Meili;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class TestSearchRelevance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'networking:test-relevance
                            {--interest= : Career interest to test relevance for}
                            {--sport= : Sport to test relevance for}
                            {--user-id= : User ID to test recommendations for}
                            {--sort= : Sort criteria to apply (relevance, name, graduation_year)}
                            {--search= : Search term to test with different sort options}
                            {--test-sorting : Run tests to validate sorting behavior}
                            {--test-pure-sorting : Test sorting without filters to show it only changes order}
                            {--test-ranking-rules : Test the specific effect of career_interests and sports ranking rules}
                            {--limit=10 : Number of results to return}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the search relevance of the users index';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $client = Meili::client();
        $usersIndex = $client->index('users');

        // Check if we're testing ranking rules specifically
        if ($this->option('test-ranking-rules')) {
            return $this->testRankingRules($usersIndex);
        }

        // Check if we're testing search with different sort options
        if ($searchTerm = $this->option('search')) {
            return $this->testSearchWithSorting($usersIndex, $searchTerm);
        }

        // Check if we're testing pure sorting behavior (without filters)
        if ($this->option('test-pure-sorting')) {
            return $this->testPureSortingBehavior($usersIndex);
        }

        // Check if we're testing sorting behavior
        if ($this->option('test-sorting')) {
            return $this->testSortingBehavior($usersIndex);
        }

        // Check if we need to retrieve recommendations for a specific user
        if ($userId = $this->option('user-id')) {
            return $this->getRecommendationsForUser($usersIndex, $userId);
        }

        // Check if we're testing relevance by interest
        if ($interest = $this->option('interest')) {
            return $this->searchByInterest($usersIndex, $interest, $this->option('sort'));
        }

        // Check if we're testing relevance by sport
        if ($sport = $this->option('sport')) {
            return $this->searchBySport($usersIndex, $sport, $this->option('sort'));
        }

        // If no specific option, run standard relevance test
        return $this->runStandardTest($usersIndex, $this->option('sort'));
    }

    /**
     * Test relevance for a specific career interest
     */
    private function searchByInterest($index, $interest, $sortBy = null)
    {
        $this->info("Testing relevance for career interest: {$interest}" . ($sortBy ? " with sort: {$sortBy}" : ""));

        $searchParams = [
            'q' => '',
            'filter' => 'career_interests = "' . $interest . '"',
            'limit' => (int) $this->option('limit'),
        ];

        // Apply sort if specified
        $this->applySortParams($searchParams, $sortBy);

        $searchResult = $index->search('', $searchParams);

        $this->displayResults($searchResult);

        return Command::SUCCESS;
    }

    /**
     * Test relevance for a specific sport
     */
    private function searchBySport($index, $sport, $sortBy = null)
    {
        $this->info("Testing relevance for sport: {$sport}" . ($sortBy ? " with sort: {$sortBy}" : ""));

        $searchParams = [
            'q' => '',
            'filter' => 'sports = "' . $sport . '"',
            'limit' => (int) $this->option('limit'),
        ];

        // Apply sort if specified
        $this->applySortParams($searchParams, $sortBy);

        $searchResult = $index->search('', $searchParams);

        $this->displayResults($searchResult);

        return Command::SUCCESS;
    }

    /**
     * Test recommendations for a specific user
     */
    private function getRecommendationsForUser($index, $userId)
    {
        // Get the user's interests and sports
        $userData = DB::table('users as u')
            ->select(
                'u.id',
                'u.first_name',
                'u.last_name',
                DB::raw('ARRAY_AGG(DISTINCT i.name) as interests'),
                DB::raw('ARRAY_AGG(DISTINCT s.name) as sports')
            )
            ->leftJoin('interest_user as iu', 'u.id', '=', 'iu.user_id')
            ->leftJoin('interests as i', 'iu.interest_id', '=', 'i.id')
            ->leftJoin('sport_user as su', 'u.id', '=', 'su.user_id')
            ->leftJoin('sports as s', 'su.sport_id', '=', 's.id')
            ->where('u.id', $userId)
            ->groupBy('u.id', 'u.first_name', 'u.last_name')
            ->first();

        if (!$userData) {
            $this->error("User with ID {$userId} not found");
            return Command::FAILURE;
        }

        $this->info("Testing recommendations for user: {$userData->first_name} {$userData->last_name}");

        // Convert string representation of arrays to actual arrays
        $interests = is_array($userData->interests) ? $userData->interests :
            (is_string($userData->interests) && $userData->interests ? explode(',', trim($userData->interests, '{}')) : []);
        $sports = is_array($userData->sports) ? $userData->sports :
            (is_string($userData->sports) && $userData->sports ? explode(',', trim($userData->sports, '{}')) : []);

        $this->line("Career interests: " . implode(', ', $interests));
        $this->line("Sports: " . implode(', ', $sports));

        // Store the parsed arrays back on the userData object for later use
        $userData->interests = $interests;
        $userData->sports = $sports;

        // Get the user's connections
        $connectionIds = DB::table('connections')
            ->where(function ($query) use ($userId) {
                $query->where('requester_id', $userId)
                    ->orWhere('recipient_id', $userId);
            })
            ->where('status', 'accepted')
            ->get()
            ->map(function ($connection) use ($userId) {
                return $connection->requester_id == $userId
                    ? $connection->recipient_id
                    : $connection->requester_id;
            })
            ->toArray();

        if (!empty($connectionIds)) {
            $this->line("Existing connections: " . implode(', ', $connectionIds));
        } else {
            $this->line("No existing connections found");
        }

        // Build search params
        $searchParams = [
            'q' => '',
            'limit' => (int) $this->option('limit'),
            'filter' => 'id != ' . $userId,
        ];

        // If user has connections, exclude them from results
        if (!empty($connectionIds)) {
            $connectionFilter = implode(' AND ', array_map(function ($id) {
                return 'id != ' . $id;
            }, $connectionIds));

            $searchParams['filter'] .= ' AND ' . $connectionFilter;
        }

        // Apply sort if specified
        $this->applySortParams($searchParams, $this->option('sort'));

        $searchResult = $index->search('', $searchParams);

        $this->displayResults($searchResult, $userData);

        return Command::SUCCESS;
    }

    /**
     * Run a standard test without specific filters
     */
    private function runStandardTest($index, $sortBy = null)
    {
        $this->info("Running standard relevance test" . ($sortBy ? " with sort: {$sortBy}" : ""));

        $searchParams = [
            'q' => '',
            'limit' => (int) $this->option('limit'),
        ];

        // Apply sort if specified
        $this->applySortParams($searchParams, $sortBy);

        $searchResult = $index->search('', $searchParams);

        $this->displayResults($searchResult);

        return Command::SUCCESS;
    }

    /**
     * Test sorting behavior to validate that ranking rules apply only with relevance sort
     */
    private function testSortingBehavior($index)
    {
        $this->info("Testing sorting behavior with different sort options");

        // Use an interest or sport if provided, otherwise test without filters
        $filter = '';
        if ($interest = $this->option('interest')) {
            $filter = 'career_interests = "' . $interest . '"';
            $this->info("Using filter: career_interests = \"{$interest}\"");
        } elseif ($sport = $this->option('sport')) {
            $filter = 'sports = "' . $sport . '"';
            $this->info("Using filter: sports = \"{$sport}\"");
        }

        // Test with relevance sort (default)
        $this->info("\nResults with relevance sort (career_interests and sports boosting should apply):");
        $relevanceParams = [
            'q' => '',
            'limit' => (int) $this->option('limit'),
        ];
        if ($filter) {
            $relevanceParams['filter'] = $filter;
        }
        $relevanceResults = $index->search('', $relevanceParams);
        $this->displayResults($relevanceResults);

        // Test with name sort
        $this->info("\nResults with name sort (career_interests and sports boosting should NOT apply):");
        $nameParams = [
            'q' => '',
            'limit' => (int) $this->option('limit'),
            'sort' => ['first_name:asc', 'last_name:asc']
        ];
        if ($filter) {
            $nameParams['filter'] = $filter;
        }
        $nameResults = $index->search('', $nameParams);
        $this->displayResults($nameResults);

        // Test with graduation year sort
        $this->info("\nResults with graduation year sort (career_interests and sports boosting should NOT apply):");
        $yearParams = [
            'q' => '',
            'limit' => (int) $this->option('limit'),
            'sort' => ['graduation_year:asc']
        ];
        if ($filter) {
            $yearParams['filter'] = $filter;
        }
        $yearResults = $index->search('', $yearParams);
        $this->displayResults($yearResults);

        return Command::SUCCESS;
    }

    /**
     * Test pure sorting behavior to demonstrate that sorting only changes order, not limits results
     */
    private function testPureSortingBehavior($index)
    {
        $this->info("Testing pure sorting behavior (without filters)");
        $this->info("This demonstrates that sorting only changes result order but doesn't limit results");

        $limit = (int) $this->option('limit');

        // First, count total results to show that all sorts return same count
        $countParams = [
            'q' => '',
            'limit' => 0, // Just get count, no results
        ];
        $countResult = $index->search('', $countParams);
        $totalResults = $countResult->getEstimatedTotalHits();

        $this->info("\nTotal available results: {$totalResults}");
        $this->info("(All sort methods should return the same {$limit} results from this total, just in different order)");

        // Test with relevance sort (default)
        $this->info("\nResults with relevance sort:");
        $relevanceParams = [
            'q' => '',
            'limit' => $limit,
        ];
        $relevanceResults = $index->search('', $relevanceParams);
        $this->displayResults($relevanceResults);

        // Test with name sort
        $this->info("\nResults with name sort:");
        $nameParams = [
            'q' => '',
            'limit' => $limit,
            'sort' => ['first_name:asc', 'last_name:asc']
        ];
        $nameResults = $index->search('', $nameParams);
        $this->displayResults($nameResults);

        // Test with graduation year sort
        $this->info("\nResults with graduation year sort:");
        $yearParams = [
            'q' => '',
            'limit' => $limit,
            'sort' => ['graduation_year:asc']
        ];
        $yearResults = $index->search('', $yearParams);
        $this->displayResults($yearResults);

        return Command::SUCCESS;
    }

    /**
     * Test search with different sort options
     */
    private function testSearchWithSorting($index, $searchTerm)
    {
        $this->info("Testing search with different sort options");
        $this->info("Search term: \"{$searchTerm}\"");
        $this->info("This demonstrates how search limits results while sorting changes their order");

        $limit = (int) $this->option('limit');

        // First, search with relevance sort (default)
        $this->info("\nResults with search term and relevance sort:");
        $relevanceParams = [
            'q' => $searchTerm,
            'limit' => $limit,
        ];
        $relevanceResults = $index->search($searchTerm, $relevanceParams);
        $this->displayResults($relevanceResults);

        // Then, search with name sort
        $this->info("\nResults with search term and name sort:");
        $nameParams = [
            'q' => $searchTerm,
            'limit' => $limit,
            'sort' => ['first_name:asc', 'last_name:asc']
        ];
        $nameResults = $index->search($searchTerm, $nameParams);
        $this->displayResults($nameResults);

        // Finally, search with graduation year sort
        $this->info("\nResults with search term and graduation year sort:");
        $yearParams = [
            'q' => $searchTerm,
            'limit' => $limit,
            'sort' => ['graduation_year:asc']
        ];
        $yearResults = $index->search($searchTerm, $yearParams);
        $this->displayResults($yearResults);

        return Command::SUCCESS;
    }

    /**
     * Apply sort parameters to the search query
     */
    private function applySortParams(&$searchParams, $sortBy)
    {
        if ($sortBy) {
            if ($sortBy == 'name') {
                $searchParams['sort'] = ['first_name:asc', 'last_name:asc'];
            } elseif ($sortBy == 'graduation_year') {
                $searchParams['sort'] = ['graduation_year:asc'];
            }
            // No sort param needed for relevance - that's the default
        }
    }

    /**
     * Display search results in a formatted way
     */
    private function displayResults($searchResult, $userData = null)
    {
        // Access the properties of the SearchResult object
        $hits = $searchResult->getHits();
        $estimatedTotalHits = $searchResult->getEstimatedTotalHits();

        $this->info("Found {$estimatedTotalHits} results");

        if (empty($hits)) {
            $this->warn("No results found");
            return;
        }

        $headers = ['ID', 'Name', 'Career Interests', 'Sports', 'Relevance Score'];
        $rows = [];

        foreach ($hits as $hit) {
            // Calculate a simple match score if we have user data
            $matchDetails = '';
            $scoreText = '-';

            if ($userData) {
                $hitInterests = $hit['career_interests'] ?? [];
                $hitSports = $hit['sports'] ?? [];

                $interestMatches = array_intersect($userData->interests ?? [], $hitInterests);
                $sportMatches = array_intersect($userData->sports ?? [], $hitSports);

                // Calculate a weighted score (interests worth 3 points, sports worth 2)
                $interestScore = count($interestMatches) * 3;
                $sportScore = count($sportMatches) * 2;
                $totalScore = $interestScore + $sportScore;

                if ($totalScore > 0) {
                    $scoreText = $totalScore . ' (I:' . $interestScore . ' + S:' . $sportScore . ')';

                    // Add interest matches details
                    if (!empty($interestMatches)) {
                        $scoreText .= "\n" . count($interestMatches) . ' interest match' .
                            (count($interestMatches) > 1 ? 'es' : '') . ': ' .
                            implode(', ', $interestMatches);
                    }

                    // Add sport matches details
                    if (!empty($sportMatches)) {
                        $scoreText .= "\n" . count($sportMatches) . ' sport match' .
                            (count($sportMatches) > 1 ? 'es' : '') . ': ' .
                            implode(', ', $sportMatches);
                    }
                }
            }

            $rows[] = [
                $hit['id'],
                ($hit['first_name'] ?? '') . ' ' . ($hit['last_name'] ?? ''),
                implode(', ', $hit['career_interests'] ?? []),
                implode(', ', $hit['sports'] ?? []),
                $scoreText
            ];
        }

        $this->table($headers, $rows);
    }

    /**
     * Test the specific effect of career_interests and sports ranking rules
     */
    private function testRankingRules($index)
    {
        $this->info("Testing the specific effect of career_interests:desc and sports:desc ranking rules");

        $limit = (int) $this->option('limit');

        // Create example users with different sports/interests distributions
        $this->info("\nScenario: A user searches for 'athlete'");
        $this->info("These users all match the search term 'athlete'");
        $this->info("Users with more career interests/sports should rank higher with relevance sort");

        // First, search with relevance sort (default) which includes career_interests:desc, sports:desc
        $this->info("\nResults with default ranking rules (includes career_interests:desc, sports:desc):");
        $relevanceParams = [
            'q' => 'athlete',
            'limit' => $limit,
        ];
        $relevanceResults = $index->search('athlete', $relevanceParams);
        $this->displayResults($relevanceResults);

        // For comparison, let's get the same search but sorted alphabetically
        // This shows results without the influence of career_interests:desc, sports:desc
        $this->info("\nComparison: Same search but with name sort (no career_interests/sports influence):");
        $nameParams = [
            'q' => 'athlete',
            'limit' => $limit,
            'sort' => ['first_name:asc', 'last_name:asc']
        ];
        $nameResults = $index->search('athlete', $nameParams);
        $this->displayResults($nameResults);

        // Explain what we're seeing in the results
        $this->info("\nObservation: Notice how the default ranking includes career_interests:desc and sports:desc");
        $this->info("Users with more interests/sports generally appear higher in the relevance sort results");
        $this->info("while name sort orders them purely alphabetically regardless of interests/sports count.");

        return Command::SUCCESS;
    }
}
