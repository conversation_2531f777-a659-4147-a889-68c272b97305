<?php

namespace App\Concerns;

use App\Contracts\HasExportableColumnsInterface;
use App\Exports\ExportableColumnsRegistry;
use App\Jobs\ProcessFilterViewExportJob;
use App\Models\FilterViewExport;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Filament\Notifications\Notification;

/**
 * Components using this trait should implement HasExportableColumnsInterface
 * to define columns available for CSV export.
 *
 * Usage:
 * 1. Use this trait in your Livewire component
 * 2. Implement HasExportableColumnsInterface
 * 3. Call $this->bootHasExportableColumns() in your component's booted() method
 * 4. To handle export events, register $this->handleExportCsv() method as a listener
 *    for 'export-csv' events in your component
 */
trait HasExportableColumns
{
    /**
     * Boot the trait and register exportable columns
     *
     * This should be called in the component's booted() method
     */
    public function bootHasExportableColumns(): void
    {
        // Only register if the component implements the interface
        if (!$this instanceof HasExportableColumnsInterface) {
            return;
        }

        $this->registerExportableColumns();

        // Register the export event listener
        // This approach works with both existing protected $listeners and getListeners() method
        $this->setupListeners();
    }

    /**
     * Setup event listeners for the component
     * This method handles merging our export listener with any existing listeners
     */
    protected function setupListeners(): void
    {
        // Check if the component has a protected $listeners property
        $reflection = new \ReflectionClass($this);
        $hasListenersProperty = $reflection->hasProperty('listeners');

        if ($hasListenersProperty) {
            $listenersProperty = $reflection->getProperty('listeners');
            $listenersProperty->setAccessible(true);

            // Get existing listeners
            $currentListeners = $listenersProperty->getValue($this);

            // Only add our listener if it's not already defined
            if (!isset($currentListeners['export-csv'])) {
                // Merge our listener with existing ones
                $newListeners = array_merge($currentListeners, ['export-csv' => 'handleExportCsv']);

                // Set the updated listeners back to the property
                $listenersProperty->setValue($this, $newListeners);
            }
        } else {
            // No existing listeners property, create it
            $this->listeners = ['export-csv' => 'handleExportCsv'];
        }
    }

    /**
     * Register the component's exportable columns with the registry
     *
     * @return void
     */
    public function registerExportableColumns(): void
    {
        // Get the resource type based on component properties or class name
        $resourceType = $this->getResourceTypeForExport();

        if (!$resourceType) {
            return;
        }

        // Register the columns with the central registry
        ExportableColumnsRegistry::register(
            $resourceType,
            $this->getExportableColumns(),
            $this->getDefaultExportColumns(),
            $this->getExportableDateColumns(),
            $this->getExportableColumnTypes()
        );
    }

    /**
     * Handle CSV export request
     *
     * @param array $data Export form data containing columns and filters
     * @return void
     */
    public function handleExportCsv(array $data): void
    {
        // Extract export data
        $formData = $data['data'] ?? $data;
        $columns = $formData['columns'] ?? [];
        $dateFilter = $formData['date_filter'] ?? null;
        $resourceType = $formData['resource_type'] ?? $this->getResourceTypeForExport();

        if (empty($columns) || !$resourceType) {
            // Show error notification
            Notification::make()
                ->title('Export Error')
                ->body('No columns selected for export or resource type not found.')
                ->danger()
                ->send();
            return;
        }

        // Get current query filters
        $filters = $this->getExportFilters();

        try {
            // This method only works with Filament Table-based components
            if (!method_exists($this, 'table') || !property_exists($this, 'table')) {
                throw new \Exception('Component does not have a table property - export not supported');
            }

            // Get the current table query with all filters already applied
            $query = $this->table($this->table)->getQuery();

            // Ensure important relationships are eager loaded
            if (method_exists($this, 'getExportEagerLoadRelations')) {
                $query->with($this->getExportEagerLoadRelations());
            }

            // Apply date filter to the query if provided
            if ($dateFilter) {
                $query = $this->applyDateFilters($query, $dateFilter);
            }

            // Count total records for the notification
            $recordCount = $query->count();

            // Serialize the query for processing
            $serializedQuery = \AnourValar\EloquentSerialize\Facades\EloquentSerializeFacade::serialize($query);

            // Create export record
            $export = FilterViewExport::create([
                'user_id' => Auth::id(),
                'resource_type' => $resourceType,
                'columns' => $columns,
                'filters' => $filters,
                'date_filter' => $dateFilter,
                'status' => 'pending',
                'total_records' => $recordCount,
            ]);

            // Get column headers/display names from the exportable columns
            $columnHeaders = $this->getExportableColumns();

            // Dispatch job with serialized query and column headers
            ProcessFilterViewExportJob::dispatch($export, $serializedQuery, $columnHeaders);

            // Close the modal
            if (method_exists($this, 'dispatch')) {
                $this->dispatch('close-modal', id: 'manage-filter-view');
            }

            // Show notification that export is processing
            Notification::make()
                ->success()
                ->title('CSV Export Started')
                ->body("Your export of {$recordCount} records is being processed and will be available shortly.")
                ->send();
        } catch (\Exception $e) {
            // Log error and show notification
            \Illuminate\Support\Facades\Log::error('Error creating export', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            Notification::make()
                ->danger()
                ->title('Export Failed')
                ->body('Could not create export: ' . $e->getMessage())
                ->send();
        }
    }

    /**
     * Apply date filters to a query
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param array $dateFilter The date filter configuration
     * @return \Illuminate\Database\Eloquent\Builder
     */
    protected function applyDateFilters($query, array $dateFilter): \Illuminate\Database\Eloquent\Builder
    {
        if (isset($dateFilter['column']) && $dateFilter['column']) {
            $columnName = $dateFilter['column'];

            // Handle both direct columns and relationship columns with dot notation
            if (str_contains($columnName, '.')) {
                // For relationship columns like 'nominations.created_at'
                // We need to join the related table
                [$relation, $column] = explode('.', $columnName, 2);

                // Check if the relation contains further nesting
                if (str_contains($column, '.')) {
                    // This is a deeply nested relationship (e.g., 'nominations.school.created_at')
                    // For this case, we advise using a custom query implementation
                    if (isset($dateFilter['from']) && $dateFilter['from']) {
                        $query->whereHas($relation, function ($q) use ($column, $dateFilter) {
                            $q->where($column, '>=', \Carbon\Carbon::parse($dateFilter['from'])->startOfDay());
                        });
                    }

                    if (isset($dateFilter['to']) && $dateFilter['to']) {
                        $query->whereHas($relation, function ($q) use ($column, $dateFilter) {
                            $q->where($column, '<=', \Carbon\Carbon::parse($dateFilter['to'])->endOfDay());
                        });
                    }
                } else {
                    // Single-level relationship
                    if (isset($dateFilter['from']) && $dateFilter['from']) {
                        $query->whereHas($relation, function ($q) use ($column, $dateFilter) {
                            $q->where($column, '>=', \Carbon\Carbon::parse($dateFilter['from'])->startOfDay());
                        });
                    }

                    if (isset($dateFilter['to']) && $dateFilter['to']) {
                        $query->whereHas($relation, function ($q) use ($column, $dateFilter) {
                            $q->where($column, '<=', \Carbon\Carbon::parse($dateFilter['to'])->endOfDay());
                        });
                    }
                }
            } else {
                // Direct column on the main model
                if (isset($dateFilter['from']) && $dateFilter['from']) {
                    $query->where($columnName, '>=', \Carbon\Carbon::parse($dateFilter['from'])->startOfDay());
                }

                if (isset($dateFilter['to']) && $dateFilter['to']) {
                    $query->where($columnName, '<=', \Carbon\Carbon::parse($dateFilter['to'])->endOfDay());
                }
            }
        }

        return $query;
    }

    /**
     * Get relations that should be eager loaded for export
     *
     * Components can override this method to specify relations to eager load
     *
     * @return array
     */
    public function getExportEagerLoadRelations(): array
    {
        // Default implementation tries to extract relationships from exportable columns
        $columns = array_keys($this->getExportableColumns());
        $relations = [];

        foreach ($columns as $column) {
            if (str_contains($column, '.')) {
                $parts = explode('.', $column);

                // Add the top-level relation
                if (count($parts) > 1) {
                    $relations[] = $parts[0];

                    // If this is a nested relation (e.g., county.state), add the full path too
                    if (count($parts) > 2) {
                        $relation = $parts[0];
                        for ($i = 1; $i < count($parts) - 1; $i++) {
                            $relation .= '.' . $parts[$i];
                            $relations[] = $relation;
                        }
                    }
                }
            }
        }

        return array_unique($relations);
    }

    /**
     * Get current filters for export
     *
     * This should be overridden by components to provide their specific filter data
     *
     * @return array
     */
    protected function getExportFilters(): array
    {
        // Default implementation tries to get filters from component properties
        // Components should override this for more accurate results

        if (property_exists($this, 'filters')) {
            return $this->filters;
        }

        if (property_exists($this, 'activeFilters')) {
            return $this->activeFilters;
        }

        return [];
    }

    /**
     * Determine the resource type for export
     *
     * Components can override this method or define a userType/resourceType property
     *
     * @return string|null
     */
    protected function getResourceTypeForExport(): ?string
    {
        // If component has a getUserResourceType method, use that
        if (method_exists($this, 'getUserResourceType')) {
            return $this->getUserResourceType();
        }

        // If component has defined user type property, map it to resource type
        if (property_exists($this, 'userType')) {
            return $this->mapUserTypeToResourceType($this->userType);
        }

        // If component has resource type property directly, use that
        if (property_exists($this, 'resourceType')) {
            return $this->resourceType;
        }

        // Fallback to class name based detection
        return $this->getResourceTypeFromClass();
    }

    /**
     * Map user type to resource type
     *
     * @param string $userType
     * @return string
     */
    protected function mapUserTypeToResourceType(string $userType): string
    {
        return match($userType) {
            'positive_athlete' => 'athletes',
            'positive_coach' => 'coaches',
            'sponsor' => 'sponsors',
            default => 'users',
        };
    }

    /**
     * Get resource type from class name
     *
     * @return string|null
     */
    protected function getResourceTypeFromClass(): ?string
    {
        $className = class_basename($this);

        // Try to extract resource type from common naming patterns
        if (Str::endsWith($className, ['IndexView', 'ListView', 'TableView'])) {
            $baseName = Str::beforeLast($className, 'View');
            $baseName = Str::beforeLast($baseName, 'Index');
            $baseName = Str::beforeLast($baseName, 'List');
            $baseName = Str::beforeLast($baseName, 'Table');

            // Convert to snake case plural
            return Str::plural(Str::snake($baseName));
        }

        return null;
    }

    /**
     * Get columns available for export
     *
     * Should be implemented by classes using this trait
     * Returns an array of column_key => display_label
     *
     * @return array<string, string>
     */
    public function getExportableColumns(): array
    {
        // Default implementation - should be overridden by components
        return [];
    }

    /**
     * Get default columns that should be pre-selected for export
     *
     * @return array<string>
     */
    public function getDefaultExportColumns(): array
    {
        // By default, return the first 5 columns or all if less than 5
        return array_slice(array_keys($this->getExportableColumns()), 0, 5);
    }

    /**
     * Identify which columns contain date values for filtering
     *
     * @return array<string>
     */
    public function getExportableDateColumns(): array
    {
        // Default implementation looks for commonly named date columns
        // Components should override this for more accurate results
        return array_filter(array_keys($this->getExportableColumns()), function ($column) {
            return Str::contains($column, ['date', 'at', 'on', 'time', 'created', 'updated']);
        });
    }

    /**
     * Get column types for exportable columns
     *
     * @return array<string, string>
     */
    public function getExportableColumnTypes(): array
    {
        $columnTypes = [];

        // Auto-detect standard date columns
        $dateColumns = $this->getExportableDateColumns();
        foreach ($dateColumns as $dateColumn) {
            $columnTypes[$dateColumn] = 'date';
        }

        return $columnTypes;
    }

    /**
     * Extract data for export from a collection of models
     *
     * @param \Illuminate\Database\Eloquent\Collection $records
     * @param array $columns Selected column keys
     * @return array Contains header row and data rows
     */
    public function extractExportData(Collection $records, array $columns): array
    {
        $exportableColumns = $this->getExportableColumns();

        // Filter to only requested columns and use their display labels
        $headers = collect($columns)
            ->mapWithKeys(fn ($column) => [$column => $exportableColumns[$column] ?? $column])
            ->toArray();

        // Extract data for each record
        $rows = $records->map(function ($record) use ($columns) {
            return collect($columns)->mapWithKeys(function ($column) use ($record) {
                // Handle nested relationships using dot notation
                if (Str::contains($column, '.')) {
                    return [$column => Arr::get($record->toArray(), $column)];
                }

                // Handle direct attributes
                return [$column => $record->{$column}];
            })->toArray();
        })->toArray();

        return [
            'headers' => $headers,
            'rows' => $rows
        ];
    }
}
