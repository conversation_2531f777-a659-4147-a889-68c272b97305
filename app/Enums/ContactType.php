<?php

namespace App\Enums;

enum ContactType: string
{
    case GENERAL = 'general';
    case ATHLETICS_DIRECTOR = 'athletics_director';
    case COACH = 'coach';
    case PARENT = 'parent';
    case STUDENT = 'student';
    case STAFF = 'staff';
    case RECRUITER = 'recruiter';
    case POSITIVE_ATHLETE = 'positive_athlete';
    case POSITIVE_COACH = 'positive_coach';

    /**
     * Map CSV type values to ContactType enum values
     */
    public static function fromCsvType(?string $type): self
    {
        return match (strtolower($type)) {
            'athlete' => self::POSITIVE_ATHLETE,
            'coach' => self::POSITIVE_COACH,
            'ad', 'athletic director', 'athletics director' => self::ATHLETICS_DIRECTOR,
            default => self::POSITIVE_ATHLETE,
        };
    }

    public function label(): string
    {
        return match($this) {
            self::GENERAL => 'General',
            self::ATHLETICS_DIRECTOR => 'Athletics Director',
            self::COACH => 'Coach',
            self::PARENT => 'Parent',
            self::STUDENT => 'Student',
            self::STAFF => 'Staff',
            self::RECRUITER => 'Recruiter',
            self::POSITIVE_ATHLETE => 'Positive Athlete',
            self::POSITIVE_COACH => 'Positive Coach',
        };
    }

    public function color(): string
    {
        return match($this) {
            self::GENERAL => 'gray',
            self::ATHLETICS_DIRECTOR => 'blue',
            self::COACH => 'green',
            self::PARENT => 'purple',
            self::STUDENT => 'yellow',
            self::STAFF => 'indigo',
            self::RECRUITER => 'orange',
            self::POSITIVE_ATHLETE => 'red',
            self::POSITIVE_COACH => 'pink',
        };
    }
}
