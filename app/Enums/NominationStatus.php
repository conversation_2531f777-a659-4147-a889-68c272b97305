<?php

namespace App\Enums;

enum NominationStatus: string
{
    case PENDING_AD_VERIFICATION = 'pending';
    case AD_VERIFIED = 'verified';
    case NOMINEE_NOTIFIED = 'notified';
    case NOMINEE_ACKNOWLEDGED = 'acknowledged';

    public function label(): string
    {
        return match($this) {
            self::PENDING_AD_VERIFICATION => 'Pending AD Verification',
            self::AD_VERIFIED => 'AD Verified',
            self::NOMINEE_NOTIFIED => 'Nominee Notified',
            self::NOMINEE_ACKNOWLEDGED => 'Nominee Acknowledged',
        };
    }

    public function color(): string
    {
        return match($this) {
            self::PENDING_AD_VERIFICATION => 'warning',
            self::AD_VERIFIED => 'gray',
            self::NOMINEE_NOTIFIED => 'info',
            self::NOMINEE_ACKNOWLEDGED => 'success',
        };
    }

    public static function getColors(): array
    {
        return [
            'warning' => self::PENDING_AD_VERIFICATION->value,
            'gray' => self::AD_VERIFIED->value,
            'info' => self::NOMINEE_NOTIFIED->value,
            'success' => self::NOMINEE_ACKNOWLEDGED->value,
        ];
    }
}
