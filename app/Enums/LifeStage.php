<?php

namespace App\Enums;

enum LifeStage: string
{
    case COLLEGE_STUDENT = 'college_student';
    case PROFESSIONAL = 'professional';
    case COLLEGE_GRADUATE = 'college_graduate';
    case GAP_YEAR = 'gap_year';
    case HIGH_SCHOOL_STUDENT = 'high_school_student';
    case HIGH_SCHOOL_GRADUATE = 'high_school_graduate';

    /**
     * Get the display name for the current life stage
     *
     * @return string
     */
    public function displayName(): string
    {
        return match ($this) {
            self::HIGH_SCHOOL_STUDENT => 'High School Student',
            self::COLLEGE_STUDENT => 'College Student',
            self::COLLEGE_GRADUATE => 'College Graduate',
            self::GAP_YEAR => 'Gap Year',
            self::PROFESSIONAL => 'Professional',
            self::HIGH_SCHOOL_GRADUATE => 'High School Graduate',
            default => 'Unknown',
        };
    }
}
