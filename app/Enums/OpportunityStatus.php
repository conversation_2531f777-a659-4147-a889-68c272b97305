<?php

namespace App\Enums;

enum OpportunityStatus: string
{
    case LISTED = 'listed';
    case UNLISTED = 'unlisted';

    /**
     * Get all values as an array
     *
     * @return array<string>
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Check if a value is valid
     *
     * @param string $value
     * @return bool
     */
    public static function isValid(string $value): bool
    {
        return in_array($value, self::values());
    }

    /**
     * Get the default status
     *
     * @return self
     */
    public static function getDefault(): self
    {
        return self::UNLISTED;
    }
}
