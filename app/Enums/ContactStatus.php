<?php

namespace App\Enums;

enum ContactStatus: string
{
    case ACTIVE = 'active';
    case INACTIVE = 'inactive';
    case PENDING = 'pending';
    case ARCHIVED = 'archived';
    case CONVERTED = 'converted';

    public function label(): string
    {
        return match($this) {
            self::ACTIVE => 'Active',
            self::INACTIVE => 'Inactive',
            self::PENDING => 'Pending',
            self::ARCHIVED => 'Archived',
            self::CONVERTED => 'Converted',
        };
    }

    public function color(): string
    {
        return match($this) {
            self::ACTIVE => 'success',
            self::INACTIVE => 'danger',
            self::PENDING => 'warning',
            self::ARCHIVED => 'secondary',
            self::CONVERTED => 'info',
        };
    }
}
