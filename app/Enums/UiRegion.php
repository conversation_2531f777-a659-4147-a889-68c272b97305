<?php

namespace App\Enums;

enum UiRegion: string
{
    case DASHBOARD = 'dashboard';
    case PROFILE = 'profile';
    case SIDEBAR = 'sidebar';
    case HEADER = 'header';
    case FOOTER = 'footer';
    case FEED = 'feed';
    case OPPORTUNITY_DETAILS = 'opportunity_details';
    case X_FACTOR = 'x_factor';
    case MESSAGING = 'messaging';

    /**
     * Get the display name for a UI region.
     */
    public function displayName(): string
    {
        return match($this) {
            self::DASHBOARD => 'Dashboard',
            self::PROFILE => 'Profile Page',
            self::SIDEBAR => 'Sidebar',
            self::HEADER => 'Header',
            self::FOOTER => 'Footer',
            self::FEED => 'News Feed',
            self::OPPORTUNITY_DETAILS => 'Opportunity Details',
            self::X_FACTOR => 'X-Factor Content',
            self::MESSAGING => 'Messaging',
        };
    }

    /**
     * Get all UI regions as an array of [value => display name].
     */
    public static function asSelectArray(): array
    {
        return collect(self::cases())
            ->mapWithKeys(fn (UiRegion $region) => [$region->value => $region->displayName()])
            ->all();
    }
}
