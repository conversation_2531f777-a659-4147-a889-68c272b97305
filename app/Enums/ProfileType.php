<?php

namespace App\Enums;

enum ProfileType: string
{
    case POSITIVE_ATHLETE = 'positive_athlete';
    case POSITIVE_COACH = 'positive_coach';
    case ATHLETICS_DIRECTOR = 'athletics_director';
    case SPONSOR = 'sponsor';
    case UTILITY = 'utility';
    case PARENT = 'parent';
    case ADMIN = 'admin';
    case COLLEGE_ATHLETE = 'college_athlete';
    case PROFESSIONAL = 'professional';
    case ALUMNI = 'alumni';

    // Future types
    case TEAM_STUDENT = 'team_student';
    case TEAM_COACH = 'team_coach';
}
