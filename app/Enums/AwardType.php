<?php

namespace App\Enums;

enum AwardType: string
{
    case REGIONAL = 'regional';
    case MARKET = 'market';
    case SUBREGIONAL = 'subregional';

    /**
     * Get a human-readable label for the award type
     *
     * @return string
     */
    public function label(): string
    {
        return match($this) {
            self::REGIONAL => 'Regional Winner',
            self::MARKET => 'Market Winner',
            self::SUBREGIONAL => 'Subregional Winner',
        };
    }
}
