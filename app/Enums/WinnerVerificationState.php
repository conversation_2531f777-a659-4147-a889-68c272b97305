<?php

namespace App\Enums;

enum WinnerVerificationState: string
{
    case PENDING_VERIFICATION = 'pending_verification';
    case VERIFIED = 'verified';
    case NOTIFIED = 'notified';
    case ACKNOWLEDGED = 'acknowledged';

    /**
     * Get a human-readable label for the verification state
     *
     * @return string
     */
    public function label(): string
    {
        return match($this) {
            self::PENDING_VERIFICATION => 'Pending AD Verification',
            self::VERIFIED => 'AD Verified',
            self::NOTIFIED => 'Nominee Notified',
            self::ACKNOWLEDGED => 'Nominee Acknowledged',
        };
    }

    /**
     * Get a color associated with this verification state for UI elements
     *
     * @return string
     */
    public function color(): string
    {
        return match($this) {
            self::PENDING_VERIFICATION => 'warning',
            self::VERIFIED => 'gray',
            self::NOTIFIED => 'blue',
            self::ACKNOWLEDGED => 'green',
        };
    }

    /**
     * Check if this state comes after another state in the verification process
     *
     * @param WinnerVerificationState $other
     * @return bool
     */
    public function isAfter(WinnerVerificationState $other): bool
    {
        $ranks = [
            self::PENDING_VERIFICATION->value => 1,
            self::VERIFIED->value => 2,
            self::NOTIFIED->value => 3,
            self::ACKNOWLEDGED->value => 4,
        ];

        return $ranks[$this->value] > $ranks[$other->value];
    }

    /**
     * Get the next state in the verification process
     *
     * @return WinnerVerificationState|null
     */
    public function next(): ?WinnerVerificationState
    {
        return match($this) {
            self::PENDING_VERIFICATION => self::VERIFIED,
            self::VERIFIED => self::NOTIFIED,
            self::NOTIFIED => self::ACKNOWLEDGED,
            self::ACKNOWLEDGED => null,
        };
    }

    /**
     * Get the rank of this state for comparison
     *
     * @return int
     */
    public function rank(): int
    {
        return match($this) {
            self::PENDING_VERIFICATION => 1,
            self::VERIFIED => 2,
            self::NOTIFIED => 3,
            self::ACKNOWLEDGED => 4,
        };
    }
}
