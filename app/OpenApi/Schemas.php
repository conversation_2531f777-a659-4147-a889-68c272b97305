<?php

namespace App\OpenApi;

/**
 * @OA\Schema(
 *     schema="Error",
 *     title="Error Response",
 *     description="Standard error response structure",
 *     @OA\Property(
 *         property="success",
 *         type="boolean",
 *         default=false,
 *         description="Indicates if the request was successful",
 *         example=false
 *     ),
 *     @OA\Property(
 *         property="message",
 *         type="string",
 *         description="Error message",
 *         example="Resource not found"
 *     ),
 *     @OA\Property(
 *         property="errors",
 *         type="object",
 *         nullable=true,
 *         description="Additional error details if available",
 *         example=null
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="ValidationError",
 *     title="Validation Error Response",
 *     description="Response structure for validation errors",
 *     @OA\Property(
 *         property="success",
 *         type="boolean",
 *         default=false,
 *         description="Indicates if the request was successful",
 *         example=false
 *     ),
 *     @OA\Property(
 *         property="message",
 *         type="string",
 *         description="Error message",
 *         example="The given data was invalid"
 *     ),
 *     @OA\Property(
 *         property="errors",
 *         type="object",
 *         description="Validation errors by field",
 *         @OA\AdditionalProperties(
 *             type="array",
 *             @OA\Items(type="string")
 *         ),
 *         example={
 *             "email": {"The email field is required"},
 *             "password": {"The password must be at least 8 characters"}
 *         }
 *     )
 * )
 */
class Schemas
{
    // This class doesn't need any implementation
    // It's just a container for OpenAPI schema annotations
}
