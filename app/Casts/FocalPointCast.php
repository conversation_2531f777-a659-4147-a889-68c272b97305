<?php

namespace App\Casts;

use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;
use InvalidArgumentException;

class FocalPointCast implements CastsAttributes
{
    /**
     * Cast the given value.
     *
     * @param  array<string, mixed>  $attributes
     */
    public function get(Model $model, string $key, mixed $value, array $attributes): ?array
    {
        if ($value === null) {
            return null;
        }

        $focalPoint = json_decode($value, true);

        return [
            'x' => (float) ($focalPoint['x'] ?? 0.5),
            'y' => (float) ($focalPoint['y'] ?? 0.5),
        ];
    }

    /**
     * Prepare the given value for storage.
     *
     * @param  array<string, mixed>  $attributes
     */
    public function set(Model $model, string $key, mixed $value, array $attributes): ?string
    {
        if ($value === null) {
            return null;
        }

        if (!is_array($value) || !isset($value['x']) || !isset($value['y'])) {
            throw new InvalidArgumentException('Focal point must be an array with x and y coordinates');
        }

        $x = (float) $value['x'];
        $y = (float) $value['y'];

        if ($x < 0 || $x > 1 || $y < 0 || $y > 1) {
            throw new InvalidArgumentException('Focal point coordinates must be between 0 and 1');
        }

        return json_encode(['x' => $x, 'y' => $y]);
    }
}
