<?php

namespace App\Casts;

use App\Data\SystemInvites\AlumniInviteData;
use App\Data\SystemInvites\CollegeAthleteInviteData;
use App\Data\SystemInvites\InviteData;
use App\Data\SystemInvites\ParentSystemInviteData;
use App\Data\SystemInvites\PositiveAthleteInviteData;
use App\Data\SystemInvites\PositiveCoachInviteData;
use App\Data\SystemInvites\ProfessionalInviteData;
use App\Data\SystemInvites\TeamStudentInviteData;
use App\Data\SystemInvites\TeamCoachInviteData;
use App\Data\SystemInvites\AthleticsDirectorInviteData;
use App\Data\SystemInvites\SponsorInviteData;
use App\Enums\ProfileType;
use App\Models\Nomination;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use InvalidArgumentException;

class AsInviteData implements CastsAttributes
{
    public function get($model, string $key, $value, array $attributes): ?InviteData
    {
        if (!$value) {
            return null;
        }

        $data = json_decode($value, true);
        $type = !empty($data['type']) ? ProfileType::tryFrom($data['type']) : null;

        return match ($type) {
            ProfileType::POSITIVE_ATHLETE => PositiveAthleteInviteData::from($data),
            ProfileType::POSITIVE_COACH => PositiveCoachInviteData::from($data),
            ProfileType::PARENT => ParentSystemInviteData::from($data),
            ProfileType::COLLEGE_ATHLETE => CollegeAthleteInviteData::from($data),
            ProfileType::PROFESSIONAL => ProfessionalInviteData::from($data),
            ProfileType::TEAM_STUDENT => TeamStudentInviteData::from($data),
            ProfileType::TEAM_COACH => TeamCoachInviteData::from($data),
            ProfileType::ATHLETICS_DIRECTOR => AthleticsDirectorInviteData::from($data),
            ProfileType::SPONSOR => SponsorInviteData::from($data),
            ProfileType::ALUMNI => AlumniInviteData::from($data),
            default => throw new InvalidArgumentException('Unknown invite type'),
        };
    }

    public function set($model, string $key, $value, array $attributes): ?string
    {
        if (!$value) {
            return null;
        }

        if (!$value instanceof InviteData) {
            throw new InvalidArgumentException('The given value is not an InviteData instance.');
        }

        $data = $value->toArray();
        $data['type'] = $this->getTypeFromClass($value);

        return json_encode($data);
    }

    private function getTypeFromClass(InviteData $data): string
    {
        return match ($data::class) {
            PositiveAthleteInviteData::class => ProfileType::POSITIVE_ATHLETE->value,
            PositiveCoachInviteData::class => ProfileType::POSITIVE_COACH->value,
            ParentSystemInviteData::class => ProfileType::PARENT->value,
            CollegeAthleteInviteData::class => ProfileType::COLLEGE_ATHLETE->value,
            ProfessionalInviteData::class => ProfileType::PROFESSIONAL->value,
            TeamStudentInviteData::class => ProfileType::TEAM_STUDENT->value,
            TeamCoachInviteData::class => ProfileType::TEAM_COACH->value,
            AthleticsDirectorInviteData::class => ProfileType::ATHLETICS_DIRECTOR->value,
            SponsorInviteData::class => ProfileType::SPONSOR->value,
            AlumniInviteData::class => ProfileType::ALUMNI->value,
            default => throw new InvalidArgumentException('Unknown invite data class'),
        };
    }
}
