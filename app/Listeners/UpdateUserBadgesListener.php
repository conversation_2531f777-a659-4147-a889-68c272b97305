<?php

namespace App\Listeners;

use App\Events\ModuleCompleted;
use App\Services\BadgeService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateUserBadgesListener
{
    /**
     * Create the event listener.
     */
    public function __construct(
        protected BadgeService $badgeService
    ) {
    }

    /**
     * Handle the event.
     */
    public function handle(ModuleCompleted $event): void
    {
        // Only process standalone modules (not part of a course)
        if (!$this->isStandaloneModule($event->module->id)) {
            return;
        }

        // Update user badges
        $result = $this->badgeService->updateUserBadges($event->user);

        // Log the result
        if ($result['success']) {
            Log::info('User badges updated after module completion', [
                'user_id' => $event->user->id,
                'module_id' => $event->module->id,
                'completed_modules' => $result['completed_modules'],
                'achieved_badges_count' => $result['achieved_badges_count'],
                'newly_earned_badges' => $result['newly_earned_badges']->pluck('name'),
                'next_badge' => $result['next_badge'] ? $result['next_badge']->name : null
            ]);

            // If new badges were earned, you could dispatch notifications here
            if ($result['newly_earned_badges']->isNotEmpty()) {
                foreach ($result['newly_earned_badges'] as $badge) {
                    // Example: Dispatch notification
                    // Notification::send($event->user, new BadgeEarnedNotification($badge));
                }
            }
        } else {
            Log::error('Failed to update user badges after module completion', [
                'user_id' => $event->user->id,
                'module_id' => $event->module->id,
                'error' => $result['error']
            ]);
        }
    }

    /**
     * Check if a module is standalone (not part of any course).
     */
    private function isStandaloneModule(int $moduleId): bool
    {
        return !DB::table('course_module')
            ->where('module_id', $moduleId)
            ->exists();
    }
}
