<?php

namespace App\Listeners;

use App\Actions\Onboarding\CompleteOnboardingAction;
use App\States\Onboarding\States\Completed;
use Spatie\ModelStates\Events\StateChanged;
use Illuminate\Support\Facades\Log;
use App\Exceptions\OnboardingCompletionException;

class HandleOnboardingCompletion
{
    public function __construct(
        private readonly CompleteOnboardingAction $completeOnboardingAction
    ) {}

    public function handle(StateChanged $event): void
    {
        if (!($event->finalState instanceof Completed)) {
            return;
        }

        try {
            $this->completeOnboardingAction->execute($event->model);
        } catch (\Exception $e) {
            Log::error('Onboarding completion failed', [
                'onboarding_id' => $event->model->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new OnboardingCompletionException(
                'Failed to complete onboarding: ' . $e->getMessage(),
                previous: $e
            );
        }
    }
}
