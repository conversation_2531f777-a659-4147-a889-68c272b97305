<?php

namespace App\Listeners;

use Illuminate\Database\Events\MigrationsEnded;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\File;

class ExportSchemaAfterMigration
{
    public function handle(MigrationsEnded $event): void
    {
        if (!App::environment('local')) {
            return;
        }

        $aiRoot = config('ai.root', '.cursor');
        if (!File::exists($aiRoot)) {
            File::makeDirectory($aiRoot, 0755, true);
        }

        Artisan::call('db:export-schema', [
            'output' => $aiRoot . '/db-schema.yaml'
        ]);
    }
}
