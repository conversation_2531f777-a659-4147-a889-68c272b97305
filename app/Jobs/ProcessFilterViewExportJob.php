<?php

namespace App\Jobs;

use App\Models\FilterViewExport;
use App\Models\User;
use Carbon\Carbon;
use Filament\Notifications\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use League\Csv\Writer;
use AnourValar\EloquentSerialize\Facades\EloquentSerializeFacade;

class ProcessFilterViewExportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The export instance.
     *
     * @var FilterViewExport
     */
    protected $export;

    /**
     * The serialized query to be executed.
     *
     * @var string|null
     */
    protected $serializedQuery;

    /**
     * The column headers for the CSV.
     *
     * @var array<string, string>
     */
    protected $columnHeaders;

    /**
     * Create a new job instance.
     *
     * @param FilterViewExport $export
     * @param string|null $serializedQuery The serialized query string
     * @param array<string, string> $columnHeaders The column headers mapping
     */
    public function __construct(
        FilterViewExport $export,
        ?string $serializedQuery = null,
        array $columnHeaders = []
    ) {
        $this->export = $export;
        $this->serializedQuery = $serializedQuery;
        $this->columnHeaders = $columnHeaders;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Initialize query from serialized query string
            $query = null;

            if (empty($this->serializedQuery)) {
                Log::error('Missing serialized query for export', [
                    'export_id' => $this->export->id
                ]);
                throw new \Exception('No query provided for export');
            }

            try {
                // Deserialize the query using the facade
                $query = EloquentSerializeFacade::unserialize($this->serializedQuery);
            } catch (\Exception $e) {
                Log::error('Error deserializing query', [
                    'export_id' => $this->export->id,
                    'error' => $e->getMessage(),
                ]);
                throw new \Exception('Failed to deserialize query: ' . $e->getMessage());
            }

            // Count records
            $recordCount = $query->count();

            // Update export with record count
            $this->export->update([
                'total_records' => $recordCount,
            ]);

            // Generate the CSV
            $csvContent = $this->generateCsv($query);

            // Generate filename
            $filename = 'export-' . $this->export->id . '-' . Carbon::now()->format('Y-m-d-His') . '.csv';

            // Store the file using media library - use original simple implementation
            $this->export->addMediaFromString($csvContent)
                ->usingFileName($filename)
                ->toMediaCollection('csv_exports');

            // Update export status
            $this->export->update([
                'status' => 'completed',
                'filename' => $filename,
            ]);

            // Send notification to user
            $this->createCompletionNotification($recordCount);
        } catch (\Exception $e) {
            Log::error('Export failed', [
                'export_id' => $this->export->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Update export with error
            $this->export->update([
                'status' => 'failed',
                'error' => $e->getMessage(),
            ]);

            // Send error notification
            $this->createErrorNotification($e->getMessage());
        }
    }

    /**
     * Generate CSV content from the query.
     */
    protected function generateCsv($query)
    {
        // Create a CSV writer
        $csv = Writer::createFromString('');

        // Explicitly set the CSV mime type header
        $csv->setOutputBOM(\League\Csv\Writer::BOM_UTF8);

        // Get column headers - use display labels from export form
        $columns = $this->export->columns;

        // Create headers with display labels
        $headers = [];
        foreach ($columns as $column) {
            // Use the provided column headers from the job, or fall back to formatting
            $headers[] = $this->columnHeaders[$column] ?? $this->formatColumnName($column);
        }

        // Insert headers
        $csv->insertOne($headers);

        // Check if we have records to process
        $recordCount = $query->count();

        // If there are no records, add a special row to indicate no results
        if ($recordCount === 0) {
            $emptyRow = array_fill(0, count($columns), '');
            $emptyRow[0] = 'No records matched your filter criteria';
            $csv->insertOne($emptyRow);

            return $csv->toString();
        }

        // Process in chunks to avoid memory issues
        $query->chunk(1000, function ($records) use ($csv, $columns) {
            foreach ($records as $record) {
                $row = [];

                foreach ($columns as $column) {
                    // Handle nested attributes if needed (e.g., user.name)
                    if (strpos($column, '.') !== false) {
                        $parts = explode('.', $column);
                        $value = $record;

                        foreach ($parts as $part) {
                            if ($value === null) {
                                break;
                            }

                            // If value is a collection (Has-Many relationship)
                            if ($value instanceof \Illuminate\Support\Collection) {
                                // Take the first item from the collection
                                $value = $value->first();
                                if ($value === null) {
                                    break;
                                }
                                $value = $value->{$part} ?? null;
                            } else {
                                // Handle regular relationships or attributes
                                $value = is_object($value) ? ($value->{$part} ?? null) : null;
                            }
                        }

                        $row[] = $this->formatValueForCsv($value);
                    } else {
                        $row[] = $this->formatValueForCsv($record->{$column} ?? null);
                    }
                }

                $csv->insertOne($row);
            }
        });

        return $csv->toString();
    }

    /**
     * Format a value for CSV export, handling special types
     */
    protected function formatValueForCsv($value)
    {
        return match(true) {
            $value instanceof \BackedEnum => $value->value,
            $value instanceof \UnitEnum => $value->name,
            $value instanceof \Carbon\Carbon => $value->toDateTimeString(),
            default => $value,
        };
    }

    /**
     * Get exportable columns for the resource type
     */
    protected function getExportableColumnsForResource(): array
    {
        // Try to get columns from registry
        $registry = app(\App\Exports\ExportableColumnsRegistry::class);
        $columns = $registry->getForResource($this->export->resource_type) ?? [];

        // Return columns or empty array if none found
        return $columns;
    }

    /**
     * Format a column name into a readable label
     */
    protected function formatColumnName(string $column): string
    {
        // Special cases for common columns
        $specialCases = [
            'id' => 'ID',
            'created_at' => 'Created Date',
            'updated_at' => 'Updated Date',
            'nominations.created_at' => 'Nomination Date',
            'nominations.status' => 'Award Status',
            'nominations.ai_score' => 'AI Score',
        ];

        if (isset($specialCases[$column])) {
            return $specialCases[$column];
        }

        // Handle relationship columns (with dots)
        if (strpos($column, '.') !== false) {
            $parts = explode('.', $column);
            $last = end($parts);
            return ucwords(str_replace('_', ' ', $last));
        }

        // Default formatting
        return ucwords(str_replace('_', ' ', $column));
    }

    /**
     * Create a notification for successful export completion
     */
    protected function createCompletionNotification($recordCount = null): void
    {
        $user = User::find($this->export->user_id);

        if (!$user) {
            return;
        }

        // Use the model's getDownloadUrl method to get the proper URL
        $downloadUrl = $this->export->getDownloadUrl();

        if (!$downloadUrl) {
            Log::error('Export completed but download URL could not be generated', [
                'export_id' => $this->export->id
            ]);
            return;
        }

        // Create notification with appropriate message based on record count
        $notificationBody = $recordCount === 0
            ? 'Your export was completed successfully, but no records matched your filter criteria.'
            : "Your export of {$recordCount} records is ready for download.";

        $notification = Notification::make()
            ->title('Export Completed')
            ->body($notificationBody)
            ->success();

        // Add download action if records exist
        if ($recordCount > 0) {
            $notification->actions([
                Action::make('download')
                    ->label('Download CSV')
                    ->url($downloadUrl)
                    ->openUrlInNewTab()
            ]);
        }

        $notification->sendToDatabase($user);
    }

    /**
     * Create an error notification.
     */
    protected function createErrorNotification($errorMessage)
    {
        $user = User::find($this->export->user_id);

        if (!$user) {
            return;
        }

        Notification::make()
            ->title('Export Failed')
            ->body("There was an error processing your export: {$errorMessage}")
            ->danger()
            ->sendToDatabase($user);
    }
}
