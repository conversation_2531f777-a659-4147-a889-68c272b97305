<?php

namespace App\Jobs;

use App\Models\Advertisement;
use App\Models\Engagement;
use App\Models\Opportunity;
use App\Enums\OpportunityStatus;
use Carbon\Carbon;
use Faker\Factory as Faker;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class GenerateEngagementDataJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var int
     */
    public $backoff = 60;

    /**
     * Calculate the number of seconds to wait before retrying the job.
     *
     * @return array<int, int>
     */
    public function backoff()
    {
        return [60, 180, 300]; // 1 minute, 3 minutes, 5 minutes
    }

    /**
     * Handle a job failure.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        Log::critical('GenerateEngagementDataJob failed completely', [
            'id' => $this->job ? $this->job->getJobId() : 'unknown',
            'type' => $this->type,
            'model_ids' => $this->modelIds,
            'attempts' => $this->attempts(),
            'error' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
        ]);
    }

    /**
     * @var string The type of engagement data to generate ('advertisement' or 'opportunity')
     */
    protected $type;

    /**
     * @var array IDs of models to generate data for
     */
    protected $modelIds;

    /**
     * @var array Configuration options for data generation
     */
    protected $config;

    /**
     * @var array User IDs to associate with engagements
     */
    protected $userIds;

    /**
     * @var array Constant browser weights
     */
    private const BROWSERS = [
        'Chrome' => 55,
        'Firefox' => 15,
        'Safari' => 20,
        'Edge' => 8,
        'Opera' => 2,
    ];

    /**
     * @var array Constant device type weights
     */
    private const DEVICE_TYPES = [
        'Desktop' => 45,
        'Mobile' => 40,
        'Tablet' => 15,
    ];

    /**
     * @var array Constant operating system weights
     */
    private const OPERATING_SYSTEMS = [
        'Windows' => 40,
        'macOS' => 20,
        'iOS' => 20,
        'Android' => 18,
        'Linux' => 2,
    ];

    /**
     * @var array Constant engagement volume tiers
     */
    private const TIERS = [
        'high' => [
            'distribution' => 20, // 20% of items get high engagement
            'impression_multiplier' => 5.0,
            'ctr_min' => 3.0,
            'ctr_max' => 5.0,
        ],
        'moderate' => [
            'distribution' => 50, // 50% of items get moderate engagement
            'impression_multiplier' => 2.0,
            'ctr_min' => 1.0,
            'ctr_max' => 3.0,
        ],
        'low' => [
            'distribution' => 30, // 30% of items get low engagement
            'impression_multiplier' => 1.0,
            'ctr_min' => 0.5,
            'ctr_max' => 1.0,
        ],
    ];

    /**
     * @var array Constant time-of-day weights for engagement probability
     */
    private const HOUR_WEIGHTS = [
        0 => 0.1,  // 12 AM
        1 => 0.05,
        2 => 0.02,
        3 => 0.01,
        4 => 0.01,
        5 => 0.05,
        6 => 0.1,
        7 => 0.3,
        8 => 0.7,  // Morning work hours
        9 => 1.0,
        10 => 1.2,
        11 => 1.3,
        12 => 1.4, // Lunch hour
        13 => 1.2,
        14 => 1.1, // Afternoon work hours
        15 => 1.0,
        16 => 0.9,
        17 => 0.8,
        18 => 0.7, // Evening
        19 => 0.6,
        20 => 0.5,
        21 => 0.4,
        22 => 0.3,
        23 => 0.2,
    ];

    /**
     * @var array Constant day-of-week weights
     */
    private const DAY_WEIGHTS = [
        0 => 0.6, // Sunday
        1 => 1.0, // Monday
        2 => 1.1, // Tuesday
        3 => 1.2, // Wednesday
        4 => 1.1, // Thursday
        5 => 1.0, // Friday
        6 => 0.5, // Saturday
    ];

    /**
     * Create a new job instance.
     */
    public function __construct(string $type, array $modelIds, array $config, array $userIds)
    {
        $this->type = $type;
        $this->modelIds = $modelIds;
        $this->config = $config;
        $this->userIds = $userIds;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage();

        // Log job start with attempt information
        Log::info("Starting GenerateEngagementDataJob", [
            'type' => $this->type,
            'model_ids_count' => count($this->modelIds),
            'attempt' => $this->attempts(),
            'job_id' => $this->job ? $this->job->getJobId() : 'unknown'
        ]);

        try {
            // Load models based on type
            $models = $this->loadModels();

            // Skip if no models found
            if ($models->isEmpty()) {
                Log::info("No {$this->type} models found with IDs: " . implode(', ', $this->modelIds));
                return;
            }

            Log::info("Generating engagement data for " . count($models) . " {$this->type} models");

            // Force garbage collection before heavy processing
            gc_collect_cycles();

            // Generate data for models
            $this->generateEngagementData($models);

            // Clear model data to free up memory
            $models = null;
            gc_collect_cycles();

            // Log execution time at completion
            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 2);

            Log::info("Completed GenerateEngagementDataJob for {$this->type}", [
                'execution_time' => $executionTime . ' seconds'
            ]);
        } catch (\Exception $e) {
            // Enhanced error logging with full context
            Log::error("Error generating engagement data: " . $e->getMessage(), [
                'exception' => $e,
                'type' => $this->type,
                'model_ids' => $this->modelIds,
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'execution_time' => round(microtime(true) - $startTime, 2) . ' seconds'
            ]);

            // Re-throw the exception to mark the job as failed
            throw $e;
        }
    }

    /**
     * Load models based on type and IDs
     */
    private function loadModels()
    {
        if ($this->type === 'advertisement') {
            return Advertisement::whereIn('id', $this->modelIds)->get();
        } else {
            // Make sure we're using the correct field name: status with OpportunityStatus::LISTED value
            // This avoids the "is_published" field name that's causing errors
            return Opportunity::whereIn('id', $this->modelIds)
                ->where('status', OpportunityStatus::LISTED->value)
                ->get();
        }
    }

    /**
     * Generate engagement data for the models
     */
    private function generateEngagementData($models): void
    {
        try {
            $faker = Faker::create();

            // Assign engagement tiers to models
            $tieredCollection = $this->assignTiers($models, $faker);

            $engagementBatch = [];
            $batchCount = 0;
            $userIdCount = count($this->userIds);
            $totalProcessed = 0;

            // Reduced batch size to prevent memory issues - original was 5000
            $optimizedBatchSize = min($this->config['batch_size'], 1000);

            // Log the start of processing with context info
            Log::info("Starting to generate engagement data", [
                'type' => $this->type,
                'model_count' => count($models),
                'tiered_collection_count' => count($tieredCollection),
                'batch_size' => $optimizedBatchSize,
                'date_range' => [
                    'start' => $this->config['start_date'] ?? null,
                    'end' => $this->config['end_date'] ?? null
                ]
            ]);

            // Pre-compute user association decision arrays for better performance
            $userAssocDecisions = [];
            for ($i = 0; $i < 100; $i++) {
                $userAssocDecisions[] = $faker->randomFloat(2, 0, 100) <= $this->config['user_association_percentage'];
            }

            $clickUserAssocDecisions = [];
            for ($i = 0; $i < 100; $i++) {
                $clickUserAssocDecisions[] = $faker->randomFloat(2, 0, 100) <= ($this->config['user_association_percentage'] * 1.5);
            }

            foreach ($tieredCollection as $index => $item) {
                try {
                    // Log progress less frequently (every 20% of total models instead of every 5 models)
                    if ($index == 0 || $index == count($tieredCollection) - 1 || $index % max(1, ceil(count($tieredCollection) * 0.2)) === 0) {
                        Log::info("Processing model {$index} of " . count($tieredCollection), [
                            'type' => $this->type,
                            'model_id' => $item['model']->id ?? 'unknown'
                        ]);
                    }

                    // Periodically free memory
                    if ($index % 5 === 0) {
                        gc_collect_cycles();
                    }

                    // Generate data for each day in the date range
                    for ($date = Carbon::parse($this->config['start_date']); $date <= Carbon::parse($this->config['end_date']); $date->addDay()) {
                        // Day of week adjustment
                        $dayOfWeekMultiplier = self::DAY_WEIGHTS[$date->dayOfWeek];

                        // Calculate impressions for this day based on tier and day of week
                        $dailyImpressions = $this->calculateDailyImpressions($item['tier'], $dayOfWeekMultiplier);

                        // Calculate clicks based on CTR
                        $dailyClicks = (int) round($dailyImpressions * ($item['ctr'] / 100));

                        // Generate hourly distribution for impressions and clicks
                        $hourlyImpressions = $this->distributeByHour($dailyImpressions);
                        $hourlyClicks = $this->distributeByHour($dailyClicks);

                        // Generate impression records
                        try {
                            foreach ($hourlyImpressions as $hour => $count) {
                                for ($i = 0; $i < $count; $i++) {
                                    $timestamp = $date->copy()->setHour($hour)->setMinute($faker->numberBetween(0, 59))->setSecond($faker->numberBetween(0, 59));

                                    $metadata = $this->shouldGenerateMetadata($faker) ? $this->generateMetadata($faker) : null;

                                    // Determine if this engagement should be associated with a user
                                    $userId = null;
                                    if ($userIdCount > 0 && $userAssocDecisions[$i % 100]) {
                                        $userId = $this->userIds[$faker->numberBetween(0, $userIdCount - 1)];
                                    }

                                    $engagementBatch[] = [
                                        'event_type' => Engagement::EVENT_IMPRESSION,
                                        'engageable_id' => $item['model']->id,
                                        'engageable_type' => get_class($item['model']),
                                        'user_id' => $userId,
                                        'ip_address' => $faker->ipv4,
                                        'user_agent' => $metadata ? $metadata['user_agent'] : null,
                                        'metadata' => $metadata ? json_encode($metadata) : null,
                                        'created_at' => $timestamp,
                                    ];

                                    $batchCount++;

                                    // Insert in batches for better performance - using reduced batch size
                                    if ($batchCount >= $optimizedBatchSize) {
                                        $this->insertBatchWithRetry($engagementBatch);
                                        $totalProcessed += $batchCount;

                                        // Only log every 10,000 records to reduce log verbosity
                                        if ($totalProcessed % 10000 === 0) {
                                            Log::info("Processed {$totalProcessed} engagement records");
                                        }

                                        $engagementBatch = [];
                                        $batchCount = 0;

                                        // Force garbage collection after each batch insert
                                        gc_collect_cycles();
                                    }
                                }
                            }
                        } catch (\Exception $e) {
                            Log::error("Error generating impression data for model {$item['model']->id}: " . $e->getMessage(), [
                                'exception' => $e,
                                'model_id' => $item['model']->id,
                                'date' => $date->toDateString()
                            ]);
                        }

                        // Generate click records
                        try {
                            foreach ($hourlyClicks as $hour => $count) {
                                for ($i = 0; $i < $count; $i++) {
                                    $timestamp = $date->copy()->setHour($hour)->setMinute($faker->numberBetween(0, 59))->setSecond($faker->numberBetween(0, 59));

                                    $metadata = $this->shouldGenerateMetadata($faker) ? $this->generateMetadata($faker) : null;

                                    // Clicks are more likely to be associated with users
                                    $userId = null;
                                    if ($userIdCount > 0 && $clickUserAssocDecisions[$i % 100]) {
                                        $userId = $this->userIds[$faker->numberBetween(0, $userIdCount - 1)];
                                    }

                                    $engagementBatch[] = [
                                        'event_type' => Engagement::EVENT_CLICK,
                                        'engageable_id' => $item['model']->id,
                                        'engageable_type' => get_class($item['model']),
                                        'user_id' => $userId,
                                        'ip_address' => $faker->ipv4,
                                        'user_agent' => $metadata ? $metadata['user_agent'] : null,
                                        'metadata' => $metadata ? json_encode($metadata) : null,
                                        'created_at' => $timestamp,
                                    ];

                                    $batchCount++;

                                    // Insert in batches for better performance
                                    if ($batchCount >= $optimizedBatchSize) {
                                        $this->insertBatchWithRetry($engagementBatch);
                                        $totalProcessed += $batchCount;

                                        // Only log every 10,000 records to reduce log verbosity
                                        if ($totalProcessed % 10000 === 0) {
                                            Log::info("Processed {$totalProcessed} engagement records");
                                        }

                                        $engagementBatch = [];
                                        $batchCount = 0;

                                        // Force garbage collection after each batch insert
                                        gc_collect_cycles();
                                    }
                                }
                            }
                        } catch (\Exception $e) {
                            Log::error("Error generating click data for model {$item['model']->id}: " . $e->getMessage(), [
                                'exception' => $e,
                                'model_id' => $item['model']->id,
                                'date' => $date->toDateString()
                            ]);
                        }
                    }
                } catch (\Exception $e) {
                    Log::error("Error processing model {$item['model']->id}: " . $e->getMessage(), [
                        'exception' => $e,
                        'model_id' => $item['model']->id
                    ]);
                }
            }

            // Insert any remaining records
            if (count($engagementBatch) > 0) {
                $this->insertBatchWithRetry($engagementBatch);
                $totalProcessed += count($engagementBatch);
                Log::info("Inserted final batch, total records processed: {$totalProcessed}");
                $engagementBatch = [];
            }

            // Clear variables to free memory
            $tieredCollection = null;
            $userAssocDecisions = null;
            $clickUserAssocDecisions = null;

            // Final garbage collection
            gc_collect_cycles();

            Log::info("Completed generating engagement data for {$this->type}, total records: {$totalProcessed}");
        } catch (\Exception $e) {
            Log::error("Error in generateEngagementData: " . $e->getMessage(), [
                'exception' => $e,
                'type' => $this->type
            ]);
            throw $e;
        }
    }

    /**
     * Insert a batch of records with retry logic
     */
    private function insertBatchWithRetry(array $batch, int $retries = 3): bool
    {
        // Reduce transaction size to prevent timeouts and memory spikes
        $chunkSize = 500; // Reduced from original batch size
        $success = true;

        try {
            // Split the batch into smaller chunks to reduce memory usage
            foreach (array_chunk($batch, $chunkSize) as $index => $chunk) {
                $attempt = 0;
                $inserted = false;

                while (!$inserted && $attempt < $retries) {
                    try {
                        DB::beginTransaction();
                        Engagement::insert($chunk);
                        DB::commit();
                        $inserted = true;
                    } catch (\Exception $e) {
                        DB::rollBack();
                        $attempt++;

                        if ($attempt >= $retries) {
                            Log::error("Failed to insert batch chunk after {$retries} attempts: " . $e->getMessage(), [
                                'exception' => $e,
                                'chunk_size' => count($chunk),
                                'chunk_index' => $index
                            ]);
                            $success = false;
                        } else {
                            // Only log the first retry attempt to reduce log spam
                            if ($attempt === 1) {
                                Log::warning("Retrying batch chunk insertion: " . $e->getMessage(), [
                                    'chunk_size' => count($chunk)
                                ]);
                            }
                            // Wait before retry (exponential backoff)
                            usleep(min(1000000, pow(2, $attempt) * 100000));
                        }
                    }
                }

                // Free memory after each chunk
                unset($chunk);
                gc_collect_cycles();
            }

            return $success;
        } catch (\Exception $e) {
            Log::error("Error in insertBatchWithRetry: " . $e->getMessage(), [
                'exception' => $e,
                'batch_size' => count($batch)
            ]);
            return false;
        }
    }

    /**
     * Assign engagement tiers to models
     */
    private function assignTiers($collection, $faker): array
    {
        $tieredCollection = [];

        foreach ($collection as $model) {
            // Determine tier based on distribution weights
            $tier = $this->getWeightedTier($faker);

            // Assign a specific CTR within the tier's range
            $ctr = $faker->randomFloat(2, self::TIERS[$tier]['ctr_min'], self::TIERS[$tier]['ctr_max']);

            $tieredCollection[] = [
                'model' => $model,
                'tier' => $tier,
                'ctr' => $ctr,
            ];
        }

        return $tieredCollection;
    }

    /**
     * Get a weighted tier based on distribution
     */
    private function getWeightedTier($faker): string
    {
        $rand = $faker->randomFloat(2, 0, 100);
        $cumulative = 0;

        foreach (self::TIERS as $tier => $config) {
            $cumulative += $config['distribution'];
            if ($rand <= $cumulative) {
                return $tier;
            }
        }

        return 'low'; // Default fallback
    }

    /**
     * Calculate impressions for a day based on tier and day of week multiplier
     */
    private function calculateDailyImpressions(string $tier, float $dayOfWeekMultiplier): int
    {
        $baseImpressions = $this->config['base_daily_impressions'] * $this->config['volume_multiplier'];
        $tierMultiplier = self::TIERS[$tier]['impression_multiplier'];

        return (int) round($baseImpressions * $tierMultiplier * $dayOfWeekMultiplier);
    }

    /**
     * Distribute a count across hours of the day based on hourly weights
     */
    private function distributeByHour(int $count): array
    {
        $distribution = [];
        $totalWeight = array_sum(self::HOUR_WEIGHTS);

        // First pass: calculate raw distribution
        foreach (self::HOUR_WEIGHTS as $hour => $weight) {
            $distribution[$hour] = (int) round(($weight / $totalWeight) * $count);
        }

        // Adjust to ensure we have exactly $count items
        $currentTotal = array_sum($distribution);
        $difference = $count - $currentTotal;

        if ($difference != 0) {
            // Get hours sorted by weight to prioritize adjustments
            $hoursByWeight = array_keys(self::HOUR_WEIGHTS);
            usort($hoursByWeight, function ($a, $b) {
                return self::HOUR_WEIGHTS[$b] <=> self::HOUR_WEIGHTS[$a];
            });

            // Add or subtract from hours starting with highest weight
            $i = 0;
            while ($difference != 0) {
                $hour = $hoursByWeight[$i % count($hoursByWeight)];
                if ($difference > 0) {
                    $distribution[$hour]++;
                    $difference--;
                } else if ($distribution[$hour] > 0) { // Don't go negative
                    $distribution[$hour]--;
                    $difference++;
                }
                $i++;
            }
        }

        return $distribution;
    }

    /**
     * Determine if we should generate metadata for this record
     */
    private function shouldGenerateMetadata($faker): bool
    {
        return $faker->randomFloat(2, 0, 100) <= $this->config['metadata_percentage'];
    }

    /**
     * Generate realistic metadata for an engagement
     */
    private function generateMetadata($faker): array
    {
        // Select browser, OS, and device type using weighted randomness
        $browser = $this->getWeightedRandom(self::BROWSERS, $faker);
        $os = $this->getWeightedRandom(self::OPERATING_SYSTEMS, $faker);
        $deviceType = $this->getWeightedRandom(self::DEVICE_TYPES, $faker);

        // Generate plausible user agent
        $userAgent = $this->generateUserAgent($browser, $os, $deviceType);

        // 40% chance of including referrer
        $hasReferrer = $faker->boolean(40);
        $referrer = $hasReferrer ? $this->generateReferrer($faker) : null;

        // 80% chance of including geo data
        $hasGeo = $faker->boolean(80);
        $geoData = $hasGeo ? [
            'country' => $faker->countryCode,
            'region' => $faker->state,
        ] : null;

        // Build metadata
        $metadata = [
            'user_agent' => $userAgent,
            'browser' => $browser,
            'os' => $os,
            'device_type' => $deviceType,
            'session_id' => $faker->uuid,
            'utm_source' => $faker->optional(0.3)->word,
        ];

        if ($hasReferrer) {
            $metadata['referrer'] = $referrer;
        }

        if ($hasGeo) {
            $metadata['geo'] = $geoData;
        }

        return $metadata;
    }

    /**
     * Get a weighted random value from an associative array
     */
    private function getWeightedRandom(array $weightedValues, $faker): string
    {
        $totalWeight = array_sum($weightedValues);
        $randomWeight = $faker->randomFloat(2, 0, $totalWeight);

        $currentWeight = 0;
        foreach ($weightedValues as $value => $weight) {
            $currentWeight += $weight;
            if ($randomWeight <= $currentWeight) {
                return $value;
            }
        }

        // Fallback (shouldn't happen but just in case)
        return array_key_first($weightedValues);
    }

    /**
     * Generate a plausible user agent string
     */
    private function generateUserAgent(string $browser, string $os, string $deviceType): string
    {
        // Very simplified for example purposes
        $version = rand(70, 120);

        return "Mozilla/5.0 ({$os}; {$deviceType}) {$browser}/{$version}.0";
    }

    /**
     * Generate a plausible referrer URL
     */
    private function generateReferrer($faker): string
    {
        $referrers = [
            'https://www.google.com/search?q=positive+athlete',
            'https://www.facebook.com/positiveathlete',
            'https://twitter.com/search?q=positive%20athlete',
            'https://www.instagram.com/positive_athlete/',
            'https://www.linkedin.com/search/results/all/?keywords=positive%20athlete',
            'https://www.bing.com/search?q=positive+athlete+scholarships',
            'https://www.yahoo.com/search?p=positive+athlete',
            'https://duckduckgo.com/?q=positive+athlete',
            'https://www.youtube.com/results?search_query=positive+athlete',
            'https://positiveathlete.org',
            'https://positiveathlete.org/programs',
            'https://positiveathlete.org/about',
            'https://positiveathlete.org/testimonials',
            'https://positiveathlete.org/contact',
        ];

        return $faker->randomElement($referrers);
    }

    /**
     * Format bytes to human-readable format
     */
    private function formatBytes($bytes, $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= (1 << (10 * $pow));

        return round($bytes, $precision) . ' ' . $units[$pow];
    }
}
