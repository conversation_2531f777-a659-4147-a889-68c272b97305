<?php

namespace App\Jobs;

use App\Models\User;
use App\Services\Import\NomineeImportService;
use App\Services\Nomination\NominationService;
use Filament\Notifications\Notification;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ImportNomineesJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The path to the CSV file.
     *
     * @var string
     */
    protected $filePath;

    /**
     * The ID of the user who initiated the import.
     *
     * @var int|null
     */
    protected $userId;

    /**
     * Create a new job instance.
     */
    public function __construct(string $filePath, ?int $userId = null)
    {
        $this->filePath = $filePath;
        $this->userId = $userId;
    }

    /**
     * Execute the job.
     */
    public function handle(NomineeImportService $importService, NominationService $nominationService): void
    {
        try {
            // Ensure we're working with a relative path for storage
            $relativePath = $this->filePath;

            // If it's already a full path, convert it to a relative path
            if (str_starts_with($relativePath, '/')) {
                $storagePath = Storage::disk('local')->path('');
                $relativePath = str_replace($storagePath, '', $relativePath);
            }

            Log::info('Starting nominee import job', [
                'original_file_path' => $this->filePath,
                'relative_path' => $relativePath,
                'exists' => Storage::disk('local')->exists($relativePath),
                'full_path' => Storage::disk('local')->path($relativePath),
                'user_id' => $this->userId
            ]);

            // Process the CSV file
            $result = $importService->processCsvFile($relativePath);

            if (!$result['success']) {
                Log::error('Nominee import failed', [
                    'file_path' => $this->filePath,
                    'errors' => $result['errors'],
                ]);

                // Send notification to the user who initiated the import
                if ($this->userId) {
                    $user = User::find($this->userId);

                    if ($user) {
                        Notification::make()
                            ->title('Nominee Import Failed')
                            ->body('An error occurred during the import process: ' . implode(', ', $result['errors']))
                            ->danger()
                            ->persistent()
                            ->sendToDatabase($user);
                    }
                }

                return;
            }

            // Log the results
            Log::info('Nominee import completed', [
                'file_path' => $this->filePath,
                'results' => $result['results'],
            ]);

            // Send notification to the user who initiated the import
            if ($this->userId) {
                $user = User::find($this->userId);

                if ($user) {
                    $results = $result['results'];
                    Notification::make()
                        ->title('Nominee Import Completed')
                        ->body("Processed {$results['total']} nominees: {$results['created']} contacts created, {$results['updated']} contacts updated, {$results['nominations_created']} nominations created, {$results['failed']} failed.")
                        ->success()
                        ->persistent()
                        ->sendToDatabase($user);
                }
            }
        } catch (\Exception $e) {
            Log::error('Error in nominee import job', [
                'file_path' => $this->filePath,
                'error' => $e->getMessage(),
            ]);

            // Send notification to the user who initiated the import
            if ($this->userId) {
                $user = User::find($this->userId);

                if ($user) {
                    Notification::make()
                        ->title('Nominee Import Failed')
                        ->body('An error occurred during the import process: ' . $e->getMessage())
                        ->danger()
                        ->persistent()
                        ->sendToDatabase($user);
                }
            }

            // Re-throw the exception to mark the job as failed
            throw $e;
        }
    }
}
