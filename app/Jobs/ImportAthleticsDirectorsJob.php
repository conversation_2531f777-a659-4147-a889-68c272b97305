<?php

namespace App\Jobs;

use App\Models\User;
use App\Services\Import\AthleticsDirectorImportService;
use App\Traits\SanitizesImportData;
use Filament\Notifications\Notification;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ImportAthleticsDirectorsJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels, SanitizesImportData;

    /**
     * The path to the CSV file.
     *
     * @var string
     */
    protected $filePath;

    /**
     * The ID of the user who initiated the import.
     *
     * @var int|null
     */
    protected $userId;

    /**
     * Create a new job instance.
     */
    public function __construct(string $filePath, ?int $userId = null)
    {
        $this->filePath = $filePath;
        $this->userId = $userId;
    }

    /**
     * Execute the job.
     */
    public function handle(AthleticsDirectorImportService $importService): void
    {
        try {
            // Ensure we're working with a relative path for storage
            $relativePath = $this->filePath;

            // If it's already a full path, convert it to a relative path
            if (str_starts_with($relativePath, '/')) {
                $storagePath = Storage::disk('local')->path('');
                $relativePath = str_replace($storagePath, '', $relativePath);
            }

            Log::info('Starting Athletics Directors import job', [
                'original_file_path' => $this->sanitizeForLogging($this->filePath),
                'relative_path' => $this->sanitizeForLogging($relativePath),
                'exists' => Storage::disk('local')->exists($relativePath),
                'full_path' => $this->sanitizeForLogging(Storage::disk('local')->path($relativePath)),
                'user_id' => $this->userId
            ]);

            // Process the CSV file
            $result = $importService->processCsvFile($relativePath);

            if (!$result['success']) {
                Log::error('Athletics Directors import failed', [
                    'file_path' => $this->sanitizeForLogging($this->filePath),
                    'errors' => array_map([$this, 'sanitizeForLogging'], $result['errors']),
                ]);

                // Send notification to the user who initiated the import
                if ($this->userId) {
                    $user = User::find($this->userId);

                    if ($user) {
                        Notification::make()
                            ->title('Athletics Directors Import Failed')
                            ->body('An error occurred during the import process: ' . implode(', ', $result['errors']))
                            ->danger()
                            ->persistent()
                            ->sendToDatabase($user);
                    }
                }

                return;
            }

            // Log the results
            Log::info('Athletics Directors import completed', [
                'file_path' => $this->sanitizeForLogging($this->filePath),
                'results' => $this->sanitizeResultsForLogging($result['results']),
            ]);

            // Send notification to the user who initiated the import
            if ($this->userId) {
                $user = User::find($this->userId);

                if ($user) {
                    $results = $result['results'];
                    $message = "Processed {$results['total']} athletics directors: {$results['created']} created, {$results['updated']} updated";

                    if ($results['schools_created'] > 0 || $results['schools_matched'] > 0) {
                        $message .= ", {$results['schools_created']} schools created, {$results['schools_matched']} schools matched";
                    }

                    $message .= ", {$results['failed']} failed.";

                    Notification::make()
                        ->title('Athletics Directors Import Completed')
                        ->body($message)
                        ->success()
                        ->persistent()
                        ->sendToDatabase($user);
                }
            }
        } catch (\Exception $e) {
            Log::error('Error in Athletics Directors import job', [
                'file_path' => $this->sanitizeForLogging($this->filePath),
                'error' => $this->sanitizeForLogging($e->getMessage()),
            ]);

            // Send notification to the user who initiated the import
            if ($this->userId) {
                $user = User::find($this->userId);

                if ($user) {
                    Notification::make()
                        ->title('Athletics Directors Import Failed')
                        ->body('An error occurred during the import process: ' . $e->getMessage())
                        ->danger()
                        ->persistent()
                        ->sendToDatabase($user);
                }
            }

            // Re-throw the exception to mark the job as failed
            throw $e;
        }
    }
}
