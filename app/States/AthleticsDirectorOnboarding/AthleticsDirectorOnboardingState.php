<?php

namespace App\States\AthleticsDirectorOnboarding;

use App\States\Onboarding\States\AccountInfo;
use App\States\Onboarding\States\Bio;
use App\States\Onboarding\States\SchoolSuccesses;
use App\States\Onboarding\States\CommunityInvolvement;
use App\States\Onboarding\States\Completed;
use App\States\Onboarding\States\Intro;
use Spatie\ModelStates\State;
use Spatie\ModelStates\StateConfig;

abstract class AthleticsDirectorOnboardingState extends State
{
    abstract public static function identifier(): string;

    public static function config(): StateConfig
    {
        return parent::config()
            ->default(Intro::class)
            ->allowAllTransitions()
            ->registerState(Intro::class)
            ->registerState(AccountInfo::class)
            ->registerState(Bio::class)
            ->registerState(SchoolSuccesses::class)
            ->registerState(CommunityInvolvement::class)
            ->registerState(Completed::class);
    }
}
