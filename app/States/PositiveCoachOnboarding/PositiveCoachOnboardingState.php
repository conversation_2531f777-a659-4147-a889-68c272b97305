<?php

namespace App\States\PositiveCoachOnboarding;

use App\States\Onboarding\States\AccountInfo;
use App\States\Onboarding\States\Completed;
use App\States\Onboarding\States\CommunityInvolvement;
use App\States\Onboarding\States\Details;
use App\States\Onboarding\States\Intro;
use App\States\Onboarding\States\Sports;
use App\States\Onboarding\States\Story;
use App\States\Onboarding\States\TeamSuccesses;
use Spatie\ModelStates\State;
use Spatie\ModelStates\StateConfig;

abstract class PositiveCoachOnboardingState extends State
{
    abstract public static function identifier(): string;

    public static function config(): StateConfig
    {
        return parent::config()
            ->default(Intro::class)
            ->allowAllTransitions()
            ->registerState(Intro::class)
            ->registerState(AccountInfo::class)
            ->registerState(Details::class)
            ->registerState(Sports::class)
            ->registerState(CommunityInvolvement::class)
            ->registerState(TeamSuccesses::class)
            ->registerState(Story::class)
            ->registerState(Completed::class);
    }
}
