<?php

namespace App\States\ProfessionalOnboarding;

use App\States\Onboarding\States\AccountInfo;
use App\States\Onboarding\States\Completed;
use App\States\Onboarding\States\Intro;
use App\States\Onboarding\States\Details;
use Spatie\ModelStates\State;
use Spatie\ModelStates\StateConfig;

abstract class ProfessionalOnboardingState extends State
{
    abstract public static function identifier(): string;

    public static function config(): StateConfig
    {
        return parent::config()
            ->default(Intro::class)
            ->allowAllTransitions()
            ->registerState(Intro::class)
            ->registerState(AccountInfo::class)
            ->registerState(Details::class)
            ->registerState(Completed::class);
    }
}
