<?php

namespace App\States\Onboarding;

use App\States\Onboarding\States\WorkExperience;
use App\States\Onboarding\States\Intro;
use App\States\Onboarding\States\AccountInfo;
use App\States\Onboarding\States\Details;
use App\States\Onboarding\States\Sports;
use App\States\Onboarding\States\Story;
use App\States\Onboarding\States\Completed;
use App\States\Onboarding\States\OrganizationInfo;
use App\States\Onboarding\States\School;
use Spatie\ModelStates\State;
use Spatie\ModelStates\StateConfig;

abstract class OnboardingState extends State
{
    abstract public static function identifier(): string;

    public function getIdentifier(): string
    {
        return static::identifier();
    }

    public static function config(): StateConfig
    {
        return parent::config()
            ->default(Intro::class)
            ->allowAllTransitions()
            ->registerState(Intro::class)
            ->registerState(AccountInfo::class)
            ->registerState(Details::class)
            ->registerState(Sports::class)
            ->registerState(School::class)
            ->registerState(OrganizationInfo::class)
            ->registerState(WorkExperience::class)
            ->registerState(Story::class)
            ->registerState(Completed::class);
    }
}
