<?php

namespace App\States\CollegeStudentOnboarding;

use App\States\Onboarding\States\AccountInfo;
use App\States\Onboarding\States\CollegeInfo;
use App\States\Onboarding\States\Completed;
use App\States\Onboarding\States\Intro;
use App\States\Onboarding\States\NextSteps;
use Spatie\ModelStates\State;
use Spatie\ModelStates\StateConfig;

abstract class CollegeStudentOnboardingState extends State
{
    abstract public static function identifier(): string;

    public static function config(): StateConfig
    {
        return parent::config()
            ->default(Intro::class)
            ->allowAllTransitions()
            ->registerState(Intro::class)
            ->registerState(NextSteps::class)
            ->registerState(AccountInfo::class)
            ->registerState(CollegeInfo::class)
            ->registerState(Completed::class);
    }
}
