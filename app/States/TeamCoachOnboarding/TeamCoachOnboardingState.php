<?php

namespace App\States\TeamCoachOnboarding;

use App\States\Onboarding\States\AccountInfo;
use App\States\Onboarding\States\BuildTeam;
use App\States\Onboarding\States\Completed;
use App\States\Onboarding\States\Details;
use App\States\Onboarding\States\Intro;
use App\States\Onboarding\States\Sports;
use App\States\Onboarding\States\TeamReview;
use Spatie\ModelStates\State;
use Spatie\ModelStates\StateConfig;

abstract class TeamCoachOnboardingState extends State
{
    abstract public static function identifier(): string;

    public static function config(): StateConfig
    {
        return parent::config()
            ->default(Intro::class)
            ->allowAllTransitions()
            ->registerState(Intro::class)
            ->registerState(AccountInfo::class)
            ->registerState(Details::class)
            ->registerState(Sports::class)
            ->registerState(BuildTeam::class)
            ->registerState(TeamReview::class)
            ->registerState(Completed::class);
    }
}
