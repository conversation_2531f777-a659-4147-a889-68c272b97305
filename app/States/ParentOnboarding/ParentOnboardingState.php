<?php

namespace App\States\ParentOnboarding;

use App\States\Onboarding\States\AccountInfo;
use App\States\Onboarding\States\Completed;
use App\States\Onboarding\States\Intro;
use Spatie\ModelStates\State;
use Spatie\ModelStates\StateConfig;

abstract class ParentOnboardingState extends State
{
    abstract public static function identifier(): string;

    public static function config(): StateConfig
    {
        return parent::config()
            ->default(Intro::class)
            ->allowAllTransitions()
            ->registerState(Intro::class)
            ->registerState(AccountInfo::class)
            ->registerState(Completed::class);
    }
}
