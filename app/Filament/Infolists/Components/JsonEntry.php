<?php

namespace App\Filament\Infolists\Components;

use Filament\Infolists\Components\Entry;

class JsonEntry extends Entry
{
    protected string $view = 'filament.infolists.components.json-entry';

    public function getState(): mixed
    {
        $state = parent::getState();

        if (empty($state)) {
            return null;
        }

        // If it's already a string, assume it's already JSON formatted
        if (is_string($state)) {
            return $state;
        }

        // Otherwise, encode it with pretty print
        return json_encode($state, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }
}
