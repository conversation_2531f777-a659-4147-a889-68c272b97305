<?php

namespace App\Filament\Admin\Pages\XFactor;

use Filament\Pages\Page;

class Categories extends Page
{
    protected static ?string $navigationLabel = 'X Factor Categories';
    protected static ?string $title = 'X Factor Categories';
    protected static ?string $slug = 'x-factor/categories';
    protected static bool $shouldRegisterNavigation = false;
    protected static string $view = 'filament.admin.pages.x-factor.categories';
    protected static string $layout = 'filament.admin.layouts.x-factor';

}
