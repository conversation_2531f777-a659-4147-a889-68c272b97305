<?php

namespace App\Filament\Admin\Pages\XFactor;

use App\Models\Course;
use App\Models\Module;
use Filament\Pages\Page;

class CourseModuleEdit extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static string $view = 'filament.admin.pages.x-factor.course-module-edit';
    protected static bool $shouldRegisterNavigation = false;
    protected static ?string $slug = 'x-factor/courses/{course}/modules/{module}/edit';
    protected static ?string $routeName = 'filament.admin.pages.x-factor.courses.{course}.modules.{module}.edit';
    protected static string $layout = 'filament.admin.layouts.x-factor';

    public Course $course;
    public Module $module;

    public function mount(Course $course, Module $module): void
    {
        $this->course = $course;
        $this->module = $module;
    }

    public function getTitle(): string
    {
        return "Edit {$this->module->name} in {$this->course->title}";
    }

    public function getLayoutBreadcrumbs(): array
    {
        return [
            route('filament.admin.pages.x-factor.courses') => 'Courses',
            route('filament.admin.pages.x-factor.courses.{course}', ['course' => $this->course]) => $this->course->title,
            null => "Edit {$this->module->name}",
        ];
    }
}
