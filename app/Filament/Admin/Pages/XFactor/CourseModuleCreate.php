<?php

namespace App\Filament\Admin\Pages\XFactor;

use App\Models\Course;
use Filament\Pages\Page;

class CourseModuleCreate extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static string $view = 'filament.admin.pages.x-factor.course-module-create';
    protected static bool $shouldRegisterNavigation = false;
    protected static ?string $slug = 'x-factor/courses/{course}/modules/create';
    protected static ?string $routeName = 'filament.admin.pages.x-factor.courses.{course}.modules.create';
    protected static string $layout = 'filament.admin.layouts.x-factor';

    public Course $course;

    public function mount(Course $course): void
    {
        $this->course = $course;
    }

    public function getTitle(): string
    {
        return "New Module for Course: {$this->course->title}";
    }

    public function getLayoutBreadcrumbs(): array
    {
        return [
            route('filament.admin.pages.x-factor.courses') => 'Courses',
            route('filament.admin.pages.x-factor.courses.{course}', ['course' => $this->course]) => $this->course->title,
            null => 'New Module',
        ];
    }
}
