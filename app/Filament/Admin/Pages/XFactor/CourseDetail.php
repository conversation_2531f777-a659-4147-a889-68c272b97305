<?php

namespace App\Filament\Admin\Pages\XFactor;

use Filament\Pages\Page;
use App\Models\Course;

class CourseDetail extends Page
{
    protected static ?string $navigationLabel = 'Course Detail';
    protected static ?string $title = 'Course Detail';
    protected static ?string $slug = 'x-factor/courses/{course}';
    protected static ?string $routeName = 'filament.admin.pages.x-factor.courses.{course}';
    protected static bool $shouldRegisterNavigation = false;
    protected static string $layout = 'filament.admin.layouts.x-factor';
    protected static string $view = 'filament.admin.pages.x-factor.course-detail';

    public Course $course;

    public function mount(Course $course): void
    {
        $this->course = $course;
    }

    public function getTitle(): string
    {
        return $this->course->title;
    }

    public function getLayoutBreadcrumbs(): array
    {
        return [
            route('filament.admin.pages.x-factor.courses') => 'Courses',
            null => $this->course->title,
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('new_module')
                ->label('New Module in Course')
                ->url(fn () => route('filament.admin.pages.x-factor.courses.{course}.modules.create', ['course' => $this->course->id]))
                ->color('primary')
                ->icon('heroicon-m-plus'),
        ];
    }
}
