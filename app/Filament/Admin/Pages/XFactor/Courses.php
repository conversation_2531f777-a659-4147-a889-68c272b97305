<?php

namespace App\Filament\Admin\Pages\XFactor;

use Filament\Pages\Page;

class Courses extends Page
{
    protected static ?string $navigationLabel = 'X Factor Courses';
    protected static ?string $title = 'X Factor Courses';
    protected static ?string $slug = 'x-factor/courses';
    protected static bool $shouldRegisterNavigation = false;
    protected static string $layout = 'filament.admin.layouts.x-factor';
    protected static string $view = 'filament.admin.pages.x-factor.courses';
}
