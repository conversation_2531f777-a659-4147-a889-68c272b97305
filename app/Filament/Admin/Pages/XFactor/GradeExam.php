<?php

namespace App\Filament\Admin\Pages\XFactor;

use Filament\Pages\Page;
use App\Models\TestAttempt;
use App\Models\Course;

class GradeExam extends Page
{
    protected static ?string $navigationLabel = 'Grade Exam';
    protected static ?string $title = null;
    protected static ?string $slug = 'x-factor/courses/{course}/grade-exam/{testAttempt}';
    protected static ?string $routeName = 'filament.admin.pages.x-factor.courses.{course}.grade-exam.{testAttempt}';
    protected static bool $shouldRegisterNavigation = false;
    protected static string $layout = 'filament.admin.layouts.x-factor';
    protected static string $view = 'filament.admin.pages.x-factor.grade-exam';

    public Course $course;
    public TestAttempt $testAttempt;

    public function mount(Course $course, TestAttempt $testAttempt): void
    {
        $this->course = $course;
        $this->testAttempt = $testAttempt;
    }

    public function getTitle(): string
    {
        return '';
    }

    public function getLayoutBreadcrumbs(): array
    {
        return [
            route('filament.admin.pages.x-factor.courses') => 'Courses',
            route('filament.admin.pages.x-factor.courses.{course}', ['course' => $this->course]) => $this->course->title,
            null => 'Grade Exam',
        ];
    }
}
