<?php

namespace App\Filament\Admin\Pages\Scholarships;

use App\Models\Scholarship;
use Filament\Pages\Page;

class ScholarshipEdit extends Page
{
    protected static ?string $title = 'Edit Scholarship';
    protected static ?string $slug = 'scholarships/{scholarship}/edit';
    protected static string $view = 'filament.admin.pages.scholarships.edit';
    protected static bool $shouldRegisterNavigation = false;

    public ?Scholarship $scholarship = null;

    public function mount(Scholarship $scholarship): void
    {
        $this->scholarship = $scholarship;
    }

    protected function authorizeAccess(): bool
    {
        return true;
    }

    public function getTitle(): string
    {
        return '';
    }

    public function getLayoutBreadcrumbs(): array
    {
        return [
            route('filament.admin.pages.scholarships') => 'Scholarships',
            '' => $this->scholarship->name,
        ];
    }
}
