<?php

namespace App\Filament\Admin\Pages\Scholarships;

use App\Models\Scholarship;
use Filament\Pages\Page;

class ScholarshipIndex extends Page
{
    protected static ?string $title = 'Scholarships';
    protected static ?string $slug = 'scholarships';
    protected static string $view = 'filament.admin.pages.scholarships.index';
    protected static ?string $navigationIcon = 'heroicon-o-academic-cap';

    protected static string $model = Scholarship::class;

    protected function authorizeAccess(): bool
    {
        return true;
    }
}
