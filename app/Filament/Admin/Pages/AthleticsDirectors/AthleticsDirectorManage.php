<?php

namespace App\Filament\Admin\Pages\AthleticsDirectors;

use Filament\Pages\Page;
use App\Models\User;

class AthleticsDirectorManage extends Page
{
    protected static ?string $navigationLabel = 'Manage Athletics Director';
    protected static ?string $slug = 'athletics-directors/{director}';
    protected static bool $shouldRegisterNavigation = false;
    protected static string $layout = 'filament.admin.layouts.users';
    protected static string $view = 'filament.admin.pages.athletics-directors.manage';

    public User $director;

    public function mount(User $director): void
    {
        $this->director = $director;
    }

    public function getLayoutBreadcrumbs(): array
    {
        return [
            route('filament.admin.pages.athletics-directors') => 'Athletics Directors',
            null => "{$this->director->first_name} {$this->director->last_name}",
        ];
    }

    public function getTitle(): string
    {
        return "";
    }
}
