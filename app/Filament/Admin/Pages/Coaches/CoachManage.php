<?php

namespace App\Filament\Admin\Pages\Coaches;

use Filament\Pages\Page;
use App\Models\User;

class CoachManage extends Page
{
    protected static ?string $navigationLabel = 'Manage Coach';
    protected static ?string $slug = 'coaches/{coach}';
    protected static bool $shouldRegisterNavigation = false;
    protected static string $layout = 'filament.admin.layouts.users';
    protected static string $view = 'filament.admin.pages.coaches.manage';

    public User $coach;

    public function mount(User $coach): void
    {
        $this->coach = $coach;
    }

    public function getLayoutBreadcrumbs(): array
    {
        return [
            route('filament.admin.pages.coaches') => 'Positive Coaches',
            null => $this->coach->full_name,
        ];
    }

    public function getTitle(): string
    {
        return "";
    }
}
