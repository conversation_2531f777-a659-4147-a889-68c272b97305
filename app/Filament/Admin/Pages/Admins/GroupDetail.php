<?php

namespace App\Filament\Admin\Pages\Admins;

use App\Models\Group;
use App\Models\User;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Illuminate\Database\Eloquent\Model;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;

class GroupDetail extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $slug = 'admins/user-groups/{group}';

    protected static string $view = 'filament.admin.pages.admins.group-detail';

    protected static bool $shouldRegisterNavigation = false;

    public ?array $data = [];

    public ?Model $record = null;

    public function mount(Group $group): void
    {
        $group->load('users');
        $this->record = $group;
        $this->form->fill([
            'name' => $group->name,
            'users' => $group->users->pluck('id')->toArray(),
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->model($this->record)
            ->schema([
                TextInput::make('name')
                    ->required()
                    ->label('Group Name'),
                Select::make('users')
                    ->multiple()
                    ->options(
                        User::query()
                            ->whereHas('roles', fn($q) => $q->where('name', 'admin'))
                            ->pluck('email', 'id')
                    )
                    ->searchable()
                    ->preload()
                    ->label('Group Members'),
            ])
            ->statePath('data');
    }

    public function save(): void
    {
        $data = $this->form->getState();

        $this->record->update([
            'name' => $data['name'],
        ]);

        $this->record->users()->sync($data['users'] ?? []);

        Notification::make()
            ->success()
            ->title('Group updated successfully')
            ->send();
    }

    public function getTitle(): string
    {
        return $this->record ? "Edit Group: {$this->record->name}" : 'Edit Group';
    }

    public function getBreadcrumbs(): array
    {
        return [
            Admins::getUrl() => 'Admins',
            '#' => $this->getTitle(),
        ];
    }
}
