<?php

namespace App\Filament\Admin\Pages\Admins;

use App\Models\Group;
use App\Models\User;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Livewire\Component;
use Filament\Tables\Concerns\InteractsWithTable;

class UserGroups extends Component
{
    use InteractsWithTable;

    protected static string $view = 'filament.admin.pages.admins.user-groups';

    public function table(Table $table): Table
    {
        return $table
            ->query(Group::query())
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('users_count')
                    ->counts('users')
                    ->label('Members'),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->actions([
                ViewAction::make()
                    ->url(fn (Group $record): string => GroupDetail::getUrl(['group' => $record])),
            ])
            ->headerActions([
                Action::make('create')
                    ->label('New Group')
                    ->form([
                        TextInput::make('name')
                            ->required(),
                        Select::make('users')
                            ->multiple()
                            ->options(User::query()
                                ->role('admin')
                                ->pluck('email', 'id'))
                            ->searchable()
                            ->preload(),
                    ])
                    ->action(function (array $data): void {
                        $group = Group::create([
                            'name' => $data['name'],
                            'team_id' => 1, // TODO: Get from context
                        ]);

                        if (isset($data['users'])) {
                            $group->users()->attach($data['users']);
                        }
                    }),
            ]);
    }
}
