<?php

namespace App\Filament\Admin\Pages\Admins;

use App\Models\User;
use Filament\Pages\Page;
use Livewire\Attributes\Computed;

class Admins extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationLabel = 'Admins';

    protected static ?string $title = 'Admins';

    protected static string $view = 'filament.admin.pages.admins';

    protected static ?int $navigationSort = 2;

    protected static ?string $slug = 'admins';

    public string $activeView = 'users';

    public function setActiveView(string $view): void
    {
        $this->activeView = $view;
    }

    #[Computed]
    public function adminCount(): int
    {
        return User::query()
            ->whereHas('roles', fn($q) => $q->where('name', 'admin'))
            ->count();
    }

    #[Computed]
    public function isUsersActive(): bool
    {
        return $this->activeView === 'users';
    }

    #[Computed]
    public function isUserGroupsActive(): bool
    {
        return $this->activeView === 'user-groups';
    }
}
