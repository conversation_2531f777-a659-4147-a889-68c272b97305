<?php

namespace App\Filament\Admin\Pages\Admins;

use App\Models\User;
use App\Models\Group;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Illuminate\Database\Eloquent\Model;
use Filament\Notifications\Notification;

class UserDetail extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $slug = 'admins/users/{user}';

    protected static string $view = 'filament.admin.pages.admins.user-detail';

    protected static bool $shouldRegisterNavigation = false;

    public ?array $data = [];

    public ?Model $record = null;

    public function mount(User $user): void
    {
        $user->load('groups');

        $this->record = $user;

        $groupIds = $user->groups->pluck('id')->toArray();

        $this->form->fill([
            'groups' => $groupIds,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->model($this->record)
            ->schema([
                Section::make('User Groups')
                    ->schema([
                        Select::make('groups')
                            ->label('')
                            ->multiple()
                            ->options(
                                Group::query()->pluck('name', 'id')
                            )
                            ->preload()
                            ->searchable()
                    ])
            ])
            ->statePath('data');
    }

    public function save(): void
    {
        $data = $this->form->getState();

        // Make sure we're getting an array, even if empty
        $groupIds = $data['groups'] ?? [];

        // Sync the groups
        $this->record->groups()->sync($groupIds);

        Notification::make()
            ->success()
            ->title('User groups updated successfully')
            ->send();
    }

    public function getTitle(): string
    {
        return $this->record ? "{$this->record->first_name} {$this->record->last_name}" : 'Edit User';
    }

    public function getBreadcrumbs(): array
    {
        return [
            Admins::getUrl() => 'Admins',
            '#' => $this->getTitle(),
        ];
    }
}
