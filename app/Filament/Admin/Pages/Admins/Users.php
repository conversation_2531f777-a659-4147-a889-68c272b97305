<?php

namespace App\Filament\Admin\Pages\Admins;

use App\Models\User;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Livewire\Component;
use Filament\Forms\Components\Select;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Filament\Tables\Concerns\InteractsWithTable;

class Users extends Component
{
    use InteractsWithTable;

    protected static string $view = 'filament.admin.pages.admins.users';

    public function table(Table $table): Table
    {
        return $table
            ->query(User::query()->role('admin'))
            ->columns([
                TextColumn::make('first_name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('last_name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('email')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->actions([
                ViewAction::make()
                    ->url(fn (User $record): string => UserDetail::getUrl(['user' => $record])),
            ])
            ->headerActions([
                Action::make('invite')
                    ->label('Invite Admin')
                    ->form([
                        TextInput::make('first_name')
                            ->required(),
                        TextInput::make('last_name')
                            ->required(),
                        TextInput::make('email')
                            ->email()
                            ->required()
                            ->unique('users', 'email'),
                        TextInput::make('password')
                            ->password()
                            ->required()
                            ->minLength(8),
                    ])
                    ->action(function (array $data): void {
                        $user = User::create([
                            'first_name' => $data['first_name'],
                            'last_name' => $data['last_name'],
                            'email' => $data['email'],
                            'password' => Hash::make($data['password']),
                            'profile_type' => 'admin',
                        ]);

                        $user->assignRole('admin');
                    }),
            ]);
    }
}
