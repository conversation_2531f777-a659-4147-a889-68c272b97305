<?php

namespace App\Filament\Admin\Pages;

use Filament\Pages\Page;
use Livewire\Attributes\Computed;

class XFactor extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationLabel = 'X Factor';

    protected static ?string $title = 'X Factor';

    protected static string $view = 'filament.admin.pages.x-factor';

    protected static ?int $navigationSort = 1;

    public string $activeView = 'modules';

    public function setActiveView(string $view): void
    {
        $this->activeView = $view;
    }

    #[Computed]
    public function isModulesActive(): bool
    {
        return $this->activeView === 'modules';
    }

    #[Computed]
    public function isCategoriesActive(): bool
    {
        return $this->activeView === 'categories';
    }

    #[Computed]
    public function isCoursesActive(): bool
    {
        return $this->activeView === 'courses';
    }
}
