<?php

namespace App\Filament\Admin\Pages\Contacts;

use App\Models\Contact;
use Filament\Pages\Page;

class EditContact extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $slug = 'contacts/{contact}/edit';
    protected static ?string $title = 'Edit Contact';
    protected static bool $shouldRegisterNavigation = false;

    public Contact $record;

    public function mount(Contact $contact): void
    {
        $this->record = $contact;
        static::$title = "Edit Contact: {$contact->first_name} {$contact->last_name}";
    }

    protected static string $view = 'filament.admin.pages.contacts.edit-contact';

    protected function getHeaderActions(): array
    {
        return [];
    }
}
