<?php

namespace App\Filament\Admin\Pages\Opportunities;

use App\Models\Opportunity;
use Filament\Pages\Page;

class ViewOpportunity extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $slug = 'opportunities/{opportunity}';
    protected static ?string $title = 'View Opportunity';
    protected static bool $shouldRegisterNavigation = false;

    public Opportunity $opportunity;

    public function mount(Opportunity $opportunity): void
    {
        $this->opportunity = $opportunity;
    }

    public function getTitle(): string
    {
        return '';
    }

    public function getLayoutBreadcrumbs(): array
    {
        return [
            route('filament.admin.pages.sponsors') => 'Recruiters/Sponsors',
            route('filament.admin.pages.sponsors.{organization}.view', ['organization' => $this->opportunity->organization]) => $this->opportunity->organization->name,
            route('filament.admin.pages.opportunities.{opportunity}', ['opportunity' => $this->opportunity]) => $this->opportunity->title,
        ];
    }

    protected static string $view = 'filament.admin.pages.opportunities.view-opportunity';
}
