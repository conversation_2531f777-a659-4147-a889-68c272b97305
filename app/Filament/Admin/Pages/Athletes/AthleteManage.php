<?php

namespace App\Filament\Admin\Pages\Athletes;

use Filament\Pages\Page;
use App\Models\User;

class AthleteManage extends Page
{
    protected static ?string $navigationLabel = 'Manage Athlete';
    protected static ?string $slug = 'athletes/{athlete}';
    protected static bool $shouldRegisterNavigation = false;
    protected static string $layout = 'filament.admin.layouts.users';
    protected static string $view = 'filament.admin.pages.athletes.manage';

    public User $athlete;

    public function mount(User $athlete): void
    {
        $this->athlete = $athlete;
    }

    public function getLayoutBreadcrumbs(): array
    {
        return [
            route('filament.admin.pages.athletes') => 'Positive Athletes',
            null => $this->athlete->full_name,
        ];
    }

    public function getTitle(): string
    {
        return "";
    }
}
