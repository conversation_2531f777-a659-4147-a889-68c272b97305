<?php

namespace App\Filament\Admin\Pages\Athletes;

use Filament\Pages\Page;

class AthleteIndex extends Page
{
    protected static ?string $title = 'Positive Athletes';
    protected static ?string $slug = 'athletes';
    protected static string $view = 'filament.admin.pages.athletes.index';
    protected static bool $shouldRegisterNavigation = false;

    public function getViewData(): array
    {
        return [
            'userType' => 'positive_athlete',
            'manageRoute' => 'filament.admin.pages.athletes'
        ];
    }
}
