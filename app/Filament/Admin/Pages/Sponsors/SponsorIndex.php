<?php

namespace App\Filament\Admin\Pages\Sponsors;

use Filament\Pages\Page;
use App\Enums\ProfileType;

class SponsorIndex extends Page
{
    protected static ?string $title = 'Recruiters/Sponsors';
    protected static ?string $slug = 'sponsors';
    protected static string $view = 'filament.admin.pages.sponsors.index';
    protected static bool $shouldRegisterNavigation = false;

    public function getViewData(): array
    {
        return [
            'userType' => ProfileType::SPONSOR->value,
            'manageRoute' => 'filament.admin.pages.sponsors'
        ];
    }
}
