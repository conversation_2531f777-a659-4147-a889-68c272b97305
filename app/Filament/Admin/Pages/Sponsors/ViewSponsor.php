<?php

namespace App\Filament\Admin\Pages\Sponsors;

use App\Models\Organization;
use Filament\Pages\Page;

class ViewSponsor extends Page
{
    protected static ?string $title = 'View Recruiter/Sponsor';
    protected static ?string $slug = 'sponsors/{organization}/view';
    protected static string $view = 'filament.admin.pages.sponsors.view';
    protected static bool $shouldRegisterNavigation = false;

    public Organization $organization;

    public function mount(Organization $organization): void
    {
        $this->organization = $organization;
    }

    public function getTitle(): string
    {
        return '';
    }

    public function getLayoutBreadcrumbs(): array
    {
        return [
            route('filament.admin.pages.sponsors') => 'Recruiters/Sponsors',
            null => $this->organization->name,
        ];
    }
}
