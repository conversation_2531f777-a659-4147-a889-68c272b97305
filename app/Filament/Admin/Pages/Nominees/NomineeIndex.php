<?php

namespace App\Filament\Admin\Pages\Nominees;

use Filament\Pages\Page;

class NomineeIndex extends Page
{
    protected static ?string $title = 'Nominees';
    protected static ?string $slug = 'nominees';
    protected static string $view = 'filament.admin.pages.nominees.index';
    protected static bool $shouldRegisterNavigation = false;

    public function getViewData(): array
    {
        return [
            'manageRoute' => 'filament.admin.pages.nominees'
        ];
    }
}
