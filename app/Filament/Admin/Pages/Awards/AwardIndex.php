<?php

namespace App\Filament\Admin\Pages\Awards;

use App\Models\Award;
use Filament\Pages\Page;

class AwardIndex extends Page
{
    protected static ?string $title = 'Awards';
    protected static ?string $slug = 'awards';
    protected static string $view = 'filament.admin.pages.awards.index';
    protected static ?string $navigationIcon = 'heroicon-o-trophy';

    protected static string $model = Award::class;

    protected function authorizeAccess(): bool
    {
        return true;
    }
}
