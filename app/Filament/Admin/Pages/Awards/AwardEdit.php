<?php

namespace App\Filament\Admin\Pages\Awards;

use App\Models\Award;
use Filament\Pages\Page;

class AwardEdit extends Page
{
    protected static ?string $title = 'Edit Award';
    protected static ?string $slug = 'awards/{award}/edit';
    protected static string $view = 'filament.admin.pages.awards.edit';
    protected static bool $shouldRegisterNavigation = false;

    public ?Award $award = null;

    public function mount(Award $award): void
    {
        $this->award = $award;
    }

    protected function authorizeAccess(): bool
    {
        return true;
    }

    public function getTitle(): string
    {
        return '';
    }

    public function getLayoutBreadcrumbs(): array
    {
        return [
            route('filament.admin.pages.awards') => 'Awards',
            '' => $this->award->name,
        ];
    }
}
