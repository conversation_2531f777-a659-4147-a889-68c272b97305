<?php

namespace App\Filament\Admin\Pages\Tools;

use App\Services\Import\NomineeImportService;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Storage;

class NomineeImport extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $navigationLabel = 'Nominee Import';
    protected static ?string $title = 'Nominee Import Tool';
    protected static ?string $slug = 'tools/nominee-import';
    protected static ?string $navigationGroup = 'Tools';
    protected static ?int $navigationSort = 2;

    protected static string $view = 'filament.admin.pages.tools.nominee-import';
    
    // Add form handling for the import
    public ?array $data = [];
    
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Import Nominees')
                    ->description('Upload a CSV file with nominee data')
                    ->schema([
                        FileUpload::make('csv_file')
                            ->label('CSV File')
                            ->required()
                            ->acceptedFileTypes(['text/csv', 'application/csv'])
                            ->disk('local')
                            ->directory('imports/nominees'),
                        Toggle::make('create_contacts')
                            ->label('Create Contact Records')
                            ->default(true)
                            ->helperText('Create or update contact records for nominees'),
                        Toggle::make('send_invites')
                            ->label('Send System Invites')
                            ->default(false)
                            ->helperText('Generate and send system invites to nominees'),
                    ]),
            ]);
    }
    
    public function import(NomineeImportService $importService)
    {
        $this->validate();
        
        $filePath = Storage::disk('local')->path($this->data['csv_file']);
        
        try {
            $result = $importService->import(
                $filePath, 
                $this->data['create_contacts'] ?? true,
                $this->data['send_invites'] ?? false
            );
            
            Notification::make()
                ->title('Import Completed')
                ->body("Processed {$result['total']} records: {$result['created']} created, {$result['updated']} updated, {$result['skipped']} skipped")
                ->success()
                ->send();
                
        } catch (\Exception $e) {
            Notification::make()
                ->title('Import Failed')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
}
