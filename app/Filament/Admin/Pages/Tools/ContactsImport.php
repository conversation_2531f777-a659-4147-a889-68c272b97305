<?php

namespace App\Filament\Admin\Pages\Tools;

use Filament\Pages\Page;

class ContactsImport extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-user-group';
    protected static ?string $navigationLabel = 'Contacts Import';
    protected static ?string $title = 'General Contacts Import Tool';
    protected static ?string $slug = 'tools/contacts-import';
    protected static ?string $navigationGroup = 'Tools';
    protected static ?int $navigationSort = 4;

    protected static string $view = 'filament.admin.pages.tools.contacts-import';
}
