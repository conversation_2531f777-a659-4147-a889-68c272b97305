<?php

namespace App\Filament\Admin\Pages\Advertisements;

use App\Models\Advertisement;
use Filament\Pages\Page;

class AdvertisementIndex extends Page
{
    protected static ?string $title = 'Advertisements';
    protected static ?string $slug = 'advertisements';
    protected static string $view = 'filament.admin.pages.advertisements.index';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static string $model = Advertisement::class;

    protected function authorizeAccess(): bool
    {
        return true;
    }
}
