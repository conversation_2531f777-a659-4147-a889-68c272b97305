<?php

namespace App\Filament\Admin\Pages\Advertisements;

use App\Models\Advertisement;
use Filament\Pages\Page;

class AdvertisementEdit extends Page
{
    protected static ?string $title = 'Edit Advertisement';
    protected static ?string $slug = 'advertisements/{advertisement}/edit';
    protected static string $view = 'filament.admin.pages.advertisements.edit';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static bool $shouldRegisterNavigation = false;

    public ?Advertisement $advertisement = null;

    public function mount(Advertisement $advertisement): void
    {
        $this->advertisement = $advertisement;
    }

    protected function authorizeAccess(): bool
    {
        return true;
    }
}
