<?php

namespace App\Filament\Resources\NomineeResource\Pages;

use App\Filament\Resources\NomineeResource;
use Filament\Resources\Pages\ViewRecord;
use Filament\Actions;

class ViewNominee extends ViewRecord
{
    protected static string $resource = NomineeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('view_all_nominations')
                ->label('View All Nominations')
                ->icon('heroicon-o-eye')
                ->url(fn (): string =>
                    route('filament.admin.resources.nominees.index', [
                        'tableFilters' => [
                            'nominee' => [
                                'first_name' => $this->record->first_name,
                                'last_name' => $this->record->last_name,
                                'email' => $this->record->email,
                            ]
                        ]
                    ])
                ),
        ];
    }
}
