<?php

namespace App\Filament\Resources\ViewOnly;

use App\Enums\NominationStatus;
use App\Filament\Resources\ViewOnly\NomineeResource\Pages;
use App\Models\Nomination;
use App\Models\School;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class NomineeResource extends Resource
{
    protected static ?string $model = Nomination::class;

    protected static ?string $navigationIcon = 'heroicon-o-star';

    protected static ?string $navigationGroup = 'Resources';

    protected static ?string $navigationLabel = 'Nominees';

    protected static ?int $navigationSort = 20;

    // Disable global search for this resource
    protected static bool $isGloballySearchable = false;

    // Disable creation of new records
    public static function canCreate(): bool
    {
        return false;
    }

    // Disable editing of records
    public static function canEdit(mixed $record): bool
    {
        return false;
    }

    // Disable deletion of records
    public static function canDelete(mixed $record): bool
    {
        return false;
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('first_name')
                    ->label('Nominee First Name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('last_name')
                    ->label('Nominee Last Name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('nominator_name')
                    ->label('Nominator')
                    ->formatStateUsing(function ($state, $record) {
                        if (is_string($state)) {
                            return $state;
                        }

                        // Fallback to constructing from individual fields
                        $firstName = $record->nominator_first_name ?? '';
                        $lastName = $record->nominator_last_name ?? '';

                        if ($firstName || $lastName) {
                            return trim("$firstName $lastName");
                        }

                        return 'Unknown';
                    })
                    ->searchable(['nominator_first_name', 'nominator_last_name', 'nominator_email'])
                    ->sortable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('school.name')
                    ->label('School')
                    ->formatStateUsing(function ($state, $record) {
                        return $record->school?->name ?? 'Unknown';
                    })
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('sport')
                    ->formatStateUsing(function ($state) {
                        return $state ?? 'Unknown';
                    })
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->formatStateUsing(function (NominationStatus $state) {
                        return $state->label();
                    })
                    ->colors([
                        'warning' => NominationStatus::PENDING_AD_VERIFICATION,
                        'gray' => NominationStatus::AD_VERIFIED,
                        'info' => NominationStatus::NOMINEE_NOTIFIED,
                        'success' => NominationStatus::NOMINEE_ACKNOWLEDGED,
                    ])
                    ->sortable(),
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->formatStateUsing(function ($state) {
                        return $state ?? 'Unknown';
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        NominationStatus::PENDING_AD_VERIFICATION->value => NominationStatus::PENDING_AD_VERIFICATION->label(),
                        NominationStatus::AD_VERIFIED->value => NominationStatus::AD_VERIFIED->label(),
                        NominationStatus::NOMINEE_NOTIFIED->value => NominationStatus::NOMINEE_NOTIFIED->label(),
                        NominationStatus::NOMINEE_ACKNOWLEDGED->value => NominationStatus::NOMINEE_ACKNOWLEDGED->label(),
                    ]),
                Tables\Filters\SelectFilter::make('type')
                    ->options(function () {
                        return Nomination::query()
                            ->select('type')
                            ->distinct()
                            ->whereNotNull('type')
                            ->limit(50)
                            ->pluck('type', 'type')
                            ->toArray();
                    }),
                Tables\Filters\Filter::make('school_id')
                    ->form([
                        Forms\Components\Select::make('school_id')
                            ->label('School')
                            ->searchable()
                            ->getSearchResultsUsing(function (string $search): array {
                                return School::query()
                                    ->where('name', 'like', "%{$search}%")
                                    ->limit(50)
                                    ->pluck('name', 'id')
                                    ->toArray();
                            })
                            ->getOptionLabelUsing(function ($value): ?string {
                                return School::find($value)?->name;
                            }),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['school_id'],
                                fn(Builder $query, $schoolId): Builder => $query->where('school_id', $schoolId),
                            );
                    }),
            ])
            ->actions([
                // Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                // No bulk actions for view-only resource
            ])
            ->defaultSort('created_at', 'desc')
            ->paginated([10, 25, 50, 100])
            ->defaultPaginationPageOption(25)
            ->deferLoading();
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with(['school']); // Eager load relationships
    }

    public static function getRelations(): array
    {
        return [
            // No relations for view-only resource
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListNominees::route('/'),
            'view' => Pages\ViewNominee::route('/{record}'),
        ];
    }
}
