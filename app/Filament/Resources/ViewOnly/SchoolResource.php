<?php

namespace App\Filament\Resources\ViewOnly;

use App\Filament\Resources\ViewOnly\SchoolResource\Pages;
use App\Models\County;
use App\Models\Region;
use App\Models\School;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class SchoolResource extends Resource
{
    protected static ?string $model = School::class;

    protected static ?string $navigationIcon = 'heroicon-o-academic-cap';

    protected static ?string $navigationGroup = 'Resources';

    protected static ?string $navigationLabel = 'Schools';

    protected static ?int $navigationSort = 30;

    // Disable global search for this resource
    protected static bool $isGloballySearchable = false;

    // Disable creation of new records
    public static function canCreate(): bool
    {
        return false;
    }

    // Disable editing of records
    public static function canEdit(mixed $record): bool
    {
        return false;
    }

    // Disable deletion of records
    public static function canDelete(mixed $record): bool
    {
        return false;
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('city')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('region.name')
                    ->label('Region')
                    ->formatStateUsing(function ($state, $record) {
                        return $record->region?->name ?? 'Unknown';
                    })
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('county.name')
                    ->label('County')
                    ->formatStateUsing(function ($state, $record) {
                        return $record->county?->name ?? 'Unknown';
                    })
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('region_id')
                    ->label('Region')
                    ->options(function () {
                        return Region::query()
                            ->orderBy('name')
                            ->limit(50)
                            ->pluck('name', 'id')
                            ->toArray();
                    }),
                Tables\Filters\Filter::make('county_id')
                    ->form([
                        Forms\Components\Select::make('county_id')
                            ->label('County')
                            ->searchable()
                            ->getSearchResultsUsing(function (string $search): array {
                                return County::query()
                                    ->join('states', 'counties.state_code', '=', 'states.code')
                                    ->where('counties.name', 'like', "%{$search}%")
                                    ->orWhere('states.name', 'like', "%{$search}%")
                                    ->select('counties.id', 'counties.name', 'states.name as state_name')
                                    ->limit(50)
                                    ->get()
                                    ->mapWithKeys(function ($county) {
                                        return [$county->id => "{$county->name} - {$county->state_name}"];
                                    })
                                    ->toArray();
                            })
                            ->getOptionLabelUsing(function ($value): ?string {
                                $county = County::query()
                                    ->join('states', 'counties.state_code', '=', 'states.code')
                                    ->where('counties.id', $value)
                                    ->select('counties.name', 'states.name as state_name')
                                    ->first();

                                if (!$county) {
                                    return null;
                                }

                                return "{$county->name} - {$county->state_name}";
                            }),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['county_id'],
                                fn (Builder $query, $countyId): Builder => $query->where('county_id', $countyId),
                            );
                    }),
            ])
            ->actions([
                // Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                // No bulk actions for view-only resource
            ])
            ->defaultSort('name', 'asc')
            ->paginated([10, 25, 50, 100])
            ->defaultPaginationPageOption(25)
            ->deferLoading();
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with(['region', 'county']); // Eager load relationships
    }

    public static function getRelations(): array
    {
        return [
            // No relations for view-only resource
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSchools::route('/'),
            'view' => Pages\ViewSchool::route('/{record}'),
        ];
    }
}
