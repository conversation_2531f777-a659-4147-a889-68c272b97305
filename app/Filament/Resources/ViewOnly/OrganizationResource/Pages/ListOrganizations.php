<?php

namespace App\Filament\Resources\ViewOnly\OrganizationResource\Pages;

use App\Filament\Resources\ViewOnly\OrganizationResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListOrganizations extends ListRecords
{
    protected static string $resource = OrganizationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // No create action for view-only resource
        ];
    }
}
