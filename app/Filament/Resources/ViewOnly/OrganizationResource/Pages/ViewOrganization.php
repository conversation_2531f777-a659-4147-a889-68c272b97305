<?php

namespace App\Filament\Resources\ViewOnly\OrganizationResource\Pages;

use App\Filament\Resources\ViewOnly\OrganizationResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewOrganization extends ViewRecord
{
    protected static string $resource = OrganizationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // No edit or delete actions for view-only resource
        ];
    }
}
