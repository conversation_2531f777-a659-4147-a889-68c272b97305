<?php

namespace App\Filament\Resources\ViewOnly;

use App\Enums\ContactStatus;
use App\Filament\Resources\ViewOnly\ContactResource\Pages;
use App\Models\Contact;
use App\Models\Region;
use App\Models\School;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ContactResource extends Resource
{
    protected static ?string $model = Contact::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    protected static ?string $navigationGroup = 'Resources';

    protected static ?string $navigationLabel = 'Contacts';

    protected static ?int $navigationSort = 10;

    // Disable global search for this resource
    protected static bool $isGloballySearchable = false;

    // Disable creation of new records
    public static function canCreate(): bool
    {
        return false;
    }

    // Disable editing of records
    public static function canEdit(mixed $record): bool
    {
        return false;
    }

    // Disable deletion of records
    public static function canDelete(mixed $record): bool
    {
        return false;
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('full_name')
                    ->formatStateUsing(function ($state, $record) {
                        if (is_string($state)) {
                            return $state;
                        }

                        // Fallback to constructing from individual fields
                        $firstName = $record->first_name ?? '';
                        $lastName = $record->last_name ?? '';

                        if ($firstName || $lastName) {
                            return trim("$firstName $lastName");
                        }

                        return 'Unknown';
                    })
                    ->searchable(['first_name', 'last_name'])
                    ->sortable(),
                Tables\Columns\TextColumn::make('email')
                    ->formatStateUsing(function ($state) {
                        return $state ?? 'Unknown';
                    })
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('phone')
                    ->formatStateUsing(function ($state) {
                        return $state ?? 'Unknown';
                    })
                    ->searchable(),
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->formatStateUsing(function ($state) {
                        return $state ?? 'Unknown';
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->formatStateUsing(function ($state) {
                        return $state->label();
                    })
                    ->colors([
                        'success' => ContactStatus::ACTIVE,
                        'gray' => ContactStatus::INACTIVE,
                    ])
                    ->sortable(),
                Tables\Columns\TextColumn::make('school.name')
                    ->formatStateUsing(function ($state, $record) {
                        return $record->school?->name ?? 'Unknown';
                    })
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('organization.name')
                    ->formatStateUsing(function ($state, $record) {
                        return $record->organization?->name ?? 'Unknown';
                    })
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options(function () {
                        return Contact::query()
                            ->select('type')
                            ->distinct()
                            ->whereNotNull('type')
                            ->limit(50)
                            ->pluck('type', 'type')
                            ->toArray();
                    }),
                Tables\Filters\SelectFilter::make('status')
                    ->options(function () {
                        return Contact::query()
                            ->select('status')
                            ->distinct()
                            ->whereNotNull('status')
                            ->limit(50)
                            ->pluck('status', 'status')
                            ->map(fn (ContactStatus $status) => $status->value)
                            ->toArray();
                    }),
                Tables\Filters\Filter::make('school_id')
                    ->form([
                        Forms\Components\Select::make('school_id')
                            ->label('School')
                            ->searchable()
                            ->getSearchResultsUsing(function (string $search): array {
                                return School::query()
                                    ->where('name', 'like', "%{$search}%")
                                    ->limit(50)
                                    ->pluck('name', 'id')
                                    ->toArray();
                            })
                            ->getOptionLabelUsing(function ($value): ?string {
                                return School::find($value)?->name;
                            }),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['school_id'],
                                fn (Builder $query, $schoolId): Builder => $query->where('school_id', $schoolId),
                            );
                    }),
                Tables\Filters\Filter::make('region_id')
                    ->form([
                        Forms\Components\Select::make('region_id')
                            ->label('Region')
                            ->searchable()
                            ->getSearchResultsUsing(function (string $search): array {
                                return Region::query()
                                    ->where('name', 'like', "%{$search}%")
                                    ->limit(50)
                                    ->pluck('name', 'id')
                                    ->toArray();
                            })
                            ->getOptionLabelUsing(function ($value): ?string {
                                return Region::find($value)?->name;
                            }),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['region_id'],
                                fn (Builder $query, $regionId): Builder => $query->where('region_id', $regionId),
                            );
                    }),
            ])
            ->actions([
                // Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                // No bulk actions for view-only resource
            ])
            ->defaultSort('created_at', 'desc')
            ->paginated([10, 25, 50, 100])
            ->defaultPaginationPageOption(25)
            ->deferLoading();
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with(['school', 'organization']); // Eager load relationships
    }

    public static function getRelations(): array
    {
        return [
            // No relations for view-only resource
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListContacts::route('/'),
            'view' => Pages\ViewContact::route('/{record}'),
        ];
    }
}
