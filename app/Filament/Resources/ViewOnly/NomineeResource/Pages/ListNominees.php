<?php

namespace App\Filament\Resources\ViewOnly\NomineeResource\Pages;

use App\Filament\Resources\ViewOnly\NomineeResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListNominees extends ListRecords
{
    protected static string $resource = NomineeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // No create action for view-only resource
        ];
    }
}
