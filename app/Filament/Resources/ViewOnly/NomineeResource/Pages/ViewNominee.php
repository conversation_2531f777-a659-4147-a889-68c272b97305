<?php

namespace App\Filament\Resources\ViewOnly\NomineeResource\Pages;

use App\Filament\Resources\ViewOnly\NomineeResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewNominee extends ViewRecord
{
    protected static string $resource = NomineeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // No edit or delete actions for view-only resource
        ];
    }
}
