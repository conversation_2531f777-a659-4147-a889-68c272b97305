<?php

namespace App\Filament\Resources\ViewOnly;

use App\Filament\Resources\ViewOnly\OrganizationResource\Pages;
use App\Models\Organization;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class OrganizationResource extends Resource
{
    protected static ?string $model = Organization::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office';

    protected static ?string $navigationGroup = 'Resources';

    protected static ?string $navigationLabel = 'Organizations';

    protected static ?int $navigationSort = 40;

    // Disable global search for this resource
    protected static bool $isGloballySearchable = false;

    // Disable creation of new records
    public static function canCreate(): bool
    {
        return false;
    }

    // Disable editing of records
    public static function canEdit(mixed $record): bool
    {
        return false;
    }

    // Disable deletion of records
    public static function canDelete(mixed $record): bool
    {
        return false;
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->formatStateUsing(function ($state) {
                        return $state ?? 'Unknown';
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Created By')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('')
                    ->grow(true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options(function () {
                        return Organization::query()
                            ->select('type')
                            ->distinct()
                            ->limit(50)
                            ->pluck('type', 'type')
                            ->toArray();
                    }),
            ])
            ->actions([
                // Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                // No bulk actions for view-only resource
            ])
            ->defaultSort('name', 'asc')
            ->paginated([10, 25, 50, 100])
            ->defaultPaginationPageOption(25)
            ->deferLoading();
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with(['user']); // Eager load relationships
    }

    public static function getRelations(): array
    {
        return [
            // No relations for view-only resource
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrganizations::route('/'),
            'view' => Pages\ViewOrganization::route('/{record}'),
        ];
    }
}
