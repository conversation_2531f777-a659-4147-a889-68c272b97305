<?php

namespace App\Filament\Resources;

use App\Models\Nomination;
use App\Filament\Resources\NomineeResource\Pages;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\DatePicker;
use Illuminate\Database\Eloquent\Builder;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Pages\ViewRecord;

class NomineeResource extends Resource
{
    protected static ?string $model = Nomination::class;
    protected static ?string $navigationIcon = 'heroicon-o-user-group';
    protected static ?string $navigationLabel = 'Nominees';
    protected static ?string $pluralModelLabel = 'Nominees';

    public static function getEloquentQuery(): Builder
    {
        // Get the most recent nomination for each individual
        return Nomination::query()
            ->selectRaw('DISTINCT ON (COALESCE(user_id, 0), LOWER(TRIM(first_name)), LOWER(TRIM(last_name))) *')
            ->orderByRaw('COALESCE(user_id, 0)')
            ->orderByRaw('LOWER(TRIM(first_name))')
            ->orderByRaw('LOWER(TRIM(last_name))')
            ->orderByDesc('created_at');
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('first_name')
                    ->label('First Name')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('last_name')
                    ->label('Last Name')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('email')
                    ->label('Email')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('school_name')
                    ->label('School')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('sport')
                    ->label('Sport')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('grade')
                    ->label('Grade')
                    ->sortable(),

                Tables\Columns\TextColumn::make('nominator_first_name')
                    ->label('Nominator')
                    ->formatStateUsing(fn ($record) =>
                        trim($record->nominator_first_name . ' ' . $record->nominator_last_name)
                    ),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Nomination Date')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('sport')
                    ->options(function () {
                        return Nomination::query()
                            ->whereNotNull('sport')
                            ->distinct()
                            ->pluck('sport', 'sport')
                            ->toArray();
                    }),

                Tables\Filters\SelectFilter::make('grade')
                    ->options(function () {
                        return Nomination::query()
                            ->whereNotNull('grade')
                            ->distinct()
                            ->orderBy('grade', 'desc')
                            ->pluck('grade', 'grade')
                            ->toArray();
                    }),

                Tables\Filters\Filter::make('school_name')
                    ->form([
                        TextInput::make('school')
                            ->label('School Name')
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['school'],
                                fn (Builder $query, $school): Builder => $query->where('school_name', 'ilike', "%{$school}%")
                            );
                    }),

                Tables\Filters\Filter::make('created_at')
                    ->form([
                        DatePicker::make('nominated_from')
                            ->label('Nominated From'),
                        DatePicker::make('nominated_until')
                            ->label('Nominated Until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['nominated_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['nominated_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\Action::make('view_nominations')
                    ->label('View All Nominations')
                    ->icon('heroicon-o-eye')
                    ->url(fn (Nomination $record): string =>
                        route('filament.admin.resources.nominees.index', [
                            'tableFilters' => [
                                'nominee' => [
                                    'first_name' => $record->first_name,
                                    'last_name' => $record->last_name,
                                    'email' => $record->email,
                                ]
                            ]
                        ])
                    )
                    ->openUrlInNewTab(),

                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListNominees::route('/'),
            'view' => Pages\ViewNominee::route('/{record}'),
        ];
    }
}
