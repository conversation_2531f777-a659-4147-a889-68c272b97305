<?php

namespace App\Filament\Resources\OrganizationResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use App\Data\SystemInvites\SponsorInviteData;
use App\Services\Invite\SystemInviteService;
use Filament\Notifications\Notification;
use Illuminate\Support\Str;

class SponsorsRelationManager extends RelationManager
{
    protected static string $relationship = 'sponsors';

    protected static ?string $recordTitleAttribute = 'full_name';

    protected static ?string $title = 'Organization Sponsor Users';

    protected static ?string $modelLabel = 'Sponsor User';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('role')
                    ->label('Role in Organization')
                    ->readOnly(),

                Forms\Components\TextInput::make('email')
                    ->label('Email')
                    ->readOnly(),

                Forms\Components\DateTimePicker::make('created_at')
                    ->label('Associated Since')
                    ->readOnly(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('full_name')
            ->columns([
                Tables\Columns\TextColumn::make('full_name')
                    ->label('Name')
                    ->sortable(['first_name', 'last_name'])
                    ->searchable(['first_name', 'last_name']),
                Tables\Columns\TextColumn::make('email')
                    ->searchable(),
                Tables\Columns\TextColumn::make('pivot.role')
                    ->label('Role'),
                Tables\Columns\IconColumn::make('deleted_at')
                    ->label('Active')
                    ->boolean()
                    ->getStateUsing(fn ($record) => $record->deleted_at === null)
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle'),
                Tables\Columns\TextColumn::make('pivot.created_at')
                    ->label('Associated Since')
                    ->dateTime(),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make()
                    ->label('Show Deactivated Sponsors'),
            ])
            ->headerActions([
                Tables\Actions\Action::make('inviteSponsor')
                    ->label('Add New Recruiter/Sponsor')
                    ->icon('heroicon-o-plus-circle')
                    ->color('primary')
                    ->modalHeading('Add a New Recruiter/Sponsor')
                    ->modalWidth('md')
                    ->form([
                        Forms\Components\TextInput::make('organization_name')
                            ->label('Organization Name')
                            ->default(fn () => $this->getOwnerRecord()->name)
                            ->disabled()
                            ->required(),
                        Forms\Components\TextInput::make('contact_name')
                            ->label('Contact Name')
                            ->required(),
                        Forms\Components\TextInput::make('email_address')
                            ->label('Email Address')
                            ->email()
                            ->required(),
                        Forms\Components\TextInput::make('phone_number')
                            ->label('Phone Number')
                            ->tel(),
                    ])
                    ->action(function (array $data): void {
                        // Split contact name into first and last name
                        $nameParts = explode(' ', $data['contact_name'], 2);
                        $firstName = $nameParts[0];
                        $lastName = count($nameParts) > 1 ? $nameParts[1] : '';

                        // Create sponsor invite
                        $inviteData = new SponsorInviteData(
                            first_name: $firstName,
                            last_name: $lastName,
                            email: $data['email_address'],
                            company_name: $this->getOwnerRecord()->name,
                            organization_id: $this->getOwnerRecord()->id,
                            token: Str::random(64),
                            created_at: now()->timestamp,
                        );

                        // Send the invite
                        $inviteService = app(SystemInviteService::class);
                        $inviteService->createForSponsor($inviteData);

                        // Show success notification
                        Notification::make()
                            ->success()
                            ->title('Invite sent successfully')
                            ->send();
                    })
                    ->modalSubmitActionLabel('Save & Send Invite')
                    ->modalCancelActionLabel('Cancel'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\Action::make('deactivate')
                    ->label('Deactivate')
                    ->requiresConfirmation()
                    ->color('danger')
                    ->icon('heroicon-o-x-circle')
                    ->hidden(fn ($record) => $record->trashed())
                    ->modalHeading('Deactivate Sponsor User')
                    ->modalDescription('Are you sure you want to deactivate this sponsor? They will no longer be able to log in.')
                    ->modalSubmitActionLabel('Yes, Deactivate Sponsor')
                    ->action(function ($record) {
                        // Soft delete the user
                        $record->delete();
                    }),
                Tables\Actions\Action::make('reactivate')
                    ->label('Reactivate')
                    ->requiresConfirmation()
                    ->color('success')
                    ->icon('heroicon-o-check-circle')
                    ->hidden(fn ($record) => ! $record->trashed())
                    ->modalHeading('Reactivate Sponsor User')
                    ->modalDescription('Are you sure you want to reactivate this sponsor? They will be able to log in again.')
                    ->modalSubmitActionLabel('Yes, Reactivate Sponsor')
                    ->action(function ($record) {
                        // Restore the user
                        $record->restore();
                    }),
            ])
            ->bulkActions([])
            ->defaultSort('first_name', 'asc');
    }
}
