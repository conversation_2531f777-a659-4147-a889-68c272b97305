<?php

namespace App\Filament\Resources\OrganizationResource\Pages;

use App\Filament\Resources\OrganizationResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\File;

class CreateOrganization extends CreateRecord
{
    protected static string $resource = OrganizationResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Set the current user as the creator
        $data['user_id'] = Auth::id();

        return $data;
    }

    protected function handleRecordCreation(array $data): Model
    {
        // Extract logo data
        $fileName = Arr::pull($data, 'logo');
        $logoPath = storage_path('app/public/' . $fileName);

        // Create the organization
        /** @var \App\Models\Organization $record */
        $record = static::getModel()::create($data);

        // Handle logo if uploaded
        if ($logoPath && File::exists($logoPath)) {
            // Add the logo to the media collection
            $record->addMedia($logoPath)
                ->toMediaCollection('logo');

            // Clean up the original file after attaching to media collection
            File::delete($logoPath);
        }

        return $record;
    }
}
