<?php

namespace App\Filament\Resources\OrganizationResource\Pages;

use App\Filament\Resources\OrganizationResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\File;

class EditOrganization extends EditRecord
{
    protected static string $resource = OrganizationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
            Actions\ViewAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Set the logo field when editing
        $record = $this->getRecord();

        // Get the logo media if it exists
        if ($record->hasMedia('logo')) {
            $logo = $record->getFirstMedia('logo');
            // Set the path relative to storage root so the FileUpload component can find it
            $data['logo'] = $logo->getPathRelativeToRoot();
        }

        return $data;
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        // Extract logo data
        $logoPath = Arr::pull($data, 'logo');

        // Update the organization data
        $record->update($data);

        // Handle logo update if it exists
        if (!empty($logoPath)) {
            // Get the full storage path
            $fullPath = storage_path('app/public/' . $logoPath);

            // Check if this is a new upload by comparing with the current logo path
            $currentMedia = $record->getFirstMedia('logo');
            $isNewUpload = !$currentMedia || $logoPath !== $currentMedia->getPathRelativeToRoot();

            if ($isNewUpload && File::exists($fullPath)) {
                // Clear existing media first
                $record->clearMediaCollection('logo');

                // Add the new image to the logo collection
                $record->addMedia($fullPath)
                    ->toMediaCollection('logo');

                // Clean up the original file after attaching to media collection
                File::delete($fullPath);
            }
        } else {
            // If logo was removed, clear the media
            $record->clearMediaCollection('logo');
        }

        return $record;
    }
}
