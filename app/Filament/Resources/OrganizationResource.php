<?php

namespace App\Filament\Resources;

use App\Filament\Resources\OrganizationResource\Pages;
use App\Filament\Resources\OrganizationResource\RelationManagers;
use App\Models\Organization;
use App\Enums\ProfileType;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class OrganizationResource extends Resource
{
    protected static ?string $model = Organization::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office';

    protected static ?string $navigationGroup = 'Resources';

    protected static ?string $navigationLabel = 'Organizations';

    protected static ?int $navigationSort = 40;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Organization Details')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Select::make('type')
                            ->options([
                                'employer' => 'Employer',
                                'school' => 'School',
                                'nonprofit' => 'Non-Profit',
                                'government' => 'Government',
                                'other' => 'Other',
                            ])
                            ->required()
                            ->native(false),
                        Forms\Components\TextInput::make('website')
                            ->url()
                            ->maxLength(255)
                            ->prefixIcon('heroicon-m-globe-alt'),
                        Forms\Components\RichEditor::make('about')
                            ->columnSpanFull(),
                        Forms\Components\FileUpload::make('logo')
                            ->label('Organization Logo')
                            ->image()
                            ->imageEditor()
                            ->disk('public')
                            ->visibility('public')
                            ->downloadable()
                            ->openable()
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/jpg', 'image/svg+xml'])
                            ->imageResizeMode('contain')
                            ->imageCropAspectRatio('1:1')
                            ->imageResizeTargetWidth('800')
                            ->imageResizeTargetHeight('800'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Additional Information')
                    ->schema([
                        Forms\Components\Textarea::make('notes')
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\ImageColumn::make('logo')
                    ->circular()
                    ->getStateUsing(function (Organization $record): ?string {
                        if ($record->hasMedia('logo')) {
                            return $record->getFirstMediaUrl('logo', 'thumbnail');
                        }
                        return null;
                    }),
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->formatStateUsing(function ($state) {
                        return $state ?? 'Unknown';
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('website')
                    ->url(fn ($record) => $record->website)
                    ->openUrlInNewTab()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Created By')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options(function () {
                        return Organization::query()
                            ->select('type')
                            ->distinct()
                            ->limit(50)
                            ->pluck('type', 'type')
                            ->toArray();
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('name', 'asc')
            ->paginated([10, 25, 50, 100])
            ->defaultPaginationPageOption(25)
            ->deferLoading();
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with(['user']); // Eager load relationships
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\SponsorsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrganizations::route('/'),
            'create' => Pages\CreateOrganization::route('/create'),
            'view' => Pages\ViewOrganization::route('/{record}'),
            'edit' => Pages\EditOrganization::route('/{record}/edit'),
        ];
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Organization Details')
                    ->schema([
                        Infolists\Components\Grid::make()
                            ->schema([
                                Infolists\Components\ImageEntry::make('logo')
                                    ->label('Organization Logo')
                                    ->height(200)
                                    ->width(200)
                                    ->extraImgAttributes(['loading' => 'lazy'])
                                    ->getStateUsing(function ($record): ?string {
                                        if ($record->hasMedia('logo')) {
                                            return $record->getFirstMediaUrl('logo');
                                        }
                                        return null;
                                    })
                                    ->columnSpan(1),

                                Infolists\Components\Group::make()
                                    ->schema([
                                        Infolists\Components\TextEntry::make('name')
                                            ->label('Organization Name')
                                            ->size(Infolists\Components\TextEntry\TextEntrySize::Large)
                                            ->weight('bold'),

                                        Infolists\Components\TextEntry::make('type')
                                            ->badge()
                                            ->formatStateUsing(function ($state) {
                                                return $state ?? 'Unknown';
                                            }),

                                        Infolists\Components\TextEntry::make('website')
                                            ->url(fn ($record) => $record->website)
                                            ->openUrlInNewTab(),
                                    ])
                                    ->columnSpan(1),
                            ])
                            ->columns(2),

                        Infolists\Components\TextEntry::make('about')
                            ->html()
                            ->columnSpanFull(),
                    ]),

                Infolists\Components\Section::make('Additional Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('notes')
                            ->columnSpanFull(),

                        Infolists\Components\TextEntry::make('user.name')
                            ->label('Created By'),

                        Infolists\Components\TextEntry::make('created_at')
                            ->label('Created On')
                            ->dateTime(),

                        Infolists\Components\TextEntry::make('updated_at')
                            ->label('Last Updated')
                            ->dateTime(),
                    ])
                    ->columns(2)
                    ->collapsible(),

                Infolists\Components\Section::make('Sponsors')
                    ->schema([
                        Infolists\Components\TextEntry::make('activeSponsors')
                            ->label('Active Sponsors')
                            ->getStateUsing(function ($record) {
                                return $record->activeSponsors()->count();
                            })
                            ->badge()
                            ->color('success'),

                        Infolists\Components\TextEntry::make('inactiveSponsors')
                            ->label('Inactive Sponsors')
                            ->getStateUsing(function ($record) {
                                return $record->inactiveSponsors()->count();
                            })
                            ->badge()
                            ->visible(fn ($record) => $record->inactiveSponsors()->count() > 0)
                            ->color('gray'),
                    ])
                    ->columns(2)
                    ->visible(fn ($record) => $record->sponsors()->exists()),
            ]);
    }
}
