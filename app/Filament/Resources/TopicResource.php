<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TopicResource\Pages;
use App\Models\Topic;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Support\Enums\MaxWidth;
use Filament\Notifications\Notification;

class TopicResource extends Resource
{
    protected static ?string $model = Topic::class;

    protected static ?string $navigationIcon = 'heroicon-o-tag';

    protected static ?string $navigationGroup = 'X-Factor LMS';

    protected static ?string $navigationLabel = 'Topics';

    protected static ?int $navigationSort = 10;

    protected static ?string $recordTitleAttribute = 'name';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Topic Details')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->unique(Topic::class, 'name', ignoreRecord: true)
                            ->helperText('Enter a descriptive name for this topic category'),
                    ])
                    ->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->description(fn (Topic $record): string =>
                        "Used in {$record->courses_count} courses and {$record->modules_count} modules"
                    ),
                Tables\Columns\TextColumn::make('courses_count')
                    ->counts('courses')
                    ->label('Courses')
                    ->sortable()
                    ->badge()
                    ->color('primary'),
                Tables\Columns\TextColumn::make('modules_count')
                    ->counts('modules')
                    ->label('Modules')
                    ->sortable()
                    ->badge()
                    ->color('secondary'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\Filter::make('has_courses')
                    ->label('Has Courses')
                    ->query(fn (Builder $query): Builder => $query->has('courses')),
                Tables\Filters\Filter::make('has_modules')
                    ->label('Has Modules')
                    ->query(fn (Builder $query): Builder => $query->has('modules')),
                Tables\Filters\Filter::make('unused')
                    ->label('Unused Topics')
                    ->query(fn (Builder $query): Builder =>
                        $query->doesntHave('courses')->doesntHave('modules')
                    ),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalWidth(MaxWidth::Medium)
                    ->modalHeading(fn (Topic $record): string => "View Topic: {$record->name}"),
                Tables\Actions\EditAction::make()
                    ->modalWidth(MaxWidth::Medium)
                    ->modalHeading(fn (Topic $record): string => "Edit Topic: {$record->name}"),
                Tables\Actions\DeleteAction::make()
                    ->before(function (Tables\Actions\DeleteAction $action, Topic $record) {
                        // Check if topic has related courses or modules
                        $coursesCount = $record->courses()->count();
                        $modulesCount = $record->modules()->count();

                        if ($coursesCount > 0 || $modulesCount > 0) {
                            Notification::make()
                                ->warning()
                                ->title('Cannot Delete Topic')
                                ->body("This topic is currently used by {$coursesCount} course(s) and {$modulesCount} module(s). Please remove these associations before deleting.")
                                ->persistent()
                                ->send();

                            $action->cancel();
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->before(function (Tables\Actions\DeleteBulkAction $action, $records) {
                            $protectedTopics = [];

                            foreach ($records as $record) {
                                $coursesCount = $record->courses()->count();
                                $modulesCount = $record->modules()->count();

                                if ($coursesCount > 0 || $modulesCount > 0) {
                                    $protectedTopics[] = "{$record->name} ({$coursesCount} courses, {$modulesCount} modules)";
                                }
                            }

                            if (!empty($protectedTopics)) {
                                Notification::make()
                                    ->warning()
                                    ->title('Cannot Delete Some Topics')
                                    ->body('The following topics cannot be deleted because they have related content: ' . implode(', ', $protectedTopics))
                                    ->persistent()
                                    ->send();

                                $action->cancel();
                            }
                        }),
                ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->modalWidth(MaxWidth::Medium)
                    ->modalHeading('Create Topic'),
            ])
            ->defaultSort('name', 'asc')
            ->paginated([10, 25, 50, 100])
            ->defaultPaginationPageOption(25)
            ->deferLoading();
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withCount(['courses', 'modules']); // Eager load counts
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTopics::route('/'),
        ];
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Topic Details')
                    ->schema([
                        Infolists\Components\TextEntry::make('name')
                            ->label('Topic Name')
                            ->size(Infolists\Components\TextEntry\TextEntrySize::Large)
                            ->weight('bold'),
                        Infolists\Components\TextEntry::make('courses_count')
                            ->label('Number of Courses')
                            ->getStateUsing(fn ($record) => $record->courses()->count())
                            ->badge()
                            ->color('primary'),
                        Infolists\Components\TextEntry::make('modules_count')
                            ->label('Number of Modules')
                            ->getStateUsing(fn ($record) => $record->modules()->count())
                            ->badge()
                            ->color('secondary'),
                    ])
                    ->columns(2),
            ]);
    }
}
