<?php

namespace App\Filament\Resources\ImportResource\RelationManagers;

use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use App\Filament\Infolists\Components\JsonEntry;

class RecordsRelationManager extends RelationManager
{
    protected static string $relationship = 'records';

    protected static ?string $recordTitleAttribute = 'row_number';

    // Disable creation of new records
    public function canCreate(): bool
    {
        return false;
    }

    // Disable editing of records
    public function canEdit(mixed $record): bool
    {
        return false;
    }

    // Disable deletion of records
    public function canDelete(mixed $record): bool
    {
        return false;
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('row_number')
            ->columns([
                Tables\Columns\TextColumn::make('row_number')
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->colors([
                        'success' => 'created',
                        'info' => 'updated',
                        'danger' => 'failed',
                    ])
                    ->sortable(),
                Tables\Columns\TextColumn::make('model_type')
                    ->label('Model')
                    ->formatStateUsing(fn ($state) => $state ? class_basename($state) : '-')
                    ->sortable(),
                Tables\Columns\TextColumn::make('model_id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'created' => 'Created',
                        'updated' => 'Updated',
                        'failed' => 'Failed',
                    ]),
            ])
            ->headerActions([
                // No create action needed for import records
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                // No bulk actions needed for import records
            ])
            ->defaultSort('row_number');
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Record Details')
                    ->schema([
                        Infolists\Components\TextEntry::make('row_number')
                            ->label('Row Number'),
                        Infolists\Components\TextEntry::make('status')
                            ->badge()
                            ->color(fn (string $state): string => match ($state) {
                                'created' => 'success',
                                'updated' => 'info',
                                'failed' => 'danger',
                                default => 'gray',
                            }),
                        Infolists\Components\TextEntry::make('model_type')
                            ->label('Model Type')
                            ->formatStateUsing(fn ($state) => $state ? class_basename($state) : '-'),
                        Infolists\Components\TextEntry::make('model_id')
                            ->label('Model ID'),
                        Infolists\Components\TextEntry::make('created_at')
                            ->dateTime(),
                    ])->columns(3),

                Infolists\Components\Section::make('Raw Data')
                    ->schema([
                        Infolists\Components\KeyValueEntry::make('raw_data'),
                    ]),

                Infolists\Components\Section::make('Processed Data')
                    ->schema([
                        JsonEntry::make('processed_data')
                            ->placeholder('No processed data available'),
                    ])
                    ->visible(fn ($record) => !empty($record->processed_data)),

                Infolists\Components\Section::make('Errors')
                    ->schema([
                        Infolists\Components\KeyValueEntry::make('errors'),
                    ])
                    ->visible(fn ($record) => !empty($record->errors)),
            ]);
    }
}
