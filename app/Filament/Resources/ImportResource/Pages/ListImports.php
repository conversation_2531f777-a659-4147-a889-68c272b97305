<?php

namespace App\Filament\Resources\ImportResource\Pages;

use App\Filament\Resources\ImportResource;
use App\Repositories\ImportRepository;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Contracts\View\View;

class ListImports extends ListRecords
{
    protected static string $resource = ImportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // No create action needed for imports
        ];
    }

    public function getHeader(): ?View
    {
        $repository = app(ImportRepository::class);
        $stats = $repository->getImportStats();

        return view('filament.resources.import-resource.pages.import-stats-header', [
            'stats' => $stats,
        ]);
    }

    public function getTitle(): string|Htmlable
    {
        return 'Data Imports';
    }

    public function getSubheading(): string|Htmlable|null
    {
        return null;
    }
}
