<?php

namespace App\Filament\Resources\ImportResource\Pages;

use App\Filament\Resources\ImportResource;
use App\Services\Import\Tracking\ImportTrackingService;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class ViewImport extends ViewRecord
{
    protected static string $resource = ImportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('download_created')
                ->label('Download Created')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('success')
                ->action(fn () => $this->downloadRecords('created'))
                ->visible(fn () => $this->record->created_count > 0),

            Actions\Action::make('download_updated')
                ->label('Download Updated')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('info')
                ->action(fn () => $this->downloadRecords('updated'))
                ->visible(fn () => $this->record->updated_count > 0),

            Actions\Action::make('download_failed')
                ->label('Download Failed')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('danger')
                ->action(fn () => $this->downloadRecords('failed'))
                ->visible(fn () => $this->record->failed_count > 0),
        ];
    }

    /**
     * Download records by status.
     *
     * @param string $status
     * @return BinaryFileResponse
     */
    protected function downloadRecords(string $status): BinaryFileResponse
    {
        $service = app(ImportTrackingService::class);
        $filePath = $service->generateCsvForStatus($this->record, $status);

        return response()->download($filePath, basename($filePath), [
            'Content-Type' => 'text/csv',
        ])->deleteFileAfterSend();
    }
}
