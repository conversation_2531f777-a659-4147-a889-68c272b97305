<?php

namespace App\Models;

use App\Enums\AwardType;
use App\Models\Concerns\HasNotesAndTags;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Award extends Model
{
    use HasFactory, SoftDeletes, HasNotesAndTags;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'details',
        'type',
        'region_id',
        'market_id',
        'state_id',
        'subregion_id',
        'year',
        'is_active',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'type' => AwardType::class,
        'year' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * Get the region associated with the award.
     */
    public function region(): BelongsTo
    {
        return $this->belongsTo(Region::class);
    }

    /**
     * Get the market associated with the award.
     */
    public function market(): BelongsTo
    {
        return $this->belongsTo(Market::class);
    }

    /**
     * Get the state associated with the award.
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'state_id', 'code');
    }

    /**
     * Get the sub-region associated with the award.
     */
    public function subregion(): BelongsTo
    {
        return $this->belongsTo(SubRegion::class, 'subregion_id');
    }

    /**
     * Get the winners associated with the award.
     */
    public function winners(): HasMany
    {
        return $this->hasMany(Winner::class);
    }
}
