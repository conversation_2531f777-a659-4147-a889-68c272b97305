<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class County extends Model
{
    use HasFactory;

    protected $fillable = [
        'market_id',
        'sub_region_id',
        'name',
        'state_code',
    ];

    public function market()
    {
        return $this->belongsTo(Market::class);
    }

    public function subRegion()
    {
        return $this->belongsTo(SubRegion::class);
    }

    public function region()
    {
        return $this->hasOneThrough(Region::class, Market::class);
    }

    public function schools()
    {
        return $this->hasMany(School::class);
    }

    public function users()
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the state that owns the county.
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'state_code', 'code');
    }
}
