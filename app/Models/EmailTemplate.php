<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class EmailTemplate extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'subject',
        'preview_text',
        'body',
        'is_active',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the user who created this template.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get all outbound messages sent using this template.
     */
    public function outboundMessages(): HasMany
    {
        return $this->hasMany(OutboundMessage::class, 'template_id');
    }

    /**
     * Get all tags associated with this template.
     */
    public function tags(): MorphToMany
    {
        return $this->morphToMany(Tag::class, 'taggable');
    }

    /**
     * Scope a query to only include active templates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter templates by tag.
     */
    public function scopeWithTag($query, string $tagName)
    {
        return $query->whereHas('tags', function ($q) use ($tagName) {
            $q->where('name', strtolower($tagName));
        });
    }

    /**
     * Process template content by substituting variables with actual values.
     * This method now uses the VariableSubstitutionService for enhanced processing.
     *
     * @param array $data Associative array of variable values
     * @return array Processed template with subject, preview_text, and body
     */
    public function processTemplate(array $data): array
    {
        $service = app(\App\Services\VariableSubstitutionService::class);
        $service->setDataSources($data);

        return [
            'subject' => $service->processTemplate($this->subject),
            'preview_text' => $service->processTemplate($this->preview_text),
            'body' => $service->processTemplate($this->body),
        ];
    }

    /**
     * Process template content using the enhanced service with additional context.
     *
     * @param array $dataSources Context-based data sources (user, nominee, nominator, etc.)
     * @param array $additionalData Additional data to merge
     * @return array Processed template with subject, preview_text, and body
     */
    public function processTemplateWithContext(array $dataSources, array $additionalData = []): array
    {
        $service = app(\App\Services\VariableSubstitutionService::class);
        $service->setDataSources($dataSources);

        return [
            'subject' => $service->processTemplate($this->subject, $additionalData),
            'preview_text' => $service->processTemplate($this->preview_text, $additionalData),
            'body' => $service->processTemplate($this->body, $additionalData),
        ];
    }

    /**
     * Get all variables used in this template using the enhanced service.
     *
     * @return array Array of unique variables found in all template fields
     */
    public function getVariables(): array
    {
        $service = app(\App\Services\VariableSubstitutionService::class);

        $variables = [];
        $variables = array_merge($variables, $service->extractVariables($this->subject));
        $variables = array_merge($variables, $service->extractVariables($this->preview_text));
        $variables = array_merge($variables, $service->extractVariables($this->body));

        return array_unique($variables);
    }

    /**
     * Validate that all variables in this template can be resolved.
     *
     * @param array $dataSources The data sources to validate against
     * @return array Array of missing variables
     */
    public function validateVariables(array $dataSources): array
    {
        $service = app(\App\Services\VariableSubstitutionService::class);
        $service->setDataSources($dataSources);

        $missing = [];
        $missing = array_merge($missing, $service->validateTemplate($this->subject));
        $missing = array_merge($missing, $service->validateTemplate($this->preview_text));
        $missing = array_merge($missing, $service->validateTemplate($this->body));

        return array_unique($missing);
    }

    /**
     * Get sample data for previewing this template.
     *
     * @param string $context The context to generate sample data for
     * @return array Sample data structure
     */
    public function getSampleData(string $context = 'all'): array
    {
        $service = app(\App\Services\VariableSubstitutionService::class);
        return $service->createSampleData($context);
    }
}
