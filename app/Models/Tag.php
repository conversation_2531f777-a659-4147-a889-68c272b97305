<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

class Tag extends Model
{
    protected $fillable = ['name'];

    /**
     * Set the tag's name, forcing it to lowercase.
     */
    public function setNameAttribute($value): void
    {
        $this->attributes['name'] = strtolower($value);
    }

    /**
     * Get all users that are tagged with this tag.
     */
    public function users(): MorphToMany
    {
        return $this->morphedByMany(User::class, 'taggable');
    }

    /**
     * Get all contacts that are tagged with this tag.
     */
    public function contacts(): MorphToMany
    {
        return $this->morphedByMany(Contact::class, 'taggable');
    }

    /**
     * Get all modules that are tagged with this tag.
     */
    public function modules(): MorphToMany
    {
        return $this->morphedByMany(Module::class, 'taggable');
    }

    /**
     * Get all email templates that are tagged with this tag.
     */
    public function emailTemplates(): MorphToMany
    {
        return $this->morphedByMany(EmailTemplate::class, 'taggable');
    }
}
