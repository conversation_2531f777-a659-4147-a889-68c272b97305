<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

class School extends Model
{
    use HasFactory;

    /**
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'county_id',
        'region_id',
        'address',
        'city',
        'zip_code',
    ];

    /**
     * The users associated with this school.
     *
     * @return HasMany
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * The teams associated with this school.
     *
     * @return HasMany
     */
    public function teams(): HasMany
    {
        return $this->hasMany(Team::class);
    }

    /**
     * Get the county associated with the school.
     */
    public function county(): BelongsTo
    {
        return $this->belongsTo(County::class);
    }

    /**
     * Get the region associated with the school.
     */
    public function region(): BelongsTo
    {
        return $this->belongsTo(Region::class);
    }

    /**
     * Get the market through the county relationship.
     */
    public function market(): HasOneThrough
    {
        return $this->hasOneThrough(Market::class, County::class, 'id', 'id', 'county_id', 'market_id');
    }

    /**
     * Get the contacts associated with the school.
     */
    public function contacts(): HasMany
    {
        return $this->hasMany(Contact::class);
    }

    /**
     * Get the nominations associated with the school.
     */
    public function nominations(): HasMany
    {
        return $this->hasMany(Nomination::class);
    }

    public function athleticsDirectorProfiles(): HasMany
    {
        return $this->hasMany(AthleticsDirectorProfile::class);
    }

    public function scopeAthleticsDirector(Builder $query): Builder
    {
        return $query->athleticsDirectorProfiles()->where('is_verified', true)->limit(1);
    }

    /**
     * Scope a query to search for schools using PostgreSQL full-text search.
     *
     * @param Builder $query
     * @param string $searchTerm
     * @return Builder
     */
    public function scopeSearch(Builder $query, string $searchTerm): Builder
    {
        // Normalize the search term by removing extra spaces and standardizing case
        $searchTerm = trim(strtolower($searchTerm));

        if (empty($searchTerm)) {
            return $query;
        }

        // Use PostgreSQL's plainto_tsquery to handle lexemes and stemming
        return $query->whereRaw('search_vector @@ plainto_tsquery(\'english\', ?)', [$searchTerm])
            ->orderByRaw('ts_rank(search_vector, plainto_tsquery(\'english\', ?)) DESC', [$searchTerm]);
    }
}
