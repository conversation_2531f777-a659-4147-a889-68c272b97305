<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use App\Enums\Gender;
use App\Enums\LifeStage;
use App\Enums\RegistrationMethod;
use App\Enums\ProfileType;
use App\Filters\Filter;
use App\Filters\Filterable;
use App\Filters\HasFilters;
use App\Models\Winner;
use Filament\Models\Contracts\FilamentUser;
use Filament\Models\Contracts\HasName;
use Filament\Panel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Laravel\Scout\Searchable;
use OpenApi\Attributes as OA;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @OA\Schema(
 *     schema="User",
 *     title="User",
 *     description="Represents a user in the system.",
 *     @OA\Property(property="id", type="integer", example=1, description="Primary key ID"),
 *     @OA\Property(property="first_name", type="string", example="John", description="User's first name"),
 *     @OA\Property(property="last_name", type="string", example="Doe", description="User's last name"),
 *     @OA\Property(property="email", type="string", format="email", example="<EMAIL>", description="User's email address (unique)"),
 *     @OA\Property(property="notification_email", type="string", format="email", example="<EMAIL>", nullable=true, description="Optional notification email address for school/work email"),
 *     @OA\Property(property="profile_type", type="string", enum={"STUDENT", "COACH", "ATHLETIC_DIRECTOR", "SPONSOR", "RECRUITER", "ADMIN"}, example="STUDENT", description="Type of user profile"),
 *     @OA\Property(property="life_stage", type="string", nullable=true, example="High School", description="Current life stage (e.g., High School, College)"),
 *     @OA\Property(property="registration_method", type="string", enum={"SYSTEM_INVITE", "SELF_REGISTRATION"}, example="SYSTEM_INVITE", description="How the user registered"),
 *     @OA\Property(property="phone", type="string", nullable=true, example="************", description="User's phone number"),
 *     @OA\Property(property="street_address_1", type="string", nullable=true, example="123 Main St", description="Primary street address"),
 *     @OA\Property(property="street_address_2", type="string", nullable=true, example="Apt 4B", description="Secondary street address line"),
 *     @OA\Property(property="city", type="string", nullable=true, example="Anytown", description="City"),
 *     @OA\Property(property="state_code", type="string", nullable=true, example="CA", description="State code (e.g., CA, NY)"),
 *     @OA\Property(property="zip", type="string", nullable=true, example="90210", description="ZIP code"),
 *     @OA\Property(property="county_id", type="integer", nullable=true, example=1, description="Foreign key to counties table"),
 *     @OA\Property(property="region_id", type="integer", nullable=true, example=1, description="Foreign key to regions table"),
 *     @OA\Property(property="school_id", type="integer", nullable=true, example=1, description="Foreign key to schools table"),
 *     @OA\Property(property="graduation_year", type="integer", nullable=true, example=2025, description="Year of graduation"),
 *     @OA\Property(property="gpa", type="number", format="float", nullable=true, example=3.8, description="Grade Point Average"),
 *     @OA\Property(property="class_rank", type="string", nullable=true, example="Top 10%", description="Class rank information"),
 *     @OA\Property(property="gender", type="string", enum={"MALE", "FEMALE", "OTHER"}, nullable=true, example="MALE", description="User's gender"),
 *     @OA\Property(property="height_in_inches", type="integer", nullable=true, example=70, description="Height in inches"),
 *     @OA\Property(property="weight", type="integer", nullable=true, example=180, description="Weight in pounds"),
 *     @OA\Property(property="twitter", type="string", nullable=true, example="johndoe", description="Twitter handle"),
 *     @OA\Property(property="linkedin", type="string", nullable=true, example="linkedin.com/in/johndoe", description="LinkedIn profile URL"),
 *     @OA\Property(property="instagram", type="string", nullable=true, example="johndoe", description="Instagram handle"),
 *     @OA\Property(property="facebook", type="string", nullable=true, example="facebook.com/johndoe", description="Facebook profile URL"),
 *     @OA\Property(property="hudl", type="string", nullable=true, example="hudl.com/profile/johndoe", description="Hudl profile URL"),
 *     @OA\Property(property="custom_link", type="string", nullable=true, example="johndoe.com", description="Custom personal website or link"),
 *     @OA\Property(property="content", type="string", nullable=true, description="General content or bio"),
 *     @OA\Property(property="notes", type="string", nullable=true, description="Internal notes about the user"),
 *     @OA\Property(property="recruiter_enabled", type="boolean", example=true, description="Whether the user profile is visible to recruiters"),
 *     @OA\Property(property="handle", type="string", nullable=true, example="johndoe25", description="Unique user handle/username"),
 *     @OA\Property(property="parent_id", type="integer", nullable=true, example=5, description="Foreign key to the parent user (if applicable)"),
 *     @OA\Property(property="pinned_conversations", type="array", nullable=true, description="Array of user IDs for pinned conversations", @OA\Items(type="integer")),
 *     @OA\Property(property="email_verified_at", type="string", format="date-time", nullable=true, example="2023-01-01T12:00:00Z", description="Timestamp when the email address was verified"),
 *     @OA\Property(property="created_at", type="string", format="date-time", example="2023-01-01T12:00:00Z", description="Timestamp when the record was created"),
 *     @OA\Property(property="updated_at", type="string", format="date-time", example="2023-01-01T12:30:00Z", description="Timestamp when the record was last updated"),
 *     @OA\Property(property="full_name", type="string", readOnly=true, example="John Doe", description="Calculated full name")
 * )
 */
class User extends Authenticatable implements HasMedia, FilamentUser, HasName, Filterable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles, HasApiTokens, InteractsWithMedia, Searchable;
    use HasFilters;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'notification_email',
        'password',
        'profile_type',
        'life_stage',
        'registration_method',
        'phone',
        'street_address_1',
        'street_address_2',
        'city',
        'state_code',
        'zip',
        'county_id',
        'region_id',
        'school_id',
        'graduation_year',
        'gpa',
        'class_rank',
        'gender',
        'height_in_inches',
        'weight',
        'twitter',
        'linkedin',
        'instagram',
        'facebook',
        'hudl',
        'custom_link',
        'content',
        'notes',
        'recruiter_enabled',
        'handle',
        'parent_id',
        'pinned_conversations',
        'public_profile',
        'metadata',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'gender' => Gender::class,
            'registration_method' => RegistrationMethod::class,
            'profile_type' => ProfileType::class,
            'life_stage' => LifeStage::class,
            'pinned_conversations' => 'array',
            'public_profile' => 'boolean',
            'metadata' => 'array',
        ];
    }

    public function getFilamentName(): string
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    public function canAccessPanel(Panel $panel): bool
    {
        return $this->hasRole('admin');
    }

    public function getFullNameAttribute(): string
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    /**
     * The organizations this user is associated with.
     * For sponsors, this represents the organizations they belong to.
     * For other users, this represents organizations they have created.
     *
     * @return BelongsToMany
     */
    public function organizations()
    {
        return $this->belongsToMany(Organization::class, 'organization_sponsor')
            ->withPivot(['role', 'metadata', 'deactivated_at'])
            ->withTimestamps();
    }

    /**
     * Get the active organization for this sponsor.
     * Returns null if user is not a sponsor or has no active organization.
     *
     * @return BelongsToMany
     */
    public function activeOrganization(): BelongsToMany
    {
        return $this->belongsToMany(Organization::class, 'organization_sponsor')
            ->withPivot(['role', 'metadata', 'deactivated_at'])
            ->wherePivotNull('deactivated_at')
            ->withTimestamps();
    }

    /**
     * Get historical (inactive) organizations for this sponsor.
     *
     * @return BelongsToMany
     */
    public function previousOrganizations(): BelongsToMany
    {
        return $this->belongsToMany(Organization::class, 'organization_sponsor')
            ->withPivot(['role', 'metadata', 'deactivated_at'])
            ->wherePivotNotNull('deactivated_at')
            ->withTimestamps();
    }

    /**
     * The school this user is associated with.
     *
     * @return BelongsTo
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    /**
     * The parent user (if any).
     *
     * @return BelongsTo
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'parent_id');
    }

    /**
     * The children users (if this user acts as a parent).
     *
     * @return HasMany
     */
    public function children(): HasMany
    {
        return $this->hasMany(User::class, 'parent_id');
    }

    /**
     * The interests of this user.
     *
     * @return BelongsToMany
     */
    public function interests(): BelongsToMany
    {
        return $this->belongsToMany(Interest::class, 'interest_user')->withTimestamps();
    }

    /**
     * The sports associated with this user.
     *
     * @return BelongsToMany
     */
    public function sports(): BelongsToMany
    {
        return $this->belongsToMany(Sport::class, 'sport_user')
            ->withPivot(['order'])
            ->withTimestamps();
    }

    /**
     * Custom sports defined by this user.
     *
     * @return HasMany
     */
    public function customSports(): HasMany
    {
        return $this->hasMany(CustomSport::class);
    }

    /**
     * The achievements of this user.
     *
     * @return HasMany
     */
    public function achievements(): HasMany
    {
        return $this->hasMany(Achievement::class);
    }

    /**
     * The work experiences of this user.
     *
     * @return HasMany
     */
    public function workExperiences(): HasMany
    {
        return $this->hasMany(WorkExperience::class);
    }

    /**
     * The endorsements associated with this user.
     * Pivot: endorsement_id, endorser_id, relation
     *
     * @return BelongsToMany
     */
    public function endorsements(): BelongsToMany
    {
        return $this->belongsToMany(Endorsement::class, 'user_endorsement')
            ->withPivot(['endorser_id', 'relation'])
            ->withTimestamps();
    }

    /**
     * The nominations this user has received.
     *
     * @return HasMany
     */
    public function nominations(): HasMany
    {
        return $this->hasMany(Nomination::class, 'email', 'email');
    }

    /**
     * The nominations this user has made.
     *
     * @return HasMany
     */
    public function madeNominations(): HasMany
    {
        return $this->hasMany(Nomination::class, 'nominator_email', 'email');
    }

    /**
     * The teams this user is part of.
     *
     * @return BelongsToMany
     */
    public function teams(): BelongsToMany
    {
        return $this->belongsToMany(Team::class, 'team_user')
            ->withPivot(['type'])
            ->withTimestamps();
    }

    /**
     * The groups this user is part of.
     *
     * @return BelongsToMany
     */
    public function groups(): BelongsToMany
    {
        return $this->belongsToMany(Group::class, 'group_user')->withTimestamps();
    }

    /**
     * The meetings this user owns.
     *
     * @return HasMany
     */
    public function ownedMeetings(): HasMany
    {
        return $this->hasMany(Meeting::class, 'owner_id');
    }

    /**
     * The meetings this user is invited to.
     *
     * @return BelongsToMany
     */
    public function invitedMeetings(): BelongsToMany
    {
        return $this->belongsToMany(Meeting::class, 'meeting_invitees')->withTimestamps();
    }

    /**
     * The team invites associated with this user.
     *
     * @return HasMany
     */
    public function teamInvites(): HasMany
    {
        return $this->hasMany(TeamInvite::class);
    }

    /**
     * The courses this user is enrolled in or has completed.
     *
     * @return BelongsToMany
     */
    public function courses(): BelongsToMany
    {
        return $this->belongsToMany(Course::class, 'course_user')
            ->withPivot(['completed_at', 'last_calculated_completion_percentage'])
            ->withTimestamps();
    }

    /**
     * The modules this user has completed or is working on.
     */
    public function modules(): BelongsToMany
    {
        return $this->belongsToMany(Module::class)
            ->using(ModuleUser::class)
            ->withPivot(['started_at', 'completed_at', 'last_position', 'completion_metadata'])
            ->withTimestamps();
    }

    /**
     * The assignments this user has.
     *
     * @return HasMany
     */
    public function assignments(): HasMany
    {
        return $this->hasMany(Assignment::class);
    }

    /**
     * The badges this user has earned.
     *
     * @return BelongsToMany
     */
    public function badges(): BelongsToMany
    {
        return $this->belongsToMany(Badge::class, 'badge_user')->withPivot('is_achieved')->withTimestamps();
    }

    /**
     * The opportunities this user has bookmarked.
     *
     * @return BelongsToMany
     */
    public function bookmarkedOpportunities(): BelongsToMany
    {
        return $this->belongsToMany(Opportunity::class, 'opportunity_bookmarks')->withTimestamps();
    }

    /**
     * The opportunities created/owned by this sponsor user.
     *
     * @return HasMany
     */
    public function opportunities(): HasMany
    {
        return $this->hasMany(Opportunity::class, 'user_id');
    }

    /**
     * The question responses given by this user.
     *
     * @return HasMany
     */
    public function questionResponses(): HasMany
    {
        return $this->hasMany(QuestionResponse::class);
    }

    /**
     * The test attempts made by this user.
     *
     * @return HasMany
     */
    public function testAttempts(): HasMany
    {
        return $this->hasMany(TestAttempt::class);
    }

    /**
     * Mark the user as verified when they complete their system invite onboarding.
     * This is the only way users can be created in the system.
     */
    public function markAsVerifiedViaInvite(): void
    {
        $this->forceFill([
            'email_verified_at' => now(),
            'registration_method' => RegistrationMethod::SystemInvite,
        ])->save();
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('profile_photos')
            ->useDisk('public')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/jpg'])
            ->registerMediaConversions(function (Media $media) {
                $this->addMediaConversion('thumbnail')
                    ->width(400)
                    ->height(300)
                    ->sharpen(10)
                    ->nonQueued();

                $this->addMediaConversion('full')
                    ->width(800)
                    ->height(600)
                    ->sharpen(10)
                    ->nonQueued();
            });

        $this->addMediaCollection('avatar')
            ->useDisk('public')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/jpg'])
            ->registerMediaConversions(function (Media $media) {
                $this->addMediaConversion('thumbnail')
                    ->width(150)
                    ->height(150)
                    ->sharpen(10)
                    ->nonQueued();

                $this->addMediaConversion('full')
                    ->width(600)
                    ->height(600)
                    ->sharpen(10)
                    ->nonQueued();
            });
    }

    public function registerMediaConversions(Media $media = null): void
    {
        // Register any global conversions here if needed
    }

    public function communityInvolvements(): HasMany
    {
        return $this->hasMany(CommunityInvolvement::class);
    }

    /**
     * The county this user is associated with.
     *
     * @return BelongsTo
     */
    public function county(): BelongsTo
    {
        return $this->belongsTo(County::class);
    }

    /**
     * Get the region associated with the user.
     */
    public function region(): BelongsTo
    {
        return $this->belongsTo(Region::class);
    }

    /**
     * Get the market through the county relationship.
     */
    public function market(): HasOneThrough
    {
        return $this->hasOneThrough(Market::class, County::class, 'id', 'id', 'county_id', 'market_id');
    }

    /**
     * Get the state associated with the user.
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'state_code', 'code');
    }

    /**
     * Get all tags for the user.
     */
    public function tags(): MorphToMany
    {
        return $this->morphToMany(Tag::class, 'taggable')->withTimestamps();
    }

    /**
     * The athletics director profile associated with this user.
     *
     * @return HasOne
     */
    public function athleticsDirectorProfile(): HasOne
    {
        return $this->hasOne(AthleticsDirectorProfile::class);
    }

    /**
     * The awards and scholarships won by this user.
     *
     * @return HasMany
     */
    public function winners(): HasMany
    {
        return $this->hasMany(Winner::class);
    }

    /**
     * Get the verified athletics director for this user's school
     */
    public function schoolVerifiedAthleticsDirector(): BelongsTo
    {
        return $this->belongsTo(User::class, 'school_id', 'school_id')
            ->whereHas('athleticsDirectorProfile', function ($query) {
                $query->where('is_verified', true);
            });
    }

    /**
     * The messages sent by this user.
     *
     * @return HasMany
     */
    public function sentMessages(): HasMany
    {
        return $this->hasMany(Message::class, 'sender_id');
    }

    /**
     * The messages received by this user.
     *
     * @return HasMany
     */
    public function receivedMessages(): HasMany
    {
        return $this->hasMany(Message::class, 'recipient_id');
    }

    /**
     * The users blocked by this user.
     *
     * @return HasMany
     */
    public function blockedUsers(): HasMany
    {
        return $this->hasMany(UserBlock::class, 'blocker_id');
    }

    /**
     * The users who have blocked this user.
     *
     * @return HasMany
     */
    public function blockedByUsers(): HasMany
    {
        return $this->hasMany(UserBlock::class, 'blocked_id');
    }

    /**
     * The connection requests sent by this user.
     *
     * @return HasMany
     */
    public function sentConnectionRequests(): HasMany
    {
        return $this->hasMany(Connection::class, 'requester_id');
    }

    /**
     * The connection requests received by this user.
     *
     * @return HasMany
     */
    public function receivedConnectionRequests(): HasMany
    {
        return $this->hasMany(Connection::class, 'recipient_id');
    }

    /**
     * Get all connections for this user (both sent and received).
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllConnections()
    {
        return Connection::forUser($this->id)->get();
    }

    /**
     * Get all accepted connections for this user.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAcceptedConnections()
    {
        return Connection::forUser($this->id)->accepted()->get();
    }

    /**
     * Get all pending connections for this user.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getPendingConnections()
    {
        return Connection::forUser($this->id)->pending()->get();
    }

    /**
     * Check if this user is connected with another user.
     *
     * @param int $otherUserId
     * @return bool
     */
    public function isConnectedWith(int $otherUserId): bool
    {
        return Connection::betweenUsers($this->id, $otherUserId)
            ->accepted()
            ->exists();
    }

    /**
     * Check if this user has a pending connection with another user.
     *
     * @param int $otherUserId
     * @return bool
     */
    public function hasPendingConnectionWith(int $otherUserId): bool
    {
        return Connection::betweenUsers($this->id, $otherUserId)
            ->pending()
            ->exists();
    }

    /**
     * Determine if the model should be searchable.
     *
     * @return bool
     */
    public function shouldBeSearchable(): bool
    {
        // Only include users with public profiles and searchable profile types
        $networkingService = app(\App\Services\NetworkingService::class);

        // The public_profile field might be null in some cases, so treat null as true
        $isPublic = $this->public_profile ?? true;

        // Check if the user's profile type is defined and searchable
        $isSearchableType = $this->profile_type &&
                          $networkingService->isSearchable($this->profile_type);

        return $isPublic && $isSearchableType;
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array<string, mixed>
     */
    public function toSearchableArray(): array
    {
        // Load necessary relationships
        $this->loadMissing('interests', 'sports', 'state', 'school');

        // Get networking service
        $networkingService = app(\App\Services\NetworkingService::class);
        $connectableByTypes = $this->profile_type
            ? $networkingService->getConnectableByTypes($this->profile_type)
            : [];

        // Safely access life_stage display name
        $lifeStageDisplay = $this->life_stage ? $this->life_stage->displayName() : null;

        // Build interests array with null defaults
        $interestNames = $this->interests ? $this->interests->pluck('name')->toArray() : [];
        $interests = $this->interests ? $this->interests->map(function ($interest) {
            return [
                'name' => $interest->name ?? '',
                'icon' => $interest->icon ?? '',
            ];
        })->toArray() : [];

        // Build sports array with null defaults
        $sports = $this->sports ? $this->sports->pluck('name')->toArray() : [];

        // Get profile image URL with null default
        $profileImageUrl = $this->getFirstMediaUrl('avatar', 'thumbnail') ?: null;

        // Check if user has won any awards or scholarships
        $hasAwards = false;
        if ($this->profile_type === ProfileType::POSITIVE_ATHLETE || $this->profile_type === ProfileType::POSITIVE_COACH) {
            $hasAwards = Winner::where('user_id', $this->id)
                ->where('is_winner', true)
                ->exists();
        }

        // Build result array with null defaults for all fields
        return [
            'id' => $this->id,
            'first_name' => $this->first_name ?? '',
            'last_name' => $this->last_name ?? '',
            'profile_type' => $this->profile_type?->value ?? $this->getRawOriginal('profile_type') ?? '',
            'life_stage' => $lifeStageDisplay ?? '',
            'high_school' => $this->school?->name ?? '',
            'graduation_year' => $this->graduation_year ?? null,
            'career_interests' => $interestNames ?? [],
            'interests' => $interests ?? [],
            'sports' => $sports ?? [],
            'state' => $this->state?->name ?? '',
            'state_code' => $this->state?->code ?? '',
            'profile_image_url' => $profileImageUrl ?? '',
            'class_of' => $this->graduation_year ? "CLASS OF {$this->graduation_year}" : '',
            'public_profile' => true,
            'can_connect' => $connectableByTypes ?? [],
            'has_achievements' => $hasAwards,
        ];
    }

    /**
     * Change the sponsor's active organization.
     * This will deactivate the current organization (if any) and activate the new one.
     *
     * @param Organization $newOrganization
     * @param array $attributes Additional pivot attributes
     * @return void
     * @throws \Exception If user is not a sponsor
     */
    public function changeOrganization(Organization $newOrganization, array $attributes = []): void
    {
        if ($this->profile_type !== ProfileType::SPONSOR) {
            throw new \Exception('Only sponsor users can change organizations.');
        }

        // The database trigger will automatically deactivate any other active organizations
        $this->organizations()->syncWithoutDetaching([
            $newOrganization->id => array_merge($attributes, [
                'deactivated_at' => null,
                'created_at' => now(),
                'updated_at' => now(),
            ])
        ]);
    }

    /**
     * Get the available filters for this model specific to positive athlete profiles
     *
     * @return array<Filter>
     */
    public static function getFilters(): array
    {
        return [
            // Basic User Fields
            Filter::make('first_name', 'First Name', 'text')
                ->withOperators(Filter::getDefaultOperatorsForType('text')),

            Filter::make('last_name', 'Last Name', 'text')
                ->withOperators(Filter::getDefaultOperatorsForType('text')),

            Filter::make('email', 'Email', 'text')
                ->withOperators(Filter::getDefaultOperatorsForType('text')),

            Filter::make('created_at', 'Created Date', 'date')
                ->withOperators(Filter::getDefaultOperatorsForType('date')),

            // Profile & Demographic Fields
            Filter::make('gender', 'Gender', 'select')
                ->withOperators(Filter::getDefaultOperatorsForType('select'))
                ->withOptions([
                    'male' => 'Male',
                    'female' => 'Female',
                    'other' => 'Other',
                ]),

            Filter::make('graduation_year', 'Graduation Year', 'number')
                ->withOperators(Filter::getDefaultOperatorsForType('number'))
                ->withDynamicOptions(function () {
                    // Generate graduation years from current year to 5 years ahead
                    $currentYear = (int) date('Y');
                    $years = range($currentYear - 1, $currentYear + 5);
                    return array_combine($years, $years);
                }),

            Filter::make('gpa', 'GPA', 'number')
                ->withOperators(Filter::getDefaultOperatorsForType('number')),

            Filter::make('class_rank', 'Class Rank', 'select')
                ->withOperators(['=', '!=', 'in', 'not_in', 'contains'])
                ->withOptions([
                    'top 1%' => 'Top 1%',
                    'top 10%' => 'Top 10%',
                    'top 20%' => 'Top 20%',
                    'top 50%' => 'Top 50%'
                ]),

            Filter::make('height_in_inches', 'Height', 'number')
                ->withOperators(Filter::getDefaultOperatorsForType('number')),

            Filter::make('recruiter_enabled', 'Recruiter Enabled', 'boolean')
                ->withOperators(Filter::getDefaultOperatorsForType('boolean')),

            Filter::make('parent_id', 'Has Parent', 'select')
                ->withOperators(['null', 'not_null']),

            // School Relationship Fields
            Filter::make('school.name', 'School', 'text')
                ->withOperators(Filter::getDefaultOperatorsForType('text'))
                ->withRelationship('school', 'name'),

            // Location Fields
            Filter::make('county.state.code', 'State', 'select')
                ->withOperators(['=', '!=', 'in', 'not_in'])
                ->withDynamicOptions(function () {
                    return DB::table('states')
                        ->orderBy('code')
                        ->pluck('name', 'code')
                        ->toArray();
                }),

            Filter::make('region.name', 'Region', 'select')
                ->withOperators(['=', '!='])
                ->withDynamicOptions(function () {
                    return DB::table('regions')
                        ->orderBy('name')
                        ->pluck('name', 'id')
                        ->toArray();
                }),

            // Sports (Many-to-Many)
            Filter::make('sports.name', 'Sport', 'select')
                ->withOperators(['=', '!=', 'in', 'not_in'])
                ->withDynamicOptions(function () {
                    return DB::table('sports')
                        ->orderBy('name')
                        ->pluck('name', 'id')
                        ->toArray();
                }),

            // Nominations (Has-Many)
            Filter::make('nominations.ai_score', 'AI Score', 'number')
                ->withOperators(Filter::getDefaultOperatorsForType('number')),

            Filter::make('nominations.status', 'Award Status', 'select')
                ->withOperators(['=', '!='])
                ->withDynamicOptions(function () {
                    $statuses = \App\Enums\NominationStatus::cases();
                    return collect($statuses)->mapWithKeys(function ($status) {
                        return [$status->value => $status->label()];
                    })->toArray();
                }),

            // Organizations (for Sponsor Users)
            Filter::make('organizations.name', 'Organization', 'text')
                ->withOperators(Filter::getDefaultOperatorsForType('text'))
                ->withRelationship('organizations', 'name'),
        ];
    }

    /**
     * Check if the user is a sponsor.
     *
     * @return bool
     */
    public function isSponsor(): bool
    {
        return $this->profile_type === ProfileType::SPONSOR;
    }

    /**
     * Check if the user is a parent
     *
     * @return bool
     */
    public function isParent(): bool
    {
        return $this->profile_type === ProfileType::PARENT;
    }

    /**
     * Determine if this user object represents a pending invitation.
     * This is a computed property for consistent API responses.
     *
     * @return bool
     */
    public function getIsPendingInviteAttribute(): bool
    {
        return $this->exists === false || (isset($this->attributes['is_pending_invite']) && $this->attributes['is_pending_invite'] === true);
    }

    /**
     * Send the password reset notification.
     *
     * @param string $token
     * @return void
     */
    public function sendPasswordResetNotification($token)
    {
        $this->notify(new \App\Notifications\Auth\ResetPasswordNotification($token));
    }
}
