<?php

namespace App\Models;

use App\Enums\TestStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Enums\QuestionType;

class TestAttempt extends Model
{
    use HasFactory;

    /**
     * @var list<string>
     */
    protected $fillable = [
        'user_id',
        'test_id',
        'started_at',
        'ends_at',
        'completed_at',
        'graded_at',
        'feedback',
        'score',
        'status',
    ];

    protected $casts = [
        'status' => TestStatus::class,
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'ends_at' => 'datetime',
        'graded_at' => 'datetime',
        'score' => 'integer',
    ];

    /**
     * The user who made this attempt.
     *
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * The test this attempt is for.
     *
     * @return BelongsTo
     */
    public function test(): BelongsTo
    {
        return $this->belongsTo(Test::class);
    }

    /**
     * The question responses for this attempt.
     *
     * @return HasMany
     */
    public function questionResponses(): HasMany
    {
        return $this->hasMany(QuestionResponse::class);
    }

    /**
     * Calculate the score for multiple choice questions only.
     * This excludes long text (free response) questions from the calculation.
     */
    public function calculateMultipleChoiceScore(): int
    {
        $multipleChoiceQuestions = $this->test->questions()
            ->where('type', QuestionType::MultipleChoice)
            ->with(['responses' => function ($query) {
                $query->where('test_attempt_id', $this->id);
            }])
            ->get();

        if ($multipleChoiceQuestions->isEmpty()) {
            return 0;
        }

        $correctCount = 0;
        $totalQuestions = $multipleChoiceQuestions->count();

        foreach ($multipleChoiceQuestions as $question) {
            $response = $question->responses->first();
            if ($response && $response->correct) {
                $correctCount++;
            }
        }

        return (int) round(($correctCount / $totalQuestions) * 100);
    }
}
