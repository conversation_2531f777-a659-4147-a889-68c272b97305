<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Winner extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'scholarship_id',
        'award_id',
        'year',
        'is_finalist',
        'is_winner',
        'verification_state',
        'verified_at',
        'notified_at',
        'acknowledged_at',
        'tshirt_size',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'year' => 'integer',
        'is_finalist' => 'boolean',
        'is_winner' => 'boolean',
        'verified_at' => 'datetime',
        'notified_at' => 'datetime',
        'acknowledged_at' => 'datetime',
    ];

    /**
     * Get the user (athlete or coach) associated with the winner.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the scholarship associated with the winner.
     */
    public function scholarship()
    {
        return $this->belongsTo(Scholarship::class);
    }

    /**
     * Get the award associated with the winner.
     */
    public function award()
    {
        return $this->belongsTo(Award::class);
    }
}
