<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class LocationCoordinate extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'city',
        'state_code',
        'latitude',
        'longitude',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'latitude' => 'float',
        'longitude' => 'float',
    ];

    /**
     * Get coordinates for a city and state
     *
     * @param string $city
     * @param string $stateCode
     * @return array|null
     */
    public static function getCoordinates(string $city, string $stateCode): ?array
    {
        $coordinates = self::query()
            ->where('city', $city)
            ->where('state_code', $stateCode)
            ->first();

        if ($coordinates) {
            return [
                'lat' => $coordinates->latitude,
                'lng' => $coordinates->longitude,
            ];
        }

        return null;
    }
}
