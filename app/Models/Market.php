<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Market extends Model
{
    use HasFactory;

    protected $fillable = [
        'region_id',
        'name',
        'slug',
    ];

    public function region()
    {
        return $this->belongsTo(Region::class);
    }

    public function subRegions()
    {
        return $this->hasMany(SubRegion::class);
    }

    public function counties()
    {
        return $this->hasMany(County::class);
    }
}
