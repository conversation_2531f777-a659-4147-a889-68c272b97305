<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class EngagementAggregate extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'event_type',
        'date',
        'count',
        'engageable_id',
        'engageable_type',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date' => 'date',
        'count' => 'integer',
    ];

    /**
     * Get the parent engageable model (polymorphic).
     */
    public function engageable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope a query to only include aggregates of a specific type.
     */
    public function scopeOfType(Builder $query, string $type): Builder
    {
        return $query->where('event_type', $type);
    }

    /**
     * Scope a query to only include impression aggregates.
     */
    public function scopeImpressions(Builder $query): Builder
    {
        return $query->ofType(Engagement::EVENT_IMPRESSION);
    }

    /**
     * Scope a query to only include click aggregates.
     */
    public function scopeClicks(Builder $query): Builder
    {
        return $query->ofType(Engagement::EVENT_CLICK);
    }

    /**
     * Scope a query to only include aggregates after a specific date.
     */
    public function scopeAfterDate(Builder $query, Carbon $date): Builder
    {
        return $query->where('date', '>=', $date->toDateString());
    }

    /**
     * Scope a query to only include aggregates before a specific date.
     */
    public function scopeBeforeDate(Builder $query, Carbon $date): Builder
    {
        return $query->where('date', '<=', $date->toDateString());
    }

    /**
     * Scope a query to include aggregates within a date range.
     */
    public function scopeBetweenDates(Builder $query, Carbon $startDate, Carbon $endDate): Builder
    {
        return $query->afterDate($startDate)->beforeDate($endDate);
    }

    /**
     * Increment the count for a specific engageable, event type, and date.
     *
     * @param Model $engageable
     * @param string $eventType
     * @param Carbon|null $date
     * @param int $incrementBy
     * @return self
     */
    public static function incrementCount(
        Model $engageable,
        string $eventType,
        ?Carbon $date = null,
        int $incrementBy = 1
    ): self {
        $date = $date ?? Carbon::today();

        $aggregate = self::query()
            ->where('engageable_id', $engageable->getKey())
            ->where('engageable_type', $engageable->getMorphClass())
            ->where('event_type', $eventType)
            ->where('date', $date->toDateString())
            ->first();

        if (!$aggregate) {
            $aggregate = new self([
                'event_type' => $eventType,
                'date' => $date->toDateString(),
                'count' => $incrementBy,
            ]);
            $aggregate->engageable()->associate($engageable);
        } else {
            $aggregate->count += $incrementBy;
        }

        $aggregate->save();

        return $aggregate;
    }
}
