<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class FilterViewExport extends Model implements HasMedia
{
    use InteractsWithMedia;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'resource_type',
        'columns',
        'filters',
        'date_filter',
        'status',
        'total_records',
        'error',
        'filename',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'columns' => 'array',
        'filters' => 'array',
        'date_filter' => 'array',
        'total_records' => 'integer',
    ];

    /**
     * Get the user that owns the export.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Register media collections for the model.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('csv_exports')
            ->singleFile()
            ->useDisk('local')
            ->acceptsMimeTypes(['text/csv', 'application/csv', 'text/plain']);
    }

    /**
     * Get the download URL for the CSV file.
     */
    public function getDownloadUrl(): ?string
    {
        $media = $this->getFirstMedia('csv_exports');
        return $media ? $media->getTemporaryUrl(now()->addDay()) : null;
    }

    /**
     * Check if the export is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if the export has failed.
     */
    public function hasFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if the export is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }
}
