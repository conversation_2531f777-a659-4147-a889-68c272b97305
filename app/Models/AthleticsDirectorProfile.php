<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AthleticsDirectorProfile extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'contact_id',
        'school_id',
        'is_verified',
        'verified_at',
        'verified_by',
        'rejected_at',
        'rejected_by',
    ];

    protected $casts = [
        'is_verified' => 'boolean',
        'verified_at' => 'datetime',
        'rejected_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function contact(): BelongsTo
    {
        return $this->belongsTo(Contact::class);
    }

    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    public function verifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    public function verify(User $verifier): void
    {
        $this->is_verified = true;
        $this->verified_at = now();
        $this->verified_by = $verifier->id;
        $this->save();
    }

    public function reject(User $rejector): void
    {
        $this->is_verified = false;
        $this->rejected_at = now();
        $this->rejected_by = $rejector->id;
        $this->save();
    }
}
