<?php

namespace App\Models;

use App\Enums\QuestionType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Question extends Model
{
    use HasFactory;

    /**
     * @var list<string>
     */
    protected $fillable = [
        'test_id',
        'question',
        'type',
    ];

    protected $casts = [
        'type' => QuestionType::class,
    ];

    /**
     * The test this question belongs to.
     *
     * @return BelongsTo
     */
    public function test(): BelongsTo
    {
        return $this->belongsTo(Test::class);
    }

    /**
     * The possible answers for this question (if multiple choice).
     *
     * @return HasMany
     */
    public function answers(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Answer::class);
    }

    /**
     * The user responses to this question.
     *
     * @return HasMany
     */
    public function responses(): HasMany
    {
        return $this->hasMany(QuestionResponse::class, 'question_id', 'id');
    }
}
