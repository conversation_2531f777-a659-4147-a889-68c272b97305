<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

/**
 * App\Models\CustomSport
 *
 * Represents a user-defined custom sport type for their profile.
 */
class CustomSport extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'user_id',
        'name',
        'order',
    ];

    /**
     * The user who created this custom sport.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the awards associated with this custom sport through the polymorphic relationship.
     *
     * @return MorphMany
     */
    public function awards(): MorphMany
    {
        return $this->morphMany(Award::class, 'sportable');
    }
}
