<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Builder;

class FilterView extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'resource_type',
        'filter_definition',
        'user_id',
        'is_public',
    ];

    protected $casts = [
        'filter_definition' => 'json',
        'is_public' => 'boolean',
    ];

    /**
     * Get the user that owns the filter view.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the groups this filter view is shared with.
     */
    public function sharedGroups(): BelongsToMany
    {
        return $this->belongsToMany(Group::class, 'filter_view_shares', 'filter_view_id', 'group_id')
            ->withTimestamps();
    }

    /**
     * Scope a query to only include filter views accessible by the given user.
     * This includes:
     * - Filter views owned by the user
     * - Public filter views
     * - Filter views shared with the user's groups
     */
    public function scopeAccessibleBy(Builder $query, User $user): Builder
    {
        return $query->where(function (Builder $query) use ($user) {
            $query->where('user_id', $user->id) // User owns the view
                ->orWhere('is_public', true)    // View is public
                ->orWhereExists(function ($query) use ($user) {
                    $query->select('filter_view_shares.id')
                        ->from('filter_view_shares')
                        ->join('group_user', 'filter_view_shares.group_id', '=', 'group_user.group_id')
                        ->where('group_user.user_id', $user->id)
                        ->whereColumn('filter_view_shares.filter_view_id', 'filter_views.id');
                }); // View is shared with user's groups
        });
    }
}
