<?php

namespace App\Models;

use App\Enums\ProfileType;
use App\Enums\UiRegion;
use App\Models\Concerns\HasNotesAndTags;
use App\Services\AdvertisementService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Advertisement extends Model implements HasMedia
{
    use HasFactory, SoftDeletes, InteractsWithMedia, HasNotesAndTags;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'user_id',
        'is_listed',
        'copy',
        'cta_text',
        'cta_url',
        'impressions',
        'clicks',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_listed' => 'boolean',
        'impressions' => 'integer',
        'clicks' => 'integer',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = [
        'click_through_rate',
    ];

    /**
     * Register media collections for the model.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('logo')
            ->useDisk('public')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/jpg'])
            ->registerMediaConversions(function (Media $media) {
                $this->addMediaConversion('thumbnail')
                    ->width(150)
                    ->height(150)
                    ->sharpen(10)
                    ->nonQueued();

                $this->addMediaConversion('full')
                    ->width(600)
                    ->height(600)
                    ->sharpen(10)
                    ->nonQueued();
            });

        $this->addMediaCollection('background')
            ->useDisk('public')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/jpg'])
            ->registerMediaConversions(function (Media $media) {
                $this->addMediaConversion('thumbnail')
                    ->width(400)
                    ->height(300)
                    ->sharpen(10)
                    ->nonQueued();

                $this->addMediaConversion('full')
                    ->width(1200)
                    ->height(800)
                    ->sharpen(10)
                    ->nonQueued();
            });
    }

    /**
     * Register media conversions when not handling a specific file.
     */
    public function registerMediaConversions(Media $media = null): void
    {
        // Register any global conversions here if needed
    }

    /**
     * Get the sponsor user associated with the advertisement.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the regions associated with the advertisement.
     */
    public function regions(): BelongsToMany
    {
        return $this->belongsToMany(Region::class, 'advertisement_region', 'advertisement_id', 'region_id')
            ->select('regions.*', 'advertisement_region.region_id');
    }

    /**
     * Get the markets associated with the advertisement.
     */
    public function markets(): BelongsToMany
    {
        return $this->belongsToMany(Market::class, 'advertisement_market', 'advertisement_id', 'market_id')
            ->select('markets.*', 'advertisement_market.market_id');
    }

    /**
     * Get the sub-regions associated with the advertisement.
     */
    public function subRegions(): BelongsToMany
    {
        return $this->belongsToMany(SubRegion::class, 'advertisement_sub_region', 'advertisement_id', 'sub_region_id')
            ->select('sub_regions.*', 'advertisement_sub_region.sub_region_id');
    }

    /**
     * Get the counties associated with the advertisement.
     */
    public function counties(): BelongsToMany
    {
        return $this->belongsToMany(County::class, 'advertisement_county', 'advertisement_id', 'county_id')
            ->select('counties.*', 'advertisement_county.county_id');
    }

    /**
     * Get the states associated with the advertisement.
     */
    public function states(): BelongsToMany
    {
        return $this->belongsToMany(State::class, 'advertisement_state', 'advertisement_id', 'state_code')
            ->select('states.*', 'advertisement_state.state_code');
    }

    /**
     * Get the profile types this advertisement targets.
     */
    public function profileTypes()
    {
        return $this->hasMany(AdvertisementProfileType::class);
    }

    /**
     * Get the UI regions this advertisement can be displayed in.
     */
    public function uiRegions()
    {
        return $this->hasMany(AdvertisementUiRegion::class);
    }

    /**
     * Get the engagement records associated with this advertisement.
     */
    public function engagements(): MorphMany
    {
        return $this->morphMany(Engagement::class, 'engageable');
    }

    /**
     * Get the engagement aggregates associated with this advertisement.
     */
    public function engagementAggregates(): MorphMany
    {
        return $this->morphMany(EngagementAggregate::class, 'engageable');
    }

    /**
     * Scope a query to only include active advertisements.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_listed', true);
    }

    /**
     * Scope advertisements by profile type
     */
    public function scopeForProfileType(Builder $query, ProfileType $profileType): Builder
    {
        return $query->whereHas('profileTypes', function (Builder $q) use ($profileType) {
            $q->where('profile_type', $profileType->value);
        });
    }

    /**
     * Scope a query to filter advertisements by UI region.
     */
    public function scopeForUiRegion(Builder $query, UiRegion $uiRegion): Builder
    {
        return $query->whereHas('uiRegions', function ($query) use ($uiRegion) {
            $query->where('ui_region', $uiRegion->value);
        });
    }

    /**
     * Scope a query to filter advertisements by geographic region.
     */
    public function scopeInRegion(Builder $query, int $regionId): Builder
    {
        return $query->whereHas('regions', function ($query) use ($regionId) {
            $query->where('region_id', $regionId);
        });
    }

    /**
     * Scope a query to filter advertisements by market.
     */
    public function scopeInMarket(Builder $query, int $marketId): Builder
    {
        return $query->whereHas('markets', function ($query) use ($marketId) {
            $query->where('market_id', $marketId);
        });
    }

    /**
     * Scope a query to filter advertisements by sub-region.
     */
    public function scopeInSubRegion(Builder $query, int $subRegionId): Builder
    {
        return $query->whereHas('subRegions', function ($query) use ($subRegionId) {
            $query->where('sub_region_id', $subRegionId);
        });
    }

    /**
     * Scope a query to filter advertisements by county.
     */
    public function scopeInCounty(Builder $query, int $countyId): Builder
    {
        return $query->whereHas('counties', function ($query) use ($countyId) {
            $query->where('county_id', $countyId);
        });
    }

    /**
     * Scope a query to filter advertisements by state.
     */
    public function scopeInState(Builder $query, string $stateCode): Builder
    {
        return $query->whereHas('states', function ($query) use ($stateCode) {
            $query->where('state_code', $stateCode);
        });
    }

    /**
     * Get the click-through rate for this advertisement.
     */
    public function getClickThroughRateAttribute(): float
    {
        if ($this->impressions === 0) {
            return 0.0;
        }

        return ($this->clicks / $this->impressions) * 100;
    }

    /**
     * Add a profile type to this advertisement.
     *
     * @param ProfileType $profileType
     * @return AdvertisementProfileType
     */
    public function addProfileType(ProfileType $profileType): AdvertisementProfileType
    {
        return $this->profileTypes()->firstOrCreate([
            'profile_type' => $profileType->value,
        ]);
    }

    /**
     * Add a UI region to this advertisement.
     *
     * @param UiRegion $uiRegion
     * @return AdvertisementUiRegion
     */
    public function addUiRegion(UiRegion $uiRegion): AdvertisementUiRegion
    {
        return $this->uiRegions()->firstOrCreate([
            'ui_region' => $uiRegion->value,
        ]);
    }

    /**
     * Record an impression for this advertisement.
     *
     * Note: This is kept for backward compatibility but will be deprecated.
     * Use EngagementService::recordImpression() instead.
     *
     * @return self
     */
    public function recordImpression(): self
    {
        $this->increment('impressions');
        return $this;
    }

    /**
     * Record a click for this advertisement.
     *
     * Note: This is kept for backward compatibility but will be deprecated.
     * Use EngagementService::recordClick() instead.
     *
     * @return self
     */
    public function recordClick(): self
    {
        $this->increment('clicks');
        return $this;
    }

    /**
     * Check if this advertisement has any geographic targeting.
     */
    public function hasGeographicTargeting(): bool
    {
        return $this->regions()->exists() ||
            $this->markets()->exists() ||
            $this->subRegions()->exists() ||
            $this->counties()->exists() ||
            $this->states()->exists();
    }

    /**
     * Get all region names for this advertisement as a comma-separated string.
     */
    public function getRegionNamesAttribute(): string
    {
        return $this->regions()->pluck('name')->implode(', ');
    }

    /**
     * Get all market names for this advertisement as a comma-separated string.
     */
    public function getMarketNamesAttribute(): string
    {
        return $this->markets()->pluck('name')->implode(', ');
    }

    /**
     * Get all sub-region names for this advertisement as a comma-separated string.
     */
    public function getSubRegionNamesAttribute(): string
    {
        return $this->subRegions()->pluck('name')->implode(', ');
    }

    /**
     * Get all county names for this advertisement as a comma-separated string.
     */
    public function getCountyNamesAttribute(): string
    {
        return $this->counties()->pluck('name')->implode(', ');
    }

    /**
     * Get all state names for this advertisement as a comma-separated string.
     */
    public function getStateNamesAttribute(): string
    {
        return $this->states()->pluck('name')->implode(', ');
    }

    /**
     * Validate the geographic relationships for this advertisement.
     *
     * @return bool
     */
    public function validateGeographicRelationships(): bool
    {
        $service = app(\App\Services\GeographicRelationsService::class);

        return $service->isValidCombination(
            $this->regions()->pluck('id')->toArray(),
            $this->markets()->pluck('id')->toArray(),
            $this->states()->pluck('code')->toArray(),
            $this->subRegions()->pluck('id')->toArray(),
            $this->counties()->pluck('id')->toArray()
        );
    }

    /**
     * Get the full geographic path for this advertisement.
     *
     * @return array
     */
    public function getGeographicPath(): array
    {
        $service = app(\App\Services\GeographicRelationsService::class);

        $entity = new \stdClass();

        if ($this->regions()->count() === 1) {
            $entity->region_id = $this->regions()->first()->id;
        }

        if ($this->markets()->count() === 1) {
            $entity->market_id = $this->markets()->first()->id;
        }

        if ($this->subRegions()->count() === 1) {
            $entity->sub_region_id = $this->subRegions()->first()->id;
        }

        if ($this->counties()->count() === 1) {
            $entity->county_id = $this->counties()->first()->id;
        }

        if ($this->states()->count() === 1) {
            $entity->state_code = $this->states()->first()->code;
        }

        return $service->getGeographicPath($entity);
    }

    /**
     * Get a summary of this advertisement's geographic targeting.
     *
     * @return string
     */
    public function getGeographicTargetingSummary(): string
    {
        $parts = [];

        if ($this->regions()->exists()) {
            $parts[] = 'Regions: ' . $this->region_names;
        }

        if ($this->markets()->exists()) {
            $parts[] = 'Markets: ' . $this->market_names;
        }

        if ($this->states()->exists()) {
            $parts[] = 'States: ' . $this->state_names;
        }

        if ($this->subRegions()->exists()) {
            $parts[] = 'Sub-Regions: ' . $this->sub_region_names;
        }

        if ($this->counties()->exists()) {
            $parts[] = 'Counties: ' . $this->county_names;
        }

        return empty($parts) ? 'Global (No Geographic Targeting)' : implode(' | ', $parts);
    }
}
