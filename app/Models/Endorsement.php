<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * @OA\Schema(
 *     schema="Endorsement",
 *     title="Endorsement",
 *     description="Represents a type of endorsement that can be given to a user.",
 *     @OA\Property(property="id", type="integer", example=1, description="Primary key ID"),
 *     @OA\Property(property="name", type="string", example="Leadership", description="The name of the endorsement category"),
 *     @OA\Property(property="icon", type="string", nullable=true, example="sun-bright", description="The FontAwesome icon identifier for the endorsement"),
 *     @OA\Property(property="description", type="string", nullable=true, example="Demonstrates strong leadership skills.", description="Optional description of the endorsement category"),
 *     @OA\Property(property="created_at", type="string", format="date-time", example="2023-01-01T12:00:00Z", description="Timestamp when the record was created"),
 *     @OA\Property(property="updated_at", type="string", format="date-time", example="2023-01-01T12:30:00Z", description="Timestamp when the record was last updated")
 * )
 */
class Endorsement extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'icon',
    ];

    /**
     * Users who have this endorsement, along with the endorser who gave it.
     * This is a many-to-many-like relationship with additional pivot data (endorser_id, relation).
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_endorsement')
            ->withPivot(['endorser_id', 'relation'])
            ->withTimestamps();
    }
}
