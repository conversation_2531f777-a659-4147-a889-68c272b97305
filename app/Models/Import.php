<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Import extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'batch_id',
        'type',
        'file_path',
        'file_name',
        'status',
        'started_at',
        'completed_at',
        'total_rows',
        'created_count',
        'updated_count',
        'failed_count',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'metadata' => 'json',
        'total_rows' => 'integer',
        'created_count' => 'integer',
        'updated_count' => 'integer',
        'failed_count' => 'integer',
    ];

    /**
     * Get the records for the import.
     */
    public function records(): HasMany
    {
        return $this->hasMany(ImportRecord::class);
    }

    /**
     * Get records by status.
     *
     * @param string $status
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getRecordsByStatus(string $status)
    {
        return $this->records()->where('status', $status)->get();
    }

    /**
     * Get created records.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getCreatedRecords()
    {
        return $this->getRecordsByStatus('created');
    }

    /**
     * Get updated records.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getUpdatedRecords()
    {
        return $this->getRecordsByStatus('updated');
    }

    /**
     * Get failed records.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getFailedRecords()
    {
        return $this->getRecordsByStatus('failed');
    }
}
