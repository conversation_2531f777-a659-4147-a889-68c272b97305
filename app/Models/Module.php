<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Carbon;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use App\Enums\ModuleType;
use App\Enums\TestType;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

/**
 * @OA\Schema(
 *     schema="Module",
 *     title="Module",
 *     description="Represents a module within a course, containing learning materials.",
 *     @OA\Property(property="id", type="integer", format="int64", description="Unique identifier for the module", readOnly=true, example=1),
 *     @OA\Property(property="title", type="string", description="Title of the module", example="Understanding Leadership Styles"),
 *     @OA\Property(property="description", type="string", nullable=true, description="Short description of the module", example="Explore different approaches to leadership."),
 *     @OA\Property(property="content", type="string", nullable=true, description="HTML content for article modules"),
 *     @OA\Property(property="slug", type="string", description="URL-friendly identifier", readOnly=true, example="understanding-leadership-styles"),
 *     @OA\Property(property="minutes", type="integer", nullable=true, description="Estimated duration in minutes", example=15),
 *     @OA\Property(property="video_url", type="string", format="url", nullable=true, description="URL for video modules", example="https://vimeo.com/123456789"),
 *     @OA\Property(property="video_start_time", type="integer", nullable=true, description="Start time in seconds for video segment", example=0),
 *     @OA\Property(property="video_end_time", type="integer", nullable=true, description="End time in seconds for video segment", example=900),
 *     @OA\Property(property="video_thumbnail_url", type="string", format="url", nullable=true, description="Thumbnail URL for video", example="https://example.com/thumbnails/video.jpg"),
 *     @OA\Property(property="order", type="integer", description="Display order within a course context", example=1),
 *     @OA\Property(property="published", type="boolean", description="Whether the module is published", example=true),
 *     @OA\Property(property="type", ref="#/components/schemas/ModuleType"),
 *     @OA\Property(property="created_at", type="string", format="date-time", description="Timestamp when the module was created", readOnly=true, example="2023-01-01T12:00:00Z"),
 *     @OA\Property(property="updated_at", type="string", format="date-time", description="Timestamp when the module was last updated", readOnly=true, example="2023-01-10T15:30:00Z"),
 *     @OA\Property(property="cover_image_url", type="string", format="url", nullable=true, description="URL of the module cover image", readOnly=true, example="https://example.com/media/modules/1/cover.jpg"),
 *     @OA\Property(property="cover_image_thumb_url", type="string", format="url", nullable=true, description="URL of the module cover image thumbnail", readOnly=true, example="https://example.com/media/modules/1/conversions/thumb.jpg")
 * )
 *
 * App\Models\Module
 *
 * Represents a module within a course, containing learning materials.
 */
class Module extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'course_id',
        'name',
        'description',
        'content',
        'slug',
        'minutes',
        'video_url',
        'video_start_time',
        'video_end_time',
        'video_thumbnail_url',
        'order',
        'published',
        'type',
    ];

    protected $casts = [
        'minutes' => 'integer',
        'order' => 'integer',
        'published' => 'boolean',
        'duration_minutes' => 'integer',
        'completed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'type' => ModuleType::class,
        'video_start_time' => 'integer',
        'video_end_time' => 'integer',
    ];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('cover')
            ->registerMediaConversions(function (Media $media) {
                $this->addMediaConversion('thumb')
                    ->nonQueued()
                    ->width(400)
                    ->height(300);

                $this->addMediaConversion('full')
                    ->nonQueued()
                    ->width(1200)
                    ->height(800);
            });

        $this->addMediaCollection('article_images')
            ->registerMediaConversions(function (Media $media) {
                $this->addMediaConversion('thumb')
                    ->nonQueued()
                    ->width(400)
                    ->height(300);

                $this->addMediaConversion('full')
                    ->nonQueued()
                    ->width(1200)
                    ->height(800);
            });
    }

    /**
     * The course this module belongs to.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * The users who have completed (or are assigned) this module.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class)
            ->using(ModuleUser::class)
            ->withPivot(['started_at', 'completed_at', 'last_position', 'completion_metadata'])
            ->withTimestamps();
    }

    /**
     * The assignments for this module.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function assignments(): HasMany
    {
        return $this->hasMany(Assignment::class);
    }

    /**
     * Tests associated with this module (via morphMany).
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function tests(): MorphMany
    {
        return $this->morphMany(Test::class, 'testable');
    }

    /**
     * Get the latest test associated with this module.
     * Note: This is kept for backward compatibility. Consider using tests() for new code.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphOne
     */
    public function test(): \Illuminate\Database\Eloquent\Relations\MorphOne
    {
        return $this->morphOne(Test::class, 'testable');
    }

    /**
     * Get the courses this module belongs to.
     */
    public function courses(): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    {
        return $this->belongsToMany(Course::class)
            ->withPivot('order')
            ->withTimestamps();
    }

    /**
     * The topics associated with this module.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function topics(): BelongsToMany
    {
        return $this->belongsToMany(Topic::class)
            ->using(ModuleTopic::class)
            ->withTimestamps();
    }

    /**
     * Get the primary topic associated with this module.
     * While the database supports many-to-many, business logic expects one topic per module.
     * This relationship returns the first associated topic.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function topic(): BelongsToMany
    {
        return $this->belongsToMany(Topic::class)
            ->using(ModuleTopic::class)
            ->withTimestamps()
            ->oldest('module_topic.created_at')
            ->take(1)
            ->as('topic_pivot');
    }

    public function isCompletedByUser(User $user): bool
    {
        return $this->users()
            ->wherePivot('user_id', $user->id)
            ->wherePivotNotNull('completed_at')
            ->exists();
    }

    public function markAsStartedForUser(User $user): void
    {
        $this->users()->syncWithoutDetaching([
            $user->id => [
                'started_at' => Carbon::now(),
            ]
        ]);
    }

    public function markAsCompletedForUser(User $user, ?array $metadata = null): void
    {
        $this->users()->syncWithoutDetaching([
            $user->id => [
                'completed_at' => Carbon::now(),
                'completion_metadata' => $metadata,
            ]
        ]);

        // Update course completion percentage
        $this->courses->first()->calculateCompletionForUser($user);
    }

    public function updateProgressForUser(User $user, int $position): void
    {
        $this->users()->syncWithoutDetaching([
            $user->id => [
                'last_position' => $position,
            ]
        ]);
    }

    /**
     * Scope a query to only include published modules.
     */
    public function scopePublished($query)
    {
        return $query->where('published', true);
    }

    /**
     * Get the module type based on its content and associated test.
     * This method is kept for backward compatibility and will be removed in future versions.
     * Use the type property directly instead.
     *
     * @deprecated
     */
    public function determineType(): ModuleType
    {
        // Use the explicitly set type first
        if ($this->type) {
            return $this->type;
        }

        // Fallback logic for legacy data
        if ($this->tests()->where('type', 'exam')->exists()) {
            return ModuleType::Exam;
        }

        if ($this->video_url) {
            return ModuleType::Video;
        }

        if ($this->content) {
            return ModuleType::Article;
        }

        return ModuleType::default();
    }

    /**
     * Scope a query to only include modules of a specific type.
     */
    public function scopeOfType($query, ModuleType $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get all tags for this module.
     *
     */
    public function tags(): MorphToMany
    {
        return $this->morphToMany(Tag::class, 'taggable')
            ->withTimestamps()
            ->using(Taggable::class)
            ->select(['tags.id', 'tags.name']);
    }
}
