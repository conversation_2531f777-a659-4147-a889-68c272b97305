<?php

namespace App\Models;

use App\Data\Resume\ResumeSectionData;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\LaravelData\WithData;

class ResumeSection extends Model
{
    use WithData;

    protected $dataClass = ResumeSectionData::class;

    protected $fillable = [
        'resume_id',
        'section_type',
        'is_enabled',
        'content',
    ];

    protected $casts = [
        'is_enabled' => 'boolean',
        'content' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function resume(): BelongsTo
    {
        return $this->belongsTo(Resume::class);
    }
}
