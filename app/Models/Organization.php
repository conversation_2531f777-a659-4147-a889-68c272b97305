<?php

namespace App\Models;

use App\Enums\ProfileType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use App\Models\Concerns\HasNotesAndTags;


class Organization extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;
    use HasNotesAndTags;

    /**
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'type',
        'user_id',
        'notes',
        'website',
        'about',
    ];

    /**
     * The opportunities offered by this organization.
     *
     * @return HasMany
     */
    public function opportunities(): HasMany
    {
        return $this->hasMany(Opportunity::class);
    }

    /**
     * Get the contacts that belong to this organization.
     *
     * @return HasMany
     */
    public function contacts(): Has<PERSON>any
    {
        return $this->hasMany(Contact::class);
    }

    /**
     * The user that created this organization.
     * Note: This is kept for backwards compatibility. Use sponsors() for accessing all sponsors.
     *
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get all sponsor users associated with this organization.
     *
     * @return BelongsToMany
     */
    public function sponsors(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'organization_sponsor')
            ->where('profile_type', ProfileType::SPONSOR->value)
            ->withPivot(['role', 'metadata', 'deactivated_at'])
            ->withTimestamps();
    }

    /**
     * Get only active sponsor users associated with this organization.
     *
     * @return BelongsToMany
     */
    public function activeSponsors(): BelongsToMany
    {
        return $this->sponsors()
            ->wherePivotNull('deactivated_at');
    }

    /**
     * Get only inactive (historical) sponsor users associated with this organization.
     *
     * @return BelongsToMany
     */
    public function inactiveSponsors(): BelongsToMany
    {
        return $this->sponsors()
            ->wherePivotNotNull('deactivated_at');
    }

    /**
     * Get all users associated with this organization.
     *
     * @return BelongsToMany
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'organization_sponsor')
            ->withPivot(['role', 'metadata', 'deactivated_at'])
            ->withTimestamps();
    }

    /**
     * Register media collections for the organization.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('logo')
            ->useDisk('public')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/jpg', 'image/svg+xml'])
            ->singleFile()
            ->registerMediaConversions(function (Media $media) {
                $this->addMediaConversion('thumbnail')
                    ->width(200)
                    ->height(200)
                    ->nonQueued();

                $this->addMediaConversion('full')
                    ->width(800)
                    ->height(800)
                    ->nonQueued();
            });
    }

    /**
     * Register media conversions for the organization.
     */
    public function registerMediaConversions(Media $media = null): void
    {
        // Register any global conversions here if needed
    }
}
