<?php

namespace App\Models;

use App\Enums\OpportunityLocationType;
use App\Enums\OpportunityStatus;
use App\Enums\OpportunitySubtype;
use App\Enums\OpportunityTerm;
use App\Enums\OpportunityType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Laravel\Scout\Searchable;
use App\Models\LocationCoordinate;

class Opportunity extends Model
{
    use HasFactory, Searchable;

    /**
     * @var list<string>
     */
    protected $fillable = [
        'title',
        'description',
        'qualifications',
        'responsibilities',
        'benefits',
        'location',
        'type',
        'status',
        'apply_url',
        'preferred_graduation_year',
        'preferred_state',
        'organization_id',
        'subtype',
        'city',
        'state_code',
        'details',
        'term',
        'location_type',
        'is_featured',
        'visible_start_date',
        'visible_end_date',
        'user_id',
        'location_coordinate_id',
        'preferred_graduation_year_start',
        'preferred_graduation_year_end',
        'preferred_states',
        'admin_disabled',
        'admin_disabled_at',
        'admin_disabled_by',
    ];

    protected $casts = [
        'type' => OpportunityType::class,
        'subtype' => OpportunitySubtype::class,
        'location_type' => OpportunityLocationType::class,
        'term' => OpportunityTerm::class,
        'status' => OpportunityStatus::class,
        'is_featured' => 'boolean',
        'visible_start_date' => 'datetime',
        'visible_end_date' => 'datetime',
        'preferred_graduation_year_start' => 'integer',
        'preferred_graduation_year_end' => 'integer',
        'preferred_states' => 'array',
        'admin_disabled' => 'boolean',
        'admin_disabled_at' => 'datetime',
    ];

    /**
     * Determine if the model should be searchable.
     *
     * @return bool
     */
    public function shouldBeSearchable(): bool
    {
        // Only index opportunities that are listed and not admin-disabled
        return $this->status === OpportunityStatus::LISTED && !$this->admin_disabled;
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array<string, mixed>
     */
    public function toSearchableArray(): array
    {
        // Load the industries relationship for indexing
        $this->loadMissing('industries', 'interests', 'organization', 'state', 'locationCoordinate');

        // Get coordinates for the location if available
        $coordinates = $this->getCoordinates();

        $searchableArray = [
            'id' => $this->id,
            // Primary searchable content
            'title' => $this->title ?? '',
            'description' => $this->description ?? '',
            'details' => $this->details ?? '',
            'qualifications' => $this->qualifications ?? '',
            'responsibilities' => $this->responsibilities ?? '',
            'benefits' => $this->benefits ?? '',

            // Filterable attributes
            'type' => $this->type?->value ?? '',
            'subtype' => $this->subtype?->value ?? '',
            'status' => $this->status ?? '',
            'location_type' => $this->location_type?->value ?? '',
            'term' => $this->term?->value ?? '',
            'is_featured' => $this->is_featured ?? false,

            // Location data (both searchable and filterable)
            'location' => $this->location ?? '', // Our computed City, ST format
            'city' => $this->city ?? '',
            'state_code' => $this->state_code ?? '',
            'state_name' => $this->state?->name ?? '',
            'location_coordinate_id' => $this->location_coordinate_id ?? null,

            // Related entities data
            'organization_name' => $this->organization?->name ?? '',
            'organization_logo' => $this->organization?->getFirstMediaUrl('logo', 'thumbnail') ?: '',
            'industries' => $this->industries ? $this->industries->pluck('name')->toArray() : [],
            'interests' => $this->interests ? $this->interests->map(fn(Interest $interest) => [
                'id' => $interest->id ?? 0,
                'name' => $interest->name ?? '',
                'icon' => $interest->icon ?? '',
            ])->toArray() : [],

            // Sortable timestamps
            'created_at' => $this->created_at?->timestamp ?? null,
            'updated_at' => $this->updated_at?->timestamp ?? null,
            'visible_start_date' => $this->visible_start_date?->timestamp ?? null,
            'visible_end_date' => $this->visible_end_date?->timestamp ?? null,

            // Visibility settings
            'preferred_graduation_year_start' => $this->preferred_graduation_year_start ?? null,
            'preferred_graduation_year_end' => $this->preferred_graduation_year_end ?? null,
            'preferred_states' => $this->preferred_states ?? [],
        ];

        // Add geolocation data if available
        if ($coordinates) {
            $searchableArray['_geo'] = [
                'lat' => $coordinates['lat'],
                'lng' => $coordinates['lng'],
            ];
        }

        return $searchableArray;
    }

    /**
     * Get coordinates for the opportunity location.
     *
     * @return array|null
     */
    protected function getCoordinates(): ?array
    {
        // If we have a location coordinate, use its lat/lng directly
        if ($this->locationCoordinate) {
            return [
                'lat' => $this->locationCoordinate->latitude,
                'lng' => $this->locationCoordinate->longitude,
            ];
        }

        // Fall back to looking up by city/state if we don't have a direct reference
        if (!$this->city || !$this->state_code) {
            return null;
        }

        return LocationCoordinate::getCoordinates($this->city, $this->state_code);
    }

    /**
     * The organization offering this opportunity.
     *
     * @return BelongsTo
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * The state where this opportunity is located.
     *
     * @return BelongsTo
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'state_code', 'code');
    }

    /**
     * The location coordinate for this opportunity.
     *
     * @return BelongsTo
     */
    public function locationCoordinate(): BelongsTo
    {
        return $this->belongsTo(LocationCoordinate::class, 'location_coordinate_id');
    }

    /**
     * The industries associated with this opportunity.
     *
     * @return BelongsToMany
     */
    public function industries(): BelongsToMany
    {
        return $this->belongsToMany(Industry::class, 'opportunity_industry')->withTimestamps();
    }

    /**
     * The interests associated with this opportunity.
     *
     * @return BelongsToMany
     */
    public function interests(): BelongsToMany
    {
        return $this->belongsToMany(Interest::class)->withTimestamps();
    }

    /**
     * The users who have bookmarked this opportunity.
     *
     * @return BelongsToMany
     */
    public function bookmarkedBy(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'opportunity_bookmarks')->withTimestamps();
    }

    /**
     * The sponsor user who created/owns this opportunity.
     *
     * @return BelongsTo
     */
    public function sponsor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * The admin user who disabled this opportunity.
     *
     * @return BelongsTo
     */
    public function adminDisabledBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'admin_disabled_by');
    }

    /**
     * Check if the opportunity is effectively listed (considering admin override).
     *
     * @return bool
     */
    public function isEffectivelyListed(): bool
    {
        // If admin disabled, it's not listed regardless of sponsor status
        if ($this->admin_disabled) {
            return false;
        }

        // If not admin disabled, check sponsor status
        return $this->status === OpportunityStatus::LISTED;
    }

    /**
     * Check if the opportunity is admin disabled.
     *
     * @return bool
     */
    public function isAdminDisabled(): bool
    {
        return $this->admin_disabled;
    }

    /**
     * Check if the opportunity can be seen by public users.
     * This is the main method that should be used for filtering opportunities
     * in public-facing queries.
     *
     * @return bool
     */
    public function isPubliclyVisible(): bool
    {
        return $this->isEffectivelyListed();
    }

    /**
     * Get the formatted location string.
     *
     * @return string|null
     */
    public function getLocationAttribute(): ?string
    {
        if ($this->city && $this->state_code) {
            return "{$this->city}, {$this->state_code}";
        }

        return null;
    }

    /**
     * Get the engagement records associated with this opportunity.
     */
    public function engagements(): MorphMany
    {
        return $this->morphMany(Engagement::class, 'engageable');
    }

    /**
     * Get the engagement aggregates associated with this opportunity.
     */
    public function engagementAggregates(): MorphMany
    {
        return $this->morphMany(EngagementAggregate::class, 'engageable');
    }
}
