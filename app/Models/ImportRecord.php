<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ImportRecord extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'import_id',
        'row_number',
        'status',
        'raw_data',
        'processed_data',
        'model_type',
        'model_id',
        'errors',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'raw_data' => 'json',
        'processed_data' => 'json',
        'errors' => 'json',
        'row_number' => 'integer',
    ];

    /**
     * Get the import that owns the record.
     */
    public function import(): BelongsTo
    {
        return $this->belongsTo(Import::class);
    }

    /**
     * Get the related model if it exists.
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function getRelatedModel()
    {
        if ($this->model_type && $this->model_id) {
            $modelClass = $this->model_type;
            return $modelClass::find($this->model_id);
        }

        return null;
    }

    /**
     * Determine if the record has errors.
     *
     * @return bool
     */
    public function hasErrors(): bool
    {
        return $this->status === 'failed' && !empty($this->errors);
    }
}
