<?php

namespace App\Models;

use App\Casts\AsInviteData;
use App\Enums\SystemInviteStatus;
use App\Enums\ProfileType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class SystemInvite extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $casts = [
        'expires_at' => 'datetime',
        'invite_data' => AsInviteData::class,
        'status' => SystemInviteStatus::class,
        'type' => ProfileType::class,
    ];

    protected $fillable = [
        'email',
        'token',
        'mobile_code',
        'expires_at',
        'invite_data',
        'status',
        'type',
    ];

    /**
     * Register media collections for the model
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('avatar')
            ->useDisk('public')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/jpg'])
            ->useFallbackUrl('/images/default-avatar.png')
            ->useFallbackPath(public_path('/images/default-avatar.png'));
    }

    /**
     * Register media conversions for the model
     */
    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumbnail')
            ->width(150)
            ->height(150)
            ->sharpen(10)
            ->nonQueued();

        $this->addMediaConversion('full')
            ->width(600)
            ->height(600)
            ->sharpen(10)
            ->nonQueued();
    }

    public function nominator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'nominator_id');
    }

    public function nomination(): BelongsTo
    {
        return $this->belongsTo(Nomination::class);
    }

    public function onboarding(): HasOne
    {
        return $this->hasOne(Onboarding::class);
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending')
            ->where('expires_at', '>', now());
    }

    public function markAsCompleted(): void
    {
        $this->update(['status' => 'completed']);
    }

    public function markAsExpired(): void
    {
        $this->update(['status' => 'expired']);
    }

    public function markAsRevoked(): void
    {
        $this->update(['status' => SystemInviteStatus::REVOKED->value]);
    }

    public function isExpired(): bool
    {
        return $this->expires_at->isPast();
    }
}
