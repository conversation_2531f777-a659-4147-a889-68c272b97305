<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Region extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'color',
    ];

    /**
     * Get the markets in this region.
     */
    public function markets()
    {
        return $this->hasMany(Market::class);
    }

    /**
     * Get all counties in this region through markets.
     */
    public function counties()
    {
        return $this->hasManyThrough(County::class, Market::class);
    }

    /**
     * Get all sub-regions in this region through markets.
     */
    public function subRegions()
    {
        return $this->hasManyThrough(SubRegion::class, Market::class);
    }

    /**
     * Get all schools in this region.
     */
    public function schools()
    {
        return $this->hasMany(School::class);
    }

    /**
     * Get all users in this region.
     */
    public function users()
    {
        return $this->hasMany(User::class);
    }
}
