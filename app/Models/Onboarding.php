<?php

namespace App\Models;

use App\Enums\ProfileType;
use App\States\Onboarding\OnboardingState;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Hash;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\ModelStates\HasStates;

class Onboarding extends Model implements HasMedia
{
    use HasStates;
    use InteractsWithMedia;
    use HasFactory;

    protected $casts = [
        'profile_type' => ProfileType::class,
        'state' => OnboardingState::class,
        'data' => 'array'
    ];

    protected $guarded = [];

    public function systemInvite(): BelongsTo
    {
        return $this->belongsTo(SystemInvite::class);
    }

    public function updateStepData(string $step, array $data): void
    {
        $currentData = $this->data ?? [];

        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        $currentData['steps'][$step] = $data;

        $this->update(['data' => $currentData]);
    }

    public function getStepData(string $step): ?array
    {
        return $this->data['steps'][$step] ?? null;
    }

    public function getNominationId(): ?int
    {
        return $this->data['nomination_id'] ?? null;
    }

    public function getNomination(): ?Nomination
    {
        $nominationId = $this->getNominationId();
        return $nominationId ? Nomination::find($nominationId) : null;
    }
}
