<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

/**
 * App\Models\Sport
 *
 * Represents a type of sport that users can associate with.
 */
class Sport extends Model
{
    use HasFactory;

    /**
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'slug',
    ];

    /**
     * The users who are associated with this sport.
     *
     * @return BelongsToMany
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'sport_user')
            ->withPivot(['order'])
            ->withTimestamps();
    }

    /**
     * Get the awards associated with this sport through the polymorphic relationship.
     *
     * @return MorphMany
     */
    public function awards(): MorphMany
    {
        return $this->morphMany(Award::class, 'sportable');
    }
}
