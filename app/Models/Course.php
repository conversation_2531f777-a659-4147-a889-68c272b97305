<?php

namespace App\Models;

use App\Enums\TestType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * @OA\Schema(
 *     schema="Course",
 *     title="Course",
 *     description="Represents an educational course containing modules, topics, and tests.",
 *     @OA\Property(property="id", type="integer", format="int64", description="Unique identifier for the course", readOnly=true, example=1),
 *     @OA\Property(property="title", type="string", description="Title of the course", example="Introduction to Leadership"),
 *     @OA\Property(property="description", type="string", nullable=true, description="Detailed description of the course", example="Learn the fundamentals of effective leadership."),
 *     @OA\Property(property="published", type="boolean", description="Whether the course is published and accessible", example=true),
 *     @OA\Property(property="order", type="integer", description="Display order of the course", example=1),
 *     @OA\Property(property="presenter", type="string", nullable=true, description="Name of the course presenter", example="Dr. Jane Doe"),
 *     @OA\Property(property="featured", type="boolean", description="Whether the course is featured", example=false),
 *     @OA\Property(property="created_at", type="string", format="date-time", description="Timestamp when the course was created", readOnly=true, example="2023-01-01T12:00:00Z"),
 *     @OA\Property(property="updated_at", type="string", format="date-time", description="Timestamp when the course was last updated", readOnly=true, example="2023-01-10T15:30:00Z"),
 *     @OA\Property(property="total_runtime_minutes", type="integer", description="Total runtime of all published modules in minutes", readOnly=true, example=120),
 *     @OA\Property(property="cover_image_url", type="string", format="url", nullable=true, description="URL of the course cover image", readOnly=true, example="https://example.com/media/courses/1/cover.jpg"),
 *     @OA\Property(property="cover_image_thumb_url", type="string", format="url", nullable=true, description="URL of the course cover image thumbnail", readOnly=true, example="https://example.com/media/courses/1/conversions/thumb.jpg")
 * )
 *
 * App\Models\Course
 *
 * Represents an educational course that can contain modules, topics, and tests.
 *
 * @property-read int|null $total_runtime_minutes Sum of minutes from all published modules
 */
class Course extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'title',
        'description',
        'published',
        'order',
        'presenter',
        'featured',
    ];

    protected $casts = [
        'published' => 'boolean',
        'order' => 'integer',
    ];

    /**
     * Scope to include the total runtime minutes from related modules
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithTotalRuntime(Builder $query): Builder
    {
        return $query->addSelect([
            'total_runtime_minutes' => Module::query()
                ->selectRaw('COALESCE(SUM(minutes), 0)')
                ->join('course_module', 'modules.id', '=', 'course_module.module_id')
                ->whereColumn('course_module.course_id', 'courses.id')
                ->where('modules.published', true)
        ]);
    }

    /**
     * Get the modules associated with the course.
     */
    public function modules(): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    {
        return $this->belongsToMany(Module::class)
            ->withPivot('order')
            ->orderBy('course_module.order')
            ->withTimestamps();
    }

    /**
     * The users who are enrolled in this course.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'course_user')
            ->withPivot(['completed_at', 'last_calculated_completion_percentage'])
            ->withTimestamps();
    }

    /**
     * The topics that are associated with this course.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function topics(): BelongsToMany
    {
        return $this->belongsToMany(Topic::class, 'course_topic')->withTimestamps();
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('cover')
            ->registerMediaConversions(function (Media $media) {
                $this->addMediaConversion('thumb')
                    ->nonQueued()
                    ->width(400)
                    ->height(300);

                $this->addMediaConversion('full')
                    ->nonQueued()
                    ->width(1200)
                    ->height(800);
            });
    }

    public function calculateCompletionForUser(User $user): void
    {
        $totalModules = $this->modules()
            ->where('published', true)
            ->count();

        if ($totalModules === 0) {
            return;
        }

        $completedModules = $this->modules()
            ->where('published', true)
            ->whereHas('users', function ($query) use ($user) {
                $query->where('user_id', $user->id)
                    ->whereNotNull('completed_at');
            })
            ->count();

        // Calculate percentage and floor it to get an integer
        $percentage = (int) floor(($completedModules / $totalModules) * 100);

        $this->users()->syncWithoutDetaching([
            $user->id => [
                'last_calculated_completion_percentage' => $percentage,
                'completed_at' => $percentage === 100 ? now() : null,
            ]
        ]);
    }

    public function isCompletedByUser(User $user): bool
    {
        return $this->users()
            ->wherePivot('user_id', $user->id)
            ->wherePivotNotNull('completed_at')
            ->exists();
    }

    public function getAverageScoreForUser(User $user): int
    {
        // Get all test IDs from modules in this course
        $testIds = Test::query()
            ->where('testable_type', Module::class)
            ->whereIn('testable_id', $this->modules()->pluck('modules.id'))
            ->pluck('id');

        $avg = TestAttempt::query()
            ->whereIn('test_id', $testIds)
            ->where('user_id', $user->id)
            ->avg('score');

        return $avg ? (int) round($avg) : 0;
    }
}
