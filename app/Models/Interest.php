<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * App\Models\Interest
 *
 * Represents an interest that a user can have.
 */
class Interest extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'icon',
    ];

    /**
     * The users who have this interest.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'interest_user')->withTimestamps();
    }

    /**
     * The opportunities related to this interest.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function opportunities(): BelongsToMany
    {
        return $this->belongsToMany(Opportunity::class)->withTimestamps();
    }
}
