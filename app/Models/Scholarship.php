<?php

namespace App\Models;

use App\Models\Concerns\HasNotesAndTags;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

class Scholarship extends Model
{
    use HasFactory, SoftDeletes, HasNotesAndTags;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'year',
        'details',
        'notes',
        'is_active',
        'limit',
        'region_id',
        'market_id',
        'state_id',
        'subregion_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'year' => 'integer',
        'is_active' => 'boolean',
        'limit' => 'integer',
    ];

    /**
     * Get the winners associated with the scholarship.
     */
    public function winners(): HasMany
    {
        return $this->hasMany(Winner::class);
    }

    /**
     * Get the region associated with the scholarship.
     */
    public function region(): BelongsTo
    {
        return $this->belongsTo(Region::class);
    }

    /**
     * Get the market associated with the scholarship.
     */
    public function market(): BelongsTo
    {
        return $this->belongsTo(Market::class);
    }

    /**
     * Get the subregion associated with the scholarship.
     */
    public function subregion(): BelongsTo
    {
        return $this->belongsTo(SubRegion::class, 'subregion_id');
    }

    /**
     * Get the state associated with the scholarship.
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'state_id', 'code');
    }
}
