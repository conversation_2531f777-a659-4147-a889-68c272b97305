<?php

namespace App\Models\Concerns;

use App\Models\Tag;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

trait HasNotesAndTags
{
    public function tags(): MorphToMany
    {
        return $this->morphToMany(Tag::class, 'taggable')
            ->withTimestamps()
            ->using('App\Models\Taggable')
            ->select(['tags.id', 'tags.name']);
    }

    public function syncTags(array $tagIds): void
    {
        $this->tags()->sync($tagIds);
    }

    public function addTag(Tag $tag): void
    {
        $this->tags()->attach($tag);
    }

    public function removeTag(Tag $tag): void
    {
        $this->tags()->detach($tag);
    }
}
