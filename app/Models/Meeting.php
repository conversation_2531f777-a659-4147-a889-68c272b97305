<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * App\Models\Meeting
 *
 * Represents a meeting for a team, owned by a user, with invitees.
 */
class Meeting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'meeting_time',
        'team_id',
        'owner_id',
        'content',
        'location',
    ];

    /**
     * The team this meeting is associated with.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * The user who owns (created) this meeting.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    /**
     * The users invited to this meeting.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function invitees(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'meeting_invitees')->withTimestamps();
    }
}
