<?php

namespace App\Models;

use App\Enums\ConnectionStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Connection extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'requester_id',
        'recipient_id',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => ConnectionStatus::class,
    ];

    /**
     * Get the requester of the connection.
     */
    public function requester(): BelongsTo
    {
        return $this->belongsTo(User::class, 'requester_id');
    }

    /**
     * Get the recipient of the connection.
     */
    public function recipient(): BelongsTo
    {
        return $this->belongsTo(User::class, 'recipient_id');
    }

    /**
     * Scope a query to only include pending connections.
     */
    public function scopePending($query)
    {
        return $query->where('status', ConnectionStatus::PENDING->value);
    }

    /**
     * Scope a query to only include accepted connections.
     */
    public function scopeAccepted($query)
    {
        return $query->where('status', ConnectionStatus::ACCEPTED->value);
    }

    /**
     * Scope a query to only include rejected connections.
     */
    public function scopeRejected($query)
    {
        return $query->where('status', ConnectionStatus::REJECTED->value);
    }

    /**
     * Scope a query to find connections between two users.
     */
    public function scopeBetweenUsers($query, int $userOneId, int $userTwoId)
    {
        return $query->where(function ($q) use ($userOneId, $userTwoId) {
            $q->where('requester_id', $userOneId)
              ->where('recipient_id', $userTwoId);
        })->orWhere(function ($q) use ($userOneId, $userTwoId) {
            $q->where('requester_id', $userTwoId)
              ->where('recipient_id', $userOneId);
        });
    }

    /**
     * Scope a query to find connections for a specific user.
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('requester_id', $userId)
                     ->orWhere('recipient_id', $userId);
    }
}
