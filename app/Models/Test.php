<?php

namespace App\Models;

use App\Enums\TestStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Models\Test
 *
 * Represents a test (exam or quiz) that is polymorphically associated with either a course or a module.
 */
class Test extends Model
{
    use HasFactory;

    /**
     * @var list<string>
     */
    protected $fillable = [
        'type',          // exam, quiz
        'testable_type', // e.g. App\Models\Course or App\Models\Module
        'testable_id',
        'passing_score',
        'time_limit',
    ];

    /**
     * Get the parent testable model (course or module).
     *
     * @return MorphTo
     */
    public function testable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * The questions for this test.
     *
     * @return HasMany
     */
    public function questions(): HasMany
    {
        return $this->hasMany(Question::class);
    }

    /**
     * The attempts made by users on this test.
     *
     * @return HasMany
     */
    public function attempts(): HasMany
    {
        return $this->hasMany(TestAttempt::class);
    }

    /**
     * Get the latest in-progress attempt for this test.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function latestAttempt(): HasOne
    {
        return $this->hasOne(TestAttempt::class)
            ->whereNull('completed_at')
            ->where('status', '!=', TestStatus::Complete)
            ->orderByDesc('created_at');
    }

    /**
     * Get the latest completed attempt for this test.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function lastCompletedAttempt(): HasOne
    {
        return $this->hasOne(TestAttempt::class)
            ->whereNotNull('completed_at')
            ->whereIn('status', [TestStatus::Complete, TestStatus::PendingReview, TestStatus::Graded])
            ->orderByDesc('completed_at');
    }

    public function userAttempts(User $user)
    {
        return $this->attempts()->where('user_id', $user->id)
            ->whereNotNull('completed_at')
            ->whereIn('status', [TestStatus::Complete, TestStatus::PendingReview, TestStatus::Graded]);
    }
}
