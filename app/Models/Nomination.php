<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Enums\NominationStatus;
use App\Enums\NominationType;

/**
 * @OA\Schema(
 *     schema="Nomination",
 *     title="Nomination",
 *     description="Represents a nomination record",
 *     @OA\Property(property="id", type="integer", example=1, description="Primary key ID"),
 *     @OA\Property(property="system_invite_id", type="integer", nullable=true, example=10, description="Foreign key to system_invites table"),
 *     @OA\Property(property="user_id", type="integer", nullable=true, example=1, description="Foreign key to users table"),
 *     @OA\Property(property="nominator_email", type="string", format="email", example="<EMAIL>", description="Email of the nominator"),
 *     @OA\Property(property="nominator_first_name", type="string", nullable=true, example="John", description="First name of the nominator"),
 *     @OA\Property(property="nominator_last_name", type="string", nullable=true, example="Doe", description="Last name of the nominator"),
 *     @OA\Property(property="email", type="string", format="email", example="<EMAIL>", description="Email of the nominated person"),
 *     @OA\Property(property="first_name", type="string", nullable=true, example="Jane", description="First name of the nominated person"),
 *     @OA\Property(property="last_name", type="string", nullable=true, example="Smith", description="Last name of the nominated person"),
 *     @OA\Property(property="school_name", type="string", nullable=true, example="Example High School", description="Name of the school"),
 *     @OA\Property(property="school_id", type="integer", nullable=true, example=5, description="Foreign key to schools table"),
 *     @OA\Property(property="sport", type="string", nullable=true, example="Basketball", description="Sport associated with the nomination"),
 *     @OA\Property(property="relationship", type="string", nullable=true, example="Coach", description="Relationship of the nominator to the nominated"),
 *     @OA\Property(property="note", type="string", nullable=true, example="Excellent leadership skills.", description="Optional note from the nominator"),
 *     @OA\Property(property="status", type="string", enum={"pending", "accepted", "declined"}, example="pending", description="Status of the nomination"),
 *     @OA\Property(property="type", type="string", nullable=true, example="Athlete", description="Type of nomination (e.g., Athlete, Coach)"),
 *     @OA\Property(property="created_at", type="string", format="date-time", example="2023-01-01T12:00:00Z", description="Timestamp when the record was created"),
 *     @OA\Property(property="updated_at", type="string", format="date-time", example="2023-01-01T12:30:00Z", description="Timestamp when the record was last updated")
 * )
 */
class Nomination extends Model
{
    use HasFactory;

    /**
     * @var list<string>
     */
    protected $fillable = [
        'system_invite_id',
        'user_id',
        'nominator_email',
        'nominator_first_name',
        'nominator_last_name',
        'email',
        'first_name',
        'last_name',
        'school_name',
        'school_id',
        'sport',
        'relationship',
        'note',
        'status',
        'type',

        // Location Information
        'state_code',
        'county',

        // Enhanced Contact Information
        'nominee_phone',
        'nominator_phone',

        // Multiple Sports Support
        'sport_2',
        'sport_3',
        'other_sport',

        // Demographic & Academic Information
        'gender',
        'grade',

        // Parent/Guardian Information
        'parent_guardian_first_name',
        'parent_guardian_last_name',
        'parent_guardian_email',
        'parent_guardian_phone',

        // Social Media & Digital Presence
        'instagram_handle',
        'twitter_handle',

        // Marketing & Attribution Data
        'how_did_you_hear',
        'referral_source_name',

        // Processing Workflow Enhancement
        'processing_status',
        'processed_at',

        // JotForm Integration Metadata
        'jotform_submission_id',
        'jotform_form_id',
        'location_resolution_notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => NominationStatus::class,
        'type' => NominationType::class,
        'processed_at' => 'datetime',
        'location_resolution_notes' => 'array',
    ];

    /**
     * The user who made the nomination.
     *
     * @return BelongsTo
     */
    public function nominatorUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'nominator_email', 'email');
    }

    /**
     * The nominated user.
     *
     * @return BelongsTo
     */
    public function nominatedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'email', 'email');
    }

    /**
     * The user associated with this nomination (after onboarding).
     *
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * The system invite.
     *
     * @return BelongsTo
     */
    public function systemInvite(): BelongsTo
    {
        return $this->belongsTo(SystemInvite::class);
    }

    /**
     * The school associated with the nomination.
     *
     * @return BelongsTo
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    /**
     * Get the nominator name.
     *
     * @return string
     */
    public function getNominatorNameAttribute(): string
    {
        if ($this->nominator_first_name || $this->nominator_last_name) {
            return trim("{$this->nominator_first_name} {$this->nominator_last_name}");
        }

        return $this->nominator_email;
    }

    /**
     * Get the formatted grade for display (e.g., "Grade 11").
     * The raw grade is stored as an integer for analytics and comparisons.
     *
     * @return string
     */
    public function getFormattedGradeAttribute(): string
    {
        if (is_null($this->grade)) {
            return '';
        }

        return "Grade {$this->grade}";
    }
}
