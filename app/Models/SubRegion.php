<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubRegion extends Model
{
    use HasFactory;

    protected $fillable = [
        'market_id',
        'name',
        'slug',
    ];

    public function market()
    {
        return $this->belongsTo(Market::class);
    }

    public function counties()
    {
        return $this->hasMany(County::class);
    }

    public function region()
    {
        return $this->hasOneThrough(Region::class, Market::class);
    }
}
