<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Message extends Model implements HasMedia
{
    use InteractsWithMedia, HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'sender_id',
        'recipient_id',
        'content',
        'read_at',
        'deleted_by_sender_at',
        'deleted_by_recipient_at',
        'is_flagged',
        'moderation_result',
        'edited_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'read_at' => 'datetime',
        'deleted_by_sender_at' => 'datetime',
        'deleted_by_recipient_at' => 'datetime',
        'is_flagged' => 'boolean',
        'moderation_result' => 'array',
        'edited_at' => 'datetime',
    ];

    /**
     * Register media collections for the model.
     *
     * @return void
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('message_images')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif'])
            ->withResponsiveImages();

        $this->addMediaCollection('message_videos')
            ->acceptsMimeTypes(['video/mp4', 'video/quicktime']);
    }

    /**
     * Get the sender of the message.
     */
    public function sender()
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    /**
     * Get the recipient of the message.
     */
    public function recipient()
    {
        return $this->belongsTo(User::class, 'recipient_id');
    }

    /**
     * Scope a query to only include messages visible to the sender.
     */
    public function scopeVisibleToSender($query)
    {
        return $query->whereNull('deleted_by_sender_at');
    }

    /**
     * Scope a query to only include messages visible to the recipient.
     */
    public function scopeVisibleToRecipient($query)
    {
        return $query->whereNull('deleted_by_recipient_at');
    }

    /**
     * Scope a query to only include messages visible to a specific user.
     */
    public function scopeVisibleTo($query, $userId)
    {
        return $query->where(function ($q) use ($userId) {
            $q->where(function ($q2) use ($userId) {
                $q2->where('sender_id', $userId)
                   ->whereNull('deleted_by_sender_at');
            })->orWhere(function ($q2) use ($userId) {
                $q2->where('recipient_id', $userId)
                   ->whereNull('deleted_by_recipient_at');
            });
        });
    }
}
