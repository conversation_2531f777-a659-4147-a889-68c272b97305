<?php

namespace App\Models;

use App\Enums\ContactStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\AsCollection;
use Illuminate\Database\Eloquent\Relations\HasOne;
use App\Models\Concerns\HasNotesAndTags;

class Contact extends Model
{
    use HasFactory;
    use SoftDeletes;
    use HasNotesAndTags;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone',
        'type',
        'status',
        'region_id',
        'market_id',
        'sub_region_id',
        'county_id',
        'state_id',
        'school_id',
        'organization_id',
        'gender',
        'graduation_year',
        'sports',
        'metadata',
        'tags',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'nominated_at' => 'datetime',
        'sports' => AsCollection::class,
        'metadata' => AsCollection::class,
        'tags' => AsCollection::class,
        'status' => ContactStatus::class,
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();
    }

    /**
     * Get the contact's full name.
     */
    public function getFullNameAttribute(): string
    {
        return trim("{$this->first_name} {$this->last_name}");
    }

    /**
     * Get the region that the contact belongs to.
     */
    public function region(): BelongsTo
    {
        return $this->belongsTo(Region::class);
    }

    /**
     * Get the market that the contact belongs to.
     */
    public function market(): BelongsTo
    {
        return $this->belongsTo(Market::class);
    }

    /**
     * Get the sub-region that the contact belongs to.
     */
    public function subRegion(): BelongsTo
    {
        return $this->belongsTo(SubRegion::class);
    }

    /**
     * Get the county that the contact belongs to.
     */
    public function county(): BelongsTo
    {
        return $this->belongsTo(County::class);
    }

    /**
     * Get the state that the contact belongs to.
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'state_id', 'code');
    }

    /**
     * Get the school that the contact belongs to.
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    /**
     * Get the organization that the contact belongs to.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the athletics director profile associated with the contact.
     */
    public function athleticsDirectorProfile(): HasOne
    {
        return $this->hasOne(AthleticsDirectorProfile::class);
    }

    /**
     * Scope a query to only include contacts of a specific type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope a query to only include active contacts.
     */
    public function scopeActive($query)
    {
        return $query->where('status', ContactStatus::ACTIVE->value);
    }

    /**
     * Scope a query to only include inactive contacts.
     */
    public function scopeInactive($query)
    {
        return $query->where('status', ContactStatus::INACTIVE->value);
    }

    /**
     * Scope a query to filter by sport.
     */
    public function scopeHasSport($query, $sport)
    {
        return $query->whereJsonContains('sports', $sport);
    }

    /**
     * Scope a query to filter by tag.
     */
    public function scopeHasTag($query, $tag)
    {
        return $query->whereJsonContains('tags', $tag);
    }

    /**
     * Scope a query to filter by graduation year.
     */
    public function scopeGraduatingIn($query, $year)
    {
        return $query->where('graduation_year', $year);
    }

    /**
     * Scope a query to filter by gender.
     */
    public function scopeOfGender($query, $gender)
    {
        return $query->where('gender', $gender);
    }
}
