<?php

namespace App\Models;

use App\Enums\UiRegion;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AdvertisementUiRegion extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'advertisement_ui_region';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'advertisement_id',
        'ui_region',
    ];

    /**
     * Get the advertisement that this UI region is associated with.
     */
    public function advertisement(): BelongsTo
    {
        return $this->belongsTo(Advertisement::class);
    }

    /**
     * Get the UI region as an enum.
     */
    public function getUiRegionEnumAttribute(): UiRegion
    {
        return UiRegion::from($this->ui_region);
    }
}
