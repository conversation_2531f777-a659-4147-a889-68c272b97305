<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Engagement extends Model
{
    use HasFactory;

    /**
     * Event types
     */
    public const EVENT_IMPRESSION = 'impression';
    public const EVENT_CLICK = 'click';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'event_type',
        'user_id',
        'ip_address',
        'user_agent',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'metadata' => 'array',
        'created_at' => 'datetime',
    ];

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * Get the parent engageable model (polymorphic).
     */
    public function engageable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the user who triggered this engagement.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope a query to only include engagements of a specific type.
     */
    public function scopeOfType(Builder $query, string $type): Builder
    {
        return $query->where('event_type', $type);
    }

    /**
     * Scope a query to only include impressions.
     */
    public function scopeImpressions(Builder $query): Builder
    {
        return $query->ofType(self::EVENT_IMPRESSION);
    }

    /**
     * Scope a query to only include clicks.
     */
    public function scopeClicks(Builder $query): Builder
    {
        return $query->ofType(self::EVENT_CLICK);
    }

    /**
     * Scope a query to only include engagements after a specific date.
     */
    public function scopeAfterDate(Builder $query, Carbon $date): Builder
    {
        return $query->where('created_at', '>=', $date->startOfDay());
    }

    /**
     * Scope a query to only include engagements before a specific date.
     */
    public function scopeBeforeDate(Builder $query, Carbon $date): Builder
    {
        return $query->where('created_at', '<=', $date->endOfDay());
    }

    /**
     * Scope a query to include engagements within a date range.
     */
    public function scopeBetweenDates(Builder $query, Carbon $startDate, Carbon $endDate): Builder
    {
        return $query->afterDate($startDate)->beforeDate($endDate);
    }
}
