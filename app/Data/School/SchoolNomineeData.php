<?php

namespace App\Data\School;

use App\Models\User;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="SchoolNomineeData",
 *     title="School Nominee Data",
 *     description="Data representing a nominee from a school",
 *     @OA\Property(property="id", type="integer", example=1, description="User ID"),
 *     @OA\Property(property="first_name", type="string", example="John", description="First name"),
 *     @OA\Property(property="last_name", type="string", example="Doe", description="Last name"),
 *     @OA\Property(property="full_name", type="string", example="John Doe", description="Full name"),
 *     @OA\Property(property="profile_type", type="string", example="positive_athlete", description="Profile type"),
 *     @OA\Property(property="graduation_year", type="integer", nullable=true, example=2024, description="Graduation year"),
 *     @OA\Property(property="gender", type="string", nullable=true, example="Male", description="Gender"),
 *     @OA\Property(property="sports", type="array", description="Sports the nominee participates in", @OA\Items(
 *         @OA\Property(property="id", type="integer", example=1),
 *         @OA\Property(property="name", type="string", example="Basketball"),
 *         @OA\Property(property="slug", type="string", example="basketball"),
 *         @OA\Property(property="icon", type="string", example="basketball")
 *     )),
 *     @OA\Property(property="nominations", type="array", description="Nominations received", @OA\Items(
 *         @OA\Property(property="id", type="integer", example=1),
 *         @OA\Property(property="nominator_name", type="string", example="Jane Smith"),
 *         @OA\Property(property="nominator_email", type="string", example="<EMAIL>"),
 *         @OA\Property(property="date_nominated", type="string", example="01/15/2023"),
 *         @OA\Property(property="sport", type="string", example="Basketball"),
 *         @OA\Property(property="relationship", type="string", example="Coach"),
 *         @OA\Property(property="note", type="string", example="An excellent athlete with great sportsmanship"),
 *         @OA\Property(property="status", type="string", example="Approved"),
 *         @OA\Property(property="type", type="string", example="Award")
 *     )),
 *     @OA\Property(property="awards", type="array", description="Awards received", @OA\Items(
 *         @OA\Property(property="id", type="integer", example=1),
 *         @OA\Property(property="name", type="string", example="Excellence Award"),
 *         @OA\Property(property="year", type="integer", example=2023),
 *         @OA\Property(property="is_finalist", type="boolean", example=true),
 *         @OA\Property(property="is_winner", type="boolean", example=true),
 *         @OA\Property(property="verification_state", type="string", example="pending"),
 *         @OA\Property(property="verified_at", type="string", nullable=true, example="01/15/2023"),
 *         @OA\Property(property="details", type="string", nullable=true, example="Award for excellence in leadership")
 *     )),
 *     @OA\Property(property="scholarships", type="array", description="Scholarships received", @OA\Items(
 *         @OA\Property(property="id", type="integer", example=1),
 *         @OA\Property(property="name", type="string", example="Athletic Scholarship"),
 *         @OA\Property(property="year", type="integer", example=2023),
 *         @OA\Property(property="is_finalist", type="boolean", example=true),
 *         @OA\Property(property="is_winner", type="boolean", example=true),
 *         @OA\Property(property="verification_state", type="string", example="pending"),
 *         @OA\Property(property="verified_at", type="string", nullable=true, example="01/15/2023"),
 *         @OA\Property(property="details", type="string", nullable=true, example="$1,000 scholarship award")
 *     )),
 *     @OA\Property(property="has_awards", type="boolean", example=true, description="Whether the nominee has any awards"),
 *     @OA\Property(property="has_scholarships", type="boolean", example=false, description="Whether the nominee has any scholarships"),
 *     @OA\Property(property="needs_verification", type="boolean", example=true, description="Whether any awards/scholarships need verification"),
 *     @OA\Property(property="profile_image", type="string", nullable=true, example="https://example.com/images/profile.jpg", description="URL to profile image"),
 *     @OA\Property(property="updated_at", type="string", example="01/15/2023", description="Last update date"),
 *     @OA\Property(property="created_at", type="string", example="01/10/2023", description="Creation date")
 * )
 */
class SchoolNomineeData extends Data
{
    public function __construct(
        public readonly int $id,
        public readonly string $first_name,
        public readonly string $last_name,
        public readonly string $full_name,
        public readonly string $profile_type,
        public readonly ?int $graduation_year,
        public readonly ?string $gender,
        public readonly array $sports,
        public readonly array $nominations,
        public readonly array $awards,
        public readonly array $scholarships,
        public readonly bool $has_awards,
        public readonly bool $has_scholarships,
        public readonly bool $needs_verification,
        public readonly ?string $profile_image,
        public readonly string $updated_at,
        public readonly string $created_at,
    ) {
    }

    /**
     * Create a SchoolNomineeData instance from a User model
     *
     * @param User $user
     * @return static
     */
    public static function fromUser(User $user): self
    {
        // Prepare sports data
        $sports = [];
        if ($user->relationLoaded('sports')) {
            $sports = $user->sports->map(function ($sport) {
                return [
                    'id' => $sport->id,
                    'name' => $sport->name,
                    'slug' => $sport->slug,
                    'icon' => $sport->icon,
                ];
            })->toArray();
        }

        // Prepare nominations data
        $nominations = [];
        if ($user->relationLoaded('nominations')) {
            $nominations = $user->nominations->map(function ($nomination) {
                return [
                    'id' => $nomination->id,
                    'nominator_name' => $nomination->nominator_first_name . ' ' . $nomination->nominator_last_name,
                    'nominator_email' => $nomination->nominator_email,
                    'date_nominated' => $nomination->created_at->format('m/d/Y'),
                    'sport' => $nomination->sport,
                    'relationship' => $nomination->relationship,
                    'note' => $nomination->note,
                    'status' => $nomination->status,
                    'type' => $nomination->type,
                ];
            })->toArray();
        }

        // Prepare awards and scholarships data from the winners relationship
        $awards = [];
        $scholarships = [];
        $has_awards = false;
        $has_scholarships = false;
        $needs_verification = false;

        if ($user->relationLoaded('winners')) {
            // Extract awards
            $awards = $user->winners->filter(function ($winner) {
                return $winner->award_id !== null;
            })->map(function ($winner) {
                return [
                    'id' => $winner->award_id,
                    'name' => $winner->award?->name ?? 'Unknown Award',
                    'year' => $winner->year,
                    'is_finalist' => $winner->is_finalist,
                    'is_winner' => $winner->is_winner,
                    'verification_state' => $winner->verification_state,
                    'verified_at' => $winner->verified_at ? $winner->verified_at->format('m/d/Y') : null,
                    'details' => $winner->award?->details,
                ];
            })->toArray();

            // Extract scholarships
            $scholarships = $user->winners->filter(function ($winner) {
                return $winner->scholarship_id !== null;
            })->map(function ($winner) {
                return [
                    'id' => $winner->scholarship_id,
                    'name' => $winner->scholarship?->name ?? 'Unknown Scholarship',
                    'year' => $winner->year,
                    'is_finalist' => $winner->is_finalist,
                    'is_winner' => $winner->is_winner,
                    'verification_state' => $winner->verification_state,
                    'verified_at' => $winner->verified_at ? $winner->verified_at->format('m/d/Y') : null,
                    'details' => $winner->scholarship?->details,
                ];
            })->toArray();

            // Set flags
            $has_awards = $user->winners->contains(function ($winner) {
                return $winner->award_id !== null && $winner->is_winner;
            });

            $has_scholarships = $user->winners->contains(function ($winner) {
                return $winner->scholarship_id !== null && $winner->is_winner;
            });

            $needs_verification = $user->winners->contains(function ($winner) {
                return $winner->is_winner && $winner->verification_state === 'pending';
            });
        }

        // Create the data object
        return new self(
            id: $user->id,
            first_name: $user->first_name,
            last_name: $user->last_name,
            full_name: $user->first_name . ' ' . $user->last_name,
            profile_type: $user->profile_type ? $user->profile_type->value : $user->getRawOriginal('profile_type'),
            graduation_year: $user->graduation_year,
            gender: $user->gender ? ($user->gender->value ?? (string)$user->gender) : null,
            sports: $sports,
            nominations: $nominations,
            awards: $awards,
            scholarships: $scholarships,
            has_awards: $has_awards,
            has_scholarships: $has_scholarships,
            needs_verification: $needs_verification,
            profile_image: $user->getFirstMediaUrl('profile_image'),
            updated_at: $user->updated_at->format('m/d/Y'),
            created_at: $user->created_at->format('m/d/Y'),
        );
    }
}
