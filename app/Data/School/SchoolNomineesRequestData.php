<?php

namespace App\Data\School;

use App\Enums\ProfileType;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Spatie\LaravelData\Attributes\Validation\In;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Date;
use Spatie\LaravelData\Attributes\Validation\Min;
use Spatie\LaravelData\Attributes\Validation\Validate;
use Spatie\LaravelData\Data;

class SchoolNomineesRequestData extends Data
{
    public function __construct(
        #[Max(255)]
        public readonly ?string $search = null,

        public readonly ?int $graduation_year = null,

        #[Max(255)]
        public readonly ?string $sport = null,

        #[In(['Male', 'Female', 'Other'])]
        public readonly ?string $gender = null,

        #[Max(255)]
        public readonly ?string $profile_type = null,

        #[Date]
        public readonly ?string $date_from = null,

        #[Date]
        public readonly ?string $date_to = null,

        #[In(['name', 'graduation_year', 'sport', 'gender', 'nominator', 'date_nominated'])]
        public readonly ?string $sort_by = null,

        #[In(['asc', 'desc'])]
        public readonly ?string $sort_direction = null,

        #[Min(1)]
        public readonly ?int $page = null,

        #[Min(1), Max(100)]
        public readonly ?int $per_page = null,

        public readonly ?bool $include_winners = null,
    ) {
    }

    /**
     * Authorize the request
     *
     * @return bool
     */
    public static function authorize(): bool
    {
        $user = Auth::user();

        return $user && $user->profile_type == ProfileType::ATHLETICS_DIRECTOR;
    }

    /**
     * Get validation rules for this data object
     *
     * @return array
     */
    public static function rules(): array
    {
        return [
            'include_winners' => ['nullable', function ($attribute, $value, $fail) {
                // Accept various boolean representations
                if ($value !== null &&
                    !in_array(strtolower((string) $value), ['true', 'false', '0', '1', 0, 1], true)) {
                    $fail('The include winners field must be true or false.');
                }
            }],
        ];
    }

    /**
     * Configure the cast for the include_winners field
     */
    protected function castIncludeWinners(mixed $value): bool
    {
        if ($value === null) {
            return false;
        }

        // Handle various representations of boolean values
        if (is_string($value)) {
            return strtolower($value) === 'true' || $value === '1';
        }

        return (bool) $value;
    }
}
