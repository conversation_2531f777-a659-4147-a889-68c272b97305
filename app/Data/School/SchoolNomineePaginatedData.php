<?php

namespace App\Data\School;

use Illuminate\Pagination\LengthAwarePaginator;
use Spa<PERSON>\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="SchoolNomineePaginatedData",
 *     title="School Nominee Paginated Data",
 *     description="Paginated collection of school nominees",
 *     @OA\Property(
 *         property="data",
 *         type="array",
 *         description="List of school nominees",
 *         @OA\Items(ref="#/components/schemas/SchoolNomineeData")
 *     ),
 *     @OA\Property(
 *         property="meta",
 *         type="object",
 *         description="Pagination metadata",
 *         @OA\Property(property="current_page", type="integer", example=1),
 *         @OA\Property(property="from", type="integer", example=1),
 *         @OA\Property(property="last_page", type="integer", example=5),
 *         @OA\Property(property="path", type="string", example="https://example.com/api/athletics-director/school/nominees"),
 *         @OA\Property(property="per_page", type="integer", example=15),
 *         @OA\Property(property="to", type="integer", example=15),
 *         @OA\Property(property="total", type="integer", example=75)
 *     )
 * )
 */
class SchoolNomineePaginatedData extends Data
{
    public function __construct(
        public readonly array $data,
        public readonly array $meta,
    ) {
    }

    /**
     * Create a SchoolNomineePaginatedData instance from a paginator
     *
     * @param LengthAwarePaginator $paginator
     * @return static
     */
    public static function fromPaginator(LengthAwarePaginator $paginator): self
    {
        // Transform each user to a SchoolNomineeData DTO
        $data = $paginator->map(function ($user) {
            return SchoolNomineeData::fromUser($user);
        })->toArray();

        // Set meta data from paginator
        $meta = [
            'current_page' => $paginator->currentPage(),
            'from' => $paginator->firstItem(),
            'last_page' => $paginator->lastPage(),
            'path' => $paginator->path(),
            'per_page' => $paginator->perPage(),
            'to' => $paginator->lastItem(),
            'total' => $paginator->total(),
        ];

        return new self(
            data: $data,
            meta: $meta,
        );
    }
}
