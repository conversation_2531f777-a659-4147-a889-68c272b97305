<?php

namespace App\Data\School;

use App\Models\School;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="SchoolData",
 *     title="School Data",
 *     description="Data representing a school",
 *     @OA\Property(property="id", type="integer", example=1, description="School ID"),
 *     @OA\Property(property="name", type="string", example="Example High School", description="Name of the school"),
 *     @OA\Property(property="county_name", type="string", nullable=true, example="Example County", description="Name of the county the school is in"),
 *     @OA\Property(property="state_code", type="string", nullable=true, example="GA", description="Two-letter state code for the school's state"),
 *     @OA\Property(property="state_name", type="string", nullable=true, example="Georgia", description="Full name of the school's state")
 * )
 */
class SchoolData extends Data
{
    public function __construct(
        public readonly int $id,
        public readonly string $name,
        public readonly ?string $county_name = null,
        public readonly ?string $state_code = null,
        public readonly ?string $state_name = null,
    ) {}

    public static function fromModel(School $school): self
    {
        return new self(
            id: $school->id,
            name: $school->name,
            county_name: $school->county?->name,
            state_code: $school->county?->state_code,
            state_name: $school->county?->state?->name,
        );
    }

    public static function collection(mixed $schools): array
    {
        return collect($schools)
            ->map(fn (School $school) => self::fromModel($school))
            ->toArray();
    }
}
