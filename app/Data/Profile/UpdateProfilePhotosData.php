<?php

namespace App\Data\Profile;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use <PERSON>tie\LaravelData\Data;
use Spatie\LaravelData\Support\Validation\ValidationContext;

/**
 * @OA\Schema(
 *     schema="UpdateProfilePhotosData",
 *     @OA\Property(
 *         property="photos",
 *         type="array",
 *         @OA\Items(
 *             type="object",
 *             required={"file"},
 *             @OA\Property(property="file", type="string", format="binary"),
 *             @OA\Property(
 *                 property="focal_point",
 *                 type="object",
 *                 required={"x", "y"},
 *                 @OA\Property(property="x", type="number", format="float", example=0.5),
 *                 @OA\Property(property="y", type="number", format="float", example=0.5)
 *             )
 *         )
 *     ),
 *     @OA\Property(
 *         property="delete_photo_ids",
 *         type="array",
 *         @OA\Items(type="integer")
 *     ),
 *     @OA\Property(property="update_photo_id", type="integer"),
 *     @OA\Property(
 *         property="focal_point",
 *         type="object",
 *         required={"x", "y"},
 *         @OA\Property(property="x", type="number", format="float", example=0.5),
 *         @OA\Property(property="y", type="number", format="float", example=0.5)
 *     )
 * )
 */
class UpdateProfilePhotosData extends Data
{
    public function __construct(
        /** @var PhotoUploadData[]|null */
        public ?array $photos,

        /** @var string[]|null */
        public ?array $delete_photo_ids,

        public ?string $update_photo_id,

        /** @var array{x: float, y: float}|null */
        public ?array $focal_point,
    ) {}

    public static function rules(ValidationContext $context): array
    {
        // Log the incoming data before validation
        Log::info('UpdateProfilePhotosData validation payload', [
            'payload' => $context
        ]);

        return [
            'photos' => ['nullable', 'array'],
            'photos.*.file' => ['required', 'image', 'max:5120'], // 5MB
            'photos.*.focal_point' => ['required', 'array'],
            'photos.*.focal_point.x' => ['required', 'numeric', 'min:0', 'max:1'],
            'photos.*.focal_point.y' => ['required', 'numeric', 'min:0', 'max:1'],
            'delete_photo_ids' => ['nullable', 'array'],
            'delete_photo_ids.*' => ['exists:media,id'],
            'update_photo_id' => ['nullable', 'exists:media,id'],
            'focal_point' => ['nullable', 'array'],
            'focal_point.x' => ['required_with:focal_point', 'numeric', 'min:0', 'max:1'],
            'focal_point.y' => ['required_with:focal_point', 'numeric', 'min:0', 'max:1'],
        ];
    }
}

class PhotoUploadData extends Data
{
    public function __construct(
        public UploadedFile $file,

        /** @var array{x: float, y: float}|null */
        public ?array $focal_point,
    ) {
        // Maintain the focal point validation that was previously in the constructor
        if ($this->focal_point) {
            if (!isset($this->focal_point['x']) || !isset($this->focal_point['y'])) {
                throw new \InvalidArgumentException('Focal point must have x and y coordinates');
            }

            $x = (float) $this->focal_point['x'];
            $y = (float) $this->focal_point['y'];

            if ($x < 0 || $x > 1 || $y < 0 || $y > 1) {
                throw new \InvalidArgumentException('Focal point coordinates must be between 0 and 1');
            }
        }
    }

    public static function rules(ValidationContext $context): array
    {
        // Log the incoming data before validation
        Log::info('PhotoUploadData validation payload', [
            'payload' => $context
        ]);

        return [
            'file' => ['required', 'image', 'max:5120'], // 5MB
            'focal_point' => ['required', 'array'],
            'focal_point.x' => ['required', 'numeric', 'min:0', 'max:1'],
            'focal_point.y' => ['required', 'numeric', 'min:0', 'max:1'],
        ];
    }
}

