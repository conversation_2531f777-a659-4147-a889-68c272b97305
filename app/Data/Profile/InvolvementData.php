<?php

namespace App\Data\Profile;

use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="InvolvementData",
 *     required={"title", "date_range", "description"},
 *     @OA\Property(
 *         property="title",
 *         type="string",
 *         description="Title of the community involvement",
 *         example="Local Food Bank Volunteer"
 *     ),
 *     @OA\Property(
 *         property="date_range",
 *         type="string",
 *         description="Date range of involvement",
 *         example="2023-01 to 2023-12"
 *     ),
 *     @OA\Property(
 *         property="description",
 *         type="string",
 *         description="Description of the involvement",
 *         example="Volunteered at local food bank helping to distribute food to families in need"
 *     )
 * )
 */
class InvolvementData extends Data
{
    public function __construct(
        public readonly string $title,
        public readonly string $date_range,
        public readonly string $description,
    ) {}
}
