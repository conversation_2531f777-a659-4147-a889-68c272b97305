<?php

namespace App\Data\Profile\Sports;

use Illuminate\Support\Collection;
use <PERSON>tie\LaravelData\Attributes\DataCollectionOf;
use <PERSON>tie\LaravelData\Data;
use <PERSON>tie\LaravelData\DataCollection;

/**
 * @OA\Schema(
 *     schema="SportSearchResponse",
 *     required={"results"},
 *     @OA\Property(
 *         property="results",
 *         type="array",
 *         @OA\Items(
 *             type="object",
 *             required={"id", "name", "category"},
 *             @OA\Property(property="id", type="integer", example=1),
 *             @OA\Property(property="name", type="string", example="Basketball"),
 *             @OA\Property(property="category", type="string", example="Team Sports")
 *         )
 *     )
 * )
 */
class SportSearchResponse extends Data
{
    public function __construct(
        /** @var array<string, DataCollection<SportResponse>> */
        public array $results,
    ) {}

    /**
     * Create a new instance from search results
     *
     * @param Collection|array $platformSports
     * @param Collection|array $customSports
     */
    public static function fromResults(Collection|array $platformSports, Collection|array $customSports): self
    {
        return new self([
            'platform' => new DataCollection(SportResponse::class,
                $platformSports instanceof Collection ? $platformSports->all() : $platformSports
            ),
            'custom' => new DataCollection(SportResponse::class,
                $customSports instanceof Collection ? $customSports->all() : $customSports
            ),
        ]);
    }
}

