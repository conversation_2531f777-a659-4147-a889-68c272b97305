<?php

namespace App\Data\Profile\Sports;

use App\Models\Sport;
use App\Models\CustomSport;
use Illuminate\Database\Eloquent\Model;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="SportResponse",
 *     required={"id", "name", "isCustom", "uniqueId"},
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         example=1,
 *         description="Sport ID"
 *     ),
 *     @OA\Property(
 *         property="name",
 *         type="string",
 *         example="Basketball",
 *         description="Sport name"
 *     ),
 *     @OA\Property(
 *         property="slug",
 *         type="string",
 *         nullable=true,
 *         example="basketball",
 *         description="URL-friendly version of the sport name"
 *     ),
 *     @OA\Property(
 *         property="icon",
 *         type="string",
 *         nullable=true,
 *         example="basketball-icon",
 *         description="Icon identifier for the sport"
 *     ),
 *     @OA\Property(
 *         property="isCustom",
 *         type="boolean",
 *         example=false,
 *         description="Whether this is a custom sport"
 *     ),
 *     @OA\Property(
 *         property="order",
 *         type="integer",
 *         nullable=true,
 *         example=0,
 *         description="Display order of the sport"
 *     ),
 *     @OA\Property(
 *         property="uniqueId",
 *         type="string",
 *         example="platform-1",
 *         description="Unique identifier combining type and ID"
 *     )
 * )
 */
class SportResponse extends Data
{
    public function __construct(
        public int $id,
        public string $name,
        public ?string $slug,
        public ?string $icon,
        public bool $isCustom,
        public ?int $order = null,
        public string $uniqueId = '',
    ) {}

    public static function fromModel(Model $model, bool $isCustom = false): self
    {
        if ($model instanceof Sport) {
            return new self(
                id: $model->id,
                name: $model->name,
                slug: $model->slug,
                icon: $model->icon,
                isCustom: false,
                order: $model->pivot?->order,
                uniqueId: "platform-{$model->id}",
            );
        }

        if ($model instanceof CustomSport) {
            return new self(
                id: $model->id,
                name: $model->name,
                slug: null,
                icon: null,
                isCustom: true,
                order: $model->order,
                uniqueId: "custom-{$model->id}",
            );
        }

        throw new \InvalidArgumentException('Invalid model type provided');
    }
}
