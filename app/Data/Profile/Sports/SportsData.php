<?php
namespace App\Data\Profile\Sports;

use <PERSON><PERSON>\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="SportsData",
 *     required={"sports"},
 *     @OA\Property(
 *         property="sports",
 *         type="array",
 *         @OA\Items(
 *             type="object",
 *             required={"order", "isCustom"},
 *             @OA\Property(
 *                 property="id",
 *                 type="integer",
 *                 nullable=true,
 *                 example=1,
 *                 description="ID of the platform sport. Required when isCustom is false."
 *             ),
 *             @OA\Property(
 *                 property="name",
 *                 type="string",
 *                 nullable=true,
 *                 example="Basketball",
 *                 description="Name of the custom sport. Required when isCustom is true."
 *             ),
 *             @OA\Property(
 *                 property="customName",
 *                 type="string",
 *                 nullable=true,
 *                 example="Basketball",
 *                 description="Name of the custom sport. Required when isCustom is true."
 *             ),
 *             @OA\Property(
 *                 property="slug",
 *                 type="string",
 *                 nullable=true,
 *                 example="basketball",
 *                 description="Name of the custom sport. Required when isCustom is true."
 *             ),
 *             @OA\Property(
 *                 property="icon",
 *                 type="string",
 *                 nullable=true,
 *                 example="fa-basketball",
 *                 description="Icon of the custom sport. Required when isCustom is true."
 *             ),
 *             @OA\Property(
 *                 property="order",
 *                 type="integer",
 *                 nullable=true,
 *                 example=0,
 *                 description="Order of the sport in the list"
 *             ),
 *             @OA\Property(
 *                 property="isCustom",
 *                 type="boolean",
 *                 nullable=true,
 *                 example=false,
 *                 description="Whether this is a custom sport or a platform sport"
 *             )
 *         )
 *     )
 * )
 */
class SportsData extends Data
{
    public function __construct(
        #[DataCollectionOf(SportUpdate::class)]
        public readonly array $sports,
    ) {}

    public static function fromArray(array $sports): self
    {
        return new self(
            sports: $sports,
        );
    }
}

/**
 * @OA\Schema(
 *     schema="SportUpdate",
 *     required={"order", "isCustom"},
 *     @OA\Property(property="id", type="integer", nullable=true),
 *     @OA\Property(property="name", type="string", nullable=true),
 *     @OA\Property(property="customName", type="string", nullable=true),
 *     @OA\Property(property="slug", type="string", nullable=true),
 *     @OA\Property(property="icon", type="string", nullable=true),
 *     @OA\Property(property="order", type="integer"),
 *     @OA\Property(property="isCustom", type="boolean")
 * )
 */
class SportUpdate extends Data
{
    public function __construct(
        public ?int $id,
        public ?string $customName,
        public int $order,
        public bool $isCustom,
    ) {}
}
