<?php

namespace App\Data\Profile;

use Spa<PERSON>\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\ArrayType;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="ProfessionalDetailsData",
 *     @OA\Property(property="state", type="string", example="CA", nullable=true),
 *     @OA\Property(property="employer", type="string", example="Starbucks Corp & LLC Brands", nullable=true),
 *     @OA\Property(property="job_title", type="string", example="Software Engineer", nullable=true),
 *     @OA\Property(
 *         property="interests",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/Interest"),
 *         nullable=true,
 *         description="Array of interest objects"
 *     ),
 *     @OA\Property(property="twitter", type="string", example="username", nullable=true),
 *     @OA\Property(property="instagram", type="string", example="username", nullable=true),
 *     @OA\Property(property="facebook", type="string", example="username", nullable=true),
 *     @OA\Property(property="hudl", type="string", example="username", nullable=true),
 *     @OA\Property(property="custom_link", type="string", example="https://example.com", nullable=true)
 * )
 */
class ProfessionalDetailsData extends Data
{
    public function __construct(
        #[Nullable, StringType]
        public readonly ?string $state = null,

        #[Nullable, StringType, Max(255)]
        public readonly ?string $employer = null,

        #[Nullable, StringType, Max(255)]
        public readonly ?string $job_title = null,

        #[Nullable, ArrayType]
        public readonly ?array $interests = null,

        #[Nullable, StringType]
        public readonly ?string $twitter = null,

        #[Nullable, StringType]
        public readonly ?string $instagram = null,

        #[Nullable, StringType]
        public readonly ?string $facebook = null,

        #[Nullable, StringType]
        public readonly ?string $hudl = null,

        #[Nullable, StringType]
        public readonly ?string $custom_link = null,
    ) {}

    public static function rules(): array
    {
        return [
            'state' => ['nullable', 'string', 'exists:states,code'],
            'employer' => ['nullable', 'string', 'max:255'],
            'job_title' => ['nullable', 'string', 'max:255'],
            // Note: interests should be submitted as array of IDs in updates,
            // but response will contain full Interest objects
            'interests' => ['nullable', 'array'],
            'interests.*' => ['integer', 'exists:interests,id'],
            'twitter' => ['nullable', 'string'],
            'instagram' => ['nullable', 'string'],
            'facebook' => ['nullable', 'string'],
            'hudl' => ['nullable', 'string'],
            'custom_link' => ['nullable', 'string'],
        ];
    }

    /**
     * Create from User model
     */
    public static function fromModel(\App\Models\User $user): self
    {
        // Get interests with full objects (not just IDs)
        $interests = $user->interests()
            ->get()
            ->map(fn($interest) => InterestData::fromModel($interest))
            ->toArray();

        return new self(
            state: $user->state_code ?? null,
            employer: $user->metadata['employer'] ?? null,
            job_title: $user->metadata['job_title'] ?? null,
            interests: count($interests) > 0 ? $interests : null,
            twitter: $user->twitter,
            instagram: $user->instagram,
            facebook: $user->facebook,
            hudl: $user->hudl,
            custom_link: $user->custom_link,
        );
    }

    /**
     * Convert to array for updating user model
     */
    public function toUserArray(): array
    {
        return [
            'state_code' => $this->state,
            'twitter' => $this->twitter,
            'instagram' => $this->instagram,
            'facebook' => $this->facebook,
            'hudl' => $this->hudl,
            'custom_link' => $this->custom_link,
        ];
    }

    /**
     * Get metadata fields
     */
    public function getMetadata(): array
    {
        return [
            'employer' => $this->employer,
            'job_title' => $this->job_title,
        ];
    }
}
