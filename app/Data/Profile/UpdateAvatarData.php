<?php

namespace App\Data\Profile;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Support\Validation\ValidationContext;

/**
 * @OA\Schema(
 *     schema="UpdateAvatarData",
 *     required={"file"},
 *     type="object",
 *     @OA\Property(
 *         property="file",
 *         type="string",
 *         format="binary",
 *         description="Avatar image file (max 5MB, image files only)"
 *     )
 * )
 */
class UpdateAvatarData extends Data
{
    public function __construct(
        public UploadedFile $file,
    ) {}

    public static function rules(ValidationContext $context): array
    {
        return [
            'file' => ['required', 'image', 'max:5120'], // 5MB max
        ];
    }
}
