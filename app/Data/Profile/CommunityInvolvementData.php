<?php

namespace App\Data\Profile;

use App\Models\CommunityInvolvement;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Data;

class CommunityInvolvementData extends Data
{
    public function __construct(
        public ?int $id,
        public string $title,
        public string $date_range,
        public string $description,
        public ?int $order = null,
    ) {}

    public static function fromModel(CommunityInvolvement $involvement): self
    {
        return new self(
            id: $involvement->id,
            title: $involvement->title,
            date_range: $involvement->date_range,
            description: $involvement->description,
            order: $involvement->order,
        );
    }

    public static function fromCollection(Collection $involvements): array
    {
        return $involvements->map(fn ($involvement) => self::fromModel($involvement))->toArray();
    }
}
