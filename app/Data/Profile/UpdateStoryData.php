<?php

namespace App\Data\Profile;

use <PERSON><PERSON>\LaravelData\Data;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Illuminate\Support\HtmlString;
use Mews\Purifier\Facades\Purifier;

/**
 * @OA\Schema(
 *     schema="UpdateStoryData",
 *     required={"content"},
 *     @OA\Property(
 *         property="content",
 *         type="string",
 *         description="The updated story content",
 *         example="My journey in sports began when..."
 *     )
 * )
 */
class UpdateStoryData extends Data
{
    public function __construct(
        #[Required]
        #[StringType]
        public string $content,
    ) {
        // Sanitize HTML content using the predefined 'story' configuration
        $this->content = Purifier::clean($this->content, 'story');
    }
}
