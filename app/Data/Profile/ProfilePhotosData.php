<?php

namespace App\Data\Profile;

use Spatie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;
use Illuminate\Support\Collection;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class ProfilePhotosData extends DataCollection
{
    public static function fromMediaCollection(Collection $media): self
    {
        return new self(ProfilePhotoData::class, ProfilePhotoData::fromCollection($media));
    }
}
