<?php

namespace App\Data\Profile;

use App\Models\User;
use <PERSON>tie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="RecruiterStatusData",
 *     required={"enabled"},
 *     @OA\Property(
 *         property="enabled",
 *         type="boolean",
 *         description="Whether the recruiter feature is enabled for this user",
 *         example=true
 *     )
 * )
 */
class RecruiterStatusData extends Data
{
    public function __construct(
        public readonly bool $enabled,
    ) {}

    public static function fromUser(User $user): self
    {
        return new self(
            enabled: $user->recruiter_enabled ?? false,
        );
    }
}
