<?php

namespace App\Data\Profile;

use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Numeric;
use Spatie\LaravelData\Attributes\Validation\Min;
use Spatie\LaravelData\Attributes\Validation\ArrayType;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="CollegeAthleteDetailsData",
 *     @OA\Property(property="state", type="string", example="CA", nullable=true),
 *     @OA\Property(property="college", type="string", example="University of Michigan", nullable=true),
 *     @OA\Property(property="graduation_year", type="integer", example=2025, nullable=true),
 *     @OA\Property(property="gpa", type="number", format="float", example=4.0, nullable=true),
 *     @OA\Property(property="gender", type="string", example="Male", nullable=true),
 *     @OA\Property(property="height", type="string", example="5'11", nullable=true),
 *     @OA\Property(property="weight", type="integer", example=175, nullable=true),
 *     @OA\Property(
 *         property="career_interests",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/Interest"),
 *         nullable=true,
 *         description="Array of career interest objects"
 *     ),
 *     @OA\Property(property="twitter", type="string", example="username", nullable=true),
 *     @OA\Property(property="instagram", type="string", example="username", nullable=true),
 *     @OA\Property(property="facebook", type="string", example="username", nullable=true),
 *     @OA\Property(property="hudl", type="string", example="username", nullable=true),
 *     @OA\Property(property="custom_link", type="string", example="https://example.com", nullable=true)
 * )
 */
class CollegeAthleteDetailsData extends Data
{
    public function __construct(
        #[Nullable, StringType]
        public readonly ?string $state = null,

        #[Nullable, StringType, Max(255)]
        public readonly ?string $college = null,

        #[Nullable, Numeric]
        public readonly ?int $graduation_year = null,

        #[Nullable, StringType]
        public readonly ?string $gender = null,

        #[Nullable, Numeric, Min(0), Max(4.0)]
        public readonly ?float $gpa = null,

        #[Nullable, StringType]
        public readonly ?string $height = null,

        #[Nullable, Numeric]
        public readonly ?int $weight = null,

        #[Nullable, ArrayType]
        public readonly ?array $career_interests = null,

        #[Nullable, StringType]
        public readonly ?string $twitter = null,

        #[Nullable, StringType]
        public readonly ?string $instagram = null,

        #[Nullable, StringType]
        public readonly ?string $facebook = null,

        #[Nullable, StringType]
        public readonly ?string $hudl = null,

        #[Nullable, StringType]
        public readonly ?string $custom_link = null,
    ) {}

    public static function rules(): array
    {
        return [
            'state' => ['nullable', 'string', 'exists:states,code'],
            'college' => ['nullable', 'string', 'max:255'],
            'graduation_year' => ['nullable', 'numeric'],
            'gpa' => ['nullable', 'numeric', 'min:0', 'max:4.0'],
            'gender' => ['nullable', 'string'],
            'height' => ['nullable', 'string'],
            'weight' => ['nullable', 'integer'],
            // Note: career_interests should be submitted as array of IDs in updates,
            // but response will contain full Interest objects
            'career_interests' => ['nullable', 'array'],
            'career_interests.*' => ['integer', 'exists:interests,id'],
            'twitter' => ['nullable', 'string'],
            'instagram' => ['nullable', 'string'],
            'facebook' => ['nullable', 'string'],
            'hudl' => ['nullable', 'string'],
            'custom_link' => ['nullable', 'string'],
        ];
    }

    /**
     * Create from User model
     */
    public static function fromModel(\App\Models\User $user): self
    {
        // Get career interests with full objects (not just IDs)
        $careerInterests = $user->interests()
            ->get()
            ->map(fn($interest) => InterestData::fromModel($interest))
            ->toArray();

        return new self(
            state: $user->state_code ?? null,
            college: $user->metadata['college'] ?? null,
            graduation_year: $user->metadata['graduation_year'] ?? null,
            gender: $user->gender?->value ?? null,
            gpa: $user->metadata['gpa'] ?? null,
            height: $user->height_in_inches ? (intval($user->height_in_inches / 12) . "'" . ($user->height_in_inches % 12) . '"') : null,
            weight: $user->weight,
            career_interests: count($careerInterests) > 0 ? $careerInterests : null,
            twitter: $user->twitter,
            instagram: $user->instagram,
            facebook: $user->facebook,
            hudl: $user->hudl,
            custom_link: $user->custom_link,
        );
    }

    /**
     * Convert to array for updating user model
     */
    public function toUserArray(): array
    {
        return [
            'state_code' => $this->state,
            'gender' => $this->gender,
            'height_in_inches' => $this->convertHeightToInches($this->height),
            'weight' => $this->weight,
            'twitter' => $this->twitter,
            'instagram' => $this->instagram,
            'facebook' => $this->facebook,
            'hudl' => $this->hudl,
            'custom_link' => $this->custom_link,
        ];
    }

    /**
     * Get metadata fields
     */
    public function getMetadata(): array
    {
        return [
            'college' => $this->college,
            'graduation_year' => $this->graduation_year,
            'gpa' => $this->gpa,
            // Note: career_interests are handled via pivot table, not metadata
        ];
    }

    /**
     * Convert height from string format (e.g. "5'11") to inches
     */
    private function convertHeightToInches(?string $height): ?int
    {
        if (!$height) {
            return null;
        }

        // Handle common formats like "5'11", "5-11", "5 11"
        $height = str_replace(["'", "-", " "], ":", $height);
        $parts = explode(":", $height);

        if (count($parts) === 2) {
            $feet = (int) $parts[0];
            $inches = (int) $parts[1];
            return ($feet * 12) + $inches;
        }

        // If it's already in inches
        if (is_numeric($height)) {
            return (int) $height;
        }

        return null;
    }
}
