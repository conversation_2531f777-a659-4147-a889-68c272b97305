<?php

namespace App\Data\Profile;

use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="UpdateRecruiterStatusData",
 *     required={"enabled"},
 *     @OA\Property(
 *         property="enabled",
 *         type="boolean",
 *         description="Whether to enable or disable the recruiter feature",
 *         example=true
 *     )
 * )
 */
class UpdateRecruiterStatusData extends Data
{
    public function __construct(
        public readonly bool $enabled,
    ) {}
}
