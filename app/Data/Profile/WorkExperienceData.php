<?php

namespace App\Data\Profile;

use App\Models\WorkExperience;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="ProfileWorkExperienceData",
 *     title="Profile Work Experience Data",
 *     description="Data structure for work experience in user profiles",
 *     required={"name", "date", "description"},
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         description="Unique identifier for the work experience",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="name",
 *         type="string",
 *         description="Name of the work experience",
 *         example="Summer Camp Counselor"
 *     ),
 *     @OA\Property(
 *         property="date",
 *         type="string",
 *         description="Date or date range of the work experience",
 *         example="Summer 2023"
 *     ),
 *     @OA\Property(
 *         property="description",
 *         type="string",
 *         description="Description of the work experience",
 *         example="Led activities and supervised campers aged 8-12"
 *     )
 * )
 */
class WorkExperienceData extends Data
{
    public function __construct(
        public readonly ?int $id = null,
        public readonly string $name,
        public readonly string $date,
        public readonly string $description,
    ) {}

    public static function fromModel(WorkExperience $experience): self
    {
        return new self(
            id: $experience->id,
            name: $experience->name,
            date: $experience->date,
            description: $experience->description,
        );
    }

    public static function fromCollection(Collection $experiences): array
    {
        return $experiences->map(fn ($experience) => self::fromModel($experience))->all();
    }
}
