<?php

namespace App\Data\Profile;

use Spatie\LaravelData\Data;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * @OA\Schema(
 *     schema="AvatarData",
 *     required={"url"},
 *     @OA\Property(
 *         property="url",
 *         type="string",
 *         format="uri",
 *         description="URL of the avatar image",
 *         example="https://example.com/media/avatar.jpg"
 *     )
 * )
 */
class AvatarData extends Data
{
    public function __construct(
        public readonly string $url,
    ) {}

    public static function fromMedia(Media $media): self
    {
        return new self(
            url: $media->getUrl(),
        );
    }
}
