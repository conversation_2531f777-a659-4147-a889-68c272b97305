<?php

namespace App\Data\Profile;

use Illuminate\Support\Collection;
use Spatie\LaravelData\Data;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * @OA\Schema(
 *     schema="ProfilePhotoData",
 *     required={"id", "url", "width", "height", "focal_point"},
 *     @OA\Property(property="id", type="integer", example=1),
 *     @OA\Property(property="url", type="string", example="https://example.com/photos/1.jpg"),
 *     @OA\Property(property="thumbnail_url", type="string", example="https://example.com/photos/1-thumb.jpg"),
 *     @OA\Property(property="width", type="integer", example=1920),
 *     @OA\Property(property="height", type="integer", example=1080),
 *     @OA\Property(property="order", type="integer", example=1),
 *     @OA\Property(
 *         property="focal_point",
 *         type="object",
 *         nullable=true,
 *         @OA\Property(property="x", type="string", format="float", example="0.5"),
 *         @OA\Property(property="y", type="string", format="float", example="0.5")
 *     )
 * )
 */
class ProfilePhotoData extends Data
{
    public function __construct(
        public readonly int $id,
        public readonly string $url,
        public readonly string $thumbnail_url,
        public readonly ?int $width,
        public readonly ?int $height,
        public readonly int $order,
        public readonly ?array $focal_point,
    ) {}

    public static function fromMedia(Media $media): self
    {
        // Get image dimensions from the media custom properties
        $width = $media->getCustomProperty('width');
        $height = $media->getCustomProperty('height');

        // If dimensions aren't in custom properties, try to get them from the file
        if (!$width || !$height) {
            $path = $media->getPath();
            if (file_exists($path)) {
                [$width, $height] = getimagesize($path);
            }
        }

        return new self(
            id: $media->id,
            url: $media->getUrl(),
            thumbnail_url: $media->getUrl('thumbnail'),
            width: $width,
            height: $height,
            order: $media->order_column ?? 0,
            focal_point: $media->getCustomProperty('focal_point'),
        );
    }

    /**
     * @param Collection<int, Media>|array<int, Media> $photos
     * @return Collection<int, ProfilePhotoData>
     */
    public static function fromCollection(Collection|array $photos): Collection
    {
        return collect($photos)->map(fn (Media $photo) => self::fromMedia($photo));
    }
}
