<?php
namespace App\Data\Profile;

use <PERSON><PERSON>\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="UpdateWorkExperiencesData",
 *     required={"experiences"},
 *     @OA\Property(
 *         property="experiences",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/ProfileWorkExperienceData")
 *     )
 * )
 */
class UpdateWorkExperiencesData extends Data
{
    public function __construct(
        #[DataCollectionOf(WorkExperienceData::class)]
        public readonly array $experiences,
    ) {}
}
