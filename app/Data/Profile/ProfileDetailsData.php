<?php

namespace App\Data\Profile;

use App\Models\User;
use Illuminate\Validation\Rule;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Optional;
use App\Enums\Gender;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\DataCollection;

/**
 * @OA\Schema(
 *     schema="InterestData",
 *     @OA\Property(property="id", type="integer", example=1),
 *     @OA\Property(property="name", type="string", example="Volunteering"),
 *     @OA\Property(property="icon", type="string", nullable=true, example="volunteer-icon", description="Icon identifier for the interest")
 * )
 *
 * @OA\Schema(
 *     schema="ProfileDetailsData",
 *     description="Detailed profile information for a user.",
 *     @OA\Property(property="state", type="string", nullable=true, example="GA", description="State code"),
 *     @OA\Property(property="county_id", type="integer", nullable=true, example=121, description="ID of the county"),
 *     @OA\Property(property="county_name", type="string", nullable=true, example="Fulton", description="Name of the county"),
 *     @OA\Property(property="email", type="string", format="email", nullable=true, example="<EMAIL>"),
 *     @OA\Property(property="city", type="string", nullable=true, example="Atlanta"),
 *     @OA\Property(property="phone", type="string", nullable=true, example="************"),
 *     @OA\Property(property="school_id", type="integer", nullable=true, example=1001, description="ID of the school"),
 *     @OA\Property(property="school_name", type="string", nullable=true, example="Central High School"),
 *     @OA\Property(property="graduation_year", type="integer", nullable=true, example=2024),
 *     @OA\Property(property="gpa", type="number", format="float", nullable=true, example=3.8),
 *     @OA\Property(property="class_rank", type="string", nullable=true, example="10/500"),
 *     @OA\Property(property="gender", type="string", enum=Gender::class, nullable=true, example="male"),
 *     @OA\Property(property="height_in_inches", type="integer", nullable=true, example=70, description="Height in inches"),
 *     @OA\Property(property="weight", type="integer", nullable=true, example=180, description="Weight in pounds"),
 *     @OA\Property(property="instagram", type="string", nullable=true, example="john_doe_insta"),
 *     @OA\Property(property="facebook", type="string", nullable=true, example="johndoeFB"),
 *     @OA\Property(property="twitter", type="string", nullable=true, example="JohnDoeTweets"),
 *     @OA\Property(property="hudl", type="string", nullable=true, example="hudl.com/profile/12345"),
 *     @OA\Property(property="custom_link", type="string", nullable=true, example="linkedin.com/in/johndoe"),
 *     @OA\Property(property="interest_ids", type="array", @OA\Items(type="integer"), description="Array of interest IDs associated with the user."),
 *     @OA\Property(property="interests", type="array", @OA\Items(ref="#/components/schemas/InterestData"), description="Detailed list of interests.")
 * )
 */
class ProfileDetailsData extends Data
{
    public function __construct(
        public string|Optional|null $state,
        public int|Optional|null $county_id,
        public string|Optional|null $county_name,
        public string|Optional|null $email,
        public string|Optional|null $city,
        public string|Optional|null $phone,
        public int|Optional|null $school_id,
        public string|Optional|null $school_name,
        public int|Optional|null $graduation_year,
        public float|Optional|null $gpa,
        public string|Optional|null $class_rank,
        public Gender|Optional|null $gender,
        public int|Optional|null $height_in_inches,
        public int|Optional|null $weight,
        public string|Optional|null $instagram,
        public string|Optional|null $facebook,
        public string|Optional|null $twitter,
        public string|Optional|null $hudl,
        public string|Optional|null $custom_link,
        /** @var array<int> */
        public array|Optional $interest_ids = [],
        /** @var DataCollection<InterestData> */
        #[DataCollectionOf(InterestData::class)]
        public DataCollection|Optional $interests = new DataCollection(InterestData::class, []),
    ) {}

    public static function rules(): array
    {
        return [
            'state' => ['nullable', 'string', 'max:255', Rule::in(array_keys(config('location.states')))],
            'county_id' => ['nullable', 'numeric', 'exists:counties,id'],
            'county_name' => ['nullable', 'string', 'max:255'],
            'email' => ['nullable', 'string', 'email', 'max:255'],
            'city' => ['nullable', 'string', 'max:255'],
            'phone' => ['nullable', 'string', 'max:255'],
            'school_id' => ['nullable', 'numeric'],
            'school_name' => ['nullable', 'string', 'max:255'],
            'graduation_year' => ['nullable', 'numeric', 'min:2020', 'max:2030'],
            'gpa' => ['nullable', 'numeric', 'min:0', 'max:4.5'],
            'class_rank' => ['nullable', 'string', 'max:255'],
            'gender' => ['nullable', 'string', Rule::enum(Gender::class)],
            'height_in_inches' => ['nullable', 'numeric', 'min:36', 'max:96'],
            'weight' => ['nullable', 'numeric', 'min:50', 'max:400'],
            'instagram' => ['nullable', 'string', 'max:255'],
            'facebook' => ['nullable', 'string', 'max:255'],
            'twitter' => ['nullable', 'string', 'max:255'],
            'hudl' => ['nullable', 'string', 'max:255'],
            'custom_link' => ['nullable', 'string', 'max:255'],
            'interest_ids' => ['nullable', 'array'],
            'interest_ids.*' => ['numeric', 'exists:interests,id'],
        ];
    }

    public static function fromModel(User $user): self
    {
        return new self(
            state: $user->county?->state_code ?? $user->state_code,
            county_id: $user->county_id,
            county_name: $user->county?->name,
            email: $user->email,
            city: $user->city,
            phone: $user->phone,
            school_id: $user->school_id,
            school_name: $user->school?->name,
            graduation_year: $user->graduation_year,
            gpa: $user->gpa,
            class_rank: $user->class_rank,
            gender: $user->gender,
            height_in_inches: $user->height_in_inches,
            weight: $user->weight,
            instagram: $user->instagram,
            facebook: $user->facebook,
            twitter: $user->twitter,
            hudl: $user->hudl,
            custom_link: $user->custom_link,
            interest_ids: $user->interests->pluck('id')->toArray(),
            interests: new DataCollection(InterestData::class, $user->interests->map(fn ($interest) => InterestData::from($interest))->all()),
        );
    }
}
