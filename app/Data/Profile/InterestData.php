<?php
namespace App\Data\Profile;

use App\Models\Interest;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="Interest",
 *     required={"id", "name"},
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         format="int64",
 *         example=1,
 *         description="Interest unique identifier"
 *     ),
 *     @OA\Property(
 *         property="name",
 *         type="string",
 *         example="Basketball",
 *         description="Name of the interest"
 *     ),
 *     @OA\Property(
 *         property="icon",
 *         type="string",
 *         nullable=true,
 *         example="fa-basketball",
 *         description="Font Awesome icon class for the interest"
 *     )
 * )
 */
class InterestData extends Data
{
    public function __construct(
        public int $id,
        public string $name,
        public ?string $icon,
    ) {}

    public static function fromModel(Interest $interest): self
    {
        return new self(
            id: $interest->id,
            name: $interest->name,
            icon: $interest->icon,
        );
    }
}
