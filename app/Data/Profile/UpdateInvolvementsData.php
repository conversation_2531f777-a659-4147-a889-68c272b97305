<?php

namespace App\Data\Profile;

use Spa<PERSON>\LaravelData\Data;
use Spatie\LaravelData\Attributes\DataCollectionOf;

/**
 * @OA\Schema(
 *     schema="UpdateInvolvementsData",
 *     required={"involvements"},
 *     @OA\Property(
 *         property="involvements",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/InvolvementData")
 *     )
 * )
 */
class UpdateInvolvementsData extends Data
{
    public function __construct(
        #[DataCollectionOf(InvolvementData::class)]
        public readonly array $involvements,
    ) {}
}
