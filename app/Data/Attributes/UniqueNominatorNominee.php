<?php

namespace App\Data\Attributes;

use App\Rules\UniqueNominatorNominee as UniqueNominatorNomineeRule;
use Attribute;
use Spatie\LaravelData\Attributes\Validation\CustomValidationAttribute;
use Spatie\LaravelData\Support\Validation\ValidationPath;

#[Attribute(Attribute::TARGET_PROPERTY | Attribute::TARGET_PARAMETER)]
class UniqueNominatorNominee extends CustomValidationAttribute
{
    public function getRules(ValidationPath $path): array|object|string
    {
        return [new UniqueNominatorNomineeRule()];
    }
}
