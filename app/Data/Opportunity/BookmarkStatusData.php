<?php

namespace App\Data\Opportunity;

use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     title="Bookmark Status Data",
 *     description="Data structure for opportunity bookmark status",
 *     @OA\Property(property="isBookmarked", type="boolean", example=true),
 *     @OA\Property(property="opportunityId", type="integer", example=1)
 * )
 */
class BookmarkStatusData extends Data
{
    public function __construct(
        public bool $isBookmarked,
        public int $opportunityId
    ) {}

    public static function fromBookmarkStatus(bool $isBookmarked, int $opportunityId): self
    {
        return new self(
            isBookmarked: $isBookmarked,
            opportunityId: $opportunityId
        );
    }
}
