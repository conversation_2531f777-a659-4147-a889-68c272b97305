<?php

namespace App\Data\Opportunity;

use Spa<PERSON>\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;

/**
 * @OA\Schema(
 *     title="Bookmarked IDs Data",
 *     description="Data structure for bookmarked opportunity IDs",
 *     @OA\Property(
 *         property="ids",
 *         type="array",
 *         @OA\Items(type="integer", example="3")
 *     )
 * )
 */
class BookmarkedIdsData extends Data
{
    public function __construct(
        public array $ids
    ) {}

    public static function fromIds(array $ids): self
    {
        return new self(
            ids: $ids
        );
    }
}
