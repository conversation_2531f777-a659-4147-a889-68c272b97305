<?php

namespace App\Data\Parent;

use <PERSON><PERSON>\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="ParentAccountInfoDTO",
 *     required={"first_name", "last_name", "email", "phone", "password"},
 *     title="Parent Account Info Data",
 *     @OA\Property(property="token", type="string", example="**********"),
 *     @OA\Property(property="first_name", type="string", example="John"),
 *     @OA\Property(property="last_name", type="string", example="Doe"),
 *     @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
 *     @OA\Property(property="phone", type="string", example="************"),
 *     @OA\Property(property="password", type="string", example="password123"),
 * )
 */
class ParentAccountInfoDTO extends Data
{
    public function __construct(
        public string $first_name,
        public string $last_name,
        public string $email,
        public string $phone,
        public string $password,
    ) {}

    public static function rules(): array
    {
        return [
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'unique:users,email'],
            'phone' => ['required', 'string', 'max:20'],
            'password' => ['required', 'string', 'min:8'],
        ];
    }
}
