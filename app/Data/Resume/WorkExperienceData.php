<?php

namespace App\Data\Resume;

use <PERSON><PERSON>\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Data;

class WorkExperienceData extends Data
{
    public function __construct(
        #[Required]
        public string $id,

        #[Required]
        #[Max(255)]
        public string $name,

        #[Max(255)]
        public ?string $date = null,

        public ?string $description = null,

        public ?int $order = null,
    ) {}
}
