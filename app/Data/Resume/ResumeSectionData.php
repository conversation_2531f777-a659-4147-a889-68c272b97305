<?php

namespace App\Data\Resume;

use App\Models\ResumeSection;
use <PERSON>tie\LaravelData\Attributes\Validation\In;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="ResumeSectionData",
 *     required={"section_type", "content", "is_enabled"},
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         format="int64",
 *         description="ID of the resume section"
 *     ),
 *     @OA\Property(
 *         property="section_type",
 *         type="string",
 *         enum={"profile", "contact", "education", "involvement", "experience", "sports"},
 *         description="Type of the resume section"
 *     ),
 *     @OA\Property(
 *         property="content",
 *         type="object",
 *         description="Content of the resume section"
 *     ),
 *     @OA\Property(
 *         property="is_enabled",
 *         type="boolean",
 *         description="Whether the section is visible"
 *     )
 * )
 */
class ResumeSectionData extends Data
{
    public function __construct(
        public ?int $id = null,
        #[Required]
        #[In(['profile', 'contact', 'education', 'involvement', 'experience', 'sports'])]
        public string $section_type,
        #[Required]
        public array $content,
        #[Required]
        public bool $is_enabled,
        public ?string $created_at = null,
        public ?string $updated_at = null,
    ) {}

    public static function fromModel(ResumeSection $section): self
    {
        return new self(
            id: $section->id,
            section_type: $section->section_type,
            content: $section->content,
            is_enabled: $section->is_enabled,
            created_at: $section->created_at?->toISOString(),
            updated_at: $section->updated_at?->toISOString(),
        );
    }
}
