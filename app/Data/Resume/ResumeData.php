<?php

namespace App\Data\Resume;

use Illuminate\Support\Collection;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;
use App\Models\Resume;
use Illuminate\Http\Request;

/**
 * @OA\Schema(
 *     schema="ResumeData",
 *     required={"name", "sections"},
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         format="int64",
 *         example=1,
 *         description="The unique identifier of the resume"
 *     ),
 *     @OA\Property(
 *         property="name",
 *         type="string",
 *         maxLength=255,
 *         example="My Professional Resume"
 *     ),
 *     @OA\Property(
 *         property="sections",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/ResumeSectionData")
 *     ),
 *     @OA\Property(
 *         property="avatar",
 *         ref="#/components/schemas/ResumeAvatarData",
 *         nullable=true
 *     )
 * )
 */
class ResumeData extends Data
{
    public function __construct(
        public ?int $id = null,

        #[Required]
        #[Max(255)]
        public string $name,

        /** @var Collection<ResumeSectionData> */
        #[Required]
        #[DataCollectionOf(ResumeSectionData::class)]
        public DataCollection $sections,

        public ?ResumeAvatarData $avatar = null,
    ) {}

    public static function from(mixed ...$payloads): static
    {
        $model = $payloads[0] ?? null;

        if ($model instanceof Resume) {
            return self::fromModel($model);
        }

        return parent::from(...$payloads);
    }

    public static function fromModel(Resume $model): self
    {
        $sections = collect($model->sections)->map(fn ($section) => ResumeSectionData::from($section));

        return new self(
            id: $model->getKey(),
            name: $model->name,
            sections: new DataCollection(ResumeSectionData::class, $sections),
            avatar: $model->getFirstMedia('avatar') ? ResumeAvatarData::fromMedia($model->getFirstMedia('avatar')) : null,
        );
    }
}
