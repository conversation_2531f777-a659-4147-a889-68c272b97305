<?php

namespace App\Data\Resume;

use Spatie\LaravelData\Data;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * @OA\Schema(
 *     schema="ResumeAvatarData",
 *     required={"url", "thumbnail_url"},
 *     @OA\Property(property="url", type="string", format="uri", description="Full size avatar URL"),
 *     @OA\Property(property="thumbnail_url", type="string", format="uri", description="Thumbnail version of avatar URL"),
 *     @OA\Property(property="responsive_urls", type="object", description="URLs for different responsive image sizes")
 * )
 */
class ResumeAvatarData extends Data
{
    public function __construct(
        public string $url,
        public string $thumbnail_url,
        public ?array $responsive_urls = null,
    ) {}

    public static function fromMedia(?Media $media): self
    {
        if (!$media) {
            return new self(
                url: '',
                thumbnail_url: '',
                responsive_urls: [],
            );
        }

        return new self(
            url: $media->getFullUrl(),
            thumbnail_url: $media->getFullUrl('thumbnail'),
            responsive_urls: $media->getResponsiveImageUrls(),
        );
    }
}
