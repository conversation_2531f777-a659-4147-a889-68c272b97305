<?php

namespace App\Data\Resume;

use Spa<PERSON>\LaravelData\Attributes\Validation\Email;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Required;
use <PERSON>tie\LaravelData\Data;

class ProfileSectionData extends Data
{
    public function __construct(
        #[Required]
        #[Max(255)]
        public string $firstName,

        #[Required]
        #[Max(255)]
        public string $lastName,

        #[Required]
        #[Email]
        #[Max(255)]
        public string $email,

        #[Max(255)]
        public ?string $phone = null,

        public ?AddressData $address = null,

        public ?AcademicData $academic = null,
    ) {}
}
