<?php

namespace App\Data\Resume;

use <PERSON><PERSON>\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Data;

class AddressData extends Data
{
    public function __construct(
        #[Max(255)]
        public ?string $street1 = null,

        #[Max(255)]
        public ?string $street2 = null,

        #[Max(255)]
        public ?string $city = null,

        #[Max(255)]
        public ?string $state = null,

        #[Max(255)]
        public ?string $zip = null,
    ) {}
}
