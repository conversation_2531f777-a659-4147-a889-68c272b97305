<?php

namespace App\Data\Resume;

use Illuminate\Http\UploadedFile;
use <PERSON>tie\LaravelData\Data;
use Spatie\LaravelData\Attributes\Validation\Image;
use Spatie\LaravelData\Attributes\Validation\Required;

/**
 * @OA\Schema(
 *     schema="UpdateResumeAvatarData",
 *     required={"file"},
 *     @OA\Property(
 *         property="file",
 *         type="string",
 *         format="binary",
 *         description="The avatar image file to upload"
 *     )
 * )
 */
class UpdateResumeAvatarData extends Data
{
    public function __construct(
        #[Required, Image]
        public UploadedFile $file
    ) {}

    public static function rules(): array
    {
        return [
            'file' => ['required', 'image', 'max:5120'], // 5MB max
        ];
    }
}
