<?php

namespace App\Data\Resume;

use Spatie\LaravelData\Attributes\Validation\Between;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Data;

class AcademicData extends Data
{
    public function __construct(
        #[Between(0, 5.0)]
        public ?float $gpa = null,

        #[Max(255)]
        public ?string $classRank = null,

        #[Between(1900, 2100)]
        public ?int $graduationYear = null,
    ) {}
}
