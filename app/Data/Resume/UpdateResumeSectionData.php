<?php

namespace App\Data\Resume;

use Illuminate\Http\UploadedFile;
use <PERSON>tie\LaravelData\Data;
use Spatie\LaravelData\Attributes\Validation\Image;
use Spatie\LaravelData\Attributes\Validation\Required;

/**
 * @OA\Schema(
 *     schema="UpdateResumeSectionData",
 *     required={"content"},
 *     @OA\Property(
 *         property="content",
 *         type="object",
 *         description="The content of the resume section"
 *     ),
 *     @OA\Property(
 *         property="is_enabled",
 *         type="boolean",
 *         description="Whether the section is enabled"
 *     ),
 *     @OA\Property(
 *         property="avatar",
 *         type="string",
 *         format="binary",
 *         description="Avatar image file (only for profile section)"
 *     )
 * )
 */
class UpdateResumeSectionData extends Data
{
    public function __construct(
        #[Required]
        public array $content,
        public ?bool $is_enabled = null,
        #[Image]
        public ?UploadedFile $avatar = null,
    ) {}

    public static function rules(): array
    {
        $sectionType = request()->route('type');
        $baseRules = [
            'content' => ['required', 'array'],
            'is_enabled' => ['boolean', 'nullable'],
            'avatar' => ['nullable', 'image', 'max:5120'],
        ];

        // Section-specific validation rules
        $sectionRules = match ($sectionType) {
            'profile' => [
                'content.name' => ['required', 'string', 'max:255'],
                'content.description' => ['nullable', 'string'],
                'content.showPhoto' => ['required', 'boolean'],
            ],
            'contact' => [
                'content.email' => ['required', 'email', 'max:255'],
                'content.phone' => ['nullable', 'string', 'max:255'],
                'content.location' => ['nullable', 'string', 'max:255'],
            ],
            'education', 'involvement', 'experience', 'sports' => [
                'content.items' => ['required', 'array'],
                'content.items.*.id' => ['required', 'string'],
            ],
            default => [],
        };

        // Add specific rules for list-based sections
        if (in_array($sectionType, ['education', 'involvement', 'experience', 'sports'])) {
            $itemRules = match ($sectionType) {
                'education' => [
                    'content.items.*.schoolName' => ['required', 'string', 'max:255'],
                    'content.items.*.dateRange' => ['required', 'string', 'max:255'],
                    'content.items.*.gpa' => ['nullable', 'string', 'max:255'],
                    'content.items.*.classRank' => ['nullable', 'string', 'max:255'],
                    'content.items.*.achievements' => ['nullable', 'string'],
                ],
                'involvement' => [
                    'content.items.*.name' => ['required', 'string', 'max:255'],
                    'content.items.*.role' => ['nullable', 'string', 'max:255'],
                    'content.items.*.dateRange' => ['required', 'string', 'max:255'],
                    'content.items.*.description' => ['nullable', 'string'],
                ],
                'experience' => [
                    'content.items.*.companyName' => ['required', 'string', 'max:255'],
                    'content.items.*.role' => ['nullable', 'string', 'max:255'],
                    'content.items.*.dateRange' => ['required', 'string', 'max:255'],
                    'content.items.*.description' => ['nullable', 'string'],
                ],
                'sports' => [
                    'content.items.*.name' => ['required', 'string', 'max:255'],
                    'content.items.*.position' => ['nullable', 'string', 'max:255'],
                    'content.items.*.level' => ['nullable', 'string', 'max:255'],
                    'content.items.*.dateRange' => ['required', 'string', 'max:255'],
                    'content.items.*.achievements' => ['nullable', 'string'],
                ],
                default => [],
            };
            $sectionRules = array_merge($sectionRules, $itemRules);
        }

        return array_merge($baseRules, $sectionRules);
    }
}
