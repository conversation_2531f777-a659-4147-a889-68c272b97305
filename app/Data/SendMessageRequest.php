<?php

namespace App\Data;

use <PERSON><PERSON>\LaravelData\Data;
use Spatie\LaravelData\Attributes\Validation\Exists;
use <PERSON>tie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Attributes\Validation\Nullable;

/**
 * @OA\Schema(
 *     schema="SendMessageRequest",
 *     title="Send Message Request",
 *     description="Data required to send a new message",
 *     required={"recipientId"},
 *     @OA\Property(
 *         property="recipientId",
 *         type="integer",
 *         description="ID of the message recipient",
 *         example=123
 *     ),
 *     @OA\Property(
 *         property="content",
 *         type="string",
 *         nullable=true,
 *         description="Message content text",
 *         example="Hello, how are you doing today?"
 *     )
 * )
 */
class SendMessageRequest extends Data
{
    public function __construct(
        #[Required]
        #[Exists('users', 'id')]
        public readonly int $recipientId,

        #[Nullable]
        #[StringType]
        public readonly ?string $content,
    ) {
    }

    public static function rules(): array
    {
        return [
            'recipientId' => ['required', 'integer', 'exists:users,id'],
            'content' => ['nullable', 'string'],
        ];
    }
}
