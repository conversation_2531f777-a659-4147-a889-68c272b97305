<?php

namespace App\Data;

use App\Enums\ProfileType;
use App\Models\Message;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="MessageData",
 *     title="Message Data",
 *     description="Data representing a message between users",
 *     @OA\Property(property="id", type="integer", example=123, description="Message ID"),
 *     @OA\Property(property="senderId", type="integer", example=456, description="ID of the message sender"),
 *     @OA\Property(property="recipientId", type="integer", example=789, description="ID of the message recipient"),
 *     @OA\Property(property="content", type="string", nullable=true, example="Hello, how are you?", description="Message content"),
 *     @OA\Property(property="readAt", type="string", format="date-time", nullable=true, example="2023-05-15T14:35:00Z", description="Timestamp when the message was read"),
 *     @OA\Property(property="createdAt", type="string", format="date-time", example="2023-05-15T14:30:00Z", description="Timestamp when the message was created"),
 *     @OA\Property(
 *         property="sender",
 *         description="Basic data about the sender",
 *         nullable=true,
 *         allOf={
 *             @OA\Schema(ref="#/components/schemas/UserBasicData")
 *         }
 *     ),
 *     @OA\Property(
 *         property="recipient",
 *         description="Basic data about the recipient",
 *         nullable=true,
 *         allOf={
 *             @OA\Schema(ref="#/components/schemas/UserBasicData")
 *         }
 *     ),
 *     @OA\Property(property="isFlagged", type="boolean", example=false, description="Whether the message has been flagged for moderation"),
 *     @OA\Property(property="editedAt", type="string", format="date-time", nullable=true, example="2023-05-15T14:35:00Z", description="Timestamp when the message was edited"),
 *     @OA\Property(property="deletedBySenderAt", type="string", format="date-time", nullable=true, example="2023-05-15T14:35:00Z", description="Timestamp when the message was deleted by sender"),
 *     @OA\Property(property="deletedByRecipientAt", type="string", format="date-time", nullable=true, example="2023-05-15T14:35:00Z", description="Timestamp when the message was deleted by recipient")
 * )
 */
class MessageData extends Data
{
    public function __construct(
        public readonly int $id,
        public readonly int $senderId,
        public readonly int $recipientId,
        public readonly ?string $content,
        public readonly ?string $readAt,
        public readonly string $createdAt,
        public readonly ?UserBasicData $sender,
        public readonly ?UserBasicData $recipient,
        public readonly bool $isFlagged = false,
        public readonly ?string $editedAt = null,
        public readonly ?string $deletedBySenderAt = null,
        public readonly ?string $deletedByRecipientAt = null,
    ) {
    }

    /**
     * Create a MessageData instance from a Message model.
     *
     * @param Message $message
     * @return static
     */
    public static function fromMessage(Message $message): static
    {
        // Make sure sender and recipient are loaded with their organization data
        if (!$message->relationLoaded('sender') || !$message->relationLoaded('recipient')) {
            $message->load([
                'sender' => function ($query) {
                    $query->with(['activeOrganization' => function ($q) {
                        $q->with('media');
                    }, 'media']);
                },
                'recipient' => function ($query) {
                    $query->with(['activeOrganization' => function ($q) {
                        $q->with('media');
                    }, 'media']);
                }
            ]);
        } else {
            // If sender/recipient are loaded but organization isn't, load it separately
            if ($message->sender && !$message->sender->relationLoaded('activeOrganization')) {
                $message->sender->load(['activeOrganization.media', 'media']);
            }

            if ($message->recipient && !$message->recipient->relationLoaded('activeOrganization')) {
                $message->recipient->load(['activeOrganization.media', 'media']);
            }
        }

        // Process organization data for sender if they're a sponsor
        if ($message->sender &&
            $message->sender->profile_type === ProfileType::SPONSOR &&
            $message->sender->relationLoaded('activeOrganization') &&
            $message->sender->activeOrganization->isNotEmpty()) {

            $org = $message->sender->activeOrganization->first();
            $message->sender->organization_id = $org->id;
            $message->sender->organization_name = $org->name;
            $message->sender->organization_logo_url = $org->hasMedia('logo') ?
                $org->getFirstMediaUrl('logo', 'thumb') : null;
        }

        // Process organization data for recipient if they're a sponsor
        if ($message->recipient &&
            $message->recipient->profile_type === ProfileType::SPONSOR &&
            $message->recipient->relationLoaded('activeOrganization') &&
            $message->recipient->activeOrganization->isNotEmpty()) {

            $org = $message->recipient->activeOrganization->first();
            $message->recipient->organization_id = $org->id;
            $message->recipient->organization_name = $org->name;
            $message->recipient->organization_logo_url = $org->hasMedia('logo') ?
                $org->getFirstMediaUrl('logo', 'thumb') : null;
        }

        // Now create DTO objects with the processed data
        $sender = $message->sender ? UserBasicData::fromUser($message->sender) : null;
        $recipient = $message->recipient ? UserBasicData::fromUser($message->recipient) : null;

        return new static(
            id: $message->id,
            senderId: $message->sender_id,
            recipientId: $message->recipient_id,
            content: $message->content,
            readAt: $message->read_at?->toIso8601String(),
            createdAt: $message->created_at->toIso8601String(),
            sender: $sender,
            recipient: $recipient,
            isFlagged: $message->is_flagged,
            editedAt: $message->edited_at?->toIso8601String(),
            deletedBySenderAt: $message->deleted_by_sender_at?->toIso8601String(),
            deletedByRecipientAt: $message->deleted_by_recipient_at?->toIso8601String(),
        );
    }
}
