<?php

namespace App\Data\Public;

use App\Models\User;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="PublicProfileData",
 *     title="Public Profile Data",
 *     description="Public profile information",
 * )
 */
class PublicProfileData extends Data
{
    /**
     * @OA\Property(
     *     property="id",
     *     type="string",
     *     format="uuid",
     *     description="User ID"
     * )
     */
    public string $id;

    /**
     * @OA\Property(
     *     property="first_name",
     *     type="string",
     *     description="First name"
     * )
     */
    public string $first_name;

    /**
     * @OA\Property(
     *     property="last_name",
     *     type="string",
     *     description="Last name"
     * )
     */
    public string $last_name;

    /**
     * @OA\Property(
     *     property="profile_type",
     *     type="string",
     *     description="The user's profile type",
     *     ref="#/components/schemas/ProfileType"
     *
     * )
     */
    public string $profile_type;

    /**
     * @OA\Property(
     *     property="school_name",
     *     type="string",
     *     nullable=true,
     *     description="School name"
     * )
     */
    public ?string $school_name;

    /**
     * @OA\Property(
     *     property="graduation_year",
     *     type="integer",
     *     nullable=true,
     *     description="Graduation year"
     * )
     */
    public ?int $graduation_year;

    /**
     * @OA\Property(
     *     property="twitter",
     *     type="string",
     *     nullable=true,
     *     description="Twitter handle"
     * )
     */
    public ?string $twitter;

    /**
     * @OA\Property(
     *     property="linkedin",
     *     type="string",
     *     nullable=true,
     *     description="LinkedIn profile"
     * )
     */
    public ?string $linkedin;

    /**
     * @OA\Property(
     *     property="instagram",
     *     type="string",
     *     nullable=true,
     *     description="Instagram handle"
     * )
     */
    public ?string $instagram;

    /**
     * @OA\Property(
     *     property="facebook",
     *     type="string",
     *     nullable=true,
     *     description="Facebook profile"
     * )
     */
    public ?string $facebook;

    /**
     * @OA\Property(
     *     property="hudl",
     *     type="string",
     *     nullable=true,
     *     description="Hudl profile"
     * )
     */
    public ?string $hudl;

    /**
     * @OA\Property(
     *     property="custom_link",
     *     type="string",
     *     nullable=true,
     *     description="Custom link"
     * )
     */
    public ?string $custom_link;

    /**
     * @OA\Property(
     *     property="profile_meta",
     *     type="object",
     *     nullable=true,
     *     description="Profile metadata - flexible JSON object containing additional profile information"
     * )
     */
    public ?array $profile_meta;

    public function __construct(
        string $id,
        string $first_name,
        string $last_name,
        string $profile_type,
        ?string $school_name = null,
        ?int $graduation_year = null,
        ?string $twitter = null,
        ?string $linkedin = null,
        ?string $instagram = null,
        ?string $facebook = null,
        ?string $hudl = null,
        ?string $custom_link = null,
        ?array $profile_meta = null
    ) {
        $this->id = $id;
        $this->first_name = $first_name;
        $this->last_name = $last_name;
        $this->profile_type = $profile_type;
        $this->school_name = $school_name;
        $this->graduation_year = $graduation_year;
        $this->twitter = $twitter;
        $this->linkedin = $linkedin;
        $this->instagram = $instagram;
        $this->facebook = $facebook;
        $this->hudl = $hudl;
        $this->custom_link = $custom_link;
        $this->profile_meta = $profile_meta;
    }

    /**
     * Create a new PublicProfileData instance from a User model.
     *
     * @param User $user
     * @return self
     */
    public static function fromModel(User $user): self
    {
        return new self(
            id: $user->id,
            first_name: $user->first_name,
            last_name: $user->last_name,
            profile_type: $user->profile_type->value,
            school_name: $user->school?->name,
            graduation_year: $user->graduation_year,
            twitter: $user->twitter,
            linkedin: $user->linkedin,
            instagram: $user->instagram,
            facebook: $user->facebook,
            hudl: $user->hudl,
            custom_link: $user->custom_link,
            profile_meta: $user->metadata
        );
    }
}
