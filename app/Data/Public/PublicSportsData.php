<?php

namespace App\Data\Public;

use App\Models\User;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;

/**
 * @OA\Schema(
 *     schema="PublicSportsData",
 *     title="Public Sports Data",
 *     description="Public sports information"
 * )
 */
class PublicSportsData extends Data
{
    /**
     * @OA\Property(
     *     property="sports",
     *     type="array",
     *     @OA\Items(ref="#/components/schemas/PublicSportData")
     * )
     *
     * @var DataCollection<PublicSportData>
     */
    public DataCollection $sports;

    public function __construct(
        DataCollection $sports
    ) {
        $this->sports = $sports;
    }

    /**
     * Create a new PublicSportsData instance from a User model.
     *
     * @param User $user
     * @return self
     */
    public static function fromModel(User $user): self
    {
        // Fetch platform sports with pivot data
        $platformSports = $user->sports()
            ->get()
            ->map(fn ($sport) => [
                'sport' => PublicSportData::from([
                    'id' => $sport->id,
                    'name' => $sport->name,
                    'is_custom' => false,
                ]),
                'order' => $sport->pivot->order,
            ]);

        // Fetch custom sports
        $customSports = $user->customSports()
            ->get()
            ->map(fn ($sport) => [
                'sport' => PublicSportData::from([
                    'id' => null,
                    'name' => $sport->name,
                    'is_custom' => true,
                ]),
                'order' => $sport->order,
            ]);

        // Combine and sort by order
        $sports = collect([...$platformSports, ...$customSports])
            ->sortBy('order')
            ->map(fn ($item) => $item['sport'])
            ->values()
            ->all();

        return new self(
            sports: new DataCollection(PublicSportData::class, $sports)
        );
    }
}
