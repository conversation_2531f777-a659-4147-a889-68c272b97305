<?php

namespace App\Data\Public;

use Spatie\LaravelData\Data;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * @OA\Schema(
 *     schema="PublicAvatarData",
 *     title="Public Avatar Data",
 *     description="Public avatar information",
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         description="Avatar media ID",
 *         nullable=true
 *     ),
 *     @OA\Property(
 *         property="url",
 *         type="string",
 *         description="Avatar URL"
 *     )
 * )
 */
class PublicAvatarData extends Data
{

    public ?int $id;

    public string $url;

    public function __construct(
        ?int $id = null,
        string $url
    ) {
        $this->id = $id;
        $this->url = $url;
    }

    /**
     * Create a new PublicAvatarData instance from a Media model.
     *
     * @param Media $media
     * @return self
     */
    public static function fromMedia(Media $media): self
    {
        return new self(
            id: $media->id,
            url: $media->getUrl()
        );
    }
}
