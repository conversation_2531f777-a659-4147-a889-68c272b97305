<?php

namespace App\Data\Public;

use App\Models\User;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="PublicCareerInterestsData",
 *     title="Public Career Interests Data",
 *     description="Public career interests information"
 * )
 */
class PublicCareerInterestsData extends Data
{
    /**
     * @OA\Property(
     *     property="interests",
     *     type="array",
     *     @OA\Items(
     *         type="object",
     *         @OA\Property(property="id", type="integer", description="Interest ID"),
     *         @OA\Property(property="name", type="string", description="Interest name"),
     *         @OA\Property(property="icon", type="string", nullable=true, description="Interest icon")
     *     ),
     *     description="Career interests"
     * )
     */
    public array $interests;

    public function __construct(
        array $interests
    ) {
        $this->interests = $interests;
    }

    /**
     * Create a new PublicCareerInterestsData instance from a User model.
     *
     * @param User $user
     * @return self
     */
    public static function fromModel(User $user): self
    {
        $user->loadMissing('interests');

        $interests = $user->interests->map(function ($interest) {
            return [
                'id' => $interest->id,
                'name' => $interest->name,
                'icon' => $interest->icon,
            ];
        })->toArray();

        return new self(
            interests: $interests
        );
    }
}
