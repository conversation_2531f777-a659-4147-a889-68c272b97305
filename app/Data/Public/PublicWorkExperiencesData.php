<?php

namespace App\Data\Public;

use App\Models\User;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;

/**
 * @OA\Schema(
 *     schema="PublicWorkExperiencesData",
 *     title="Public Work Experiences Data",
 *     description="Public work experiences information"
 * )
 */
class PublicWorkExperiencesData extends Data
{
    /**
     * @OA\Property(
     *     property="experiences",
     *     type="array",
     *     @OA\Items(ref="#/components/schemas/PublicWorkExperienceData")
     * )
     *
     * @var DataCollection<PublicWorkExperienceData>
     */
    public DataCollection $experiences;

    public function __construct(
        DataCollection $experiences
    ) {
        $this->experiences = $experiences;
    }

    /**
     * Create a new PublicWorkExperiencesData instance from a User model.
     *
     * @param User $user
     * @return self
     */
    public static function fromModel(User $user): self
    {
        $experiences = $user->workExperiences()
            ->orderBy('order')
            ->get()
            ->map(fn ($experience) => PublicWorkExperienceData::from([
                'id' => $experience->id,
                'title' => $experience->name ?? 'Untitled',
                'company' => 'N/A',
                'date_range' => $experience->date ?? '',
                'description' => $experience->description ?? '',
            ]))
            ->toArray();

        return new self(
            experiences: new DataCollection(PublicWorkExperienceData::class, $experiences)
        );
    }
}
