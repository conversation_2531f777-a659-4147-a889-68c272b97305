<?php

namespace App\Data\Public;

use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="PublicInvolvementData",
 *     title="Public Involvement Data",
 *     description="Public involvement information"
 * )
 */
class PublicInvolvementData extends Data
{
    /**
     * @OA\Property(
     *     property="id",
     *     type="integer",
     *     description="Involvement ID"
     * )
     */
    public ?int $id;

    /**
     * @OA\Property(
     *     property="title",
     *     type="string",
     *     description="Involvement title"
     * )
     */
    public string $title;

    /**
     * @OA\Property(
     *     property="date_range",
     *     type="string",
     *     description="Date range"
     * )
     */
    public string $date_range;

    /**
     * @OA\Property(
     *     property="description",
     *     type="string",
     *     description="Involvement description"
     * )
     */
    public string $description;

    public function __construct(
        ?int $id = null,
        string $title,
        string $date_range,
        string $description
    ) {
        $this->id = $id;
        $this->title = $title;
        $this->date_range = $date_range;
        $this->description = $description;
    }
}
