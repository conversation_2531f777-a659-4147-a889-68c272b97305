<?php

namespace App\Data\Public;

use <PERSON>tie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="PublicWorkExperienceData",
 *     title="Public Work Experience Data",
 *     description="Public work experience information"
 * )
 */
class PublicWorkExperienceData extends Data
{
    /**
     * @OA\Property(
     *     property="id",
     *     type="integer",
     *     description="Work experience ID"
     * )
     */
    public ?int $id;

    /**
     * @OA\Property(
     *     property="title",
     *     type="string",
     *     description="Job title"
     * )
     */
    public string $title;

    /**
     * @OA\Property(
     *     property="company",
     *     type="string",
     *     description="Company name"
     * )
     */
    public string $company;

    /**
     * @OA\Property(
     *     property="date_range",
     *     type="string",
     *     description="Date range"
     * )
     */
    public string $date_range;

    /**
     * @OA\Property(
     *     property="description",
     *     type="string",
     *     description="Job description"
     * )
     */
    public string $description;

    public function __construct(
        ?int $id = null,
        string $title = 'Untitled',
        string $company = 'N/A',
        string $date_range = '',
        string $description = ''
    ) {
        $this->id = $id;
        $this->title = $title;
        $this->company = $company;
        $this->date_range = $date_range;
        $this->description = $description;
    }
}
