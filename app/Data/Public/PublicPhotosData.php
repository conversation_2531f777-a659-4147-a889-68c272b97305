<?php

namespace App\Data\Public;

use <PERSON><PERSON>\LaravelData\Data;
use Spatie\LaravelData\DataCollection;
use Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection;

/**
 * @OA\Schema(
 *     schema="PublicPhotosData",
 *     title="Public Photos Data",
 *     description="Public photos information"
 * )
 */
class PublicPhotosData extends Data
{
    /**
     * @OA\Property(
     *     property="photos",
     *     type="array",
     *     @OA\Items(ref="#/components/schemas/PublicPhotoData")
     * )
     *
     * @var DataCollection<PublicPhotoData>
     */
    public DataCollection $photos;

    public function __construct(
        DataCollection $photos
    ) {
        $this->photos = $photos;
    }

    /**
     * Create a new PublicPhotosData instance from a MediaCollection.
     *
     * @param MediaCollection $mediaCollection
     * @return self
     */
    public static function fromMediaCollection(MediaCollection $mediaCollection): self
    {
        $photos = $mediaCollection->map(function ($media) {
            return PublicPhotoData::from([
                'id' => $media->id,
                'url' => $media->getUrl(),
                'width' => $media->getCustomProperty('width'),
                'height' => $media->getCustomProperty('height'),
                'focal_point' => $media->getCustomProperty('focal_point'),
            ]);
        })->toArray();

        return new self(
            photos: new DataCollection(PublicPhotoData::class, $photos)
        );
    }
}
