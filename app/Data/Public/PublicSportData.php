<?php

namespace App\Data\Public;

use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="PublicSportData",
 *     title="Public Sport Data",
 *     description="Public sport information"
 * )
 */
class PublicSportData extends Data
{
    /**
     * @OA\Property(
     *     property="id",
     *     type="integer",
     *     nullable=true,
     *     description="Sport ID (null for custom sports)"
     * )
     */
    public ?int $id;

    /**
     * @OA\Property(
     *     property="name",
     *     type="string",
     *     description="Sport name"
     * )
     */
    public string $name;

    /**
     * @OA\Property(
     *     property="is_custom",
     *     type="boolean",
     *     description="Whether this is a custom sport"
     * )
     */
    public bool $is_custom;

    public function __construct(
        ?int $id,
        string $name,
        bool $is_custom
    ) {
        $this->id = $id;
        $this->name = $name;
        $this->is_custom = $is_custom;
    }
}
