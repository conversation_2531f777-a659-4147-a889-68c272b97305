<?php

namespace App\Data\Public;

use App\Models\User;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;

/**
 * @OA\Schema(
 *     schema="PublicInvolvementsData",
 *     title="Public Involvements Data",
 *     description="Public involvements information"
 * )
 */
class PublicInvolvementsData extends Data
{
    /**
     * @OA\Property(
     *     property="involvements",
     *     type="array",
     *     @OA\Items(ref="#/components/schemas/PublicInvolvementData")
     * )
     *
     * @var DataCollection<PublicInvolvementData>
     */
    public DataCollection $involvements;

    public function __construct(
        DataCollection $involvements
    ) {
        $this->involvements = $involvements;
    }

    /**
     * Create a new PublicInvolvementsData instance from a User model.
     *
     * @param User $user
     * @return self
     */
    public static function fromModel(User $user): self
    {
        $involvements = $user->communityInvolvements()
            ->orderBy('order')
            ->get()
            ->map(fn ($involvement) => PublicInvolvementData::from([
                'id' => $involvement->id,
                'title' => $involvement->title,
                'date_range' => $involvement->date_range,
                'description' => $involvement->description,
            ]))
            ->toArray();

        return new self(
            involvements: new DataCollection(PublicInvolvementData::class, $involvements)
        );
    }
}
