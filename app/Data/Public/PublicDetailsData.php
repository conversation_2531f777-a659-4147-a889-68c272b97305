<?php

namespace App\Data\Public;

use App\Models\User;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="PublicDetailsData",
 *     title="Public Details Data",
 *     description="Public detailed profile information"
 * )
 */
class PublicDetailsData extends Data
{
    /**
     * @OA\Property(
     *     property="county",
     *     type="string",
     *     nullable=true,
     *     description="County name"
     * )
     */
    public ?string $county;

    /**
     * @OA\Property(
     *     property="state",
     *     type="string",
     *     nullable=true,
     *     description="State name"
     * )
     */
    public ?string $state;

    /**
     * @OA\Property(
     *     property="gpa",
     *     type="number",
     *     format="float",
     *     nullable=true,
     *     description="Current GPA"
     * )
     */
    public ?float $gpa;

    /**
     * @OA\Property(
     *     property="class_rank",
     *     type="string",
     *     nullable=true,
     *     description="Current class rank (e.g. '16/402')"
     * )
     */
    public ?string $class_rank;

    /**
     * @OA\Property(
     *     property="gender",
     *     type="string",
     *     nullable=true,
     *     description="Gender"
     * )
     */
    public ?string $gender;

    /**
     * @OA\Property(
     *     property="profile_meta",
     *     type="object",
     *     nullable=true,
     *     description="Profile metadata - flexible JSON object containing additional profile information"
     * )
     */
    public ?array $profile_meta;

    public function __construct(
        ?string $county = null,
        ?string $state = null,
        ?float $gpa = null,
        ?string $class_rank = null,
        ?string $gender = null,
        ?array $profile_meta = null
    ) {
        $this->county = $county;
        $this->state = $state;
        $this->gpa = $gpa;
        $this->class_rank = $class_rank;
        $this->gender = $gender;
        $this->profile_meta = $profile_meta;
    }

    /**
     * Create a new PublicDetailsData instance from a User model.
     *
     * @param User $user
     * @return self
     */
    public static function fromModel(User $user): self
    {
        $county = $user->county?->name;
        if(!$county && $user->school) {
            $county = $user->school->county?->name;
        }

        $state = $user->state?->name;
        if(!$state && $user->school) {
            $state = $user->school->state?->name;
        }

        return new self(
            county: $county,
            state: $state,
            gpa: $user->gpa,
            class_rank: $user->class_rank,
            gender: $user->gender?->value,
            profile_meta: $user->metadata
        );
    }
}
