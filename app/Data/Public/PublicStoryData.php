<?php

namespace App\Data\Public;

use App\Models\User;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="PublicStoryData",
 *     title="Public Story Data",
 *     description="Public story information"
 * )
 */
class PublicStoryData extends Data
{
    /**
     * @OA\Property(
     *     property="content",
     *     type="string",
     *     description="Story content"
     * )
     */
    public string $content;

    public function __construct(
        string $content
    ) {
        $this->content = $content;
    }

    /**
     * Create a new PublicStoryData instance from a User model.
     *
     * @param User $user
     * @return self
     */
    public static function fromModel(User $user): self
    {
        return new self(
            content: $user->content ?? ''
        );
    }
}
