<?php

namespace App\Data\Public;

use <PERSON><PERSON>\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="PublicPhotoData",
 *     title="Public Photo Data",
 *     description="Public photo information"
 * )
 */
class PublicPhotoData extends Data
{
    /**
     * @OA\Property(
     *     property="id",
     *     type="integer",
     *     description="Photo ID"
     * )
     */
    public int $id;

    /**
     * @OA\Property(
     *     property="url",
     *     type="string",
     *     format="uri",
     *     description="Photo URL"
     * )
     */
    public string $url;

    /**
     * @OA\Property(
     *     property="width",
     *     type="integer",
     *     description="Photo width"
     * )
     */
    public ?int $width;

    /**
     * @OA\Property(
     *     property="height",
     *     type="integer",
     *     description="Photo height"
     * )
     */
    public ?int $height;

    /**
     * @OA\Property(
     *     property="focal_point",
     *     type="object",
     *     nullable=true,
     *     @OA\Property(property="x", type="number", format="float"),
     *     @OA\Property(property="y", type="number", format="float"),
     *     description="Focal point coordinates"
     * )
     */
    public ?array $focal_point;

    public function __construct(
        int $id,
        string $url,
        ?int $width = null,
        ?int $height = null,
        ?array $focal_point = null
    ) {
        $this->id = $id;
        $this->url = $url;
        $this->width = $width;
        $this->height = $height;
        $this->focal_point = $focal_point;
    }
}
