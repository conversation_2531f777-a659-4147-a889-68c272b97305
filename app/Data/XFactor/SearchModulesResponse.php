<?php
namespace App\Data\XFactor;

use Illuminate\Pagination\LengthAwarePaginator;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="SearchModulesResponse",
 *     title="Search Modules Response",
 *     description="Paginated search results for modules.",
 *     @OA\Property(
 *         property="data",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/XFactorModuleSummary")
 *     ),
 *     @OA\Property(
 *         property="links",
 *         type="array",
 *         description="Pagination links",
 *         @OA\Items(
 *             type="object",
 *             @OA\Property(property="url", type="string", format="uri", nullable=true, example="http://api.example.com/modules?page=2"),
 *             @OA\Property(property="label", type="string", example="2"),
 *             @OA\Property(property="active", type="boolean", example=false)
 *         )
 *     ),
 *     @OA\Property(
 *         property="meta",
 *         type="object",
 *         description="Pagination metadata",
 *         @OA\Property(property="current_page", type="integer", example=1),
 *         @OA\Property(property="from", type="integer", example=1),
 *         @OA\Property(property="last_page", type="integer", example=5),
 *         @OA\Property(property="path", type="string", example="http://api.example.com/modules"),
 *         @OA\Property(property="per_page", type="integer", example=10),
 *         @OA\Property(property="to", type="integer", example=10),
 *         @OA\Property(property="total", type="integer", example=50),
 *     )
 * )
 */
class SearchModulesResponse extends Data
{
    public function __construct(
        public array $data,
        public array $links,
        public array $meta,
    ) {}

    /**
     * Create SearchModulesResponse from paginator
     *
     * @param LengthAwarePaginator $paginator
     * @return self
     */
    public static function fromPaginator(LengthAwarePaginator $paginator): self
    {
        return new self(
            data: $paginator->items(),
            links: $paginator->toArray()['links'] ?? [],
            meta: array_diff_key($paginator->toArray(), ['data' => null, 'links' => null]),
        );
    }
}
