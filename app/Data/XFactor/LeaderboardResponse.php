<?php
namespace App\Data\XFactor;

use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;
use Spatie\LaravelData\Attributes\WithoutValidation;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;

/**
 * @OA\Schema(
 *     schema="LeaderboardResponse",
 *     title="Leaderboard Response",
 *     description="X-Factor leaderboard response data"
 * )
 */
class LeaderboardResponse extends Data
{
    /**
     * @OA\Property(
     *     property="rankings",
     *     type="array",
     *     @OA\Items(ref="#/components/schemas/LeaderboardUserData"),
     *     description="Collection of user rankings"
     * )
     *
     * @OA\Property(
     *     property="current_user",
     *     nullable=true,
     *     @OA\Property(
     *         property="data",
     *         ref="#/components/schemas/LeaderboardUserData"
     *     ),
     *     description="Current authenticated user's data if available"
     * )
     *
     * @OA\Property(
     *     property="current_user_rank",
     *     type="integer",
     *     nullable=true,
     *     description="Current user's rank in the leaderboard",
     *     example=15
     * )
     *
     * @OA\Property(
     *     property="region_name",
     *     type="string",
     *     description="Name of the selected region",
     *     example="Georgia"
     * )
     *
     * @OA\Property(
     *     property="state_name",
     *     type="string",
     *     nullable=true,
     *     description="Name of the selected state",
     *     example="Georgia"
     * )
     *
     * @OA\Property(
     *     property="graduation_year",
     *     type="integer",
     *     nullable=true,
     *     description="Selected graduation year filter",
     *     example=2025
     * )
     *
     * @OA\Property(
     *     property="academic_year",
     *     type="string",
     *     nullable=true,
     *     description="Selected academic year filter",
     *     example="2023-24"
     * )
     *
     * @OA\Property(
     *     property="start_date",
     *     type="string",
     *     format="date-time",
     *     nullable=true,
     *     description="Start date of the date range filter",
     *     example="2023-07-01T00:00:00Z"
     * )
     *
     * @OA\Property(
     *     property="end_date",
     *     type="string",
     *     format="date-time",
     *     nullable=true,
     *     description="End date of the date range filter",
     *     example="2024-06-30T23:59:59Z"
     * )
     *
     * @OA\Property(
     *     property="all_time",
     *     type="boolean",
     *     description="Whether the results are for all time",
     *     example=false
     * )
     *
     * @OA\Property(
     *     property="total",
     *     type="integer",
     *     description="Total number of records",
     *     example=211
     * )
     *
     * @OA\Property(
     *     property="per_page",
     *     type="integer",
     *     description="Number of records per page",
     *     example=50
     * )
     *
     * @OA\Property(
     *     property="current_page",
     *     type="integer",
     *     description="Current page number",
     *     example=1
     * )
     *
     * @OA\Property(
     *     property="last_page",
     *     type="integer",
     *     description="Last page number",
     *     example=5
     * )
     */
    public function __construct(
        #[WithoutValidation]
        public DataCollection $rankings,
        public ?LeaderboardUserData $current_user = null,
        public ?int $current_user_rank = null,
        public string $region_name,
        public ?string $state_name = null,
        public ?int $graduation_year = null,
        public ?string $academic_year = null,
        public ?Carbon $start_date = null,
        public ?Carbon $end_date = null,
        public bool $all_time = false,
        public int $total = 0,
        public int $per_page = 50,
        public int $current_page = 1,
        public int $last_page = 1,
    ) {}

    /**
     * Override the toArray method to ensure rankings has a data wrapper
     *
     * @return array
     */
    public function toArray(): array
    {
        $array = parent::toArray();

        // Ensure rankings has a data wrapper
        $array['rankings'] = [
            'data' => $array['rankings']
        ];

        return $array;
    }

    public static function fromPaginator(
        LengthAwarePaginator $paginator,
        ?LeaderboardUserData $currentUser,
        ?int $currentUserRank,
        string $regionName,
        ?string $stateName = null,
        ?int $graduationYear = null,
        ?string $academicYear = null,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
        bool $allTime = false,
        ?\App\Models\User $currentUserModel = null
    ): self {
        // Convert items to LeaderboardUserData collection
        $userDataCollection = new DataCollection(
            LeaderboardUserData::class,
            collect($paginator->items())->map(fn($item) => LeaderboardUserData::fromObject($item, $currentUserModel))->all()
        );

        return new self(
            rankings: $userDataCollection,
            current_user: $currentUser,
            current_user_rank: $currentUserRank,
            region_name: $regionName,
            state_name: $stateName,
            graduation_year: $graduationYear,
            academic_year: $academicYear,
            start_date: $startDate,
            end_date: $endDate,
            all_time: $allTime,
            total: $paginator->total(),
            per_page: $paginator->perPage(),
            current_page: $paginator->currentPage(),
            last_page: $paginator->lastPage(),
        );
    }
}
