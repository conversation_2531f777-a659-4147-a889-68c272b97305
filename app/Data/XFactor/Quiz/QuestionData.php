<?php

namespace App\Data\XFactor\Quiz;

use App\Enums\QuestionType;
use App\Models\Question;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use <PERSON>tie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;

/**
 * @OA\Schema(
 *     schema="QuizQuestionData",
 *     title="Quiz Question Data",
 *     description="Data structure for quiz question information",
 *     @OA\Property(property="id", type="integer", example=1),
 *     @OA\Property(property="question", type="string", example="What is the primary benefit of positive reinforcement?"),
 *     @OA\Property(
 *         property="type",
 *         type="string",
 *         enum={"multiple_choice", "true_false", "text"},
 *         example="multiple_choice"
 *     ),
 *     @OA\Property(
 *         property="answers",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/QuizAnswerData")
 *     )
 * )
 */
class QuestionData extends Data
{
    public function __construct(
        public int $id,
        public string $question,
        public QuestionType $type,
        #[DataCollectionOf(AnswerData::class)]
        public DataCollection $answers,
    ) {}

    public static function fromModel(Question $question): self
    {
        return new self(
            id: (int) $question->id,
            question: $question->question,
            type: $question->type,
            answers: new DataCollection(AnswerData::class, $question->answers->map(fn ($a) => AnswerData::fromModel($a))->all()),
        );
    }
}
