<?php

namespace App\Data\XFactor\Quiz;

use App\Enums\TestStatus;
use App\Models\TestAttempt;
use Carbon\Carbon;
use Carbon\CarbonInterface;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;

/**
 * @OA\Schema(
 *     schema="QuizTestAttemptData",
 *     title="Quiz Test Attempt Data",
 *     description="Data structure for quiz attempt information",
 *     @OA\Property(property="id", type="integer", example=1),
 *     @OA\Property(property="testId", type="integer", example=123),
 *     @OA\Property(property="userId", type="integer", example=456),
 *     @OA\Property(
 *         property="status",
 *         type="string",
 *         enum={"in_progress", "completed", "graded", "passed", "failed"},
 *         example="completed"
 *     ),
 *     @OA\Property(property="startedAt", type="string", format="date-time", example="2023-10-15T14:30:00Z"),
 *     @OA\Property(property="endsAt", type="string", format="date-time", example="2023-10-15T15:30:00Z"),
 *     @OA\Property(property="completedAt", type="string", format="date-time", nullable=true, example="2023-10-15T15:15:00Z"),
 *     @OA\Property(property="score", type="number", format="float", nullable=true, example=85.5),
 *     @OA\Property(
 *         property="responses",
 *         type="array",
 *         nullable=true,
 *         @OA\Items(ref="#/components/schemas/QuizQuestionResponseData")
 *     )
 * )
 */
class TestAttemptData extends Data
{
    public function __construct(
        public int $id,
        public int $testId,
        public int $userId,
        public TestStatus $status,
        public CarbonInterface $startedAt,
        public CarbonInterface $endsAt,
        public ?CarbonInterface $completedAt,
        public ?float $score,
        #[DataCollectionOf(QuestionResponseData::class)]
        public ?DataCollection $responses,
    ) {}

    public static function fromModel(TestAttempt $attempt): self
    {
        return new self(
            id: (int) $attempt->id,
            testId: (int) $attempt->test_id,
            userId: (int) $attempt->user_id,
            status: $attempt->status,
            startedAt: Carbon::parse($attempt->started_at),
            endsAt: Carbon::parse($attempt->ends_at),
            completedAt: $attempt->completed_at ? Carbon::parse($attempt->completed_at) : null,
            score: $attempt->score,
            responses: $attempt->questionResponses ? new DataCollection(
                QuestionResponseData::class,
                $attempt->questionResponses->map(fn ($r) => QuestionResponseData::fromModel($r))->all()
            ) : null,
        );
    }
}
