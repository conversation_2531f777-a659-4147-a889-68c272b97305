<?php

namespace App\Data\XFactor\Quiz;

use App\Models\Answer;
use <PERSON>tie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="QuizAnswerData",
 *     title="Quiz Answer Data",
 *     description="Data structure for question answer options in quizzes",
 *     @OA\Property(property="id", type="integer", example=1),
 *     @OA\Property(property="answer", type="string", example="It builds confidence and self-esteem"),
 *     @OA\Property(property="isCorrect", type="boolean", example=true)
 * )
 */
class AnswerData extends Data
{
    public function __construct(
        public int $id,
        public string $answer,
        public bool $isCorrect,
    ) {}

    public static function fromModel(Answer $answer): self
    {
        return new self(
            id: (int) $answer->id,
            answer: $answer->answer,
            isCorrect: $answer->is_correct,
        );
    }
}
