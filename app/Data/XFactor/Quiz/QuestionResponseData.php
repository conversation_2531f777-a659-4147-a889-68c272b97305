<?php

namespace App\Data\XFactor\Quiz;

use App\Models\QuestionResponse;
use Spa<PERSON>\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="QuizQuestionResponseData",
 *     title="Quiz Question Response Data",
 *     description="Data structure for user responses to quiz questions",
 *     @OA\Property(property="id", type="integer", example=1),
 *     @OA\Property(property="questionId", type="integer", example=123),
 *     @OA\Property(property="response", type="string", example="B"),
 *     @OA\Property(property="correct", type="boolean", nullable=true, example=true)
 * )
 */
class QuestionResponseData extends Data
{
    public function __construct(
        public int $id,
        public int $questionId,
        public string $response,
        public ?bool $correct,
    ) {}

    public static function fromModel(QuestionResponse $response): self
    {
        return new self(
            id: (int) $response->id,
            questionId: (int) $response->question_id,
            response: $response->response,
            correct: $response->correct,
        );
    }
}
