<?php

namespace App\Data\XFactor\Quiz\Requests;

use App\Models\Question;
use App\Models\TestAttempt;
use <PERSON>tie\LaravelData\Data;
use Spatie\LaravelData\Attributes\Validation\Exists;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;

class SubmitQuestionResponseRequest extends Data
{
    public function __construct(
        #[Required, Exists(Question::class, 'id')]
        public int $questionId,

        #[Required, StringType]
        public string $response,
    ) {}

    public static function rules(): array
    {
        return [
            'questionId' => [
                'required',
                'integer',
                'exists:questions,id',
                function (string $attribute, mixed $value, \Closure $fail) {
                    // Ensure the question belongs to the current test attempt
                    $attemptId = request()->route('attemptId');
                    if (!$attemptId) {
                        $fail('No test attempt found.');
                        return;
                    }

                    $attempt = TestAttempt::find($attemptId);
                    if (!$attempt) {
                        $fail('Invalid test attempt.');
                        return;
                    }

                    $questionBelongsToTest = Question::query()
                        ->whereHas('test', function ($query) use ($attempt) {
                            $query->where('id', $attempt->test_id);
                        })
                        ->where('id', $value)
                        ->exists();

                    if (!$questionBelongsToTest) {
                        $fail('The question does not belong to the current test.');
                    }
                },
            ],
            'response' => 'required|string',
        ];
    }
}
