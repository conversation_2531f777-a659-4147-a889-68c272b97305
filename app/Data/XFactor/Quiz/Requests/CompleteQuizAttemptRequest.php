<?php
namespace App\Data\XFactor\Quiz\Requests;

use App\Models\Module;
use App\Models\Test;
use App\Models\TestAttempt;
use <PERSON>tie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="QuizAnswer",
 *     title="Quiz Answer",
 *     description="Data structure for quiz answer information",
 *     required={"questionId", "answerId"},
 *     @OA\Property(property="questionId", type="integer", description="ID of the question", example=1),
 *     @OA\Property(property="answerId", type="integer", description="ID of the chosen answer", example=4)
 * )
 * @OA\Schema(
 *     schema="CompleteQuizAttemptRequest",
 *     title="Complete Quiz Attempt Request",
 *     required={"responses"},
 *     @OA\Property(
 *         property="responses",
 *         type="array",
 *         description="Array of user responses to quiz questions",
 *         @OA\Items(ref="#/components/schemas/QuizAnswer")
 *     )
 * )
 */
class CompleteQuizAttemptRequest extends Data
{
    public function __construct(
        /** @var array<array{questionId: int, answerId: int}> */
        public array $responses
    ) {}

    public static function rules(): array
    {
        return [
            'responses'              => ['required', 'array'],
            'responses.*.questionId' => ['required', 'integer', 'exists:questions,id'],
            'responses.*.answerId'   => ['required', 'integer', 'exists:answers,id'],
        ];
    }

    /**
     * Validate the latest quiz attempt for the given module and user
     */
    public static function validateAttempt(int $moduleId, int $userId, array $responses): TestAttempt
    {
        // First check if the module has a quiz
        $quiz = Test::query()
            ->where('testable_type', Module::class)
            ->where('testable_id', $moduleId)
            ->first();

        if (! $quiz) {
            throw new \Illuminate\Validation\ValidationException(
                validator([], []),
                response()->json(['message' => 'No quiz found for this module.'], 422)
            );
        }

        // Then get the latest attempt for this quiz
        $latestAttempt = TestAttempt::query()
            ->where('test_id', $quiz->id)
            ->where('user_id', $userId)
            ->latest()
            ->first();

        if (! $latestAttempt) {
            throw new \Illuminate\Validation\ValidationException(
                validator([], []),
                response()->json(['message' => 'Please start a quiz attempt before trying to complete it.'], 422)
            );
        }

        if ($latestAttempt->completed_at) {
            throw new \Illuminate\Validation\ValidationException(
                validator([], []),
                response()->json(['message' => 'This quiz attempt has already been completed.'], 422)
            );
        }

        // Check if all questions have been answered
        $totalQuestions    = $quiz->questions()->count();
        $answeredQuestions = count($responses);

        if ($answeredQuestions < $totalQuestions) {
            throw new \Illuminate\Validation\ValidationException(
                validator([], []),
                response()->json(['message' => 'All questions must be answered before completing the quiz.'], 422)
            );
        }

        // Validate that all responses are for questions in this quiz
        $quizQuestionIds     = $quiz->questions()->pluck('id')->toArray();
        $responseQuestionIds = array_column($responses, 'questionId');
        $invalidQuestions    = array_diff($responseQuestionIds, $quizQuestionIds);

        if (! empty($invalidQuestions)) {
            throw new \Illuminate\Validation\ValidationException(
                validator([], []),
                response()->json(['message' => 'Some responses are for questions not in this quiz.'], 422)
            );
        }

        return $latestAttempt;
    }
}
