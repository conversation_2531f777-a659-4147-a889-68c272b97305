<?php
namespace App\Data\XFactor\Quiz;

use App\Models\Test;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use <PERSON>tie\LaravelData\Data;
use <PERSON>tie\LaravelData\DataCollection;

/**
 * @OA\Schema(
 *     title="Quiz Data",
 *     description="Data structure for quiz information",
 *     @OA\Property(property="id", type="integer", example=1),
 *     @OA\Property(property="moduleId", type="integer", example=123),
 *     @OA\Property(
 *         property="questions",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/QuizQuestionData")
 *     ),
 *     @OA\Property(
 *         property="latestAttempt",
 *         nullable=true,
 *         allOf={
 *             @OA\Schema(ref="#/components/schemas/QuizTestAttemptData")
 *         }
 *     ),
 *     @OA\Property(
 *         property="lastCompletedAttempt",
 *         nullable=true,
 *         allOf={
 *             @OA\Schema(ref="#/components/schemas/QuizTestAttemptData")
 *         }
 *     ),
 *     @OA\Property(property="totalAttempts", type="integer", example=2),
 *     @OA\Property(property="attemptsAllowed", type="integer", example=3),
 *     @OA\Property(property="timeLimit", type="integer", nullable=true, example=30, description="Time limit in minutes"),
 *     @OA\Property(property="passingScore", type="number", format="float", example=70.0, description="Passing score percentage")
 * )
 */
class QuizData extends Data
{
    public function __construct(
        public int $id,
        public int $moduleId,
        #[DataCollectionOf(QuestionData::class)]
        public DataCollection $questions,
        public ?TestAttemptData $latestAttempt,
        public ?TestAttemptData $lastCompletedAttempt,
        public int $totalAttempts,
        public int $attemptsAllowed,
        public ?int $timeLimit,
        public float $passingScore,
    ) {}

    public static function fromModel(Test $test, ?int $userId = null): self
    {
        // Ensure we have all required relationships
        $test->loadMissing(['questions.answers']);

        $latestAttempt        = null;
        $lastCompletedAttempt = null;

        if ($userId) {
            // Load user-specific relationships if not already loaded
            $test->loadMissing([
                'latestAttempt'        => function ($query) use ($userId) {
                    $query->where('user_id', $userId);
                },
                'lastCompletedAttempt' => function ($query) use ($userId) {
                    $query->where('user_id', $userId);
                },
            ]);

            $latestAttempt        = $test->latestAttempt;
            $lastCompletedAttempt = $test->lastCompletedAttempt;
        }

        return new self(
            id: (int) $test->id,
            moduleId: (int) $test->testable_id,
            questions: new DataCollection(QuestionData::class, $test->questions->map(fn($q) => QuestionData::fromModel($q))->all()),
            latestAttempt: $latestAttempt ? TestAttemptData::fromModel($latestAttempt) : null,
            lastCompletedAttempt: $lastCompletedAttempt ? TestAttemptData::fromModel($lastCompletedAttempt) : null,
            totalAttempts: $userId ? $test->attempts()->where('user_id', $userId)->count() : 0,
            attemptsAllowed: $test->attempts_allowed ?? 3, // Default to 3 attempts if not set
            timeLimit: $test->time_limit_minutes,
            passingScore: $test->passing_score ?? 70.0, // Default to 70% if not set
        );
    }
}
