<?php

namespace App\Data\XFactor;

use App\Models\Topic;
use App\Models\User;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="TopicModulesResponse",
 *     title="Topic Modules Response",
 *     description="Response containing a topic and its associated modules.",
 *     type="object",
 *     @OA\Property(property="id", type="string", example=1),
 *     @OA\Property(property="name", type="string", example="Leadership"),
 *     @OA\Property(
 *         property="modules",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/XFactorModuleSummary")
 *     )
 * )
 */
class TopicModulesResponse extends Data
{
    public function __construct(
        public string $id,
        public string $name,
        /** @var ModuleResponse[] */
        public Collection $modules,
    ) {}

    public static function fromModel(Topic $topic, ?User $user = null): self
    {
        return new self(
            id: (string) $topic->id,
            name: $topic->name,
            modules: $topic->modules
            ->map(fn ($module) => ModuleResponse::fromModel($module, $user))
            ->values(),
        );
    }
}
