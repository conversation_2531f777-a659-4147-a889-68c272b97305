<?php

namespace App\Data\XFactor;

use Carbon\Carbon;
use <PERSON>tie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="LeaderboardRequest",
 *     title="Leaderboard Request",
 *     description="X-Factor leaderboard request parameters"
 * )
 */
class LeaderboardRequest extends Data
{
    /**
     * @OA\Property(
     *     property="region",
     *     type="string",
     *     description="Region ID to filter by",
     *     example="1"
     * )
     *
     * @OA\Property(
     *     property="state",
     *     type="string",
     *     description="State code to filter by",
     *     example="GA"
     * )
     *
     * @OA\Property(
     *     property="graduation_year",
     *     type="integer",
     *     description="Graduation year to filter by",
     *     example=2025
     * )
     *
     * @OA\Property(
     *     property="all_time",
     *     type="boolean",
     *     description="Whether to show all-time results",
     *     example=false
     * )
     *
     * @OA\Property(
     *     property="academic_year",
     *     type="string",
     *     description="Academic year in format YYYY-YY",
     *     example="2023-24"
     * )
     *
     * @OA\Property(
     *     property="start_date",
     *     type="string",
     *     format="date",
     *     description="Start date for custom date range",
     *     example="2023-07-01"
     * )
     *
     * @OA\Property(
     *     property="end_date",
     *     type="string",
     *     format="date",
     *     description="End date for custom date range",
     *     example="2024-06-30"
     * )
     *
     * @OA\Property(
     *     property="page",
     *     type="integer",
     *     description="Page number for pagination",
     *     example=1
     * )
     *
     * @OA\Property(
     *     property="per_page",
     *     type="integer",
     *     description="Number of items per page",
     *     example=50
     * )
     */
    public function __construct(
        public ?string $region = null,
        public ?string $state = null,
        public ?int $graduation_year = null,
        public mixed $all_time = false,
        public ?string $academic_year = null,
        public ?Carbon $start_date = null,
        public ?Carbon $end_date = null,
        public int $page = 1,
        public int $per_page = 50,
    ) {
        // Always ensure all_time is a boolean using match expression
        if (!is_bool($this->all_time)) {
            $this->all_time = match(true) {
                is_string($this->all_time) => filter_var($this->all_time, FILTER_VALIDATE_BOOLEAN),
                is_null($this->all_time) => false,
                default => (bool)$this->all_time,
            };
        }

        // If academic year is provided but no dates, calculate the date range
        // Academic year format: "2023-24" spanning from July 1, 2023 to June 30, 2024
        if ($this->academic_year && !$this->start_date && !$this->end_date && !$this->all_time) {
            list($startYear, $endYearSuffix) = explode('-', $this->academic_year);
            $endYear = intval("20$endYearSuffix");

            $this->start_date = Carbon::createFromDate($startYear, 7, 1)->startOfDay();
            $this->end_date = Carbon::createFromDate($endYear, 6, 30)->endOfDay();
        }

        // If all_time is true, don't use date filters
        if ($this->all_time) {
            $this->start_date = null;
            $this->end_date = null;
        }

        // If no date range is specified and all_time is false, default to current academic year
        if (!$this->start_date && !$this->end_date && !$this->all_time) {
            $currentMonth = Carbon::now()->month;
            $currentYear = Carbon::now()->year;

            // Academic year runs from July to June
            if ($currentMonth >= 7) { // July to December
                $this->start_date = Carbon::createFromDate($currentYear, 7, 1)->startOfDay();
                $this->end_date = Carbon::createFromDate($currentYear + 1, 6, 30)->endOfDay();
            } else { // January to June
                $this->start_date = Carbon::createFromDate($currentYear - 1, 7, 1)->startOfDay();
                $this->end_date = Carbon::createFromDate($currentYear, 6, 30)->endOfDay();
            }
        }
    }

    public static function rules(): array
    {
        return [
            'region' => ['sometimes', 'string'],
            'state' => ['sometimes', 'string', 'size:2'],
            'graduation_year' => ['sometimes', 'integer', 'min:2000', 'max:2100'],
            'academic_year' => ['sometimes', 'string', 'regex:/^\d{4}-\d{2}$/'],
            'all_time' => ['sometimes', 'in:0,1,true,false'],
            'start_date' => ['sometimes', 'date'],
            'end_date' => ['sometimes', 'date', 'after:start_date'],
            'page' => ['sometimes', 'integer', 'min:1'],
            'per_page' => ['sometimes', 'integer', 'min:1', 'max:100'],
        ];
    }
}
