<?php

namespace App\Data\XFactor;

use App\Models\Module;
use App\Models\User;
use App\Models\TestAttempt;
use App\Enums\ModuleType;
use App\Enums\TestStatus;
use Carbon\Carbon;
use Spatie\LaravelData\Data;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * @OA\Schema(
 *     schema="XFactorModuleSummary",
 *     title="XFactor Module Summary",
 *     description="Summary information for an X-Factor module, including user progress.",
 *     @OA\Property(property="id", type="integer", example=456),
 *     @OA\Property(property="name", type="string", example="Introduction to Leadership"),
 *     @OA\Property(property="description", type="string", nullable=true, example="An introduction to leadership concepts"),
 *     @OA\Property(property="cover_image", type="string", nullable=true, example="/media/modules/1/cover.jpg"),
 *     @OA\Property(property="published", type="boolean", example=true),
 *     @OA\Property(property="course_id", type="integer", nullable=true, example=10),
 *     @OA\Property(property="course_name", type="string", nullable=true, example="Leadership 101"),
 *     @OA\Property(property="duration_minutes", type="integer", nullable=true, example=30),
 *     @OA\Property(property="topics", type="array", @OA\Items(type="string"), example={"Leadership", "Teamwork"}),
 *     @OA\Property(property="has_quiz", type="boolean", nullable=true, example=true),
 *     @OA\Property(property="has_exam", type="boolean", nullable=true, example=false),
 *     @OA\Property(property="type", type="string", enum={"article", "video", "quiz", "exam"}, example="video"),
 *     @OA\Property(property="progress", type="number", format="float", nullable=true, example=75.5),
 *     @OA\Property(property="completed_at", type="string", format="date-time", nullable=true, example="2023-10-26T10:00:00Z"),
 *     @OA\Property(property="score", type="integer", nullable=true, example=85),
 *     @OA\Property(property="order", type="integer", nullable=true, example=1),
 *     @OA\Property(property="next_attempt_available_at", type="string", format="date-time", nullable=true, example="2023-10-27T10:00:00Z")
 * )
 *
 * @OA\Schema(
 *     schema="ModuleResponse",
 *     title="XFactor Module Detail",
 *     description="Detailed information about an X-Factor module, including content and user progress.",
 *     allOf={@OA\Schema(ref="#/components/schemas/XFactorModuleSummary")},
 *     @OA\Property(property="content", type="string", nullable=true, description="HTML content for article modules"),
 *     @OA\Property(property="video_url", type="string", format="url", nullable=true, description="URL for video modules"),
 *     @OA\Property(property="video_start_time", type="integer", nullable=true, description="Start time in seconds for video segment"),
 *     @OA\Property(property="video_end_time", type="integer", nullable=true, description="End time in seconds for video segment")
 * )
 */
class ModuleResponse extends Data
{
    public function __construct(
        public int $id,
        public string $name,
        public string $description,
        public ?string $cover_image,
        public bool $published,
        public ?int $course_id,
        public ?string $course_name,
        public ?int $duration_minutes,
        public array $topics,
        public ?bool $has_quiz,
        public ?bool $has_exam,
        public ModuleType $type,
        public ?float $progress = null,
        public ?string $completed_at = null,
        public ?string $content = null,
        public ?string $video_url = null,
        public ?int $video_start_time = null,
        public ?int $video_end_time = null,
        public ?int $score = null,
        public ?int $order = null,
        public ?string $next_attempt_available_at = null,
    ) {}

    private static function handleExamAttempt(TestAttempt $attempt, int $passingScore): array
    {
        $completed_at = $attempt->completed_at;
        $score = $attempt->score;
        $next_attempt_available_at = null;

        if ($attempt->status === TestStatus::Expired) {
            // For expired attempts, calculate when the next attempt is available
            if ($attempt->test->wait_period) {
                $next_attempt_available_at = Carbon::parse($completed_at)
                    ->addSeconds($attempt->test->wait_period);
            }
        } else {
            // For non-expired attempts, only mark as completed if passing score is met
            $completed_at = $score >= $passingScore ? $completed_at : null;
        }

        return [
            'completed_at' => $completed_at,
            'score' => $score,
            'next_attempt_available_at' => $next_attempt_available_at,
        ];
    }

    private static function handleQuizAttempt(Module $module): array
    {
        return [
            'completed_at' => $module->users->first()?->pivot->completed_at,
            'score' => $module->test->attempts->first()?->score,
            'next_attempt_available_at' => null,
        ];
    }

    private static function handleContentModule(Module $module): array
    {
        return [
            'completed_at' => $module->users->first()?->pivot->completed_at,
            'score' => null,
            'next_attempt_available_at' => null,
        ];
    }

    public static function fromModel(Module $module, ?User $user = null): self
    {
        // Load necessary relationships
        $module->loadMissing([
            'media',
            'topics',
            'test.questions',
            'test.attempts' => fn($q) => $q->where('user_id', $user?->id)
                ->orderBy('completed_at', 'desc')
        ]);

        if ($user) {
            $module->loadMissing(['users' => fn($q) => $q->where('user_id', $user->id)]);
        }

        // Initialize completion data
        $completionData = [
            'completed_at' => null,
            'score' => null,
            'next_attempt_available_at' => null,
        ];

        // Handle different module types
        $lastAttempt = null;
        if ($module->test) {
            $lastAttempt = $module->test->attempts
                ->where('completed_at', '!=', null)
                ->whereIn('status', [TestStatus::Complete, TestStatus::PendingReview, TestStatus::Graded, TestStatus::Expired])
                ->first();

            if ($lastAttempt) {
                if ($module->test->type === 'exam') {
                    $completionData = self::handleExamAttempt(
                        $lastAttempt,
                        $module->test->passing_score ?? 0
                    );
                } else {
                    $completionData = self::handleQuizAttempt($module);
                }
            }
        } else {
            $completionData = self::handleContentModule($module);
        }

        return new self(
            id: $module->id,
            name: $module->name,
            description: $module->description,
            cover_image: $module->getFirstMediaUrl('cover', 'full') ?: null,
            published: $module->published,
            course_id: $module->pivot?->course_id,
            course_name: $module->course?->name,
            duration_minutes: $module->minutes,
            topics: $module->topics->pluck('name')->toArray(),
            has_quiz: $module->test?->type === 'quiz',
            has_exam: $module->test?->type === 'exam',
            type: $module->type,
            progress: null,
            completed_at: $completionData['completed_at'],
            content: $module->content,
            video_url: $module->video_url,
            video_start_time: $module->video_start_time,
            video_end_time: $module->video_end_time,
            score: $completionData['score'],
            order: $module->pivot?->order,
            next_attempt_available_at: $completionData['next_attempt_available_at'],
        );
    }
}
