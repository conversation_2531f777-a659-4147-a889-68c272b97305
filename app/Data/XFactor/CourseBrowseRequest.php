<?php

namespace App\Data\XFactor;

use <PERSON><PERSON>\LaravelData\Data;
use Spa<PERSON>\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Attributes\Validation\In;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Nullable;

class CourseBrowseRequest extends Data
{
    public function __construct(
        #[Nullable]
        #[StringType]
        #[Max(255)]
        public ?string $search = null,

        #[StringType]
        #[In(['newest', 'title'])]
        public string $sort = 'newest',

        #[Nullable]
        public ?int $topicId = null,

        public int $page = 1,

        public int $perPage = 10,

        public ?array $topics = null,

        public ?string $status = null,
    ) {}
}

