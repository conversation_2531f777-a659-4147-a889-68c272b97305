<?php

namespace App\Data\XFactor\Exam;

use App\Models\Question;
use <PERSON><PERSON>\LaravelData\Data;
use App\Enums\QuestionType;
use <PERSON><PERSON>\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\DataCollection;

/**
 * @OA\Schema(
 *     schema="QuestionData",
 *     title="Exam Question Data",
 *     description="Data structure for exam/quiz question information",
 *     @OA\Property(property="id", type="integer", example=1),
 *     @OA\Property(property="question", type="string", example="What is the primary benefit of positive reinforcement?"),
 *     @OA\Property(
 *         property="type",
 *         type="string",
 *         enum={"multiple_choice", "short_text", "long_text"},
 *         example="multiple_choice"
 *     ),
 *     @OA\Property(
 *         property="answers",
 *         type="array",
 *         nullable=true,
 *         @OA\Items(ref="#/components/schemas/AnswerData")
 *     )
 * )
 */
class QuestionData extends Data
{
    public function __construct(
        public readonly int $id,
        public readonly string $question,
        public readonly QuestionType $type,
        #[DataCollectionOf(AnswerData::class)]
        public readonly ?DataCollection $answers = null,
    ) {}

    public static function fromModel(Question $question): self
    {
        return new self(
            id: $question->id,
            question: $question->question,
            type: $question->type,
            answers: $question->type === QuestionType::MultipleChoice && $question->answers
                ? new DataCollection(AnswerData::class, $question->answers->map(fn ($a) => AnswerData::fromModel($a))->all())
                : null,
        );
    }

    public static function collection($questions): DataCollection
    {
        return new DataCollection(
            self::class,
            $questions->map(fn ($q) => self::fromModel($q))->all()
        );
    }
}
