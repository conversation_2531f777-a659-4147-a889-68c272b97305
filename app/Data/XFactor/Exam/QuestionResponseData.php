<?php

namespace App\Data\XFactor\Exam;

use App\Models\QuestionResponse;
use Spa<PERSON>\LaravelData\Data;

/**
 * @OA\Schema(
 *     title="Question Response Data",
 *     description="Data structure for user responses to exam/quiz questions",
 *     @OA\Property(property="id", type="integer", example=1),
 *     @OA\Property(property="questionId", type="integer", example=123),
 *     @OA\Property(property="userId", type="integer", example=456),
 *     @OA\Property(property="testAttemptId", type="integer", example=789),
 *     @OA\Property(property="response", type="string", example="B"),
 *     @OA\Property(property="responseHtml", type="string", nullable=true, example="<p>This is my detailed response</p>"),
 *     @OA\Property(property="correct", type="boolean", nullable=true, example=true)
 * )
 */
class QuestionResponseData extends Data
{
    public function __construct(
        public readonly int $id,
        public readonly int $questionId,
        public readonly int $userId,
        public readonly int $testAttemptId,
        public readonly string $response,
        public readonly ?string $responseHtml = null,
        public readonly ?bool $correct = null,
    ) {}

    public static function fromModel(QuestionResponse $response): self
    {
        return new self(
            id: $response->id,
            questionId: $response->question_id,
            userId: $response->user_id,
            testAttemptId: $response->test_attempt_id,
            response: $response->response,
            responseHtml: $response->response_html,
            correct: $response->correct,
        );
    }
}
