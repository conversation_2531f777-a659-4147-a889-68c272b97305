<?php

namespace App\Data\XFactor\Exam;

use App\Models\Test;
use <PERSON>tie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;
use Spatie\LaravelData\Attributes\DataCollectionOf;

/**
 * @OA\Schema(
 *     schema="ExamData",
 *     title="Exam Data",
 *     description="Data structure representing an exam with questions",
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         format="int64",
 *         description="Unique identifier for the exam",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="moduleId",
 *         type="integer",
 *         format="int64",
 *         description="ID of the module this exam belongs to",
 *         example=5
 *     ),
 *     @OA\Property(
 *         property="timeLimit",
 *         type="integer",
 *         description="Time limit for the exam in minutes",
 *         example=30
 *     ),
 *     @OA\Property(
 *         property="passingScore",
 *         type="integer",
 *         description="Minimum score required to pass the exam (percentage)",
 *         example=70
 *     ),
 *     @OA\Property(
 *         property="waitPeriod",
 *         type="integer",
 *         description="Required waiting period before retaking the exam (in hours)",
 *         example=24
 *     ),
 *     @OA\Property(
 *         property="attemptsAllowed",
 *         type="integer",
 *         description="Maximum number of attempts allowed for the exam",
 *         example=3
 *     ),
 *     @OA\Property(
 *         property="attemptsUsed",
 *         type="integer",
 *         description="Number of attempts used by the current user for the exam",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="questions",
 *         type="array",
 *         description="List of questions in the exam",
 *         @OA\Items(ref="#/components/schemas/QuestionData")
 *     ),
 *     @OA\Property(
 *         property="latestAttempt",
 *         type="object",
 *         nullable=true,
 *         ref="#/components/schemas/TestAttemptData"
 *     ),
 *     @OA\Property(
 *         property="lastCompletedAttempt",
 *         type="object",
 *         nullable=true,
 *         ref="#/components/schemas/TestAttemptData"
 *     )
 * )
 */
class ExamData extends Data
{
    public function __construct(
        public readonly int $id,
        public readonly int $moduleId,
        public readonly int $timeLimit,
        public readonly int $passingScore,
        public readonly int $waitPeriod,
        public readonly int $attemptsAllowed,
        public readonly int $attemptsUsed,
        #[DataCollectionOf(QuestionData::class)]
        public readonly DataCollection $questions,
        public readonly ?TestAttemptData $latestAttempt = null,
        public readonly ?TestAttemptData $lastCompletedAttempt = null,
    ) {
    }

    public static function fromModel(Test $test): self
    {
        if ($test->testable_type !== 'App\\Models\\Module') {
            throw new \InvalidArgumentException('Test must be associated with a module');
        }

        // Load user-specific relationships if not already loaded
        $latestAttemptModel = null;
        $lastCompletedAttemptModel = null;

        if (request()->user()) {
            // Ensure that we only load attempts for the current user.
            $test->loadMissing([
                'latestAttempt' => fn ($query) => $query->where('user_id', request()->user()->id),
                'lastCompletedAttempt' => fn ($query) => $query->where('user_id', request()->user()->id),
            ]);

            $latestAttemptModel = $test->latestAttempt;
            $lastCompletedAttemptModel = $test->lastCompletedAttempt;
        }

        return new self(
            id: $test->id,
            moduleId: $test->testable_id,
            timeLimit: $test->time_limit,
            passingScore: $test->passing_score,
            waitPeriod: $test->wait_period,
            attemptsAllowed: $test->attempts_allowed,
            attemptsUsed: request()->user() ? $test->userAttempts(request()->user())->count() : 0,
            questions: QuestionData::collection($test->questions),
            latestAttempt: $latestAttemptModel ? TestAttemptData::fromModel($latestAttemptModel) : null,
            lastCompletedAttempt: $lastCompletedAttemptModel ? TestAttemptData::fromModel($lastCompletedAttemptModel) : null,
        );
    }
}
