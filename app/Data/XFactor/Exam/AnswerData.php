<?php

namespace App\Data\XFactor\Exam;

use App\Models\Answer;
use <PERSON>tie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="AnswerData",
 *     title="Exam Answer Data",
 *     description="Data structure for question answer options",
 *     @OA\Property(property="id", type="integer", example=1),
 *     @OA\Property(property="answer", type="string", example="It builds confidence and self-esteem"),
 *     @OA\Property(property="isCorrect", type="boolean", example=true)
 * )
 */
class AnswerData extends Data
{
    public function __construct(
        public readonly int $id,
        public readonly string $answer,
        public readonly bool $isCorrect,
    ) {}

    public static function fromModel(Answer $answer): self
    {
        return new self(
            id: $answer->id,
            answer: $answer->answer,
            isCorrect: $answer->is_correct,
        );
    }
}
