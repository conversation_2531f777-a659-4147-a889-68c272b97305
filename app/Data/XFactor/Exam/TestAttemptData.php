<?php

namespace App\Data\XFactor\Exam;

use App\Models\TestAttempt;
use Spatie\LaravelData\Data;
use App\Enums\TestStatus;
use Carbon\Carbon;
use Spatie\LaravelData\DataCollection;

/**
 * @OA\Schema(
 *     title="Test Attempt Data",
 *     description="Data structure for exam/quiz attempt information",
 *     @OA\Property(property="id", type="integer", example=1),
 *     @OA\Property(property="testId", type="integer", example=123),
 *     @OA\Property(property="userId", type="integer", example=456),
 *     @OA\Property(
 *         property="status",
 *         type="string",
 *         enum={"in_progress", "completed", "graded", "passed", "failed"},
 *         example="completed"
 *     ),
 *     @OA\Property(property="score", type="integer", nullable=true, example=85),
 *     @OA\Property(property="startedAt", type="string", format="date-time", example="2023-10-15T14:30:00Z"),
 *     @OA\Property(property="endsAt", type="string", format="date-time", example="2023-10-15T15:30:00Z"),
 *     @OA\Property(property="completedAt", type="string", format="date-time", nullable=true, example="2023-10-15T15:15:00Z"),
 *     @OA\Property(property="gradedAt", type="string", format="date-time", nullable=true, example="2023-10-15T15:20:00Z"),
 *     @OA\Property(property="feedback", type="string", nullable=true, example="Great job on the exam!"),
 *     @OA\Property(
 *         property="responses",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/QuestionResponseData")
 *     )
 * )
 */
class TestAttemptData extends Data
{
    public function __construct(
        public readonly int $id,
        public readonly int $testId,
        public readonly int $userId,
        public readonly TestStatus $status,
        public readonly ?int $score,
        public readonly Carbon $startedAt,
        public readonly Carbon $endsAt,
        public readonly ?Carbon $completedAt = null,
        public readonly ?Carbon $gradedAt = null,
        public readonly ?string $feedback = null,
        public readonly array $responses = [],
    ) {}

    public static function fromModel(TestAttempt $attempt): self
    {
        return new self(
            id: $attempt->id,
            testId: $attempt->test_id,
            userId: $attempt->user_id,
            status: $attempt->status,
            score: $attempt->score,
            startedAt: $attempt->started_at,
            endsAt: $attempt->ends_at,
            completedAt: $attempt->completed_at,
            gradedAt: $attempt->graded_at,
            feedback: $attempt->feedback,
            responses: $attempt->questionResponses
                ? (new DataCollection(QuestionResponseData::class, $attempt->questionResponses->map(fn ($q) => QuestionResponseData::fromModel($q))->all()))->toArray()
                : [],
        );
    }
}
