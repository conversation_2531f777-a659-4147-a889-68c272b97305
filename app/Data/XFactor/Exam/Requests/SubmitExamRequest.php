<?php

namespace App\Data\XFactor\Exam\Requests;

use App\Enums\TestStatus;
use App\Models\TestAttempt;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Attributes\Validation\Enum;

class SubmitExamRequest extends Data
{
    public function __construct(
        #[Enum(TestStatus::class)]
        public readonly TestStatus $status,
        public readonly array $responses,
    ) {}

    public static function rules(): array
    {
        return [
            'status' => ['required', 'string', 'in:complete,expired,pending_review'],
            'responses' => ['required_unless:status,expired', 'array'],
            'responses.*.questionId' => ['required_with:responses', 'integer', 'exists:questions,id'],
            'responses.*.response' => ['required_with:responses', 'string'],
            'responses.*.responseHtml' => ['nullable', 'string'],
        ];
    }

    public static function validateAttempt(int $moduleId, int $userId, array $responses): TestAttempt
    {
        // Get the latest attempt for this module and user
        $attempt = TestAttempt::query()
            ->whereHas('test', function ($query) use ($moduleId) {
                $query->where('testable_id', $moduleId);
            })
            ->where('user_id', $userId)
            ->whereNull('completed_at')
            ->latest()
            ->first();

        if (!$attempt) {
            abort(404, 'No active exam attempt found.');
        }

        if ($attempt->ends_at->isPast()) {
            abort(403, 'Exam time limit has expired.');
        }

        // Only validate question count if not expired
        if (request('status') !== 'expired') {
            // Validate that all required questions are answered
            $questionIds = collect($responses)->pluck('questionId');
            $requiredQuestionCount = $attempt->test->questions()->count();

            if ($questionIds->unique()->count() !== $requiredQuestionCount) {
                abort(422, 'All questions must be answered.');
            }
        }

        return $attempt;
    }
}
