<?php

namespace App\Data\XFactor;

use Spatie\LaravelData\Data;
use App\Models\User;

/**
 * @OA\Schema(
 *     schema="LeaderboardUserData",
 *     title="Leaderboard User Data",
 *     description="User data for X-Factor leaderboard"
 * )
 */
class LeaderboardUserData extends Data
{
    /**
     * @OA\Property(
     *     property="id",
     *     type="integer",
     *     description="User ID",
     *     example=123
     * )
     *
     * @OA\Property(
     *     property="rank",
     *     type="integer",
     *     description="User's rank in the leaderboard",
     *     example=5
     * )
     *
     * @OA\Property(
     *     property="first_name",
     *     type="string",
     *     description="User's first name",
     *     example="John"
     * )
     *
     * @OA\Property(
     *     property="last_name",
     *     type="string",
     *     description="User's last name",
     *     example="Doe"
     * )
     *
     * @OA\Property(
     *     property="graduation_year",
     *     type="integer",
     *     nullable=true,
     *     description="User's graduation year",
     *     example=2025
     * )
     *
     * @OA\Property(
     *     property="school_id",
     *     type="integer",
     *     nullable=true,
     *     description="User's school ID",
     *     example=42
     * )
     *
     * @OA\Property(
     *     property="school_name",
     *     type="string",
     *     nullable=true,
     *     description="User's school name",
     *     example="Central High School"
     * )
     *
     * @OA\Property(
     *     property="state_code",
     *     type="string",
     *     nullable=true,
     *     description="User's state code",
     *     example="GA"
     * )
     *
     * @OA\Property(
     *     property="badge_name",
     *     type="string",
     *     nullable=true,
     *     description="User's badge name",
     *     example="Varsity"
     * )
     *
     * @OA\Property(
     *     property="completed_modules_count",
     *     type="integer",
     *     description="Number of modules completed by the user",
     *     example=28
     * )
     *
     * @OA\Property(
     *     property="can_connect",
     *     type="boolean",
     *     description="Whether the current user can connect with this user",
     *     example=true
     * )
     *
     * @OA\Property(
     *     property="avatar_url",
     *     type="string",
     *     nullable=true,
     *     description="URL to the user's avatar image (thumbnail)",
     *     example="https://cdn.example.com/avatars/123-thumb.jpg"
     * )
     *
     * @OA\Property(
     *     property="public_profile",
     *     type="boolean",
     *     description="Whether the user's profile is public",
     *     example=true
     * )
     *
     * @OA\Property(
     *     property="sports",
     *     type="array",
     *     description="List of user's sports",
     *     @OA\Items(type="string"),
     *     example={"Basketball", "Track & Field"}
     * )
     */
    public function __construct(
        public int $id,
        public int $rank,
        public string $first_name,
        public string $last_name,
        public ?int $graduation_year,
        public ?int $school_id,
        public ?string $school_name,
        public ?string $state_code,
        public ?string $badge_name,
        public int $completed_modules_count,
        public bool $can_connect = false,
        public ?string $avatar_url = null,
        public bool $public_profile = true,
        public array $sports = [],
    ) {
    }

    /**
     * @param object $user Leaderboard user object (from DB or Eloquent)
     * @param \App\Models\User|null $currentUser The currently authenticated user (for can_connect)
     * @return self
     */
    public static function fromObject(object $user, ?User $currentUser = null): self
    {
        // Try to resolve a full User model for avatar and public_profile
        $userModel = $user instanceof \App\Models\User ? $user : \App\Models\User::find($user->id);
        $avatarUrl = $userModel ? $userModel->getFirstMediaUrl('avatar', 'thumbnail') : null;
        $publicProfile = $userModel ? ($userModel->public_profile ?? true) : true;

        // can_connect: only if currentUser is provided and not the same as the leaderboard user
        $canConnect = false;
        if ($currentUser && $userModel && $currentUser->id !== $userModel->id) {
            $networkingService = app(\App\Services\NetworkingService::class);
            $canConnect = $networkingService->canUserConnect($currentUser, $userModel);
        }

        // Get user sports, prioritizing platform sports first
        $sports = [];
        if ($userModel) {
            // Get platform sports first
            $platformSports = $userModel->sports()->get()->pluck('name')->toArray();

            // Get custom sports
            $customSports = $userModel->customSports()->get()->pluck('name')->toArray();

            // Combine platform sports and custom sports (platform sports first)
            $sports = array_merge($platformSports, $customSports);
        }

        return new self(
            id: $user->id,
            rank: $user->rank ?? 0,
            first_name: $user->first_name,
            last_name: $user->last_name,
            graduation_year: $user->graduation_year,
            school_id: $user->school_id,
            school_name: $user->school_name,
            state_code: $user->state_code,
            badge_name: $user->badge_name,
            completed_modules_count: $user->completed_modules_count,
            can_connect: $canConnect,
            avatar_url: $avatarUrl,
            public_profile: $publicProfile,
            sports: $sports,
        );
    }
}
