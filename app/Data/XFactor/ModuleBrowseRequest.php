<?php

namespace App\Data\XFactor;

use <PERSON><PERSON>\LaravelData\Data;
use Spa<PERSON>\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Attributes\Validation\In;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Nullable;

class ModuleBrowseRequest extends Data
{
    public function __construct(
        #[Nullable]
        #[StringType]
        #[Max(255)]
        public ?string $search = null,

        #[StringType]
        #[In(['newest', 'title', 'duration'])]
        public string $sort = 'newest',

        public int $page = 1,

        public int $perPage = 10,

        public ?array $topics = null,

        #[StringType]
        #[In(['not_started', 'in_progress', 'completed'])]
        public ?string $status = null,

        public ?bool $standalone = null,
    ) {}
}
