<?php
namespace App\Data\XFactor;

use Carbon\CarbonInterface;
use <PERSON>tie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="XFactorCourseDetail",
 *     title="XFactor Course Detail",
 *     description="Detailed information about an X-Factor course, including modules and user progress.",
 *     type="object",
 *     @OA\Property(property="id", type="string", example="123"),
 *     @OA\Property(property="title", type="string", example="Leadership Fundamentals"),
 *     @OA\Property(property="description", type="string", example="Learn the basics of leadership"),
 *     @OA\Property(property="presenter", type="string", example="<PERSON> Smith"),
 *     @OA\Property(property="coverImageUrl", type="string", format="uri", nullable=true, example="https://example.com/images/course_cover.jpg"),
 *     @OA\Property(property="coverImageThumbUrl", type="string", format="uri", nullable=true, example="https://example.com/images/course_thumb.jpg"),
 *     @OA\Property(property="isCompleted", type="boolean", example=false),
 *     @OA\Property(property="createdAt", type="string", format="date-time", example="2023-01-01T10:00:00Z"),
 *     @OA\Property(property="progress", type="integer", example=75, description="User's progress percentage"),
 *     @OA\Property(property="modulesCompleted", type="integer", example=3),
 *     @OA\Property(property="totalModules", type="integer", example=4),
 *     @OA\Property(property="averageScore", type="number", format="float", example=88.5),
 *     @OA\Property(property="topics", type="array", @OA\Items(type="string"), example={"Leadership", "Communication"}),
 *     @OA\Property(property="lastAccessedAt", type="string", format="date-time", nullable=true, example="2023-05-15T14:30:00Z"),
 *     @OA\Property(property="totalRuntimeMinutes", type="integer", nullable=true, example=120),
 *     @OA\Property(property="modules", type="array", @OA\Items(ref="#/components/schemas/XFactorModuleSummary")),
 *     @OA\Property(property="exams", type="array", @OA\Items(ref="#/components/schemas/ExamData")),
 *     @OA\Property(property="completedAt", type="string", format="date-time", nullable=true, example="2023-06-01T12:00:00Z")
 * )
 */

class CourseDetailResponse extends Data
{
    public function __construct(
        public string $id,
        public string $title,
        public string $description,
        public string $presenter,
        public ?string $coverImageUrl,
        public ?string $coverImageThumbUrl,
        public bool $isCompleted,
        public CarbonInterface $createdAt,
        public int $progress,
        public int $modulesCompleted,
        public int $totalModules,
        public float $averageScore,
        public array $topics,
        public ?CarbonInterface $lastAccessedAt,
        public ?int $totalRuntimeMinutes,
        public array $modules,
        public array $exams,
        public ?CarbonInterface $completedAt = null,
    ) {}
}
