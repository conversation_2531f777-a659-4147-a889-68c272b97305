<?php

namespace App\Data\SystemInvites;

use <PERSON><PERSON>\LaravelData\Data;

class ParentSystemInviteData extends InviteData
{
    public function __construct(
        public string $first_name,
        public string $last_name,
        string $email,
        string $token,
        int $created_at,
        public int $athlete_user_id,
        public string $athlete_name,
    ) {
        parent::__construct($email, $token, $created_at);
    }

    public static function rules(): array
    {
        return [
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'max:255'],
            'athlete_user_id' => ['required', 'integer', 'exists:users,id'],
            'athlete_name' => ['required', 'string', 'max:255'],
        ];
    }
}
