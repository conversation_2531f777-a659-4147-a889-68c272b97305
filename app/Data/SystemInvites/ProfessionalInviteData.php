<?php

namespace App\Data\SystemInvites;

use <PERSON><PERSON>\LaravelData\Data;

class ProfessionalInviteData extends InviteData
{
    public function __construct(
        public string $first_name,
        public string $last_name,
        public string $company,
        string $email,
        string $token,
        int $created_at,
    ) {
        parent::__construct($email, $token, $created_at);
    }

    public static function rules(): array
    {
        return [
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'company' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'max:255'],
        ];
    }
}
