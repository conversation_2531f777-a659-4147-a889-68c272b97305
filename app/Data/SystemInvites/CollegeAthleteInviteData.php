<?php

namespace App\Data\SystemInvites;

use <PERSON><PERSON>\LaravelData\Attributes\WithoutValidation;

class CollegeAthleteInviteData extends InviteData
{
    public function __construct(
        string $email,
        string $first_name,
        string $last_name,
        ?string $college_name = null,
        ?string $sport = null,
        #[WithoutValidation]
        string $token = '',
        #[WithoutValidation]
        int $created_at = 0,
    ) {
        parent::__construct($email, $token, $created_at);
        $this->first_name = $first_name;
        $this->last_name = $last_name;
        $this->college_name = $college_name;
        $this->sport = $sport;
    }

    public readonly string $first_name;
    public readonly string $last_name;
    public readonly ?string $college_name;
    public readonly ?string $sport;
}
