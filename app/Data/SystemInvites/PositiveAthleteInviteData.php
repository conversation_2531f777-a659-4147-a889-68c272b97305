<?php

namespace App\Data\SystemInvites;

use App\Models\Nomination;
use Illuminate\Support\Str;

class PositiveAthleteInviteData extends InviteData
{
    public function __construct(
        string $email,
        string $token,
        int $created_at,
        public readonly Nomination $nomination,
        public readonly string $nominator_email,
        public readonly string $nominator_name,
        public readonly string $school_name,
        public readonly string $sport,
        public readonly string $relationship,
        public readonly ?string $note,
    ) {
        parent::__construct($email, $token, $created_at);
    }

    public function toArray(): array
    {
        return [
            'email' => $this->email,
            'token' => $this->token,
            'created_at' => $this->created_at,
            'nomination' => [
                'id' => $this->nomination->id,
                'email' => $this->nomination->email,
                'nominator_email' => $this->nomination->nominator_email,
                'nominator_first_name' => $this->nomination->nominator_first_name,
                'nominator_last_name' => $this->nomination->nominator_last_name,
                'school_name' => $this->nomination->school_name,
                'sport' => $this->nomination->sport,
                'relationship' => $this->nomination->relationship,
                'note' => $this->nomination->note,
            ],
            'nominator_email' => $this->nominator_email,
            'nominator_name' => $this->nominator_name,
            'school_name' => $this->school_name,
            'sport' => $this->sport,
            'relationship' => $this->relationship,
            'note' => $this->note,
        ];
    }

    public static function fromArray(array $data): static
    {
        return new static(
            email: $data['email'],
            token: $data['token'],
            created_at: $data['created_at'],
            nomination: Nomination::find($data['nomination_id']),
            nominator_email: $data['nominator_email'],
            nominator_name: $data['nominator_name'],
            school_name: $data['school_name'],
            sport: $data['sport'],
            relationship: $data['relationship'],
            note: $data['note'],
        );
    }

    public static function fromNomination(Nomination $nomination, string $token): self
    {
        return new self(
            email: $nomination->email,
            token: $token,
            created_at: now()->timestamp,
            nomination: $nomination,
            nominator_email: $nomination->nominator_email,
            nominator_name: "{$nomination->nominator_first_name} {$nomination->nominator_last_name}",
            school_name: $nomination->school_name,
            sport: $nomination->sport,
            relationship: $nomination->relationship,
            note: $nomination->note,
        );
    }

    public static function from(mixed ...$payloads): static
    {
        $data = $payloads[0];
        if (!is_array($data)) {
            throw new \InvalidArgumentException('Invalid payload format');
        }

        $nomination = Nomination::find($data['nomination']['id']);
        if (!$nomination) {
            throw new \InvalidArgumentException('Nomination not found');
        }

        return new static(
            email: $data['email'],
            token: $data['token'],
            created_at: $data['created_at'],
            nomination: $nomination,
            nominator_email: $data['nominator_email'],
            nominator_name: $data['nominator_name'],
            school_name: $data['school_name'],
            sport: $data['sport'],
            relationship: $data['relationship'],
            note: $data['note'],
        );
    }
}
