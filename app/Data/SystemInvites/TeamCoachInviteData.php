<?php

namespace App\Data\SystemInvites;

use <PERSON><PERSON>\LaravelData\Data;

class TeamCoachInviteData extends InviteData
{
    public function __construct(
        public string $first_name,
        public string $last_name,
        public string $school_name,
        string $email,
        string $token,
        int $created_at,
    ) {
        parent::__construct($email, $token, $created_at);
    }

    public static function rules(): array
    {
        return [
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'school_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'max:255'],
        ];
    }
}
