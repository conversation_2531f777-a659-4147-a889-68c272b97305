<?php

namespace App\Data\SystemInvites;

use App\Enums\LifeStage;
use <PERSON><PERSON>\LaravelData\Attributes\WithoutValidation;

class AlumniInviteData extends InviteData
{
    public function __construct(
        string $email,
        string $first_name,
        string $last_name,
        ?LifeStage $life_stage = null,
        ?string $college_name = null,
        ?string $company = null,
        ?string $sport = null,
        #[WithoutValidation]
        string $token = '',
        #[WithoutValidation]
        int $created_at = 0,
    ) {
        parent::__construct($email, $token, $created_at);
        $this->first_name = $first_name;
        $this->last_name = $last_name;
        $this->life_stage = $life_stage;
        $this->college_name = $college_name;
        $this->company = $company;
        $this->sport = $sport;
    }

    public readonly string $first_name;
    public readonly string $last_name;
    public readonly ?LifeStage $life_stage;
    public readonly ?string $college_name;
    public readonly ?string $company;
    public readonly ?string $sport;

    public static function rules(): array
    {
        return [
            'email' => ['required', 'email', 'max:255'],
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'life_stage' => ['nullable', 'string'],
            'college_name' => ['nullable', 'string', 'max:255'],
            'company' => ['nullable', 'string', 'max:255'],
            'sport' => ['nullable', 'string', 'max:255'],
        ];
    }
}
