<?php

namespace App\Data\SystemInvites;

use App\Models\Nomination;
use App\Models\SystemInvite;
use Illuminate\Support\Facades\Log;

class PositiveCoachInviteData extends InviteData
{
    public function __construct(
        public readonly Nomination $nomination,
        ?string $email = null,
        ?string $token = null,
        ?int $created_at = null,
    ) {
        // Get the token directly from the system_invite if it exists and no token was provided
        if (!$token && $nomination->system_invite_id) {
            $systemInvite = SystemInvite::find($nomination->system_invite_id);
            if ($systemInvite) {
                $token = $systemInvite->token;
            }
        }

        parent::__construct(
            email: $email ?? $nomination->email,
            token: $token ?? '',
            created_at: $created_at ?? time(),
        );
    }

    public function toArray(): array
    {
        return [
            'email' => $this->email,
            'token' => $this->token,
            'created_at' => $this->created_at,
            'nomination' => [
                'id' => $this->nomination->id,
            ],
        ];
    }

    public static function fromNomination(Nomination $nomination): self
    {
        // Get the token directly from the system_invite if it exists
        $token = null;
        if ($nomination->system_invite_id) {
            $systemInvite = SystemInvite::find($nomination->system_invite_id);
            if ($systemInvite) {
                $token = $systemInvite->token;
            }
        }

        return new self(
            nomination: $nomination,
            token: $token,
        );
    }

    public static function from(mixed ...$payloads): static
    {
        $data = $payloads[0];
        if (!is_array($data)) {
            throw new \InvalidArgumentException('Invalid payload format');
        }

        $nomination = Nomination::find($data['nomination']['id']);
        if (!$nomination) {
            throw new \InvalidArgumentException('Nomination not found');
        }

        // Get the token directly from the system_invite if it exists and no token was provided in data
        $token = $data['token'] ?? null;
        if (!$token && $nomination->system_invite_id) {
            $systemInvite = SystemInvite::find($nomination->system_invite_id);
            if ($systemInvite) {
                $token = $systemInvite->token;
            }
        }

        return new static(
            nomination: $nomination,
            email: $data['email'] ?? null,
            token: $token,
            created_at: $data['created_at'] ?? null,
        );
    }
}
