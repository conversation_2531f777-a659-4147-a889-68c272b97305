<?php

namespace App\Data\SystemInvites;

use App\Data\SystemInvites\InviteData;

class SponsorInviteData extends InviteData
{
    public function __construct(
        public readonly string $first_name,
        public readonly string $last_name,
        public readonly string $email,
        public readonly string $company_name,
        public readonly int $organization_id,
        public readonly string $token,
        public readonly int $created_at,
    ) {
        // No explicit parent::__construct call needed
        // Spatie/laravel-data handles property assignment
    }
}
