<?php

namespace App\Data\SystemInvites;

use Illuminate\Support\Carbon;

class TeamStudentInviteData extends InviteData
{
    public function __construct(
        public string $first_name,
        public string $last_name,
        public string $team_name,
        public string $school_name,
        string $email,
        ?string $token = null,
        ?int $created_at = null,
    ) {
        parent::__construct(
            email: $email,
            token: $token ?? '',
            created_at: $created_at ?? now()->timestamp,
        );
    }

    public static function fromArray(array $data): self
    {
        return new self(
            first_name: $data['first_name'],
            last_name: $data['last_name'],
            email: $data['email'],
            team_name: $data['team_name'],
            school_name: $data['school_name'],
            token: $data['token'] ?? null,
            created_at: $data['created_at'] ?? null,
        );
    }
}
