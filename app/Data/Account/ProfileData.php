<?php

namespace App\Data\Account;

use App\Enums\LifeStage;
use App\Models\User;
use App\Enums\ProfileType;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="ProfileData",
 *     title="User Profile Data",
 *     description="User profile information",
 *     required={"id", "first_name", "last_name", "email", "profile_type"},
 *     @OA\Property(property="id", type="integer", format="int64", example=1, description="User ID"),
 *     @OA\Property(property="first_name", type="string", example="John"),
 *     @OA\Property(property="last_name", type="string", example="Doe"),
 *     @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
 *     @OA\Property(property="notification_email", type="string", format="email", example="<EMAIL>", nullable=true, description="Optional notification email address for school/work email"),
 *     @OA\Property(property="phone", type="string", nullable=true, example="************"),
 *     @OA\Property(property="avatar_url", type="string", nullable=true, example="https://example.com/avatars/user.jpg"),
 *     @OA\Property(property="profile_type", type="string", description="The user's profile type", ref="#/components/schemas/ProfileType"),
 *     @OA\Property(
 *         property="life_stage",
 *         type="string",
 *         nullable=true,
 *         description="User's life stage, represented as an enum value or string",
 *         example="HIGH_SCHOOL",
 *         enum={"HIGH_SCHOOL", "COLLEGE", "PROFESSIONAL", "ALUMNI"}
 *     ),
 *     @OA\Property(property="created_at", type="string", format="date-time", example="2023-01-01T12:00:00Z"),
 *     @OA\Property(property="updated_at", type="string", format="date-time", example="2023-01-01T13:00:00Z"),
 * )
 * @OA\Schema(
 *     schema="ProfileType",
 *     type="string",
 *     enum={"positive_coach", "positive_athlete", "athletics_director", "sponsor", "utility", "parent", "admin", "college_athlete", "professional", "alumni", "team_student", "team_coach"}
 * )
 */
class ProfileData extends Data
{
    public function __construct(
        public readonly int $id,
        public readonly string $first_name,
        public readonly string $last_name,
        public readonly string $email,
        public readonly ?string $notification_email,
        public readonly ?string $phone,
        public readonly ProfileType|string $profile_type,
        public readonly null|LifeStage|string $life_stage,
        public readonly ?string $created_at,
        public readonly ?string $updated_at
    ) {}

    public static function fromModel(User $user): self
    {
        return new self(
            id: $user->id,
            first_name: $user->first_name,
            last_name: $user->last_name,
            email: $user->email,
            notification_email: $user->notification_email,
            phone: $user->phone,
            profile_type: $user->profile_type,
            life_stage: $user->life_stage,
            created_at: $user->created_at?->toIso8601String(),
            updated_at: $user->updated_at?->toIso8601String()
        );
    }
}
