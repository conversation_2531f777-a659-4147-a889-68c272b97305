<?php

namespace App\Data\Account;

use <PERSON><PERSON>\LaravelData\Data;
use <PERSON><PERSON>\LaravelData\Attributes\Validation\Email;
use <PERSON><PERSON>\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\Required;

/**
 * @OA\Schema(
 *     schema="LinkParentData",
 *     title="Link Parent Data",
 *     description="Data for linking a parent account to an athlete",
 *     required={"first_name", "last_name", "email"},
 *     @OA\Property(property="first_name", type="string", example="Jane"),
 *     @OA\Property(property="last_name", type="string", example="Doe"),
 *     @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
 *     @OA\Property(property="phone", type="string", nullable=true, example="************")
 * )
 */
class LinkParentData extends Data
{
    public function __construct(
        public readonly string $first_name,
        public readonly string $last_name,
        public readonly string $email,
        public readonly ?string $phone = null,
    ) {}

    public static function rules(): array
    {
        return [
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255'],
            'phone' => ['sometimes', 'string', 'max:20', 'nullable'],
        ];
    }
}
