<?php

namespace App\Data\Account;

use App\Models\User;
use OpenApi\Annotations as OA;
use Spatie\LaravelData\Data;
use <PERSON>tie\LaravelData\DataCollection;

/**
 * @OA\Schema(
 *     schema="ParentAccountData",
 *     title="Parent Account Data",
 *     description="Data representing a parent account linked to an athlete",
 *     required={"first_name", "last_name", "email"},
 *     @OA\Property(property="id", type="integer", format="int64", nullable=true, example=1, description="Parent User ID (null if pending invite)"),
 *     @OA\Property(property="first_name", type="string", example="John"),
 *     @OA\Property(property="last_name", type="string", example="Doe"),
 *     @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
 *     @OA\Property(property="phone", type="string", nullable=true, example="************"),
 *     @OA\Property(property="created_at", type="string", format="date-time", nullable=true, description="Timestamp when parent account was created (null if pending invite)"),
 *     @OA\Property(property="status", type="string", enum={"active", "pending"}, default="active", description="Status of the parent account link"),
 *     @OA\Property(property="invitation_sent_at", type="string", format="date-time", nullable=true, description="Timestamp when invitation was sent (if pending)"),
 *     @OA\Property(property="invitation_expires_at", type="string", format="date-time", nullable=true, description="Timestamp when invitation expires (if pending)"),
 *     @OA\Property(property="is_pending_invite", type="boolean", default=false, description="Indicates if this represents a pending invitation rather than an active linked account")
 * )
 */
class ParentAccountData extends Data
{
    /**
     * @param int|null $id
     * @param string $first_name
     * @param string $last_name
     * @param string $email
     * @param string|null $phone
     * @param string|null $created_at
     * @param string|null $status
     * @param string|null $invitation_sent_at
     * @param string|null $invitation_expires_at
     * @param bool $is_pending_invite
     * @param string|null $token
     */
    public function __construct(
        public readonly ?int $id,
        public readonly string $first_name,
        public readonly string $last_name,
        public readonly string $email,
        public readonly ?string $phone,
        public readonly ?string $created_at,
        public readonly ?string $status = 'active',
        public readonly ?string $invitation_sent_at = null,
        public readonly ?string $invitation_expires_at = null,
        public readonly bool $is_pending_invite = false,
        public readonly ?string $token = null
    ) {
    }

    /**
     * Create a ParentAccountData from a User model
     *
     * @param User|ParentInviteData $model
     * @return ParentAccountData
     */
    public static function fromModel($model): self
    {
        if ($model instanceof ParentInviteData) {
            return new self(
                id: $model->id,
                first_name: $model->first_name,
                last_name: $model->last_name,
                email: $model->email,
                phone: $model->phone,
                created_at: $model->created_at,
                status: $model->status,
                invitation_sent_at: $model->invitation_sent_at,
                invitation_expires_at: $model->invitation_expires_at,
                is_pending_invite: $model->is_pending_invite,
                token: $model->token
            );
        }

        $isPendingInvite = $model->is_pending_invite ?? false;

        return new self(
            id: $model->id,
            first_name: $model->first_name,
            last_name: $model->last_name,
            email: $model->email,
            phone: $model->phone,
            created_at: $model->created_at?->toIso8601String(),
            status: $isPendingInvite ? 'pending' : 'active',
            invitation_sent_at: $model->invitation_sent_at ? (is_string($model->invitation_sent_at) ? $model->invitation_sent_at : $model->invitation_sent_at->toIso8601String()) : null,
            invitation_expires_at: $model->invitation_expires_at ? (is_string($model->invitation_expires_at) ? $model->invitation_expires_at : $model->invitation_expires_at->toIso8601String()) : null,
            is_pending_invite: $isPendingInvite,
            token: null
        );
    }

    /**
     * Create a collection of ParentAccountData from a collection of models.
     *
     * @param \Illuminate\Support\Collection $models
     * @return array
     */
    public static function collection($models): array
    {
        return $models->map(fn ($model) => self::fromModel($model))->toArray();
    }
}
