<?php

namespace App\Data\Account;

use App\Models\User;
use <PERSON>tie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="AddressData",
 *     title="User Address Data",
 *     description="User address information",
 *     @OA\Property(property="id", type="integer", format="int64", description="User ID", example=1),
 *     @OA\Property(property="street_address_1", type="string", nullable=true, example="123 Main St"),
 *     @OA\Property(property="street_address_2", type="string", nullable=true, example="Apt 4B"),
 *     @OA\Property(property="city", type="string", nullable=true, example="New York"),
 *     @OA\Property(property="state_code", type="string", nullable=true, example="NY", description="State/Province code"),
 *     @OA\Property(property="zip", type="string", nullable=true, example="10001", description="Postal code"),
 *     @OA\Property(property="updated_at", type="string", format="date-time", nullable=true, example="2023-01-01T13:00:00Z", description="Last update timestamp"),

 * )
 */
class AddressData extends Data
{
    public function __construct(
        public readonly int $id,
        public readonly ?string $street_address_1,
        public readonly ?string $street_address_2,
        public readonly ?string $city,
        public readonly ?string $state_code,
        public readonly ?string $zip,
        public readonly ?string $updated_at
    ) {}

    public static function fromModel(User $user): self
    {
        return new self(
            id: $user->id,
            street_address_1: $user->street_address_1,
            street_address_2: $user->street_address_2,
            city: $user->city,
            state_code: $user->state_code,
            zip: $user->zip,
            updated_at: $user->updated_at?->toIso8601String()
        );
    }
}
