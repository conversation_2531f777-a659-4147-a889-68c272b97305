<?php

namespace App\Data\Account;

use OpenApi\Annotations as OA;
use <PERSON><PERSON>\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="DeleteAccountData",
 *     title="Delete Account Data",
 *     description="Data required for account deletion",
 *     required={"password", "confirmation"},
 *     @OA\Property(property="password", type="string", format="password", example="yourpassword123"),
 *     @OA\Property(property="confirmation", type="string", example="DELETE MY ACCOUNT", description="Must be exactly 'DELETE MY ACCOUNT' to confirm deletion")
 * )
 */
class DeleteAccountData extends Data
{
    public function __construct(
        public readonly string $password,
        public readonly string $confirmation,
    ) {}

    public static function rules(): array
    {
        return [
            'password' => ['required', 'string'],
            'confirmation' => ['required', 'string', 'in:DELETE MY ACCOUNT'],
        ];
    }

    public static function messages(): array
    {
        return [
            'confirmation.in' => 'You must type "DELETE MY ACCOUNT" to confirm deletion.',
        ];
    }
}
