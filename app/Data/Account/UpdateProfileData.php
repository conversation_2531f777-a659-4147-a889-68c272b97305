<?php

namespace App\Data\Account;

use App\Enums\LifeStage;
use Illuminate\Validation\Rule;
use OpenApi\Annotations as OA;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Optional;

/**
 * @OA\Schema(
 *     schema="UpdateProfileData",
 *     title="Update Profile Data",
 *     description="Data for updating a user's profile",
 *     @OA\Property(property="first_name", type="string", example="John", nullable=true),
 *     @OA\Property(property="last_name", type="string", example="Doe", nullable=true),
 *     @OA\Property(property="email", type="string", format="email", example="<EMAIL>", nullable=true),
 *     @OA\Property(property="notification_email", type="string", format="email", example="<EMAIL>", nullable=true, description="Optional notification email address for school/work email"),
 *     @OA\Property(property="phone", type="string", example="************", nullable=true),
 *     @OA\Property(property="life_stage", type="string", enum={"high_school_student", "high_school_graduate", "college_student", "college_graduate", "gap_year", "professional"}, nullable=true)
 * )
 */
class UpdateProfileData extends Data
{
    public function __construct(
        public string|Optional|null $first_name = null,
        public string|Optional|null $last_name = null,
        public string|Optional|null $email = null,
        public string|Optional|null $notification_email = null,
        public string|Optional|null $phone = null,
        public LifeStage|string|Optional|null $life_stage = null,
    ) {}

    public static function rules(): array
    {
        return [
            'first_name' => ['sometimes', 'string', 'max:255'],
            'last_name' => ['sometimes', 'string', 'max:255'],
            'email' => [
                'sometimes',
                'string',
                'email',
                'max:255',
                Rule::unique('users')->ignore(request()->user()->id),
            ],
            'notification_email' => [
                'sometimes',
                'nullable',
                'string',
                'email',
                'max:255',
            ],
            'phone' => ['sometimes', 'string', 'max:20', 'nullable'],
            'life_stage' => ['sometimes', 'nullable', 'string', Rule::enum(LifeStage::class)],
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @param array<string, mixed> $properties
     *
     * @return array<string, mixed>
     */
    public static function prepareForValidation(array $properties): array
    {
        // Remove life_stage if it has a falsy value
        if (isset($properties['life_stage']) && empty($properties['life_stage'])) {
            unset($properties['life_stage']);
        }

        // Ensure we're not sending empty strings that would null out existing values
        foreach ($properties as $key => $value) {
            // If the value is an empty string, unset it to prevent nulling out existing values
            if ($value === '') {
                unset($properties[$key]);
            }
        }

        return $properties;
    }
}
