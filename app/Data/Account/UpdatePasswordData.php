<?php

namespace App\Data\Account;

use Illuminate\Validation\Rules\Password;
use OpenApi\Annotations as OA;
use <PERSON>tie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="UpdatePasswordData",
 *     title="Update Password Data",
 *     description="Data for updating a user's password",
 *     required={"current_password", "new_password", "new_password_confirmation"},
 *     @OA\Property(property="current_password", type="string", format="password", example="current123"),
 *     @OA\Property(property="new_password", type="string", format="password", example="newPassword123"),
 *     @OA\Property(property="new_password_confirmation", type="string", format="password", example="newPassword123")
 * )
 */
class UpdatePasswordData extends Data
{
    public function __construct(
        public readonly string $current_password,
        public readonly string $new_password,
        public readonly string $new_password_confirmation,
    ) {}

    public static function rules(): array
    {
        return [
            'current_password' => ['required', 'string'],
            'new_password' => ['required', 'string', Password::defaults()],
            'new_password_confirmation' => ['required', 'string', 'same:new_password'],
        ];
    }
}
