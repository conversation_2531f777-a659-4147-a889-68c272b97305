<?php

namespace App\Data\Account;

use OpenApi\Annotations as OA;
use <PERSON><PERSON>\LaravelData\Data;
use <PERSON><PERSON>\LaravelData\Optional;

/**
 * @OA\Schema(
 *     schema="UpdateAddressData",
 *     title="Update Address Data",
 *     description="Data for updating a user's address",
 *     @OA\Property(property="street_address_1", type="string", example="123 Main St", nullable=true),
 *     @OA\Property(property="street_address_2", type="string", example="Apt 4B", nullable=true),
 *     @OA\Property(property="city", type="string", example="Pittsburgh", nullable=true),
 *     @OA\Property(property="state_code", type="string", example="PA", nullable=true),
 *     @OA\Property(property="zip", type="string", example="15213", nullable=true)
 * )
 */
class UpdateAddressData extends Data
{
    public function __construct(
        public string|Optional|null $street_address_1 = null,
        public string|Optional|null $street_address_2 = null,
        public string|Optional|null $city = null,
        public string|Optional|null $state_code = null,
        public string|Optional|null $zip = null,
    ) {}

    public static function rules(): array
    {
        return [
            'street_address_1' => ['sometimes', 'string', 'max:255'],
            'street_address_2' => ['sometimes', 'string', 'max:255', 'nullable'],
            'city' => ['sometimes', 'string', 'max:255'],
            'state_code' => ['sometimes', 'string', 'size:2'],
            'zip' => ['sometimes', 'string', 'max:10'],
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @param array<string, mixed> $properties
     *
     * @return array<string, mixed>
     */
    public static function prepareForValidation(array $properties): array
    {
        // Ensure we're not sending empty strings that would null out existing values
        foreach ($properties as $key => $value) {
            // If the value is an empty string, unset it to prevent nulling out existing values
            if ($value === '') {
                unset($properties[$key]);
            }
        }

        return $properties;
    }
}
