<?php

namespace App\Data\Account;

use App\Enums\ProfileType;
use App\Models\SystemInvite;
use App\Models\User;
use Carbon\Carbon;
use Spatie\LaravelData\Data;

class ParentInviteData extends Data
{
    public function __construct(
        public readonly ?int $id,
        public readonly string $first_name,
        public readonly string $last_name,
        public readonly string $email,
        public readonly ?string $phone,
        public readonly ?string $created_at,
        public readonly string $status = 'pending',
        public readonly ?string $invitation_sent_at = null,
        public readonly ?string $invitation_expires_at = null,
        public readonly bool $is_pending_invite = true,
        public readonly ?string $token = null
    ) {
    }

    public static function fromSystemInvite(SystemInvite $invite): self
    {
        $inviteData = $invite->invite_data;

        return new self(
            id: -1 * $invite->id, // Use negative ID to indicate pending invite
            first_name: $inviteData?->first_name ?? 'Unknown',
            last_name: $inviteData?->last_name ?? 'Unknown',
            email: $invite->email,
            phone: null, // System invites may not store phone numbers
            created_at: $invite->created_at?->toIso8601String(),
            status: 'pending',
            invitation_sent_at: $invite->created_at?->toIso8601String(),
            invitation_expires_at: $invite->expires_at?->toIso8601String(),
            is_pending_invite: true,
            token: $invite->token
        );
    }

    /**
     * Create a collection of ParentInviteData from a collection of system invites
     *
     * @param \Illuminate\Support\Collection $invites
     * @return \Illuminate\Support\Collection
     */
    public static function collection($invites)
    {
        return $invites->map(fn ($invite) => self::fromSystemInvite($invite));
    }
}
