<?php

namespace App\Data;

use App\Enums\ConnectionStatus;
use App\Models\Connection;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Spatie\LaravelData\Data;
use stdClass;
use OpenApi\Attributes as OA;

/**
 * @OA\Schema(
 *     schema="ConnectionData",
 *     title="Connection Data",
 *     description="Data representing a connection between two users",
 *     @OA\Property(property="id", type="integer", example=1, description="Connection ID"),
 *     @OA\Property(property="requesterId", type="integer", example=10, description="ID of the user who initiated the connection request"),
 *     @OA\Property(property="recipientId", type="integer", example=15, description="ID of the user who received the connection request"),
 *     @OA\Property(property="status", type="string", enum={"pending", "accepted", "declined", "blocked"}, example="accepted", description="Current status of the connection"),
 *     @OA\Property(property="createdAt", type="string", format="date-time", example="2023-05-15T14:30:00Z", description="Timestamp when the connection was created or last updated"),
 *     @OA\Property(
 *         property="requester",
 *         description="Basic data about the requester user",
 *         nullable=true,
 *         allOf={@OA\Schema(ref="#/components/schemas/UserBasicData")}
 *     ),
 *     @OA\Property(
 *         property="recipient",
 *         description="Basic data about the recipient user",
 *         nullable=true,
 *         allOf={@OA\Schema(ref="#/components/schemas/UserBasicData")}
 *     )
 * )
 */
class ConnectionData extends Data
{
    public function __construct(
        public readonly int $id,
        public readonly int $requesterId,
        public readonly int $recipientId,
        public readonly string $status,
        public readonly string $createdAt,
        public readonly ?UserBasicData $requester,
        public readonly ?UserBasicData $recipient,
    ) {
    }

    /**
     * Create a new instance from a Connection model.
     * This method handles the conversion of User models to UserBasicData.
     */
    public static function fromConnection(Connection $connection): static
    {
        Log::debug('ConnectionData::fromConnection', [
            'connectionId' => $connection->id,
            'status' => $connection->status->value,
            'hasRequester' => $connection->requester !== null,
            'hasRecipient' => $connection->recipient !== null
        ]);

        try {
            $data = new static(
                id: $connection->id,
                requesterId: $connection->requester_id,
                recipientId: $connection->recipient_id,
                status: $connection->status->value,
                createdAt: $connection->created_at->toIso8601String(),
                requester: $connection->requester ? UserBasicData::fromStdClass($connection->requester) : null,
                recipient: $connection->recipient ? UserBasicData::fromStdClass($connection->recipient) : null,
            );

            return $data;
        } catch (\Throwable $e) {
            Log::error('Error in ConnectionData::fromConnection', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'connectionId' => $connection->id,
                'statusType' => is_object($connection->status) ? get_class($connection->status) : gettype($connection->status),
                'statusValue' => $connection->status instanceof ConnectionStatus ? $connection->status->value : 'N/A'
            ]);
            throw $e;
        }
    }
}
