<?php

namespace App\Data\Onboarding;

use <PERSON><PERSON>\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="WorkExperienceItemDTO",
 *     required={"name", "date_range", "description", "order"},
 *     @OA\Property(property="name", type="string", example="Local Sports Store", description="Name of the employer or workplace"),
 *     @OA\Property(property="date_range", type="string", example="Summer 2023", description="Date range of employment"),
 *     @OA\Property(property="description", type="string", example="Worked as a sales associate", description="Description of the work experience"),
 *     @OA\Property(property="order", type="integer", example=1, description="Display order of the work experience")
 * )
 */
class WorkExperienceItemDTO extends Data
{
    public function __construct(
        #[Required, StringType]
        public readonly string $name,

        #[Required, StringType]
        public readonly string $date_range,

        #[Required, StringType]
        public readonly string $description,

        #[Required]
        public readonly int $order,
    ) {}
}
