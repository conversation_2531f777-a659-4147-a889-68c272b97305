<?php

namespace App\Data\Onboarding;

use <PERSON><PERSON>\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="SportsDTO",
 *     required={"sports"},
 *     @OA\Property(
 *         property="token",
 *         type="string",
 *         example="1234567890"
 *     ),
 *     @OA\Property(
 *         property="sports",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/SportDTO")
 *     )
 * )
 */
class SportsDTO extends Data
{
    public function __construct(
        #[Required, DataCollectionOf(SportDTO::class)]
        public readonly array $sports,
    ) {}
}
