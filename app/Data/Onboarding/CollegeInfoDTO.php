<?php

namespace App\Data\Onboarding;

use <PERSON><PERSON>\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Data;
/**
 * @OA\Schema(
 *     schema="CollegeInfoDTO",
 *     title="College Information Data Transfer Object",
 *     description="Holds the data for college-related information during onboarding.",
 *     required={"state", "college", "college_graduation_year", "current_gpa", "gender", "height", "weight", "career_interests", "sports"},
 *     @OA\Property(
 *         property="state",
 *         type="string",
 *         description="The state where the college is located"
 *     ),
 *     @OA\Property(
 *         property="college",
 *         type="string",
 *         description="The name of the college"
 *     ),
 *     @OA\Property(
 *         property="college_graduation_year",
 *         type="string",
 *         description="The year the user will graduate from college"
 *     ),
 *     @OA\Property(
 *         property="current_gpa",
 *         type="number",
 *         description="The user's current Grade Point Average"
 *     ),
 *     @OA\Property(
 *         property="gender",
 *         type="string",
 *         description="The user's gender"
 *     ),
 *     @OA\Property(
 *         property="height",
 *         type="string",
 *         description="The user's height"
 *     ),
 *     @OA\Property(
 *         property="weight",
 *         type="string",
 *         description="The user's weight"
 *     ),
 *     @OA\Property(
 *         property="career_interests",
 *         type="array",
 *         description="The user's career interests",
 *         @OA\Items(type="integer")
 *     ),
 *     @OA\Property(
 *         property="sports",
 *         type="array",
 *         description="The user's sports",
 *         @OA\Items(type="integer")
 *     ),
 *     @OA\Property(
 *         property="social_links",
 *         type="array",
 *         description="The user's social links",
 *         @OA\Items(type="string")
 *     )
 * )
 */

class CollegeInfoDTO extends Data
{
    public function __construct(
        #[Required]
        public string $state,

        #[Required]
        public string $college,

        #[Required]
        public string $college_graduation_year,

        #[Required]
        public float $current_gpa,

        #[Required]
        public string $gender,

        #[Required]
        public string $height,

        #[Required]
        public string $weight,

        #[Required]
        public array $career_interests,

        #[Required]
        public array $sports,

        public ?array $social_links = null,
    ) {}

    public static function rules(): array
    {
        return [
            'state' => ['required', 'string'],
            'college' => ['required', 'string'],
            'college_graduation_year' => ['required', 'string'],
            'current_gpa' => ['required', 'numeric', 'min:0', 'max:4.0'],
            'gender' => ['required', 'string'],
            'height' => ['required', 'string'],
            'weight' => ['required', 'string'],
            'career_interests' => ['required', 'array'],
            'career_interests.*' => ['string'],
            'sports' => ['required', 'array'],
            'sports.*.name' => ['required', 'string'],
            'sports.*.id' => ['required', 'integer'],
            'sports.*.is_custom' => ['required', 'boolean'],
            'social_links' => ['nullable', 'array'],
        ];
    }
}
