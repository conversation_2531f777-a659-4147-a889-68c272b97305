<?php

namespace App\Data\Onboarding\Sponsor;

use Illuminate\Http\UploadedFile;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Attributes\Validation\Url;
use Spatie\LaravelData\Attributes\Validation\Image;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\File;
use Spatie\LaravelData\Attributes\Validation\Mimes;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Optional;

/**
 * @OA\Schema(
 *     schema="SponsorOrganizationInfoDTO",
 *     title="Sponsor Organization Info DTO",
 *     description="Data for sponsor organization information step of onboarding",
 *     @OA\Property(property="organization_id", type="integer", example=null, description="Existing organization ID if selected from search"),
 *     @OA\Property(property="organization_name", type="string", example="The Home Depot"),
 *     @OA\Property(property="organization_description", type="string", example="A home improvement retailer company"),
 *     @OA\Property(property="organization_website", type="string", format="url", nullable=true, example="https://positiveathlete.org"),
 *     @OA\Property(property="organization_logo", type="string", format="binary", nullable=true, description="Organization logo file (max 2MB)"),
 *     @OA\Property(property="organization_about", type="string", nullable=true, description="Rich text/HTML description of the organization")
 * )
 */
class OrganizationInfoDTO extends Data
{
    public function __construct(
        #[Nullable]
        #[MapInputName('organization_id')]
        public ?int $organizationId = null,

        #[Required, StringType]
        #[MapInputName('organization_name')]
        public string $organizationName,

        #[Nullable, StringType]
        #[MapInputName('organization_description')]
        public ?string $organizationDescription = null,

        #[Nullable, StringType, Url]
        #[MapInputName('organization_website')]
        public ?string $organizationWebsite = null,

        #[Nullable, Image, Max(10240)]
        #[MapInputName('organization_logo')]
        public ?UploadedFile $organizationLogo = null,

        #[Nullable, StringType]
        #[MapInputName('organization_about')]
        public string|Optional|null $organizationAbout = null,
    ) {
    }
}
