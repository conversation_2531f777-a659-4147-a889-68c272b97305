<?php

namespace App\Data\Onboarding\Sponsor;

use <PERSON><PERSON>\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="SponsorOnboardingStepResponse",
 *     title="Sponsor Onboarding Step Response",
 *     description="Response for sponsor onboarding step update",
 *     @OA\Property(property="current_step", type="string", example="account_info"),
 *     @OA\Property(property="next_step", type="string", example="organization_info"),
 *     @OA\Property(property="step_data", type="object", nullable=true),
 * )
 */
class OnboardingStepResponse extends Data
{
    public function __construct(
        public string $current_step,
        public ?string $next_step = null,
        public ?array $step_data = null,
    ) {
    }
}
