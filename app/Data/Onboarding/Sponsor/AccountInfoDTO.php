<?php

namespace App\Data\Onboarding\Sponsor;

use <PERSON>tie\LaravelData\Data;
use <PERSON>tie\LaravelData\Attributes\Validation\Email;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Attributes\Validation\Min;
use Spatie\LaravelData\Attributes\Validation\Confirmed;
use Spatie\LaravelData\Attributes\MapInputName;

/**
 * @OA\Schema(
 *     schema="SponsorAccountInfoDTO",
 *     title="Sponsor Account Info DTO",
 *     description="Data for sponsor account information step of onboarding",
 *     @OA\Property(property="first_name", type="string", example="John"),
 *     @OA\Property(property="last_name", type="string", example="Doe"),
 *     @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
 *     @OA\Property(property="phone", type="string", example="************"),
 *     @OA\Property(property="password", type="string", format="password", example="SecurePassword123"),
 *     @OA\Property(property="password_confirmation", type="string", format="password", example="SecurePassword123")
 * )
 */
class AccountInfoDTO extends Data
{
    public function __construct(
        #[Required, StringType]
        #[MapInputName('first_name')]
        public string $firstName,

        #[Required, StringType]
        #[MapInputName('last_name')]
        public string $lastName,

        #[Required, Email]
        public string $email,

        #[Required, StringType]
        public string $phone,

        #[Required, StringType, Min(8)]
        public string $password
    ) {
    }
}
