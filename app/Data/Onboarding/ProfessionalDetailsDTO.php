<?php

namespace App\Data\Onboarding;

use <PERSON><PERSON>\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="ProfessionalDetailsDTO",
 *     required={"state", "employer", "industry"},
 *     @OA\Property(property="state", type="string", example="CA", description="2-letter state code"),
 *     @OA\Property(property="employer", type="string", example="Acme Corp", description="Current employer name"),
 *     @OA\Property(property="industry", type="string", example="Technology", description="Industry sector"),
 *     @OA\Property(
 *         property="social_links",
 *         type="array",
 *         nullable=true,
 *         @OA\Items(
 *             type="object",
 *             @OA\Property(property="platform", type="string", example="hudl"),
 *             @OA\Property(property="url", type="string", example="https://hudl.com/profile/username")
 *         )
 *     ),
 *     @OA\Property(
 *         property="profile_photo",
 *         type="string",
 *         format="binary",
 *         nullable=true,
 *         description="Profile photo upload (max 5MB)"
 *     ),
 *       @OA\Property(
 *         property="token",
 *         type="string",
 *         example="1234567890",
 *         description="The token of the alumni"
 *     ),
 * )
 */
class ProfessionalDetailsDTO extends Data
{
    public function __construct(
        #[Required, StringType]
        public readonly string $state,

        #[Required, StringType]
        public readonly string $employer,

        #[Required, StringType]
        public readonly string $industry,

        public readonly ?array $social_links = null,

        public readonly ?string $profile_photo = null,
    ) {}
}
