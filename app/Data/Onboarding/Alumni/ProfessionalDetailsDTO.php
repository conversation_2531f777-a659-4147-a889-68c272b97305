<?php

namespace App\Data\Onboarding\Alumni;

use Illuminate\Http\UploadedFile;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\ArrayType;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\Image;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="AlumniProfessionalDetailsDTO",
 *     required={"state", "employer", "interests"},
 *     @OA\Property(property="state", type="string", example="CA"),
 *     @OA\Property(property="employer", type="string", example="Starbucks Corp & LLC Brands"),
 *     @OA\Property(property="job_title", type="string", example="Store Manager", nullable=true),
 *     @OA\Property(
 *         property="interests",
 *         type="array",
 *         @OA\Items(type="integer", example=1)
 *     ),
 *     @OA\Property(property="twitter", type="string", example="username", nullable=true),
 *     @OA\Property(property="instagram", type="string", example="username", nullable=true),
 *     @OA\Property(property="facebook", type="string", example="username", nullable=true),
 *     @OA\Property(property="hudl", type="string", example="username", nullable=true),
 *     @OA\Property(property="custom_link", type="string", example="https://example.com", nullable=true),
 *     @OA\Property(property="token", type="string", example="1234567890", description="The token of the alumni"),
 *     @OA\Property(property="profile_photo", type="string", format="binary", description="Profile photo upload (max 5MB)")
 * )
 */
class ProfessionalDetailsDTO extends Data
{
    public function __construct(
        #[Required, StringType]
        public readonly string $state,

        #[Required, StringType, Max(255)]
        public readonly string $employer,

        #[Nullable, StringType, Max(255)]
        public readonly ?string $job_title = null,

        #[Required, ArrayType]
        public readonly array $interests,

        #[Nullable, StringType]
        public readonly ?string $twitter = null,

        #[Nullable, StringType]
        public readonly ?string $instagram = null,

        #[Nullable, StringType]
        public readonly ?string $facebook = null,

        #[Nullable, StringType]
        public readonly ?string $hudl = null,

        #[Nullable, StringType]
        public readonly ?string $custom_link = null,

        #[Nullable, Image]
        public readonly ?UploadedFile $profile_photo = null,
    ) {}

    public static function rules(): array
    {
        return [
            'state' => ['required', 'string', 'exists:states,code'],
            'employer' => ['required', 'string', 'max:255'],
            'job_title' => ['nullable', 'string', 'max:255'],
            'interests' => ['required', 'array'],
            'interests.*' => ['integer', 'exists:interests,id'],
            'twitter' => ['nullable', 'string'],
            'instagram' => ['nullable', 'string'],
            'facebook' => ['nullable', 'string'],
            'hudl' => ['nullable', 'string'],
            'custom_link' => ['nullable', 'string'],
            'profile_photo' => ['nullable', 'image', 'max:5120'], // 5MB max
        ];
    }
}
