<?php

namespace App\Data\Onboarding\Alumni;

use Illuminate\Http\UploadedFile;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Numeric;
use Spatie\LaravelData\Attributes\Validation\Min;
use Spatie\LaravelData\Attributes\Validation\ArrayType;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\Image;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="AlumniCollegeDetailsDTO",
 *     required={"state", "college", "graduation_year", "gender"},
 *     @OA\Property(property="state", type="string", example="CA"),
 *     @OA\Property(property="college", type="string", example="University of Michigan"),
 *     @OA\Property(property="graduation_year", type="string", example="2025"),
 *     @OA\Property(property="gpa", type="number", format="float", example=4.0, nullable=true),
 *     @OA\Property(property="gender", type="string", example="Male"),
 *     @OA\Property(property="height", type="string", example="5'11", nullable=true),
 *     @OA\Property(property="weight", type="integer", example=175, nullable=true),
 *     @OA\Property(
 *         property="career_interests",
 *         type="array",
 *         @OA\Items(type="integer", example=1)
 *     ),
 *     @OA\Property(property="twitter", type="string", example="username", nullable=true),
 *     @OA\Property(property="instagram", type="string", example="username", nullable=true),
 *     @OA\Property(property="facebook", type="string", example="username", nullable=true),
 *     @OA\Property(property="hudl", type="string", example="username", nullable=true),
 *     @OA\Property(property="custom_link", type="string", example="https://example.com", nullable=true),
 *     @OA\Property(property="token", type="string", example="1234567890", description="The token of the alumni"),
 *     @OA\Property(property="profile_photo", type="string", format="binary", description="Profile photo upload (max 5MB)")
 * )
 */
class CollegeDetailsDTO extends Data
{
    public function __construct(
        #[Required, StringType]
        public readonly string $state,

        #[Required, StringType, Max(255)]
        public readonly string $college,

        #[Required, StringType]
        public readonly string $graduation_year,

        #[Numeric, Min(0), Max(4.0)]
        public readonly ?float $gpa = null,

        #[Required, StringType]
        public readonly string $gender,

        #[StringType]
        public readonly ?string $height = null,

        #[Nullable, Numeric]
        public readonly ?int $weight = null,

        #[ArrayType]
        public readonly ?array $career_interests = null,

        #[Nullable, StringType]
        public readonly ?string $twitter = null,

        #[Nullable, StringType]
        public readonly ?string $instagram = null,

        #[Nullable, StringType]
        public readonly ?string $facebook = null,

        #[Nullable, StringType]
        public readonly ?string $hudl = null,

        #[Nullable, StringType]
        public readonly ?string $custom_link = null,

        #[Nullable, Image]
        public readonly ?UploadedFile $profile_photo = null,
    ) {}

    public static function rules(): array
    {
        return [
            'state' => ['required', 'string', 'exists:states,code'],
            'college' => ['required', 'string', 'max:255'],
            'graduation_year' => ['required', 'string'],
            'gpa' => ['nullable', 'numeric', 'min:0', 'max:4.0'],
            'gender' => ['required', 'string'],
            'height' => ['nullable', 'string'],
            'weight' => ['nullable', 'integer'],
            'career_interests' => ['nullable', 'array'],
            'career_interests.*' => ['integer', 'exists:interests,id'],
            'twitter' => ['nullable', 'string'],
            'instagram' => ['nullable', 'string'],
            'facebook' => ['nullable', 'string'],
            'hudl' => ['nullable', 'string'],
            'custom_link' => ['nullable', 'string'],
            'profile_photo' => ['nullable', 'image', 'max:5120'], // 5MB max
        ];
    }
}
