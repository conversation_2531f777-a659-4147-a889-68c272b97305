<?php

namespace App\Data\Onboarding\Alumni;

use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\Email;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Confirmed;
use Spatie\LaravelData\Attributes\Validation\Min;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="AlumniAccountInfoDTO",
 *     required={"first_name", "last_name", "email", "phone", "street_address", "city", "state", "zip_code", "password", "password_confirmation"},
 *     @OA\Property(property="first_name", type="string", example="John"),
 *     @OA\Property(property="last_name", type="string", example="Doe"),
 *     @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
 *     @OA\Property(property="phone", type="string", example="************", maxLength=20),
 *     @OA\Property(property="street_address", type="string", example="123 Main St"),
 *     @OA\Property(property="unit", type="string", nullable=true, example="Apt 4B", maxLength=50),
 *     @OA\Property(property="city", type="string", example="Anytown"),
 *     @OA\Property(property="state", type="string", example="CA"),
 *     @OA\Property(property="zip_code", type="string", example="90210", maxLength=10),
 *     @OA\Property(property="password", type="string", format="password", example="SecurePassword123"),
 *     @OA\Property(property="password_confirmation", type="string", format="password", example="SecurePassword123"),
 *   @OA\Property(
 *         property="token",
 *         type="string",
 *         example="**********",
 *         description="The token of the alumni"
 *     ),
 * )
 */
class AccountInfoDTO extends Data
{
    public function __construct(
        #[Required, StringType, Max(255)]
        public readonly string $first_name,

        #[Required, StringType, Max(255)]
        public readonly string $last_name,

        #[Required, Email, Max(255)]
        public readonly string $email,

        #[Required, StringType, Max(20)]
        public readonly string $phone,

        #[Required, StringType, Max(255)]
        public readonly string $street_address,

        #[StringType, Max(50)]
        public readonly ?string $unit,

        #[Required, StringType, Max(100)]
        public readonly string $city,

        #[Required, StringType]
        public readonly string $state,

        #[Required, StringType, Max(10)]
        public readonly string $zip_code,

        #[Required, StringType, Min(8), Confirmed]
        public readonly string $password,

        #[Required, StringType]
        public readonly string $password_confirmation,
    ) {}

    public static function rules(): array
    {
        return [
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'max:255'],
            'phone' => ['required', 'string', 'max:20'],
            'street_address' => ['required', 'string', 'max:255'],
            'unit' => ['nullable', 'string', 'max:50'],
            'city' => ['required', 'string', 'max:100'],
            'state' => ['required', 'string'],
            'zip_code' => ['required', 'string', 'max:10'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
            'password_confirmation' => ['required', 'string'],
        ];
    }
}
