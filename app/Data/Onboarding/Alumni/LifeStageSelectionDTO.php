<?php

namespace App\Data\Onboarding\Alumni;

use App\Enums\LifeStage;
use App\Enums\ProfileType;
use <PERSON><PERSON>\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Data;
/**
 * @OA\Schema(
 *     schema="AlumniLifeStageSelectionDTO",
 *     required={"life_stage", "intended_profile_type"},
 *     @OA\Property(
 *         property="life_stage",
 *         type="string",
 *         enum={"college_student", "professional", "college_graduate", "gap_year", "high_school_student", "high_school_graduate"},
 *         description="The life stage of the alumni",
 *         example="college_student"
 *     ),
 *
 *     @OA\Property(
 *         property="token",
 *         type="string",
 *         example="1234567890",
 *         description="The token of the alumni"
 *     ),
 *     @OA\Property(
 *         property="intended_profile_type",
 *         type="string",
 *         example="alumni",
 *         description="The intended profile type, should be 'alumni'"
 *     )
 * )
 */

class LifeStageSelectionDTO extends Data
{
    public function __construct(
        #[Required, StringType]
        public readonly string $life_stage,

        #[Required, StringType]
        public readonly string $intended_profile_type,
    ) {}

    public static function rules(): array
    {
        return [
            'life_stage' => ['required', 'string', 'in:' . implode(',', array_column(LifeStage::cases(), 'value'))],
            'intended_profile_type' => ['required', 'string', 'in:' . implode(',', [ProfileType::COLLEGE_ATHLETE->value, ProfileType::PROFESSIONAL->value])],
        ];
    }
}
