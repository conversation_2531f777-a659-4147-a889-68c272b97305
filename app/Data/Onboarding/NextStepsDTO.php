<?php

namespace App\Data\Onboarding;

use <PERSON><PERSON>\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="NextStepsDTO",
 *     title="Next Steps Data Transfer Object",
 *     description="Holds the data for the next steps in the onboarding process."
 * )
 */
class NextStepsDTO extends Data
{
    /**
     * @OA\Property(description="The user's current status", type="string", enum={"college", "working", "high_school", "gap_year"})
     */
    public string $status; // 'college', 'working', 'high_school', or 'gap_year'

    public function __construct(
        string $status,
    ) {
        $this->status = $status;
    }
}
