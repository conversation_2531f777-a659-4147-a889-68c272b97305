<?php

namespace App\Data\Onboarding;

use <PERSON><PERSON>\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="WorkExperienceData",
 *     title="Work Experience Data",
 *     description="Data structure for work experience information",
 *     @OA\Property(
 *         property="title",
 *         type="string",
 *         description="Job title",
 *         example="Sales Associate"
 *     ),
 *     @OA\Property(
 *         property="company",
 *         type="string",
 *         description="Company or organization name",
 *         example="Local Retail Store"
 *     ),
 *     @OA\Property(
 *         property="date_range",
 *         type="string",
 *         description="Employment date range",
 *         example="June 2023 - August 2023"
 *     ),
 *     @OA\Property(
 *         property="description",
 *         type="string",
 *         nullable=true,
 *         description="Description of job responsibilities",
 *         example="Assisted customers, managed inventory, operated cash register"
 *     )
 * )
 */
class WorkExperienceData extends Data
{
    public function __construct(
        public string $title,
        public string $company,
        public string $date_range,
        public ?string $description,
    ) {}

    public static function rules(): array
    {
        return [
            'title' => 'required|string',
            'company' => 'required|string',
            'date_range' => 'required|string',
            'description' => 'nullable|string',
        ];
    }
}
