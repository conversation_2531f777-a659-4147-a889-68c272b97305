<?php

namespace App\Data\Onboarding;

use <PERSON><PERSON>\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Attributes\Validation\Exists;
use <PERSON>tie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="SportDTO",
 *     required={"id", "order"},
 *     @OA\Property(property="name", type="string", example="Basketball", description="Name of the sport"),
 *     @OA\Property(property="id", type="integer", example=1, description="Sport ID from the database"),
 *     @OA\Property(property="order", type="integer", example=1, description="Display order of the sport"),
 *     @OA\Property(property="is_custom", type="boolean", example=false, description="Whether this is a custom sport added by the user")
 * )
 */
class SportDTO extends Data
{
    public function __construct(
        public readonly ?string $name = null,

        #[Required, Exists('sports', 'id')]
        public readonly int $id,

        #[Required]
        public readonly int $order,

        #[Required]
        public readonly bool $is_custom = false,
    ) {}

    /**
     * Define the validation rules.
     *
     * @return array<string, mixed>
     */
    public static function rules(): array
    {
        return [
            'name' => ['nullable', 'string'],
            'id' => ['required', 'integer', 'exists:sports,id'],
            'order' => ['required', 'integer'],
            'is_custom' => ['required', 'boolean'],
        ];
    }
}
