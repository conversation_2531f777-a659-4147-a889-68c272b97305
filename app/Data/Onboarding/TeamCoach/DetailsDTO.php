<?php

namespace App\Data\Onboarding\TeamCoach;

use <PERSON><PERSON>\LaravelData\Data;

class DetailsDTO extends Data
{
    public function __construct(
        public string $title,
        public string $state,
        public string $county,
        public string $high_school,
        public ?array $social_links = null,
        public ?string $profile_photo = null,
    ) {}

    public static function rules(): array
    {
        return [
            'title' => ['required', 'string', 'max:255'],
            'state' => ['required', 'string'],
            'county' => ['required', 'string'],
            'high_school' => ['required', 'string', 'max:255'],
            'social_links' => ['nullable', 'array'],
            'social_links.hudl' => ['nullable', 'url'],
            'social_links.twitter' => ['nullable', 'url'],
            'profile_photo' => ['nullable', 'string'],
        ];
    }
}
