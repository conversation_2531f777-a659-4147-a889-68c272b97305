<?php

namespace App\Data\Onboarding;

use Illuminate\Http\UploadedFile;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Numeric;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Attributes\Validation\Image;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Data;
use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *   schema="DetailsDTO",
 *   description="Data transfer object for athlete details",
 *   @OA\Property(property="token", type="string", example="1234567890"),
 *   @OA\Property(property="state", type="string", example="CA"),
 *   @OA\Property(property="county", type="string", example="Los Angeles"),
 *   @OA\Property(property="school_id", type="integer", example=123),
 *   @OA\Property(property="graduation_year", type="string", example="2024"),
 *   @OA\Property(property="current_gpa", type="number", example=3.8),
 *   @OA\Property(property="current_class_rank", type="string", example="16/402"),
 *   @OA\Property(property="gender", type="string", example="male"),
 *   @OA\Property(property="height", type="string", example="5-11"),
 *   @OA\Property(property="weight", type="number", example=160),
 *   @OA\Property(property="career_interests", type="array", @OA\Items(type="integer"), description="Array of interest IDs (maintained for backward compatibility)"),
 *   @OA\Property(property="interests", type="array", @OA\Items(type="integer"), description="Array of interest IDs, preferred over career_interests"),
 *   @OA\Property(property="twitter", type="string", example="username"),
 *   @OA\Property(property="instagram", type="string", example="username"),
 *   @OA\Property(property="facebook", type="string", example="username"),
 *   @OA\Property(property="hudl", type="string", example="username"),
 *   @OA\Property(property="custom_link", type="string", example="https://example.com"),
 *   @OA\Property(property="profile_photo", type="string", format="binary")
 * )
 */
class DetailsDTO extends Data
{
    public function __construct(
        #[Nullable, StringType]
        public readonly ?string $state = null,

        #[Nullable, StringType]
        public readonly ?string $county = null,

        #[Nullable, Numeric]
        public readonly ?int $school_id = null,

        #[Nullable, StringType]
        public readonly ?string $graduation_year = null,

        #[Nullable, Numeric, Max(4.0)]
        public readonly ?float $current_gpa = null,

        #[Nullable, StringType]
        public readonly ?string $current_class_rank = null,

        #[Nullable, StringType]
        public readonly ?string $gender = null,

        #[Nullable, StringType]
        public readonly ?string $height = null,

        #[Nullable, Numeric]
        public readonly ?float $weight = null,

        #[Nullable]
        public readonly ?array $career_interests = null,

        #[Nullable]
        public readonly ?array $interests = null,

        #[Nullable, StringType]
        public readonly ?string $twitter = null,

        #[Nullable, StringType]
        public readonly ?string $instagram = null,

        #[Nullable, StringType]
        public readonly ?string $facebook = null,

        #[Nullable, StringType]
        public readonly ?string $hudl = null,

        #[Nullable, StringType]
        public readonly ?string $custom_link = null,

        #[Nullable, Image]
        public readonly ?UploadedFile $profile_photo = null,
    ) {}

    public static function rules(): array
    {
        return [
            'state' => ['nullable', 'string'],
            'county' => ['nullable', 'string'],
            'school_id' => ['nullable', 'integer', 'exists:schools,id'],
            'graduation_year' => ['nullable', 'string'],
            'current_gpa' => ['nullable', 'numeric', 'max:4.0'],
            'current_class_rank' => ['nullable', 'string'],
            'gender' => ['nullable', 'string'],
            'height' => ['nullable', 'string'],
            'weight' => ['nullable', 'numeric'],
            'career_interests' => ['nullable', 'array'],
            'career_interests.*' => ['nullable', 'integer', 'exists:interests,id'],
            'interests' => ['nullable', 'array'],
            'interests.*' => ['nullable', 'integer', 'exists:interests,id'],
            'twitter' => ['nullable', 'string'],
            'instagram' => ['nullable', 'string'],
            'facebook' => ['nullable', 'string'],
            'hudl' => ['nullable', 'string'],
            'custom_link' => ['nullable', 'string'],
            'profile_photo' => ['nullable', 'image', 'max:5120'], // 5MB max
        ];
    }
}
