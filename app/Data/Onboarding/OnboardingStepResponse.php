<?php

namespace App\Data\Onboarding;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use <PERSON>tie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="OnboardingStepResponse",
 *     @OA\Property(property="current_step", type="string", example="account_info", description="The current step in the onboarding process"),
 *     @OA\Property(property="next_step", type="string", nullable=true, example="details", description="The next step in the onboarding process, or null if completed"),
 *     @OA\Property(property="prefill", type="object", nullable=true, description="Optional prefill data for the next step"),
 *     @OA\Property(property="redirect", type="string", nullable=true, example="/login", description="Optional redirect URL after onboarding is completed")
 * )
 */
class OnboardingStepResponse extends Data
{
    public function __construct(
        public readonly string $current_step,
        public readonly ?string $next_step = null,
        public readonly ?array $prefill = null,
        public readonly ?string $redirect = null,
    ) {}

    public static function fromState(string $currentStep, ?string $nextStep = null, ?array $prefill = null, ?string $redirect = null): self
    {
        return new self(
            current_step: $currentStep,
            next_step: $nextStep,
            prefill: $prefill,
            redirect: $redirect
        );
    }

    /**
     * Convert the data to a JSON response with optional status code
     *
     * @param Request $request
     * @param int $status
     * @return JsonResponse
     */
    public function toResponse($request, int $status = 200): JsonResponse
    {
        return response()->json($this->toArray(), $status);
    }
}
