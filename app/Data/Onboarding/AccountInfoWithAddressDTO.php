<?php

namespace App\Data\Onboarding;

use <PERSON><PERSON>\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;

/**
 * @OA\Schema(
 *     schema="AccountInfoWithAddressDTO",
 *     required={"email", "password", "first_name", "last_name", "phone", "street_address", "city", "state", "zip_code"},
 *     @OA\Property(property="token", type="string", example="**********"),
 *     @OA\Property(property="first_name", type="string", example="John"),
 *     @OA\Property(property="last_name", type="string", example="Doe"),
 *     @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
 *     @OA\Property(property="notification_email", type="string", format="email", example="<EMAIL>", nullable=true),
 *     @OA\Property(property="phone", type="string", example="************", maxLength=20),
 *     @OA\Property(property="street_address", type="string", example="123 Main St"),
 *     @OA\Property(property="unit", type="string", nullable=true, example="Apt 4B", maxLength=50),
 *     @OA\Property(property="city", type="string", example="Anytown"),
 *     @OA\Property(property="state", type="string", example="CA"),
 *     @OA\Property(property="zip_code", type="string", example="90210", maxLength=10),
 *     @OA\Property(property="password", type="string", format="password", example="securepassword123")
 * )
 */
class AccountInfoWithAddressDTO extends AccountInfoDTO
{
    public function __construct(
        string $first_name,
        string $last_name,
        string $email,
        ?string $notification_email,
        string $phone,
        string $password,

        #[Required, StringType, Max(255)]
        public readonly string $street_address,

        #[Required, StringType, Max(100)]
        public readonly string $city,

        #[Required, StringType]
        public readonly string $state,

        #[Required, StringType, Max(10)]
        public readonly string $zip_code,

        #[StringType, Max(50)]
        public readonly ?string $unit = null,
    ) {
        parent::__construct(
            first_name: $first_name,
            last_name: $last_name,
            email: $email,
            notification_email: $notification_email,
            phone: $phone,
            password: $password
        );
    }
}
