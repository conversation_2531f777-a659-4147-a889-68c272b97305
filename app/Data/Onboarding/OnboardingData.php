<?php

namespace App\Data\Onboarding;

use <PERSON><PERSON>\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Data;
use <PERSON>tie\LaravelData\DataCollection;

/**
 * @OA\Schema(
 *     schema="OnboardingData",
 *     title="Onboarding Data",
 *     description="Data structure for user onboarding information",
 *     @OA\Property(
 *         property="token",
 *         type="string",
 *         description="Invitation token for onboarding",
 *         example="abc123def456"
 *     ),
 *     @OA\Property(
 *         property="password",
 *         type="string",
 *         format="password",
 *         description="User's password (min 8 characters)",
 *         example="SecurePassword123"
 *     ),
 *     @OA\Property(
 *         property="password_confirmation",
 *         type="string",
 *         format="password",
 *         description="Password confirmation",
 *         example="SecurePassword123"
 *     ),
 *     @OA\Property(
 *         property="name",
 *         type="string",
 *         description="User's full name",
 *         example="<PERSON>"
 *     ),
 *     @OA\Property(
 *         property="phone",
 *         type="string",
 *         description="User's phone number",
 *         example="************"
 *     ),
 *     @OA\Property(
 *         property="school_id",
 *         type="integer",
 *         description="ID of the user's school",
 *         example=12345
 *     ),
 *     @OA\Property(
 *         property="graduation_year",
 *         type="integer",
 *         description="User's graduation year",
 *         example=2025
 *     ),
 *     @OA\Property(
 *         property="sports",
 *         type="array",
 *         description="List of sports the user participates in",
 *         @OA\Items(
 *             type="object",
 *             @OA\Property(property="type", type="integer", example=1, description="Sport ID")
 *         )
 *     ),
 *     @OA\Property(
 *         property="gpa",
 *         type="number",
 *         format="float",
 *         description="User's GPA",
 *         example=3.85
 *     ),
 *     @OA\Property(
 *         property="class_rank",
 *         type="string",
 *         description="User's class rank",
 *         example="Top 10%"
 *     ),
 *     @OA\Property(
 *         property="activities",
 *         type="array",
 *         nullable=true,
 *         description="List of user's extracurricular activities",
 *         @OA\Items(ref="#/components/schemas/ActivityData")
 *     ),
 *     @OA\Property(
 *         property="work_experience",
 *         type="array",
 *         nullable=true,
 *         description="List of user's work experiences",
 *         @OA\Items(ref="#/components/schemas/WorkExperienceData")
 *     )
 * )
 */
class OnboardingData extends Data
{
    public function __construct(
        public string $token,
        public string $password,
        public string $name,
        public string $phone,
        public int $school_id,
        public int $graduation_year,
        public array $sports,
        public float $gpa,
        public string $class_rank,
        #[DataCollectionOf(ActivityData::class)]
        public ?DataCollection $activities,
        #[DataCollectionOf(WorkExperienceData::class)]
        public ?DataCollection $work_experience,
    ) {}

    public static function rules(): array
    {
        return [
            'token' => 'required|string|exists:system_invites,token',
            'password' => 'required|min:8|confirmed',
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'school_id' => 'required|exists:schools,id',
            'graduation_year' => 'required|date_format:Y|after:now',
            'sports' => 'required|array|min:1',
            'sports.*.type' => 'required|exists:sports,id',
            'gpa' => 'required|numeric|between:0,5.0',
            'class_rank' => 'required|string',
        ];
    }
}
