<?php

namespace App\Data\Onboarding\PositiveCoach;

use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Data;
use OpenApi\Annotations as OA;
/**
 * @OA\Schema(
 *     schema="TeamSuccessItemDTO",
 *     required={"name",  "order"},
 *     @OA\Property(property="name", type="string", example="Student Council", description="Name of the involvement activity"),
 *     @OA\Property(property="date_range", type="string", example="2022-2024", description="Date range of involvement"),
 *     @OA\Property(property="description", type="string", example="Served as Student Council Secretary", description="Description of the involvement"),
 *     @OA\Property(property="order", type="integer", example=1, description="Display order of the involvement")
 * )

 * @OA\Schema(
 *   schema="TeamSuccessesDTO",
 *   required={"successes"},
 *   description="Data transfer object for coach team successes",
 *
 *       @OA\Property(property="token", type="string", example="1234567890"),
 *   @OA\Property(
 *     property="successes",
 *     type="array",
 *     @OA\Items(ref="#/components/schemas/TeamSuccessItemDTO")
 *     )
 *   ),
 *   @OA\Property(property="additional_info", type="string", nullable=true, example="Additional information about team successes")
 * )
 */
class TeamSuccessesDTO extends Data
{
    public function __construct(
        #[Required]
        public array $successes,

        #[Nullable, StringType]
        public ?string $additional_info = null,
    ) {}

    public static function rules(): array
    {
        return [
            'successes' => ['required', 'array'],
            'successes.*.name' => ['required', 'string'],
            'successes.*.date_range' => ['required', 'string'],
            'successes.*.description' => ['nullable', 'string'],
            'successes.*.order' => ['nullable', 'integer'],
            'additional_info' => ['nullable', 'string'],
        ];
    }
}
