<?php

namespace App\Data\Onboarding\PositiveCoach;

use Illuminate\Http\UploadedFile;
use Spatie\LaravelData\Attributes\Validation\Image;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Attributes\Validation\Url;
use Spatie\LaravelData\Data;
use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *   schema="PositiveCoachDetailsDTO",
 *   description="Data transfer object for coach details",
 *   @OA\Property(property="state", type="string", example="CA"),
 *   @OA\Property(property="county", type="string", example="Los Angeles"),
 *   @OA\Property(property="high_school", type="string", example="Central High School"),
 *   @OA\Property(property="school_id", type="integer", example=123),
 *   @OA\Property(property="twitter", type="string", example="https://twitter.com/username"),
 *   @OA\Property(property="instagram", type="string", example="https://instagram.com/username"),
 *   @OA\Property(property="facebook", type="string", example="https://facebook.com/username"),
 *   @OA\Property(property="hudl", type="string", example="https://hudl.com/username"),
 *   @OA\Property(property="custom_link", type="string", example="https://example.com"),
 *   @OA\Property(property="profile_photo", type="string", format="binary")
 * )
 */
class DetailsDTO extends Data
{
    public function __construct(
        #[Nullable, StringType]
        public ?string $state = null,

        #[Nullable, StringType]
        public ?string $county = null,

        #[Nullable, StringType]
        public ?string $high_school = null,

        #[Nullable]
        public ?int $school_id = null,

        #[Nullable, StringType, Url]
        public ?string $twitter = null,

        #[Nullable, StringType, Url]
        public ?string $instagram = null,

        #[Nullable, StringType, Url]
        public ?string $facebook = null,

        #[Nullable, StringType, Url]
        public ?string $hudl = null,

        #[Nullable, StringType, Url]
        public ?string $custom_link = null,

        #[Nullable, Image, Max(5120)]
        public ?UploadedFile $profile_photo = null,
    ) {}

    public static function rules(): array
    {
        return [
            'state' => ['nullable', 'string'],
            'county' => ['nullable', 'string'],
            'high_school' => ['nullable', 'string'],
            'school_id' => ['nullable', 'integer', 'exists:schools,id'],
            'twitter' => ['nullable', 'url'],
            'instagram' => ['nullable', 'url'],
            'facebook' => ['nullable', 'url'],
            'hudl' => ['nullable', 'url'],
            'custom_link' => ['nullable', 'url'],
            'profile_photo' => ['nullable', 'image', 'max:5120'], // 5MB max
        ];
    }
}
