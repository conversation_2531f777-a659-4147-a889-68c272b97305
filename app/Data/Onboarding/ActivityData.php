<?php

namespace App\Data\Onboarding;

use <PERSON><PERSON>\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="ActivityData",
 *     title="Activity Data",
 *     description="Data structure for extracurricular activities",
 *     @OA\Property(
 *         property="title",
 *         type="string",
 *         description="Title of the activity",
 *         example="Student Government Association"
 *     ),
 *     @OA\Property(
 *         property="date_range",
 *         type="string",
 *         description="Date range of participation",
 *         example="2022-2024"
 *     ),
 *     @OA\Property(
 *         property="description",
 *         type="string",
 *         nullable=true,
 *         description="Description of the activity and role",
 *         example="Served as class representative, organized school events"
 *     )
 * )
 */
class ActivityData extends Data
{
    public function __construct(
        public string $title,
        public string $date_range,
        public ?string $description,
    ) {}

    public static function rules(): array
    {
        return [
            'title' => 'required|string',
            'date_range' => 'required|string',
            'description' => 'nullable|string',
        ];
    }
}
