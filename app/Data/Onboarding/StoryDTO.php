<?php

namespace App\Data\Onboarding;

use <PERSON><PERSON>\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="StoryDTO",
 *     required={"content"},
 *     @OA\Property(
 *         property="token",
 *         type="string",
 *         example="1234567890"
 *     ),
 *     @OA\Property(
 *         property="content",
 *         type="string",
 *         description="The athlete's personal story and achievements",
 *         example="I've been playing basketball since I was 8 years old..."
 *     )
 * )
 */
class StoryDTO extends Data
{
    public function __construct(
        #[Required, StringType]
        public readonly string $content,
    ) {}
}
