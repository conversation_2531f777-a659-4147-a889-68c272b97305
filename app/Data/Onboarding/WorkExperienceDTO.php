<?php

namespace App\Data\Onboarding;

use <PERSON><PERSON>\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="WorkExperienceDTO",
 *     required={"items"},
 *   @OA\Property(
 *         property="token",
 *         type="string",
 *         example="1234567890"
 *     ),
 *     @OA\Property(
 *         property="items",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/WorkExperienceItemDTO")
 *     )
 * )
 */
class WorkExperienceDTO extends Data
{
    public function __construct(
        #[Required, DataCollectionOf(WorkExperienceItemDTO::class)]
        public readonly ?array $items = null,
    ) {}
}
