<?php

namespace App\Data\Onboarding\AthleticsDirector;

use Illuminate\Http\UploadedFile;
use Spatie\LaravelData\Attributes\Validation\Exists;
use Spatie\LaravelData\Attributes\Validation\Image;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\Numeric;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Data;
use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *     schema="AthleticsDirectorDetailsDTO",
 *     required={"title", "state", "county", "school_id"},
 *     @OA\Property(property="title", type="string", example="Athletic Director"),
 *     @OA\Property(property="state", type="string", example="CA"),
 *     @OA\Property(property="county", type="string", example="Los Angeles"),
 *     @OA\Property(property="school_id", type="integer", example=123),
 *     @OA\Property(property="twitter", type="string", nullable=true, example="username"),
 *     @OA\Property(property="instagram", type="string", nullable=true, example="username"),
 *     @OA\Property(property="facebook", type="string", nullable=true, example="username"),
 *     @OA\Property(property="hudl", type="string", nullable=true, example="username"),
 *     @OA\Property(property="custom_link", type="string", nullable=true, example="https://example.com"),
 *     @OA\Property(property="profile_photo", type="string", format="binary", nullable=true)
 * )
 */
class DetailsDTO extends Data
{
    public function __construct(
        #[Required, StringType]
        public readonly string $title,

        #[Required, StringType]
        public readonly string $state,

        #[Required, StringType]
        public readonly string $county,

        #[Required, Numeric, Exists('schools', 'id')]
        public readonly int $school_id,

        #[Nullable, StringType]
        public readonly ?string $twitter = null,

        #[Nullable, StringType]
        public readonly ?string $instagram = null,

        #[Nullable, StringType]
        public readonly ?string $facebook = null,

        #[Nullable, StringType]
        public readonly ?string $hudl = null,

        #[Nullable, StringType]
        public readonly ?string $custom_link = null,

        #[Nullable, Image]
        public readonly ?UploadedFile $profile_photo = null,
    ) {}

    public static function rules(): array
    {
        return [
            'title' => ['required', 'string', 'max:255'],
            'state' => ['required', 'string', 'max:255'],
            'county' => ['required', 'string', 'max:255'],
            'school_id' => ['required', 'integer', 'exists:schools,id'],
            'twitter' => ['nullable', 'string', 'max:255'],
            'instagram' => ['nullable', 'string', 'max:255'],
            'facebook' => ['nullable', 'string', 'max:255'],
            'hudl' => ['nullable', 'string', 'max:255'],
            'custom_link' => ['nullable', 'string', 'max:255'],
            'profile_photo' => ['nullable', 'image', 'max:5120'], // 5MB max
        ];
    }
}
