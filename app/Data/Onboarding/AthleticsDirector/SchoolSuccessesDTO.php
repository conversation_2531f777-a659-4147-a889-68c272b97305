<?php

namespace App\Data\Onboarding\AthleticsDirector;

use <PERSON><PERSON>\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Data;
use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *     schema="AthleticsDirectorSchoolSuccessesDTO",
 *     required={"successes"},
 *     @OA\Property(
 *         property="successes",
 *         type="array",
 *         @OA\Items(
 *             type="object",
 *             @OA\Property(property="name", type="string", example="State Champions"),
 *             @OA\Property(property="date_range", type="string", example="2023"),
 *             @OA\Property(property="description", type="string", example="Football team won the state championship"),
 *             @OA\Property(property="order", type="integer", example=1)
 *         )
 *     )
 * )
 */
class SchoolSuccessesDTO extends Data
{
    public function __construct(
        #[Required]
        public array $successes,
    ) {}

    public static function rules(): array
    {
        return [
            'successes' => ['required', 'array'],
            'successes.*.name' => ['required', 'string'],
            'successes.*.date_range' => ['required', 'string'],
            'successes.*.description' => ['required', 'string'],
            'successes.*.order' => ['integer'],
        ];
    }
}
