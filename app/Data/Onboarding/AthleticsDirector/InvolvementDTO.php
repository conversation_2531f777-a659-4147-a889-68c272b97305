<?php

namespace App\Data\Onboarding\AthleticsDirector;

use <PERSON><PERSON>\LaravelData\Attributes\Validation\Required;
use <PERSON>tie\LaravelData\Data;
use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *     schema="AthleticsDirectorInvolvementDTO",
 *     required={"items"},
 *     @OA\Property(
 *         property="items",
 *         type="array",
 *         @OA\Items(
 *             type="object",
 *             @OA\Property(property="name", type="string", example="Local Food Bank Volunteer"),
 *             @OA\Property(property="date_range", type="string", example="2022-2023"),
 *             @OA\Property(property="description", type="string", example="Weekly volunteer work at the community food bank"),
 *             @OA\Property(property="order", type="integer", example=1)
 *         )
 *     )
 * )
 */
class InvolvementDTO extends Data
{
    public function __construct(
        #[Required]
        public array $items,
    ) {}

    public static function rules(): array
    {
        return [
            'items' => ['required', 'array'],
            'items.*.name' => ['required', 'string'],
            'items.*.date_range' => ['required', 'string'],
            'items.*.description' => ['required', 'string'],
            'items.*.order' => ['integer'],
        ];
    }
}
