<?php

namespace App\Data\Onboarding\AthleticsDirector;

use <PERSON><PERSON>\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Data;
use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *     schema="AthleticsDirectorBioDTO",
 *     required={"content"},
 *     @OA\Property(property="content", type="string", example="I've been the Athletic Director at Lincoln High School for 5 years...")
 * )
 */
class BioDTO extends Data
{
    public function __construct(
        #[Required, StringType]
        public readonly string $content,
    ) {}

    public static function rules(): array
    {
        return [
            'content' => ['required', 'string'],
        ];
    }
}
