<?php

namespace App\Data\Onboarding;

use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Data;
use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *     schema="CommunityInvolvementDTO",
 *     required={"activities", "organizations"},
 *     @OA\Property(
 *         property="activities",
 *         type="array",
 *         @OA\Items(
 *             type="object",
 *             @OA\Property(property="name", type="string", example="Local Food Bank Volunteer"),
 *             @OA\Property(property="description", type="string", example="Weekly volunteer work at the community food bank"),
 *             @OA\Property(property="hours", type="integer", example=5)
 *         )
 *     ),
 *     @OA\Property(
 *         property="organizations",
 *         type="array",
 *         @OA\Items(
 *             type="object",
 *             @OA\Property(property="name", type="string", example="Youth Mentorship Program"),
 *             @OA\Property(property="role", type="string", example="Mentor")
 *         )
 *     ),
 *     @OA\Property(property="additional_info", type="string", nullable=true, example="I also participate in annual charity events...")
 * )
 */
class CommunityInvolvementDTO extends Data
{
    public function __construct(
        #[Required]
        public array $activities,

        #[Required]
        public array $organizations,

        #[Nullable, StringType]
        public ?string $additional_info = null,
    ) {}

    public static function rules(): array
    {
        return [
            'activities' => ['required', 'array'],
            'activities.*.name' => ['required', 'string'],
            'activities.*.description' => ['required', 'string'],
            'activities.*.hours' => ['required', 'integer'],
            'organizations' => ['required', 'array'],
            'organizations.*.name' => ['required', 'string'],
            'organizations.*.role' => ['required', 'string'],
            'additional_info' => ['nullable', 'string'],
        ];
    }
}
