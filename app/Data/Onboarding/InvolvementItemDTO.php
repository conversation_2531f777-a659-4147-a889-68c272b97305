<?php

namespace App\Data\Onboarding;

use <PERSON><PERSON>\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="InvolvementItemDTO",
 *     required={"name", "date_range", "description", "order"},
 *     @OA\Property(property="name", type="string", example="Student Council", description="Name of the involvement activity"),
 *     @OA\Property(property="date_range", type="string", example="2022-2024", description="Date range of involvement"),
 *     @OA\Property(property="description", type="string", example="Served as Student Council Secretary", description="Description of the involvement"),
 *     @OA\Property(property="order", type="integer", example=1, description="Display order of the involvement")
 * )
 */
class InvolvementItemDTO extends Data
{
    public function __construct(
        #[Required, StringType]
        public readonly string $name,

        #[Required, StringType]
        public readonly string $date_range,

        #[Required, StringType]
        public readonly string $description,

        #[Required]
        public readonly int $order,
    ) {}
}
