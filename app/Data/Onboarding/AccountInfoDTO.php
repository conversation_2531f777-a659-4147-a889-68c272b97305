<?php

namespace App\Data\Onboarding;

use <PERSON><PERSON>\LaravelData\Attributes\Validation\Email;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use <PERSON>tie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="AccountInfoDTO",
 *     required={"email", "password", "first_name", "last_name", "phone"},
 *     @OA\Property(property="token", type="string", example="**********"),
 *     @OA\Property(property="first_name", type="string", example="John"),
 *     @OA\Property(property="last_name", type="string", example="Doe"),
 *     @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
 *     @OA\Property(property="notification_email", type="string", format="email", example="<EMAIL>", nullable=true),
 *     @OA\Property(property="phone", type="string", example="************", maxLength=20),
 *     @OA\Property(property="password", type="string", format="password", example="securepassword123")
 * )
 */
class AccountInfoDTO extends Data
{
    public function __construct(
        #[Required, StringType, Max(255)]
        public readonly string $first_name,

        #[Required, StringType, Max(255)]
        public readonly string $last_name,

        #[Required, Email, Max(255)]
        public readonly string $email,

        #[Email, Max(255)]
        public readonly ?string $notification_email = null,

        #[Required, StringType, Max(20)]
        public readonly string $phone,

        #[Required, StringType, Max(255)]
        public readonly string $password,
    ) {}
}
