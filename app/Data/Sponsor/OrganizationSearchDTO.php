<?php

namespace App\Data\Sponsor;

use <PERSON><PERSON>\LaravelData\Data;
use <PERSON><PERSON>\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;

/**
 * @OA\Schema(
 *     schema="OrganizationSearchDTO",
 *     title="Organization Search DTO",
 *     description="Data for searching organizations",
 *     @OA\Property(property="query", type="string", example="Home Depot", description="Search query for organization name"),
 * )
 */
class OrganizationSearchDTO extends Data
{
    public function __construct(
        #[Required, StringType]
        public string $query,
    ) {
    }
}
