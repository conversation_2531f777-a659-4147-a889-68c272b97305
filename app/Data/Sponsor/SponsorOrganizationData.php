<?php

namespace App\Data\Sponsor;

use App\Models\Organization;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Lazy;

/**
 * @OA\Schema(
 *     schema="SponsorOrganizationData",
 *     title="Sponsor Organization Data",
 *     description="Data structure for organization details related to a sponsor",
 *     @OA\Property(property="id", type="integer", example=1),
 *     @OA\Property(property="name", type="string", example="The Home Depot"),
 *     @OA\Property(property="type", type="string", example="Corporate", nullable=true),
 *     @OA\Property(property="website", type="string", format="url", example="https://homedepot.com", nullable=true),
 *     @OA\Property(property="about", type="string", example="The Home Depot is a home improvement retailer.", nullable=true),
 *     @OA\Property(property="logo_url", type="string", format="url", example="https://example.com/logos/homedepot.png", nullable=true),
 *     @OA\Property(property="active_sponsors_count", type="integer", example=3, description="Number of active sponsors from this organization"),
 *     @OA\Property(property="sponsor_role", type="string", example="admin", description="The role of the current sponsor within this organization"),
 * )
 */
class SponsorOrganizationData extends Data
{
    public function __construct(
        public int $id,
        public string $name,
        public ?string $type,
        public ?string $website,
        public ?string $about,
        public ?string $logo_url,
        public Lazy|int $active_sponsors_count = 0,
        public ?string $sponsor_role = null,
    ) {
    }

    /**
     * Create the DTO from an Organization model and the current user's role
     */
    public static function fromOrganization(Organization $organization, ?string $currentUserRole = null): self
    {
        return new self(
            id: $organization->id,
            name: $organization->name,
            type: $organization->type,
            website: $organization->website,
            about: $organization->about,
            logo_url: $organization->getFirstMediaUrl('logo'),
            active_sponsors_count: Lazy::create(fn () => $organization->activeSponsors()->count()),
            sponsor_role: $currentUserRole,
        );
    }

    /**
     * Create a collection of DTOs from a collection of organizations
     */
    public static function collection(Collection $organizations, ?string $currentUserRole = null): Collection
    {
        return $organizations->map(fn (Organization $organization) => self::fromOrganization($organization, $currentUserRole));
    }
}
