<?php

namespace App\Data\Sponsor;

use App\Models\Organization;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Lazy;

/**
 * @OA\Schema(
 *     schema="OrganizationSearchResultDTO",
 *     title="Organization Search Result DTO",
 *     description="Data for organization search results",
 *     @OA\Property(property="id", type="integer", example=1),
 *     @OA\Property(property="name", type="string", example="The Home Depot"),
 *     @OA\Property(property="logo_url", type="string", format="url", example="https://example.com/logos/homedepot.png", nullable=true),
 *     @OA\Property(property="website", type="string", format="url", example="https://homedepot.com", nullable=true),
 *     @OA\Property(property="active_sponsors_count", type="integer", example=3, description="Number of active sponsors from this organization"),
 * )
 */
class OrganizationSearchResultDTO extends Data
{
    public function __construct(
        public int $id,
        public string $name,
        public ?string $logo_url = null,
        public ?string $website = null,
        public Lazy|int $active_sponsors_count = 0,
    ) {
    }

    public static function fromOrganization(Organization $organization): self
    {
        return new self(
            id: $organization->id,
            name: $organization->name,
            logo_url: $organization->getFirstMediaUrl('logo'),
            website: $organization->website ?? null,
            active_sponsors_count: Lazy::create(fn () => $organization->activeSponsors()->count()),
        );
    }
}
