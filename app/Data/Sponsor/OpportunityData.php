<?php

namespace App\Data\Sponsor;

use App\Models\Opportunity;
use Carbon\Carbon;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="OpportunityData",
 *     title="Opportunity Data",
 *     description="Opportunity data representation",
 *     @OA\Property(property="id", type="integer", description="The unique identifier of the opportunity"),
 *     @OA\Property(property="title", type="string", description="The title of the opportunity"),
 *     @OA\Property(property="subtitle", type="string", description="The subtitle of the opportunity"),
 *     @OA\Property(property="description", type="string", description="The description of the opportunity"),
 *     @OA\Property(property="details", type="string", description="The details of the opportunity"),
 *     @OA\Property(property="organizationId", type="integer", description="The ID of the organization associated with the opportunity"),
 *     @OA\Property(property="organizationLogo", type="string", description="The logo of the organization associated with the opportunity"),
 *     @OA\Property(property="organizationName", type="string", description="The name of the organization associated with the opportunity"),
 *     @OA\Property(property="organizationWebsite", type="string", description="The website of the organization associated with the opportunity"),
 *     @OA\Property(property="organizationAbout", type="string", description="The about of the organization associated with the opportunity"),
 *     @OA\Property(property="term", type="string", description="The term of the opportunity"),
 *     @OA\Property(property="type", type="string", description="The type of the opportunity"),
 *     @OA\Property(property="subtype", type="string", description="The subtype of the opportunity"),
 *     @OA\Property(property="status", type="string", description="The status of the opportunity"),
 *     @OA\Property(property="industries", type="array", description="The industries associated with the opportunity", @OA\Items(type="object", ref="#/components/schemas/IndustryData")),
 *     @OA\Property(property="interests", type="array", description="The interests associated with the opportunity", @OA\Items(type="object", ref="#/components/schemas/InterestData")),
 *     @OA\Property(property="isFeatured", type="boolean", description="Whether the opportunity is featured"),
 *     @OA\Property(property="city", type="string", description="The city of the opportunity"),
 *     @OA\Property(property="stateCode", type="string", description="The state code of the opportunity"),
 *     @OA\Property(property="locationType", type="string", description="The location type of the opportunity"),
 *     @OA\Property(property="location_coordinate_id", type="integer", description="The ID of the location coordinate associated with the opportunity"),
 *     @OA\Property(property="location", type="string", description="The location of the opportunity"),
 *     @OA\Property(property="location_display", type="string", description="The display location of the opportunity"),
 *     @OA\Property(property="applyUrl", type="string", description="The URL to apply for the opportunity"),
 *     @OA\Property(property="qualifications", type="string", description="The qualifications for the opportunity"),
 *     @OA\Property(property="responsibilities", type="string", description="The responsibilities for the opportunity"),
 *     @OA\Property(property="benefits", type="string", description="The benefits for the opportunity"),
 *     @OA\Property(property="visibleStartDate", type="string", description="The start date of the opportunity"),
 *     @OA\Property(property="visibleEndDate", type="string", description="The end date of the opportunity"),
 *     @OA\Property(property="preferredGraduationYearStart", type="integer", description="The preferred graduation year start of the opportunity"),
 *     @OA\Property(property="preferredGraduationYearEnd", type="integer", description="The preferred graduation year end of the opportunity"),
 *     @OA\Property(property="preferredStates", type="array", description="The preferred states of the opportunity", @OA\Items(type="string")),
 *     @OA\Property(property="createdAt", type="string", description="The creation date of the opportunity"),
 *     @OA\Property(property="updatedAt", type="string", description="The last update date of the opportunity"),
 *     @OA\Property(property="userId", type="integer", description="The user ID of the opportunity"),
 * )
 */
class OpportunityData extends Data
{
    public function __construct(
        public readonly int $id,
        public readonly string $title,
        public readonly ?string $subtitle,
        public readonly string $description,
        public readonly ?string $details,
        public readonly int $organizationId,
        public readonly ?string $organizationLogo,
        public readonly ?string $organizationName,
        public readonly ?string $organizationWebsite,
        public readonly ?string $organizationAbout,
        public readonly string $term,
        public readonly ?string $type,
        public readonly ?string $subtype,
        public readonly string $status,
        public readonly array $industries,
        public readonly array $interests,
        public readonly bool $isFeatured,
        public readonly ?string $city,
        public readonly ?string $stateCode,
        public readonly ?string $locationType,
        public readonly ?string $location_coordinate_id,
        public readonly ?string $location,
        public readonly ?string $location_display,
        public readonly ?string $applyUrl,
        public readonly ?string $qualifications,
        public readonly ?string $responsibilities,
        public readonly ?string $benefits,
        public readonly ?Carbon $visibleStartDate,
        public readonly ?Carbon $visibleEndDate,
        public readonly ?int $preferredGraduationYearStart,
        public readonly ?int $preferredGraduationYearEnd,
        public readonly ?array $preferredStates,
        public readonly Carbon $createdAt,
        public readonly Carbon $updatedAt,
        public readonly ?int $userId,
    ) {}

    public static function fromModel(Opportunity $opportunity): self
    {
        $locationDisplay = null;
        if ($opportunity->city && $opportunity->state_code) {
            $locationDisplay = "{$opportunity->city}, {$opportunity->state_code}";
        }

        return new self(
            id: $opportunity->id,
            title: $opportunity->title,
            subtitle: $opportunity->subtype?->value,
            description: $opportunity->description,
            details: $opportunity->details,
            organizationId: $opportunity->organization_id,
            organizationLogo: $opportunity->organization?->getFirstMediaUrl('logo', 'thumbnail'),
            organizationName: $opportunity->organization?->name,
            organizationWebsite: $opportunity->organization?->website,
            organizationAbout: $opportunity->organization?->about,
            term: $opportunity->term?->value ?? '',
            type: $opportunity->type?->value,
            subtype: $opportunity->subtype?->value,
            status: $opportunity->status?->value ?? '',
            industries: $opportunity->industries->map(fn ($industry) => [
                'id' => $industry->id,
                'name' => $industry->name,
            ])->toArray(),
            interests: $opportunity->interests->map(fn ($interest) => [
                'id' => $interest->id,
                'name' => $interest->name,
                'icon' => $interest->icon,
            ])->toArray(),
            isFeatured: $opportunity->is_featured,
            city: $opportunity->city,
            stateCode: $opportunity->state_code,
            locationType: $opportunity->location_type?->value,
            location_coordinate_id: $opportunity->location_coordinate_id,
            location: $opportunity->location_coordinate_id,
            location_display: $locationDisplay,
            applyUrl: $opportunity->apply_url,
            qualifications: $opportunity->qualifications,
            responsibilities: $opportunity->responsibilities,
            benefits: $opportunity->benefits,
            visibleStartDate: $opportunity->visible_start_date,
            visibleEndDate: $opportunity->visible_end_date,
            preferredGraduationYearStart: $opportunity->preferred_graduation_year_start,
            preferredGraduationYearEnd: $opportunity->preferred_graduation_year_end,
            preferredStates: $opportunity->preferred_states,
            createdAt: $opportunity->created_at,
            updatedAt: $opportunity->updated_at,
            userId: $opportunity->user_id,
        );
    }
}
