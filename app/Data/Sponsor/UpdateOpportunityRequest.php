<?php

namespace App\Data\Sponsor;

use App\Enums\OpportunityLocationType;
use App\Enums\OpportunityStatus;
use App\Enums\OpportunitySubtype;
use App\Enums\OpportunityTerm;
use App\Enums\OpportunityType;
use App\Traits\SanitizesHtml;
use <PERSON><PERSON>\LaravelData\Data;

class UpdateOpportunityRequest extends Data
{
    use SanitizesHtml;

    public function __construct(
        public string $title,
        public string $description,
        public ?string $details,
        public ?string $term = null,
        public string $type,
        public ?string $subtype,
        public array $industryIds,
        public ?array $interestIds = [],
        public ?string $city,
        public ?string $stateCode,
        public ?string $locationType,
        public ?string $location_coordinate_id,
        public ?string $location,
        public ?string $applyUrl,
        public ?string $qualifications,
        public ?string $responsibilities,
        public ?string $benefits,
        public ?string $visibleStartDate,
        public ?string $visibleEndDate,
        public ?int $preferredGraduationYearStart = null,
        public ?int $preferredGraduationYearEnd = null,
        public ?array $preferredStates = null,
        public bool $isFeatured = false,
        public string $status,
    ) {
        if (!empty($location) && empty($location_coordinate_id)) {
            $this->location_coordinate_id = $location;
        }

        // Define the rich text fields that need sanitization
        $richTextFields = [
            'description',
            'details',
            'qualifications',
            'responsibilities',
            'benefits'
        ];

        // Sanitize the rich text fields
        $this->sanitizeFields($richTextFields);
    }

    public static function rules(): array
    {
        return [
            'title' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string'],
            'details' => ['nullable', 'string'],
            'term' => [
                function ($attribute, $value, $fail) {
                    // Get the opportunity type from the request
                    $type = request()->input('type');

                    // If type is education, term is required
                    if ($type === OpportunityType::Education->value) {
                        if (empty($value)) {
                            $fail('The term field is required when type is education.');
                            return;
                        }

                        // Check if term is a valid value
                        $validTerms = array_map(fn($case) => $case->value, OpportunityTerm::cases());
                        if (!in_array($value, $validTerms)) {
                            $fail('The term must be one of: ' . implode(', ', $validTerms));
                        }
                    }
                    // For other types, term is optional but must be valid if provided
                    elseif (!empty($value)) {
                        $validTerms = array_map(fn($case) => $case->value, OpportunityTerm::cases());
                        if (!in_array($value, $validTerms)) {
                            $fail('The term must be one of: ' . implode(', ', $validTerms));
                        }
                    }
                }
            ],
            'type' => ['required', 'string', 'in:' . implode(',', array_map(fn($case) => $case->value, OpportunityType::cases()))],
            'subtype' => ['nullable', 'string', 'in:' . implode(',', array_map(fn($case) => $case->value, OpportunitySubtype::cases()))],
            'industryIds' => ['nullable', 'array'],
            'industryIds.*' => ['integer', 'exists:industries,id'],
            'interestIds' => ['required', 'array'],
            'interestIds.*' => ['required', 'integer', 'exists:interests,id'],
            'city' => ['nullable', 'string', 'max:255'],
            'stateCode' => ['nullable', 'string', 'max:255', 'exists:states,code'],
            'locationType' => ['nullable', 'string', 'in:' . implode(',', array_map(fn($case) => $case->value, OpportunityLocationType::cases()))],
            'location_coordinate_id' => ['nullable', 'uuid', 'exists:location_coordinates,id'],
            'location' => ['nullable', 'uuid', 'exists:location_coordinates,id'],
            'applyUrl' => ['nullable', 'string', 'max:255', function ($attribute, $value, $fail) {
                // If the value is empty, it's valid (because it's nullable)
                if (empty($value)) {
                    return;
                }

                // Check if it's a valid email
                if (filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    return;
                }

                // Check if it's a valid URL
                if (filter_var($value, FILTER_VALIDATE_URL)) {
                    return;
                }

                // If we get here, it's neither a valid email nor URL
                $fail('The :attribute must be a valid email address or URL.');
            }],
            'qualifications' => ['nullable', 'string'],
            'responsibilities' => ['nullable', 'string'],
            'benefits' => ['nullable', 'string'],
            'visibleStartDate' => ['nullable', 'date'],
            'visibleEndDate' => ['nullable', 'date', 'after_or_equal:visibleStartDate'],
            'status' => ['required', 'string', 'in:' . implode(',', array_map(fn($case) => $case->value, OpportunityStatus::cases()))],
            'isFeatured' => ['boolean'],
            'preferredGraduationYearStart' => ['nullable', 'integer'],
            'preferredGraduationYearEnd' => [
                'nullable',
                'integer',
                function ($attribute, $value, $fail) {
                    $start = request()->input('preferredGraduationYearStart');
                    if (!is_null($value) && !is_null($start) && $value < $start) {
                        $fail('The graduation year end must be greater than or equal to the graduation year start.');
                    }
                }
            ],
            'preferredStates' => ['nullable', 'array'],
            'preferredStates.*' => ['string', 'size:2', 'exists:states,code'],
        ];
    }
}
