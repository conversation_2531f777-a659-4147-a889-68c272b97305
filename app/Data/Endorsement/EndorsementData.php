<?php

namespace App\Data\Endorsement;

use App\Models\Endorsement;
use OpenApi\Attributes as OA;
use <PERSON>tie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="EndorsementData",
 *     description="Data transfer object for an Endorsement category",
 *     required={"id", "name"},
 *     type="object",
 *     @OA\Property(property="id", type="integer", example=1, description="Endorsement ID"),
 *     @OA\Property(property="name", type="string", example="Leadership", description="Endorsement name"),
 *     @OA\Property(property="icon", type="string", nullable=true, example="sun-bright", description="The icon identifier for the endorsement"),
 *     @OA\Property(property="description", type="string", nullable=true, example="Demonstrates exceptional leadership qualities", description="Endorsement description")
 * )
 */
class EndorsementData extends Data
{
    public function __construct(
        public int $id,

        public string $name,

        public ?string $icon = null,

        public ?string $description = null,
    ) {
    }

    /**
     * Create an EndorsementData instance from an Endorsement model
     */
    public static function fromEndorsement(Endorsement $endorsement): self
    {
        return new self(
            id: $endorsement->id,
            name: $endorsement->name,
            icon: $endorsement->icon,
            description: $endorsement->description,
        );
    }
}
