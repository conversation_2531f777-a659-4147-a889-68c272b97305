<?php

namespace App\Data\Endorsement;

use OpenApi\Attributes as OA;
use <PERSON><PERSON>\LaravelData\Attributes\Validation\Exists;
use Spa<PERSON>\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use <PERSON><PERSON>\LaravelData\Data;

#[OA\Schema(
    schema: 'CreateEndorsementData',
    description: 'Data for creating an endorsement',
    required: ['endorsement_id', 'endorser_id', 'relation'],
    type: 'object',
)]
class CreateEndorsementData extends Data
{
    public function __construct(
        #[OA\Property(
            property: 'endorsement_id',
            description: 'ID of the endorsement category',
            type: 'integer',
            example: 1,
        )]
        #[Required]
        #[Exists('endorsements', 'id')]
        public int $endorsement_id,

        #[OA\Property(
            property: 'endorser_id',
            description: 'ID of the user giving the endorsement',
            type: 'integer',
            example: 2,
        )]
        #[Required]
        #[Exists('users', 'id')]
        public int $endorser_id,

        #[OA\Property(
            property: 'relation',
            description: 'Relationship between the endorser and the endorsed user',
            type: 'string',
            example: 'Coach',
        )]
        #[Required]
        #[StringType]
        public string $relation,
    ) {
    }
}
