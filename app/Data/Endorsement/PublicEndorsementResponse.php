<?php

namespace App\Data\Endorsement;

use OpenApi\Attributes as OA;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="PublicEndorsementResponse",
 *     description="Response after submitting endorsements",
 *     @OA\Property(property="success", type="boolean", example=true, description="Whether the endorsement was successfully submitted"),
 *     @OA\Property(property="message", type="string", example="Endorsements submitted successfully", description="Success or error message"),
 *     @OA\Property(property="endorsementCount", type="integer", example=3, description="Number of endorsements successfully submitted")
 * )
 */
class PublicEndorsementResponse extends Data
{
    public function __construct(
        public bool $success,
        public string $message,
        public int $endorsementCount = 0,
    ) {
    }

    public static function success(int $count): self
    {
        return new self(
            success: true,
            message: 'Endorsements submitted successfully',
            endorsementCount: $count
        );
    }

    public static function error(string $message): self
    {
        return new self(
            success: false,
            message: $message
        );
    }
}
