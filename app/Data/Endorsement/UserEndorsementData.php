<?php

namespace App\Data\Endorsement;

use App\Models\User;
use OpenApi\Attributes as OA;
use <PERSON>tie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="UserEndorsementData",
 *     description="Details of a single user endorsement, including the endorser's information.",
 *     required={"endorserName", "endorserRole"},
 *     @OA\Property(property="endorserName", type="string", example="Adam Graber", description="Name of the user who gave the endorsement"),
 *     @OA\Property(property="endorserRole", type="string", example="Peer", description="Role or relationship of the endorser to the endorsed user (e.g., <PERSON>, <PERSON>eer, Teacher)"),
 *     @OA\Property(property="endorserId", type="integer", nullable=true, example=1, description="ID of the endorser user")
 * )
 */
class UserEndorsementData extends Data
{
    public function __construct(
        public string $endorserName,

        public string $endorserRole,

        public ?int $endorserId = null,
    ) {
    }

    /**
     * Create a UserEndorsementData object from a pivot record
     */
    public static function fromPivot(object $pivot): self
    {
        // Get the endorser user
        $endorser = User::find($pivot->endorser_id);

        if (!$endorser) {
            return new self(
                endorserName: 'Unknown User',
                endorserRole: $pivot->relation ?? 'Unknown',
                endorserId: $pivot->endorser_id,
            );
        }

        return new self(
            endorserName: $endorser->first_name . ' ' . $endorser->last_name,
            endorserRole: $pivot->relation ?? 'Unknown',
            endorserId: $endorser->id,
        );
    }
}
