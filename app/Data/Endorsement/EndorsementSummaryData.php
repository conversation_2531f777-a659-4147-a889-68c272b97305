<?php

namespace App\Data\Endorsement;

use OpenApi\Attributes as OA;
use <PERSON>tie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;

/**
 * @OA\Schema(
 *     schema="EndorsementSummaryData",
 *     description="Summary of endorsements for a specific category, including count and individual endorser details.",
 *     required={"id", "name", "count", "endorsers"},
 *     @OA\Property(property="id", type="integer", example=1, description="Unique identifier for the endorsement category"),
 *     @OA\Property(property="name", type="string", example="Optimistic", description="Name of the endorsement category"),
 *     @OA\Property(property="icon", type="string", nullable=true, example="sun-bright", description="The icon identifier for the endorsement"),
 *     @OA\Property(property="count", type="integer", example=3, description="Number of endorsements received in this category"),
 *     @OA\Property(
 *         property="endorsers",
 *         description="List of individual endorsers for this category",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/UserEndorsementData")
 *     )
 * )
 */
class EndorsementSummaryData extends Data
{
    public function __construct(
        public int $id,

        public string $name,

        public ?string $icon = null,

        public int $count,

        #[DataCollectionOf(UserEndorsementData::class)]
        public DataCollection $endorsers,
    ) {
    }
}
