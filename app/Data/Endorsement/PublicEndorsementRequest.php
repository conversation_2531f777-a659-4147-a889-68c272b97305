<?php

namespace App\Data\Endorsement;

use Illuminate\Support\Collection;
use OpenApi\Attributes as OA;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\IntegerType;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\ArrayType;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="PublicEndorsementRequest",
 *     description="Data required for submitting a public endorsement",
 *     required={"userId", "relation", "endorsements"},
 *     @OA\Property(property="userId", type="integer", example=12345, description="ID of the user being endorsed"),
 *     @OA\Property(property="relation", type="string", example="Coach", description="Relationship of the endorser to the user (e.g., <PERSON>, Teacher, Peer)"),
 *     @OA\Property(property="endorsements", type="array", description="List of endorsement IDs to assign",
 *         @OA\Items(type="integer", example=1)
 *     ),
 *     @OA\Property(property="endorserName", type="string", nullable=true, example="John <PERSON>", description="Optional name of the endorser when not authenticated"),
 *     @OA\Property(property="endorserEmail", type="string", nullable=true, example="<EMAIL>", description="Optional email of the endorser when not authenticated")
 * )
 */
class PublicEndorsementRequest extends Data
{
    public function __construct(
        #[Required, IntegerType]
        #[MapInputName('userId')]
        public int $userId,

        #[Required, Max(255)]
        public string $relation,

        #[Required, ArrayType]
        public array $endorsements,

        #[Max(255)]
        public ?string $endorserName = null,

        #[Max(255)]
        public ?string $endorserEmail = null,
    ) {
    }
}
