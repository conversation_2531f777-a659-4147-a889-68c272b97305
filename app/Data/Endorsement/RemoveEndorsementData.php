<?php

namespace App\Data\Endorsement;

use OpenApi\Attributes as OA;
use <PERSON><PERSON>\LaravelData\Attributes\Validation\Exists;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Data;

#[OA\Schema(
    schema: 'RemoveEndorsementData',
    description: 'Data for removing an endorsement',
    required: ['endorsement_id', 'endorser_id'],
    type: 'object',
)]
class RemoveEndorsementData extends Data
{
    public function __construct(
        #[OA\Property(
            property: 'endorsement_id',
            description: 'ID of the endorsement category',
            type: 'integer',
            example: 1,
        )]
        #[Required]
        #[Exists('endorsements', 'id')]
        public int $endorsement_id,

        #[OA\Property(
            property: 'endorser_id',
            description: 'ID of the user who gave the endorsement',
            type: 'integer',
            example: 2,
        )]
        #[Required]
        #[Exists('users', 'id')]
        public int $endorser_id,
    ) {
    }
}
