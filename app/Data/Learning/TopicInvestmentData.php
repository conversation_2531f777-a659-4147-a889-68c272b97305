<?php

namespace App\Data\Learning;

use OpenApi\Attributes as OA;
use <PERSON><PERSON>\LaravelData\Data;

#[OA\Schema(
    schema: 'TopicInvestmentData',
    description: 'Learning investment in a specific topic',
    required: ['name', 'percentage', 'minutesSpent', 'modulesCompleted']
)]
class TopicInvestmentData extends Data
{
    public function __construct(
        #[OA\Property(
            description: 'Name of the topic',
            example: 'Leadership'
        )]
        public string $name,

        #[OA\Property(
            description: 'Percentage of total learning time spent on this topic',
            example: 60,
            minimum: 0,
            maximum: 100
        )]
        public int $percentage,

        #[OA\Property(
            description: 'Total minutes spent on this topic',
            example: 360,
            minimum: 0
        )]
        public int $minutesSpent,

        #[OA\Property(
            description: 'Number of modules completed in this topic',
            example: 3,
            minimum: 0
        )]
        public int $modulesCompleted,
    ) {}
}
