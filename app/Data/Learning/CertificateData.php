<?php

namespace App\Data\Learning;

use App\Models\Course;
use App\Models\Module;
use App\Models\TestAttempt;
use Carbon\Carbon;
use OpenApi\Attributes as OA;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="CertificateData",
 *     description="Certificate earned by a user for completing a course",
 *     required={"id", "name", "earnedAt", "courseId", "courseName"},
 *     @OA\Property(property="id", type="integer", description="Course ID that this certificate represents", example=123),
 *     @OA\Property(property="name", type="string", description="Name of the certificate", example="Leadership Fundamentals"),
 *     @OA\Property(property="description", type="string", nullable=true, description="Description of the certificate", example="Awarded for completing the Leadership Fundamentals course"),
 *     @OA\Property(property="imageUrl", type="string", nullable=true, description="URL to the certificate image", example="https://example.com/media/certificates/leadership.png"),
 *     @OA\Property(property="earnedAt", type="string", format="date-time", description="Date when the certificate was earned", example="2023-05-15T14:30:00Z"),
 *     @OA\Property(property="courseId", type="integer", description="ID of the course this certificate is for (same as id)", example=123),
 *     @OA\Property(property="courseName", type="string", description="Name of the course this certificate is for", example="Leadership Fundamentals")
 * )
 */
class CertificateData extends Data
{
    public function __construct(
        public int $id,
        public string $name,
        public ?string $description = null,
        public ?string $imageUrl = null,
        public string $earnedAt,
        public int $courseId,
        public string $courseName,
    ) {}

    /**
     * Create a CertificateData object from a completed course
     */
    public static function fromCompletedCourse(Course $course): self
    {
        return new self(
            id: $course->id,
            name: $course->title . ' Certificate',
            description: 'Certificate of completion for ' . $course->title,
            imageUrl: $course->getFirstMediaUrl('cover') ?: null,
            earnedAt: $course->pivot->completed_at,
            courseId: $course->id,
            courseName: $course->title,
        );
    }
}
