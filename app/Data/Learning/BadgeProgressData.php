<?php

namespace App\Data\Learning;

use App\Models\Badge;
use OpenApi\Attributes as OA;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="BadgeProgressData",
 *     description="Badge progress information",
 *     required={"id", "name", "moduleRequirement", "isAchieved"},
 *     @OA\Property(property="id", type="integer", example=1, description="Badge ID"),
 *     @OA\Property(property="name", type="string", example="Rookie", description="Badge name"),
 *     @OA\Property(property="moduleRequirement", type="integer", example=5, minimum=0, description="Number of modules required to achieve this badge"),
 *     @OA\Property(property="isAchieved", type="boolean", example=false, description="Whether the badge has been achieved"),
 *     @OA\Property(property="achievedAssetUrl", type="string", nullable=true, example="https://example.com/badges/rookie-achieved.png", description="URL to the achieved badge asset"),
 *     @OA\Property(property="unachievedAssetUrl", type="string", nullable=true, example="https://example.com/badges/rookie-unachieved.png", description="URL to the unachieved badge asset"),
 *     @OA\Property(property="currentProgress", type="integer", nullable=true, example=3, minimum=0, description="Current progress towards achieving the badge"),
 *     @OA\Property(property="progressPercentage", type="integer", nullable=true, example=60, minimum=0, maximum=100, description="Progress percentage towards achieving the badge")
 * )
 */
class BadgeProgressData extends Data
{
    public function __construct(
        public int $id,
        public string $name,
        public int $moduleRequirement,
        public bool $isAchieved,
        public ?string $achievedAssetUrl = null,
        public ?string $unachievedAssetUrl = null,
        public ?int $currentProgress = null,
        public ?int $progressPercentage = null,
    ) {}

    /**
     * Create a BadgeProgressData object from a Badge model
     */
    public static function fromBadge(Badge $badge, bool $isAchieved, int $completedModulesCount): self
    {
        $currentProgress = min($completedModulesCount, $badge->module_requirement);
        $progressPercentage = $badge->module_requirement > 0
            ? min(100, intval(($currentProgress / $badge->module_requirement) * 100))
            : 0;

        return new self(
            id: $badge->id,
            name: $badge->name,
            moduleRequirement: $badge->module_requirement,
            isAchieved: $isAchieved,
            achievedAssetUrl: $badge->achieved_asset_url,
            unachievedAssetUrl: $badge->unachieved_asset_url,
            currentProgress: $currentProgress,
            progressPercentage: $progressPercentage,
        );
    }
}
