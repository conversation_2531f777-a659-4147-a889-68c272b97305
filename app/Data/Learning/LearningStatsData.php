<?php

namespace App\Data\Learning;

use OpenApi\Attributes as OA;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="LearningStatsData",
 *     title="Learning Stats Data",
 *     description="Learning statistics for a user",
 *     required={"averageModuleScore", "hoursSpent", "modulesCompleted"},
 *     @OA\Property(property="averageModuleScore", type="number", format="float", example=85.5, minimum=0, maximum=100, description="Average score across all completed modules"),
 *     @OA\Property(property="hoursSpent", type="integer", example=12, minimum=0, description="Total hours spent learning"),
 *     @OA\Property(property="modulesCompleted", type="integer", example=5, minimum=0, description="Number of modules completed"),
 *     @OA\Property(property="coursesCompleted", type="integer", example=2, minimum=0, description="Number of courses completed"),
 *     @OA\Property(property="stateLeaderboardRank", type="integer", nullable=true, example=10, description="User's rank in state leaderboard"),
 *     @OA\Property(property="stateName", type="string", nullable=true, example="Georgia", description="Name of the user's state"),
 *     @OA\Property(property="nationalLeaderboardRank", type="integer", nullable=true, example=100, description="User's rank in national leaderboard"),
 *     @OA\Property(property="academicYear", type="string", nullable=true, example="2023-24", description="Current academic year")
 * )
 */
class LearningStatsData extends Data
{
    public function __construct(
        public float $averageModuleScore,

        public int $hoursSpent,

        public int $modulesCompleted,

        public int $coursesCompleted = 0,

        public ?int $stateLeaderboardRank = null,

        public ?string $stateName = null,

        public ?int $nationalLeaderboardRank = null,

        public ?string $academicYear = null,
    ) {}
}
