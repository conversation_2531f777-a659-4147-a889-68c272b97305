<?php

namespace App\Data\Learning;

use OpenApi\Attributes as OA;
use <PERSON><PERSON>\LaravelData\Data;
use Spatie\LaravelData\DataCollection;
use Spatie\LaravelData\Attributes\DataCollectionOf;

#[OA\Schema(
    schema: 'LearningInvestmentData',
    description: 'Learning investment distribution across topics',
    required: ['topics']
)]
class LearningInvestmentData extends Data
{
    public function __construct(
        #[OA\Property(
            description: 'Collection of topic investments',
            type: 'array',
            items: new OA\Items(ref: '#/components/schemas/TopicInvestmentData')
        )]
        #[DataCollectionOf(TopicInvestmentData::class)]
        public DataCollection $topics,

        #[OA\Property(
            description: 'Name of the primary topic (highest percentage)',
            example: 'Leadership',
            nullable: true
        )]
        public ?string $primaryTopic = null,

        #[OA\Property(
            description: 'Percentage of the primary topic',
            example: 60,
            minimum: 0,
            maximum: 100,
            nullable: true
        )]
        public ?int $primaryTopicPercentage = null,
    ) {}
}
