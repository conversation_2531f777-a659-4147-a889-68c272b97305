<?php

namespace App\Data\Learning;

use OpenApi\Attributes as OA;
use <PERSON><PERSON>\LaravelData\Data;
use Spatie\LaravelData\DataCollection;
use Spatie\LaravelData\Attributes\DataCollectionOf;

#[OA\Schema(
    schema: 'LearningProgressSummaryData',
    description: 'Complete summary of a user\'s learning progress',
    required: ['certificates', 'stats', 'investment', 'badges']
)]
class LearningProgressSummaryData extends Data
{
    public function __construct(
        #[OA\Property(
            description: 'Collection of certificates earned by the user',
            type: 'array',
            items: new OA\Items(ref: '#/components/schemas/CertificateData')
        )]
        #[DataCollectionOf(CertificateData::class)]
        public DataCollection $certificates,

        #[OA\Property(
            description: 'Learning statistics for the user',
            ref: '#/components/schemas/LearningStatsData'
        )]
        public LearningStatsData $stats,

        #[OA\Property(
            description: 'Learning investment distribution for the user',
            ref: '#/components/schemas/LearningInvestmentData'
        )]
        public LearningInvestmentData $investment,

        #[OA\Property(
            description: 'Badge progress for the user',
            type: 'array',
            items: new OA\Items(ref: '#/components/schemas/BadgeProgressData')
        )]
        #[DataCollectionOf(BadgeProgressData::class)]
        public DataCollection $badges,
    ) {}
}
