<?php

namespace App\Data\Engagement;

use Spa<PERSON>\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="DailyCountData",
 *     title="Daily Count Data",
 *     description="Daily engagement count for a specific date",
 *     @OA\Property(property="date", type="string", format="date", example="2023-10-15"),
 *     @OA\Property(property="count", type="integer", example=42)
 * )
 */
class DailyCountData extends Data
{
    public function __construct(
        public readonly string $date,
        public readonly int $count
    ) {
    }

    /**
     * Create a DailyCountData instance from a date string and count.
     */
    public static function fromArray(array $data): static
    {
        return new static(
            date: $data['date'],
            count: $data['count'],
        );
    }
}
