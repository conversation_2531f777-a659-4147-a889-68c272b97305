<?php

namespace App\Data\Engagement;

use Illuminate\Support\Collection;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;

/**
 * @OA\Schema(
 *     schema="EngagementMetricsData",
 *     title="Engagement Metrics Data",
 *     description="Engagement metrics for a trackable item",
 *     @OA\Property(property="impressions", type="integer", example=1250, description="Total number of impressions"),
 *     @OA\Property(property="clicks", type="integer", example=75, description="Total number of clicks"),
 *     @OA\Property(property="ctr", type="number", format="float", example=6.0, description="Click-through rate as a percentage"),
 *     @OA\Property(
 *         property="daily",
 *         type="object",
 *         @OA\Property(
 *             property="impressions",
 *             type="array",
 *             @OA\Items(ref="#/components/schemas/DailyCountData")
 *         ),
 *         @OA\Property(
 *             property="clicks",
 *             type="array",
 *             @OA\Items(ref="#/components/schemas/DailyCountData")
 *         )
 *     )
 * )
 */
class EngagementMetricsData extends Data
{
    public function __construct(
        public readonly int $impressions,
        public readonly int $clicks,
        public readonly float $ctr,
        public readonly array $daily
    ) {
    }

    /**
     * Create an EngagementMetricsData instance from raw metrics data.
     *
     * @param int $impressions
     * @param int $clicks
     * @param float $ctr
     * @param Collection $dailyImpressions
     * @param Collection $dailyClicks
     * @return static
     */
    public static function fromRawData(
        int $impressions,
        int $clicks,
        float $ctr,
        Collection $dailyImpressions,
        Collection $dailyClicks
    ): static {
        // Transform daily data collections to arrays of DailyCountData objects
        $impressionData = $dailyImpressions->map(fn ($item) => DailyCountData::fromArray([
            'date' => is_string($item['date']) ? $item['date'] : $item['date']->toDateString(),
            'count' => $item['count']
        ]))->values()->toArray();

        $clickData = $dailyClicks->map(fn ($item) => DailyCountData::fromArray([
            'date' => is_string($item['date']) ? $item['date'] : $item['date']->toDateString(),
            'count' => $item['count']
        ]))->values()->toArray();

        return new static(
            impressions: $impressions,
            clicks: $clicks,
            ctr: $ctr,
            daily: [
                'impressions' => $impressionData,
                'clicks' => $clickData
            ]
        );
    }
}
