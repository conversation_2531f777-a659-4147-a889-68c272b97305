<?php

namespace App\Data\Engagement;

use <PERSON><PERSON>\LaravelData\Attributes\Validation\In;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="EngagementRequestData",
 *     title="Engagement Request Data",
 *     description="Request data for recording impressions and clicks",
 *     required={"type", "id"},
 *     @OA\Property(property="type", type="string", enum={"advertisement", "opportunity"}, example="advertisement", description="Type of trackable item"),
 *     @OA\Property(property="id", type="integer", example=1, description="ID of the trackable item"),
 *     @OA\Property(property="metadata", type="object", example={"source": "homepage", "position": "sidebar"}, description="Additional tracking metadata")
 * )
 */
class EngagementRequestData extends Data
{
    public function __construct(
        #[Required]
        #[In(['advertisement', 'opportunity'])]
        public readonly string $type,

        #[Required]
        public readonly int $id,

        public readonly ?array $metadata = null
    ) {
    }

    /**
     * Create an EngagementRequestData instance from request data.
     */
    public static function fromRequest(array $data): static
    {
        return new static(
            type: $data['type'],
            id: $data['id'],
            metadata: $data['metadata'] ?? null
        );
    }
}
