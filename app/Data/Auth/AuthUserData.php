<?php

namespace App\Data\Auth;

use App\Data\UserBasicData;
use App\Enums\ProfileType;
use App\Models\User;
use App\Services\ParentChildResolverService;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="AuthUserData",
 *     title="Auth User Data",
 *     description="User information with related athlete for parent users",
 *     @OA\Property(property="id", type="integer", example=107),
 *     @OA\Property(property="first_name", type="string", example="Reynold"),
 *     @OA\Property(property="last_name", type="string", example="Wilderman"),
 *     @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
 *     @OA\Property(property="created_at", type="string", format="date-time", example="2025-04-03T16:10:15.000000Z"),
 *     @OA\Property(property="life_stage", type="string", nullable=true),
 *     @OA\Property(property="phone", type="string", example="******-595-6552", nullable=true),
 *     @OA\Property(property="profile_type", type="string", description="The user's profile type", ref="#/components/schemas/ProfileType"),
 *     @OA\Property(property="updated_at", type="string", format="date-time", example="2025-04-03T16:10:15.000000Z"),
 *     @OA\Property(
 *         property="athlete",
 *         type="object",
 *         nullable=true,
 *         description="Related athlete for parent users",
 *         @OA\Property(property="id", type="integer", example=108),
 *         @OA\Property(property="first_name", type="string", example="John"),
 *         @OA\Property(property="last_name", type="string", example="Smith"),
 *         @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
 *         @OA\Property(property="recruiter_enabled", type="boolean", example=true)
 *     ),
 *     @OA\Property(
 *         property="profile_meta",
 *         type="object",
 *         nullable=true,
 *         description="Dynamic profile-specific metadata object containing fields relevant to the user's profile type and individual needs. Structure varies based on profile type.",
 *         additionalProperties=true,
 *         example={
 *             "college": "University Name",
 *             "career_interests": {"2", "5"},
 *             "social_links": {},
 *             "is_linked": true,
 *             "school": {
 *                 "id": 123,
 *                 "name": "School Name",
 *                 "is_verified": true
 *             }
 *         }
 *     )
 * )
 */
class AuthUserData extends Data
{
    public function __construct(
        public readonly int $id,
        public readonly string $first_name,
        public readonly string $last_name,
        public readonly string $email,
        public readonly string $created_at,
        public readonly ?string $life_stage,
        public readonly ?string $phone,
        public readonly ProfileType $profile_type,
        public readonly string $updated_at,
        public readonly ?AthleteData $athlete = null,
        public readonly ?array $profile_meta = null,
    ) {}

    /**
     * Create a new instance from a User model.
     */
    public static function fromUser(User $user, ?User $athleteUser = null): self
    {
        $athleteData = null;

        if ($athleteUser) {
            $athleteData = new AthleteData(
                id: $athleteUser->id,
                first_name: $athleteUser->first_name,
                last_name: $athleteUser->last_name,
                email: $athleteUser->email,
                recruiter_enabled: $athleteUser->recruiter_enabled
            );
        }

        // Start with user's metadata field from database
        $profileMeta = $user->metadata ? (array) $user->metadata : [];

        // Add profile-specific metadata based on user type
        if ($user->profile_type === ProfileType::ATHLETICS_DIRECTOR) {
            $adMeta = self::generateAthleticsDirectorMeta($user);
            if ($adMeta) {
                $profileMeta = array_merge($profileMeta, $adMeta);
            }
        }

        // If no metadata exists at all, set to null instead of empty array
        $profileMeta = empty($profileMeta) ? null : $profileMeta;

        return new self(
            id: $user->id,
            first_name: $user->first_name,
            last_name: $user->last_name,
            email: $user->email,
            created_at: $user->created_at->toISOString(),
            life_stage: $user->life_stage?->value,
            phone: $user->phone,
            profile_type: $user->profile_type,
            updated_at: $user->updated_at->toISOString(),
            athlete: $athleteData,
            profile_meta: $profileMeta
        );
    }

    /**
     * Generate metadata for Athletics Director profile type
     */
    private static function generateAthleticsDirectorMeta(User $user): ?array
    {
        // Load the athletics director profile relationship if not already loaded
        if (!$user->relationLoaded('athleticsDirectorProfile')) {
            $user->load(['athleticsDirectorProfile.school']);
        }

        $adProfile = $user->athleticsDirectorProfile;

        if (!$adProfile) {
            return [
                'is_linked' => false,
                'school' => null
            ];
        }

        $schoolData = null;
        if ($adProfile->school) {
            $schoolData = [
                'id' => $adProfile->school->id,
                'name' => $adProfile->school->name,
                'is_verified' => $adProfile->is_verified,
                'verified_at' => $adProfile->verified_at?->toISOString(),
            ];
        }

        return [
            'is_linked' => $adProfile?->school_id && $adProfile?->is_verified,
            'school' => $schoolData
        ];
    }
}
