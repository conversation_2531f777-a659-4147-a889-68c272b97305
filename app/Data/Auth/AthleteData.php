<?php

namespace App\Data\Auth;

use App\Models\User;
use <PERSON>tie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="AthleteData",
 *     title="Athlete Data",
 *     description="Basic athlete information for parent users",
 *     @OA\Property(property="id", type="integer", example=108),
 *     @OA\Property(property="first_name", type="string", example="John"),
 *     @OA\Property(property="last_name", type="string", example="Smith"),
 *     @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
 *     @OA\Property(property="recruiter_enabled", type="boolean", example=true)
 * )
 */
class AthleteData extends Data
{
    public function __construct(
        public readonly int $id,
        public readonly string $first_name,
        public readonly string $last_name,
        public readonly string $email,
        public readonly bool $recruiter_enabled,
    ) {
    }

    /**
     * Create a new instance from a User model.
     */
    public static function from<PERSON>ser(User $user): self
    {
        return new self(
            id: $user->id,
            first_name: $user->first_name,
            last_name: $user->last_name,
            email: $user->email,
            recruiter_enabled: $user->recruiter_enabled
        );
    }
}
