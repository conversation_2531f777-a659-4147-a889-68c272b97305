<?php

namespace App\Data;

use App\Enums\ProfileType;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Spatie\LaravelData\Data;
use stdClass;

/**
 * @OA\Schema(
 *     schema="UserBasicData",
 *     title="User Basic Data",
 *     description="Basic user information used across multiple features",
 *     @OA\Property(property="id", type="integer", example=123, description="User ID"),
 *     @OA\Property(property="firstName", type="string", example="John", description="User's first name"),
 *     @OA\Property(property="lastName", type="string", example="Doe", description="User's last name"),
 *     @OA\Property(property="profileImageUrl", type="string", format="uri", nullable=true, example="https://example.com/images/profile.jpg", description="URL to user's profile image"),
 *     @OA\Property(property="organizationId", type="integer", nullable=true, example=456, description="ID of the user's organization (for sponsor users)"),
 *     @OA\Property(property="organizationName", type="string", nullable=true, example="Acme Inc", description="Name of the user's organization (for sponsor users)"),
 *     @OA\Property(property="organizationLogoUrl", type="string", format="uri", nullable=true, example="https://example.com/images/logo.jpg", description="URL to the organization's logo (for sponsor users)")
 * )
 */
class UserBasicData extends Data
{
    public function __construct(
        public readonly int $id,
        public readonly string $firstName,
        public readonly string $lastName,
        public readonly ?string $profileImageUrl,
        public readonly ?int $organizationId = null,
        public readonly ?string $organizationName = null,
        public readonly ?string $organizationLogoUrl = null,
    ) {
    }

    /**
     * Create a new instance from a User model or stdClass object.
     * This method can handle both User models and stdClass objects.
     */
    public static function fromStdClass($value): static
    {
        if ($value instanceof User) {
            $organizationId = null;
            $organizationName = null;
            $organizationLogoUrl = null;

            // Extract organization data if available
            if (isset($value->organization_id)) {
                $organizationId = $value->organization_id;
            }
            if (isset($value->organization_name)) {
                $organizationName = $value->organization_name;
            }
            if (isset($value->organization_logo_url)) {
                $organizationLogoUrl = $value->organization_logo_url;
            }

            // Get avatar from Media Library if available
            $profileImageUrl = null;
            if (method_exists($value, 'hasMedia') && $value->hasMedia('avatar')) {
                $profileImageUrl = $value->getFirstMediaUrl('avatar', 'thumb');
            } elseif (isset($value->profile_image_url)) {
                $profileImageUrl = $value->profile_image_url;
            }

            return new static(
                id: $value->id,
                firstName: $value->first_name,
                lastName: $value->last_name,
                profileImageUrl: $profileImageUrl,
                organizationId: $organizationId,
                organizationName: $organizationName,
                organizationLogoUrl: $organizationLogoUrl,
            );
        }

        if ($value instanceof stdClass) {
            try {
                $organizationId = null;
                $organizationName = null;
                $organizationLogoUrl = null;

                // Extract organization data if available
                if (isset($value->organization_id)) {
                    $organizationId = $value->organization_id;
                }
                if (isset($value->organization_name)) {
                    $organizationName = $value->organization_name;
                }
                if (isset($value->organization_logo_url)) {
                    $organizationLogoUrl = $value->organization_logo_url;
                }

                // For stdClass objects, we need to rely on explicitly provided avatar_url from database query
                $profileImageUrl = null;
                if (isset($value->avatar_url)) {
                    $profileImageUrl = $value->avatar_url;
                } elseif (isset($value->profile_image_url)) {
                    $profileImageUrl = $value->profile_image_url;
                }

                return new static(
                    id: $value->id,
                    firstName: $value->first_name,
                    lastName: $value->last_name,
                    profileImageUrl: $profileImageUrl,
                    organizationId: $organizationId,
                    organizationName: $organizationName,
                    organizationLogoUrl: $organizationLogoUrl,
                );
            } catch (\Throwable $e) {
                Log::error('Error in UserBasicData::fromStdClass with stdClass', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'properties' => json_encode(get_object_vars($value))
                ]);
                throw $e;
            }
        }

        Log::error('UserBasicData::fromStdClass - invalid input type', [
            'actualType' => get_class($value)
        ]);
        throw new \InvalidArgumentException('Value must be an instance of User or stdClass');
    }

    /**
     * Create a new instance from a User model.
     */
    public static function fromUser(User $user): static
    {
        try {
            $organizationId = null;
            $organizationName = null;
            $organizationLogoUrl = null;

            // Extract organization data if available and user is a sponsor
            if ($user->profile_type === ProfileType::SPONSOR &&
                $user->relationLoaded('activeOrganization') &&
                $user->activeOrganization->isNotEmpty()) {

                $org = $user->activeOrganization->first();
                $organizationId = $org->id;
                $organizationName = $org->name;

                if ($org->hasMedia('logo')) {
                    $organizationLogoUrl = $org->getFirstMediaUrl('logo', 'thumb');
                }
            }

            // Get avatar from Media Library
            $profileImageUrl = null;
            if ($user->hasMedia('avatar')) {
                $profileImageUrl = $user->getFirstMediaUrl('avatar', 'thumb');
            } elseif (isset($user->profile_image_url)) {
                $profileImageUrl = $user->profile_image_url;
            }

            return new static(
                id: $user->id,
                firstName: $user->first_name,
                lastName: $user->last_name,
                profileImageUrl: $profileImageUrl,
                organizationId: $organizationId,
                organizationName: $organizationName,
                organizationLogoUrl: $organizationLogoUrl,
            );
        } catch (\Throwable $e) {
            Log::error('Error in UserBasicData::fromUser', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'userId' => $user->id
            ]);
            throw $e;
        }
    }
}
