<?php

namespace App\Data;

use Illuminate\Support\Facades\Log;
use Spatie\LaravelData\Data;
use stdClass;

/**
 * @OA\Schema(
 *     schema="ConversationData",
 *     title="Conversation Data",
 *     description="Data representing a message conversation",
 *     @OA\Property(property="otherUserId", type="integer", example=123, description="ID of the other user in the conversation"),
 *     @OA\Property(property="lastMessageAt", type="string", format="date-time", example="2023-05-15T14:30:00Z", description="Timestamp of the last message"),
 *     @OA\Property(property="messageCount", type="integer", example=15, description="Total number of messages in this conversation"),
 *     @OA\Property(property="lastMessage", type="string", nullable=true, example="Hello, how are you?", description="Content of the last message"),
 *     @OA\Property(property="lastMessageRead", type="boolean", nullable=true, example=true, description="Whether the last message has been read"),
 *     @OA\Property(property="isPinned", type="boolean", example=false, description="Whether the conversation is pinned"),
 *     @OA\Property(
 *         property="otherUser",
 *         description="Basic data about the other user",
 *         nullable=true,
 *         allOf={
 *             @OA\Schema(ref="#/components/schemas/UserBasicData")
 *         }
 *     ),
 *     @OA\Property(property="connectionId", type="integer", nullable=true, example=456, description="ID of the connection between users"),
 *     @OA\Property(property="connectionStatus", type="string", nullable=true, example="accepted", description="Status of the connection between users"),
 *     @OA\Property(property="connectionRequesterId", type="integer", nullable=true, example=789, description="ID of the user who requested the connection"),
 *     @OA\Property(property="isReadonly", type="boolean", example=false, description="Whether this conversation is read-only (colleague conversation)"),
 *     @OA\Property(
 *         property="sponsorUser",
 *         description="Basic data about the sponsor user who owns this conversation (for read-only conversations)",
 *         nullable=true,
 *         allOf={
 *             @OA\Schema(ref="#/components/schemas/UserBasicData")
 *         }
 *     )
 * )
 */
class ConversationData extends Data
{
    public function __construct(
        public readonly int $otherUserId,
        public readonly string $lastMessageAt,
        public readonly int $messageCount,
        public readonly ?string $lastMessage,
        public readonly ?bool $lastMessageRead,
        public readonly bool $isPinned,
        public readonly ?UserBasicData $otherUser,
        public readonly ?int $connectionId,
        public readonly ?string $connectionStatus,
        public readonly ?int $connectionRequesterId,
        public readonly bool $isReadonly = false,
        public readonly ?UserBasicData $sponsorUser = null,
    ) {
    }

    /**
     * Create a new instance from a stdClass object.
     * This is needed because DB::table() returns stdClass objects.
     */
    public static function fromStdClass(stdClass $value): static
    {
        $lastMessageRead = null;
        if (isset($value->last_message_read_at) && $value->last_message_read_at !== null) {
            $lastMessageRead = true;
        }

        // If other_user is an array, convert it to an object for compatibility
        if (isset($value->other_user) && is_array($value->other_user)) {
            $value->other_user = (object) $value->other_user;
        }

        // If sponsor_user is an array, convert it to an object for compatibility
        if (isset($value->sponsor_user) && is_array($value->sponsor_user)) {
            $value->sponsor_user = (object) $value->sponsor_user;
        }

        try {
            // Create user basic data with organization information if available
            $otherUser = null;
            if (isset($value->other_user)) {
                $otherUser = UserBasicData::fromStdClass($value->other_user);
            }

            // Create sponsor user data with organization information if available
            $sponsorUser = null;
            if (isset($value->sponsor_user)) {
                $sponsorUser = UserBasicData::fromStdClass($value->sponsor_user);
            }

            return new static(
                otherUserId: $value->other_user_id,
                lastMessageAt: $value->last_message_at,
                messageCount: $value->message_count,
                lastMessage: $value->last_message ?? null,
                lastMessageRead: $lastMessageRead,
                isPinned: $value->is_pinned ?? false,
                otherUser: $otherUser,
                connectionId: $value->connection_id ?? null,
                connectionStatus: $value->connection_status ?? null,
                connectionRequesterId: $value->connection_requester_id ?? null,
                isReadonly: $value->is_readonly ?? false,
                sponsorUser: $sponsorUser,
            );
        } catch (\Throwable $e) {
            Log::error('Error in ConversationData::fromStdClass', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            throw $e;
        }
    }
}
