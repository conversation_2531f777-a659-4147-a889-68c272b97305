<?php

namespace App\Data\Nomination;

use App\Models\Nomination;
use Carbon\Carbon;
use OpenApi\Attributes as OA;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="UserNominationData",
 *     description="Nomination data specifically formatted for user display",
 *     required={"id", "nominatorName", "nominatorRole", "date", "content"},
 *     @OA\Property(property="id", type="integer", example=1, description="Unique identifier for the nomination"),
 *     @OA\Property(property="nominatorName", type="string", example="<PERSON>", description="Name of the nominator"),
 *     @OA\Property(property="nominatorRole", type="string", example="SOCCER COACH", description="Role or relationship of the nominator to the nominee"),
 *     @OA\Property(property="date", type="string", example="12/8/25", description="Date of the nomination in MM/DD/YY format"),
 *     @OA\Property(property="content", type="string", example="Nulla eget ipsum rhoncus erat sit sagittis diam quis.", description="Content of the nomination note"),
 *     @OA\Property(property="sport", type="string", nullable=true, example="Soccer", description="Sport associated with the nomination"),
 *     @OA\Property(property="schoolName", type="string", nullable=true, example="Lincoln High School", description="School associated with the nomination")
 * )
 */
class UserNominationData extends Data
{
    public function __construct(
        public int $id,

        public string $nominatorName,

        public string $nominatorRole,

        public string $date,

        public string $content,

        public ?string $sport = null,

        public ?string $schoolName = null,
    ) {
    }

    /**
     * Create a UserNominationData object from a Nomination model
     */
    public static function fromNomination(Nomination $nomination): self
    {
        // Format the date as MM/DD/YY
        $date = Carbon::parse($nomination->created_at)->format('m/d/y');

        // Format the nominator role (relationship) in uppercase
        $nominatorRole = strtoupper($nomination->relationship) .
            ($nomination->sport ? ' - ' . strtoupper($nomination->sport) : '');

        return new self(
            id: $nomination->id,
            nominatorName: $nomination->getNominatorNameAttribute(),
            nominatorRole: $nominatorRole,
            date: $date,
            content: $nomination->note ?? '',
            sport: $nomination->sport,
            schoolName: $nomination->school_name,
        );
    }
}
