<?php

namespace App\Data\Nomination;

use App\Data\Attributes\UniqueNominatorNominee;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Attributes\Validation\Email;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Enum;
use Spatie\LaravelData\Attributes\Validation\ArrayType;
use App\Enums\NominationType;
use OpenApi\Attributes as OA;

/**
 * @OA\Schema(
 *     schema="NominationData",
 *     title="Nomination Data",
 *     description="Data structure for creating a nomination",
 *     required={"email", "first_name", "last_name", "nominator_email", "nominator_first_name", "nominator_last_name", "school_name", "sport", "relationship", "type"},
 *     @OA\Property(property="email", type="string", format="email", example="<EMAIL>", description="Email of the nominee"),
 *     @OA\Property(property="first_name", type="string", example="John", description="First name of the nominee"),
 *     @OA\Property(property="last_name", type="string", example="Doe", description="Last name of the nominee"),
 *     @OA\Property(property="nominator_email", type="string", format="email", example="<EMAIL>", description="Email of the nominator"),
 *     @OA\Property(property="nominator_first_name", type="string", example="Jane", description="First name of the nominator"),
 *     @OA\Property(property="nominator_last_name", type="string", example="Smith", description="Last name of the nominator"),
 *     @OA\Property(property="school_name", type="string", example="Central High School", description="Name of the school"),
 *     @OA\Property(property="sport", type="string", example="Basketball", description="Sport for the nomination"),
 *     @OA\Property(property="relationship", type="string", example="Coach", description="Relationship between nominator and nominee"),
 *     @OA\Property(property="type", type="string", enum={"ATHLETE", "COACH"}, example="ATHLETE", description="Type of nomination"),
 *     @OA\Property(property="note", type="string", nullable=true, example="Great team player", description="Optional note about the nominee"),
 *     @OA\Property(property="school_id", type="integer", nullable=true, example=1, description="Optional school ID if known"),
 *     @OA\Property(property="user_id", type="integer", nullable=true, example=5, description="User ID linked to this nomination after onboarding"),
 *     @OA\Property(property="state_code", type="string", nullable=true, example="IA", description="State abbreviation"),
 *     @OA\Property(property="county", type="string", nullable=true, example="Wright", description="County name"),
 *     @OA\Property(property="location_resolution_notes", type="array", nullable=true, items=@OA\Items(type="string", maxLength=255), maxItems=10, example={"County resolved consistently: state_specific, general_county"}, description="Notes about how location data was resolved (max 10 entries, 255 chars each)"),
 *     @OA\Property(property="nominee_phone", type="string", nullable=true, example="(*************", description="Nominee phone number"),
 *     @OA\Property(property="nominator_phone", type="string", nullable=true, example="(*************", description="Nominator phone number"),
 *     @OA\Property(property="sport_2", type="string", nullable=true, example="Swimming", description="Second sport"),
 *     @OA\Property(property="sport_3", type="string", nullable=true, example="Track", description="Third sport"),
 *     @OA\Property(property="other_sport", type="string", nullable=true, example="Lacrosse", description="Custom/adapted sport"),
 *     @OA\Property(property="gender", type="string", nullable=true, example="Female", description="Nominee gender"),
 *     @OA\Property(property="grade", type="string", nullable=true, example="Senior", description="Grade level"),
 *     @OA\Property(property="parent_guardian_first_name", type="string", nullable=true, example="Robert", description="Parent/Guardian first name"),
 *     @OA\Property(property="parent_guardian_last_name", type="string", nullable=true, example="Johnson", description="Parent/Guardian last name"),
 *     @OA\Property(property="parent_guardian_email", type="string", nullable=true, example="<EMAIL>", description="Parent/Guardian email"),
 *     @OA\Property(property="parent_guardian_phone", type="string", nullable=true, example="(*************", description="Parent/Guardian phone"),
 *     @OA\Property(property="instagram_handle", type="string", nullable=true, example="@athlete_username", description="Instagram handle"),
 *     @OA\Property(property="twitter_handle", type="string", nullable=true, example="@athlete_handle", description="Twitter/X handle"),
 *     @OA\Property(property="how_did_you_hear", type="string", nullable=true, example="School counselor", description="How they heard about us"),
 *     @OA\Property(property="referral_source_name", type="string", nullable=true, example="Lincoln High School", description="Name of referring entity"),
 *     @OA\Property(property="processing_status", type="string", nullable=true, enum={"received", "validated", "invited", "onboarded"}, example="received", description="Processing status"),
 *     @OA\Property(property="jotform_submission_id", type="string", nullable=true, example="123456789", description="JotForm submission ID"),
 *     @OA\Property(property="jotform_form_id", type="string", nullable=true, example="FORM123", description="JotForm form ID")
 * )
 */
class NominationData extends Data
{
    public function __construct(
        #[Required, Email, UniqueNominatorNominee]
        public string $email,

        #[Required, StringType, Max(100)]
        public string $first_name,

        #[Required, StringType, Max(100)]
        public string $last_name,

        #[Required, Email]
        public string $nominator_email,

        #[Required, StringType, Max(100)]
        public string $nominator_first_name,

        #[Required, StringType, Max(100)]
        public string $nominator_last_name,

        #[Required, StringType, Max(200)]
        public string $school_name,

        #[Required, StringType, Max(100)]
        public string $sport,

        #[Required, StringType, Max(50)]
        public string $relationship,

        #[Required, Enum(NominationType::class)]
        public NominationType $type,

        #[StringType]
        public ?string $note = null,

        public ?int $school_id = null,

        public ?int $user_id = null,

        // Location Information
        #[StringType]
        public ?string $state_code = null,

        #[StringType]
        public ?string $county = null,

        // Location Resolution Notes (stored as JSON array)
        #[ArrayType('string')]
        public ?array $location_resolution_notes = null,

        // Enhanced Contact Information
        #[StringType]
        public ?string $nominee_phone = null,

        #[StringType]
        public ?string $nominator_phone = null,

        // Multiple Sports Support
        #[StringType]
        public ?string $sport_2 = null,

        #[StringType]
        public ?string $sport_3 = null,

        #[StringType]
        public ?string $other_sport = null,

        // Demographic & Academic Information
        #[StringType]
        public ?string $gender = null,

        #[StringType]
        public ?string $grade = null,

        // Parent/Guardian Information
        #[StringType]
        public ?string $parent_guardian_first_name = null,

        #[StringType]
        public ?string $parent_guardian_last_name = null,

        #[Email]
        public ?string $parent_guardian_email = null,

        #[StringType]
        public ?string $parent_guardian_phone = null,

        // Social Media & Digital Presence
        #[StringType]
        public ?string $instagram_handle = null,

        #[StringType]
        public ?string $twitter_handle = null,

        // Marketing & Attribution Data
        #[StringType]
        public ?string $how_did_you_hear = null,

        #[StringType]
        public ?string $referral_source_name = null,

        // Processing Workflow Enhancement
        #[StringType]
        public ?string $processing_status = null,

        // JotForm Integration Metadata
        #[StringType]
        public ?string $jotform_submission_id = null,

        #[StringType]
        public ?string $jotform_form_id = null,
    ) {}

    /**
     * Custom validation rules for enhanced data validation
     */
    public static function rules(): array
    {
        return [
            'location_resolution_notes' => [
                'nullable',
                'array',
                'max:10', // Limit to maximum 10 entries to prevent large payloads
            ],
            'location_resolution_notes.*' => [
                'string',
                'max:255', // Limit each note to 255 characters
            ],
        ];
    }
}
