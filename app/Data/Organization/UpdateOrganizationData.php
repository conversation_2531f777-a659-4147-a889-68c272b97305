<?php

namespace App\Data\Organization;

use Illuminate\Http\UploadedFile;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Attributes\Validation\Sometimes;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Optional;

class UpdateOrganizationData extends Data
{
    public function __construct(
        #[Sometimes, Nullable]
        public readonly UploadedFile|null|Optional $organization_logo,
    ) {
    }

    /**
     * Add custom preparation for organization_logo
     */
    public static function prepareForPipeline(array $properties): array
    {
        // Handle special null case for organization_logo
        if (isset($properties['organization_logo']) && ($properties['organization_logo'] === 'null' || $properties['organization_logo'] === '')) {
            $properties['organization_logo'] = null;
        }

        return $properties;
    }
}
