<?php

namespace App\Data\Organization;

use App\Models\Organization;
use <PERSON><PERSON>\LaravelData\Data;

class OrganizationData extends Data
{
    public function __construct(
        public readonly int $id,
        public readonly string $name,
        public readonly ?string $website,
        public readonly ?string $about,
        public readonly ?string $logo_url,
        public readonly string $created_at,
        public readonly string $updated_at,
    ) {
    }

    /**
     * Create a new instance from an Organization model.
     *
     * @param mixed ...$payloads
     * @return static
     */
    public static function from(mixed ...$payloads): static
    {
        $organization = $payloads[0];

        if (!$organization instanceof Organization) {
            // Handle other payload types as needed or throw an exception
            throw new \InvalidArgumentException('Expected an Organization model');
        }

        $logo_url = null;

        // Get logo URL from media library
        if ($organization->hasMedia('logo')) {
            $logo_url = $organization->getFirstMediaUrl('logo');
        }

        return new static(
            id: $organization->id,
            name: $organization->name,
            website: $organization->website,
            about: $organization->about,
            logo_url: $logo_url,
            created_at: $organization->created_at->toISOString(),
            updated_at: $organization->updated_at->toISOString(),
        );
    }
}
