<?php

namespace App\Data\Location;

use App\Models\County;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="CountyDetailsData",
 *     title="County Details Data",
 *     description="Data structure representing detailed county information including regional hierarchy",
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         format="int64",
 *         description="Unique identifier for the county",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="name",
 *         type="string",
 *         description="Name of the county",
 *         example="Allegheny"
 *     ),
 *     @OA\Property(
 *         property="state",
 *         type="string",
 *         description="State code where the county is located",
 *         example="PA"
 *     ),
 *     @OA\Property(
 *         property="hierarchy",
 *         type="object",
 *         description="Regional hierarchy information for the county",
 *         @OA\Property(
 *             property="market",
 *             type="object",
 *             description="Market information",
 *             @OA\Property(property="id", type="integer", example=1),
 *             @OA\Property(property="name", type="string", example="Pittsburgh"),
 *             @OA\Property(
 *                 property="region",
 *                 type="object",
 *                 description="Region information",
 *                 @OA\Property(property="id", type="integer", example=1),
 *                 @OA\Property(property="name", type="string", example="Western Pennsylvania")
 *             )
 *         ),
 *         @OA\Property(
 *             property="sub_region",
 *             type="object",
 *             nullable=true,
 *             description="Sub-region information if available",
 *             @OA\Property(property="id", type="integer", example=2),
 *             @OA\Property(property="name", type="string", example="Southwestern PA")
 *         )
 *     )
 * )
 */
class CountyDetailsData extends Data
{
    public function __construct(
        public readonly int $id,
        public readonly string $name,
        public readonly string $state,
        public readonly array $hierarchy,
    ) {}

    public static function from(mixed ...$payloads): static
    {
        $county = $payloads[0];

        if (is_array($county)) {
            return new static(
                id: $county['id'],
                name: $county['name'],
                state: $county['state'],
                hierarchy: $county['hierarchy'],
            );
        }

        // Build the hierarchy array with all relevant information
        $hierarchy = [
            'market' => [
                'id' => $county->market->id,
                'name' => $county->market->name,
                'region' => [
                    'id' => $county->market->region->id,
                    'name' => $county->market->region->name,
                ]
            ],
            'sub_region' => $county->subRegion ? [
                'id' => $county->subRegion->id,
                'name' => $county->subRegion->name,
            ] : null,
        ];

        return new static(
            id: $county->id,
            name: $county->name,
            state: $county->state,
            hierarchy: $hierarchy,
        );
    }
}
