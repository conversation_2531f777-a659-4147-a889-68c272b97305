<?php

namespace App\Data\Location;

use App\Models\County;
use App\Models\State;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="CountyData",
 *     title="County Data",
 *     description="Data structure representing basic county information",
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         format="int64",
 *         description="Unique identifier for the county",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="name",
 *         type="string",
 *         description="Name of the county",
 *         example="Allegheny"
 *     ),
 *     @OA\Property(
 *         property="state",
 *         type="string",
 *         description="State code where the county is located",
 *         example="PA"
 *     ),
 *     @OA\Property(
 *         property="state_name",
 *         type="string",
 *         nullable=true,
 *         description="Full name of the state where the county is located",
 *         example="Pennsylvania"
 *     )
 * )
 */
class CountyData extends Data
{
    public function __construct(
        public int $id,
        public string $name,
        public string $state,
        public ?string $state_name = null,
    ) {
    }

    public static function fromModel(County $county): self
    {
        // Get the state name from the relationship if loaded, otherwise load it directly
        $stateName = $county->relationLoaded('state')
            ? $county->state?->name
            : State::query()->where('code', $county->state_code)->value('name');

        return new self(
            id: $county->id,
            name: $county->name,
            state: $county->state_code,
            state_name: $stateName,
        );
    }
}
