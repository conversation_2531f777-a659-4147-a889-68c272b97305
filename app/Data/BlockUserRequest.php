<?php

namespace App\Data;

use <PERSON><PERSON>\LaravelData\Data;
use Spatie\LaravelData\Attributes\Validation\Exists;
use Spatie\LaravelData\Attributes\Validation\Required;

class BlockUserRequest extends Data
{
    public function __construct(
        #[Required]
        #[Exists('users', 'id')]
        public readonly int $userId,
    ) {
    }

    public static function rules(): array
    {
        return [
            'userId' => ['required', 'integer', 'exists:users,id'],
        ];
    }
}
