<?php

namespace App\Data;

use <PERSON><PERSON>\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Data;

/**
 * @OA\Schema(
 *     schema="EditMessageData",
 *     title="Edit Message Data",
 *     description="Data required to edit a message",
 *     @OA\Property(
 *         property="content",
 *         type="string",
 *         description="New content for the message",
 *         example="Updated message content"
 *     )
 * )
 */
class EditMessageData extends Data
{
    public function __construct(
        #[Required]
        #[StringType]
        public readonly string $content,
    ) {}

    /**
     * Define validation rules
     *
     * @return array
     */
    public static function rules(): array
    {
        return [
            'content' => ['required', 'string', 'max:5000'],
        ];
    }
}
