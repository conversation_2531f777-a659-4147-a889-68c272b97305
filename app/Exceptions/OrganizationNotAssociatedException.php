<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class OrganizationNotAssociatedException extends Exception
{
    /**
     * Create a new organization not associated exception.
     */
    public function __construct(string $message = 'Sponsor user is not associated with an organization')
    {
        parent::__construct($message);
    }

    /**
     * Render the exception as an HTTP response.
     */
    public function render(Request $request): JsonResponse
    {
        return response()->json([
            'message' => $this->getMessage()
        ], Response::HTTP_BAD_REQUEST);
    }
}
