<?php

namespace App\Livewire\XFactor;

use Livewire\Component;
use App\Models\Module;
use App\Models\Test;
use App\Models\Question;
use App\Models\Answer;
use App\Models\Topic;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Contracts\View\View;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Form;
use Filament\Forms\Set;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Filament\Notifications\Notification;
use App\Enums\ModuleType;
use App\Rules\VideoTimeRange;

class ModulesView extends Component implements HasTable, HasForms
{
    use InteractsWithTable;
    use InteractsWithForms;

    public ?array $data = [];
    public ?Module $module = null;

    public function mount(): void
    {
        $this->form->fill();
    }

    public function resetForm(): void
    {
        $this->form->fill();
        $this->module = null;
    }

    public function edit(Module $module): void
    {
        $this->module = $module;

        // Get the first topic ID since we're using a many-to-many relationship
        $topicId = $module->topics()->first()?->id;

        // Get the test data if it exists
        $test = $module->test;
        $questions = [];

        if ($test) {
            foreach ($test->questions as $question) {
                $answers = [];
                foreach ($question->answers as $answer) {
                    $answers[] = [
                        'answer' => $answer->answer,
                        'is_correct' => $answer->is_correct,
                    ];
                }

                $questions[] = [
                    'question' => $question->question,
                    'answers' => $answers,
                ];
            }
        }

        // Get the existing cover image if it exists
        $coverMedia = $module->getFirstMedia('cover');
        $existingCover = null;

        if ($coverMedia) {
            $existingCover = [
                'name' => $coverMedia->file_name,
                'size' => $coverMedia->size,
                'type' => $coverMedia->mime_type,
                'path' => $coverMedia->getPathRelativeToRoot(),
            ];
        }

        $this->form->fill([
            'name' => $module->name,
            'slug' => $module->slug,
            'description' => $module->description,
            'content' => $module->content ?? null,
            'video_url' => $module->video_url,
            'video_start_time' => $module->video_start_time,
            'video_end_time' => $module->video_end_time,
            'minutes' => $module->minutes,
            'published' => $module->published,
            'topic_id' => $topicId,
            'questions' => $questions,
            'cover' => !empty($existingCover['path']) ? $existingCover['path'] : null,
        ]);

        $this->dispatch('open-modal', id: 'manage-module');
    }

    public function save(): void
    {
        $data = $this->form->getState();

        try {
            DB::beginTransaction();

            $module = $this->module ?? new Module();

            // Determine the module type based on content
            $type = ModuleType::Video; // Default to video
            if (!empty($data['content']) && empty($data['video_url'])) {
                $type = ModuleType::Article;
            } elseif (!empty($data['video_url'])) {
                $type = ModuleType::Video;
            }

            $module->fill([
                'name' => $data['name'],
                'slug' => $data['slug'],
                'description' => $data['description'],
                'content' => $data['content'] ?? null,
                'video_url' => $data['video_url'],
                'video_start_time' => $data['video_start_time'] ?? null,
                'video_end_time' => $data['video_end_time'] ?? null,
                'minutes' => $data['minutes'],
                'published' => $data['published'],
                'type' => $type,
            ]);
            $module->save();

            // Handle topics
            $module->topics()->sync([$data['topic_id']]);

            // Handle cover image
            if (!empty($data['cover'])) {
                // Check if this is a new upload by comparing with the current image path
                $currentMedia = $module->getFirstMedia('cover');
                $isNewUpload = !$currentMedia || $data['cover'] !== $currentMedia->getPathRelativeToRoot();

                if ($isNewUpload) {
                    // Clear existing media first
                    $module->clearMediaCollection('cover');
                    // Add the new image
                    $module->addMedia(storage_path('app/public/' . $data['cover']))
                        ->toMediaCollection('cover');
                }
            } else {
                // If cover was removed, clear the media
                $module->clearMediaCollection('cover');
            }

            // Handle quiz
            if (!empty($data['questions'])) {
                // Delete existing test if editing
                if ($this->module) {
                    $module->tests()->delete();
                }

                $test = new Test();
                $test->fill([
                    'type' => 'quiz',
                    'testable_type' => Module::class,
                    'testable_id' => $module->id,
                ]);
                $test->save();

                foreach ($data['questions'] as $questionData) {
                    $question = new Question();
                    $question->fill([
                        'test_id' => $test->id,
                        'question' => $questionData['question'],
                        'type' => 'multiple_choice',
                    ]);
                    $question->save();

                    foreach ($questionData['answers'] as $answerData) {
                        $answer = new Answer();
                        $answer->fill([
                            'question_id' => $question->id,
                            'answer' => $answerData['answer'],
                            'is_correct' => $answerData['is_correct'],
                        ]);
                        $answer->save();
                    }
                }
            }

            DB::commit();

            // Reset form and module
            $this->form->fill();
            $this->module = null;

            // Close modal and show success notification
            $this->dispatch('close-modal', id: 'manage-module');

            Notification::make()
                ->success()
                ->title('Success')
                ->body($this->module ? 'Module updated successfully.' : 'Module created successfully.')
                ->send();

        } catch (\Exception $e) {
            DB::rollBack();

            ray('SAVE ERROR', $e->getMessage());

            Notification::make()
                ->danger()
                ->title('Error')
                ->body('Failed to save module: ' . $e->getMessage())
                ->send();
        }
    }

    public function delete(): void
    {
        try {
            DB::beginTransaction();

            // Delete associated tests and media
            $this->module->tests()->delete();
            $this->module->clearMediaCollection('cover');
            $this->module->delete();

            DB::commit();

            // Reset form and module
            $this->form->fill();
            $this->module = null;

            $this->dispatch('close-modal', id: 'manage-module');

            Notification::make()
                ->success()
                ->title('Success')
                ->body('Module deleted successfully.')
                ->send();

        } catch (\Exception $e) {
            DB::rollBack();

            Notification::make()
                ->danger()
                ->title('Error')
                ->body('Failed to delete module: ' . $e->getMessage())
                ->send();
        }
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make()
                    ->columns(3)
                    ->schema([
                        Grid::make()
                            ->columnSpan(2)
                            ->schema([
                                Section::make('Video & Thumbnail')
                                    ->schema([
                                        TextInput::make('video_url')
                                            ->label('Video URL')
                                            ->url()
                                            ->placeholder('https://www.youtube.com/watch?v=...')
                                            ->columnSpan('full'),

                                        Grid::make()
                                            ->columns(2)
                                            ->schema([
                                                TextInput::make('video_start_time')
                                                    ->label('Video Start Time (seconds)')
                                                    ->numeric()
                                                    ->minValue(0)
                                                    ->placeholder('e.g., 30')
                                                    ->helperText('Optional. Time in seconds from the start of the video.'),

                                                TextInput::make('video_end_time')
                                                    ->label('Video End Time (seconds)')
                                                    ->numeric()
                                                    ->minValue(0)
                                                    ->placeholder('e.g., 120')
                                                    ->helperText('Optional. Time in seconds from the start of the video.')
                                                    ->rules([new VideoTimeRange()]),
                                            ]),

                                        FileUpload::make('cover')
                                            ->label('Module Cover')
                                            ->image()
                                            ->required(fn () => !$this->module)
                                            ->imageEditor()
                                            ->disk('public')
                                            ->visibility('public')
                                            ->columnSpan('full')
                                            ->downloadable()
                                            ->openable()
                                            ->preserveFilenames()
                                            ->imagePreviewHeight('250')
                                            ->panelAspectRatio('2:1')
                                            ->panelLayout('integrated')
                                            ->removeUploadedFileButtonPosition('right')
                                            ->uploadProgressIndicatorPosition('left'),
                                    ])->collapsible(),

                                Section::make('Quiz')
                                    ->schema([
                                        Repeater::make('questions')
                                            ->schema([
                                                TextInput::make('question')
                                                    ->label('Question')
                                                    ->required()
                                                    ->maxLength(255)
                                                    ->columnSpan('full'),

                                                Repeater::make('answers')
                                                    ->schema([
                                                        TextInput::make('answer')
                                                            ->label('Answer Option')
                                                            ->required()
                                                            ->maxLength(255),

                                                        Toggle::make('is_correct')
                                                            ->label('Correct Answer')
                                                            ->inline(false)
                                                            ->default(false)
                                                            ->afterStateUpdated(function ($state, Set $set, $context) {
                                                                if ($state) {
                                                                    $answers = data_get($context, 'answers', []);
                                                                    foreach ($answers as $key => $answer) {
                                                                        if ($key !== $context->key) {
                                                                            $set("answers.{$key}.is_correct", false);
                                                                        }
                                                                    }
                                                                }
                                                            }),
                                                    ])
                                                    ->columns(2)
                                                    ->minItems(2)
                                                    ->maxItems(4)
                                                    ->addActionLabel('Add Answer Option')
                                                    ->columnSpan('full'),
                                            ])
                                            ->collapsible()
                                            ->itemLabel(fn (array $state): ?string => $state['question'] ?? null)
                                            ->addActionLabel('Add Question')
                                            ->defaultItems(1),
                                    ]),
                            ]),

                        Section::make('Details')
                            ->columnSpan(1)
                            ->schema([
                                Toggle::make('published')
                                    ->label('Published')
                                    ->default(false)
                                    ->inline(false),

                                TextInput::make('name')
                                    ->label('Athlete Name')
                                    ->required()
                                    ->maxLength(255),

                                Select::make('topic_id')
                                    ->label('Category')
                                    ->options(Topic::pluck('name', 'id'))
                                    ->required()
                                    ->searchable(),

                                TextInput::make('slug')
                                    ->label('URL Slug')
                                    ->required()
                                    ->unique(
                                        Module::class,
                                        'slug',
                                        ignorable: fn () => $this->module
                                    )
                                    ->maxLength(255)
                                    ->rules(['alpha_dash'])
                                    ->afterStateUpdated(function (string $operation, $state, Set $set) {
                                        if ($operation !== 'create') {
                                            return;
                                        }

                                        $set('slug', Str::slug($state));
                                    }),

                                TextInput::make('minutes')
                                    ->label('Duration (minutes)')
                                    ->numeric()
                                    ->minValue(1)
                                    ->maxValue(180),

                                Textarea::make('description')
                                    ->label('Description')
                                    ->rows(3),
                            ])->columns(1),
                    ]),
            ])
            ->statePath('data');
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(Module::query())
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('topic.name')
                    ->label('Category')
                    ->searchable(query: function ($query, $search) {
                        return $query->whereHas('topic', function ($query) use ($search) {
                            $query->where('name', 'like', "%{$search}%");
                        });
                    })
                    ->sortable(query: function ($query, $direction) {
                        return $query
                            ->join('module_topic', 'modules.id', '=', 'module_topic.module_id')
                            ->join('topics', 'module_topic.topic_id', '=', 'topics.id')
                            ->orderBy('topics.name', $direction)
                            ->select('modules.*');
                    }),
                TextColumn::make('minutes')
                    ->label('Duration (minutes)')
                    ->sortable(),
                TextColumn::make('description')
                    ->limit(50)
                    ->searchable(),
                IconColumn::make('published')
                    ->boolean()
                    ->sortable(),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->defaultSort('created_at', 'desc')
            ->actions([
                Action::make('edit')
                    ->icon('heroicon-o-pencil-square')
                    ->action(fn (Module $record) => $this->edit($record)),
                Action::make('delete')
                    ->icon('heroicon-o-trash')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading('Delete Module')
                    ->modalDescription('Are you sure you want to delete this module? This action cannot be undone.')
                    ->modalSubmitActionLabel('Yes, delete it')
                    ->action(function (Module $record) {
                        try {
                            DB::beginTransaction();
                            $record->tests()->delete();
                            $record->clearMediaCollection('cover');
                            $record->delete();
                            DB::commit();

                            Notification::make()
                                ->success()
                                ->title('Success')
                                ->body('Module deleted successfully.')
                                ->send();
                        } catch (\Exception $e) {
                            DB::rollBack();
                            Notification::make()
                                ->danger()
                                ->title('Error')
                                ->body('Failed to delete module: ' . $e->getMessage())
                                ->send();
                        }
                    }),
            ]);
    }

    public function render(): View
    {
        return view('livewire.x-factor.modules-view');
    }
}
