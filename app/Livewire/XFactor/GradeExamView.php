<?php

namespace App\Livewire\XFactor;

use Livewire\Component;
use App\Models\Course;
use App\Models\TestAttempt;
use App\Enums\QuestionType;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use Illuminate\Contracts\View\View;
use Filament\Notifications\Notification;
use App\Enums\TestStatus;

class GradeExamView extends Component implements HasForms
{
    use InteractsWithForms;

    public Course $course;
    public TestAttempt $testAttempt;
    public array $data = [];
    public ?int $currentQuestionIndex = 0;
    public array $freeResponseQuestions = [];
    public array $freeResponseAnswers = [];

    public function mount(Course $course, TestAttempt $testAttempt): void
    {
        $this->course = $course;
        $this->testAttempt = $testAttempt->load([
            'test.questions' => function ($query) {
                $query->where('type', QuestionType::LongText)
                    ->with(['responses' => function ($query) {
                        $query->where('test_attempt_id', $this->testAttempt->id);
                    }]);
            },
        ]);

        // Load all free response questions and their responses
        $this->freeResponseQuestions = $this->testAttempt->test->questions->toArray();
        $this->freeResponseAnswers = $this->testAttempt->test->questions->mapWithKeys(function ($question) {
            return [$question->id => $question->responses->first()?->response ?? null];
        })->toArray();

        // Initialize form data with the multiple choice score as the minimum grade
        $multipleChoiceScore = $this->getCurrentGradeWithoutFreeResponseProperty();
        $this->data = [
            'grade' => max($testAttempt->score ?: $multipleChoiceScore, $multipleChoiceScore),
            'notes' => $testAttempt->feedback,
        ];
    }

    public function nextQuestion(): void
    {
        if ($this->currentQuestionIndex < count($this->freeResponseQuestions) - 1) {
            $this->currentQuestionIndex++;
        }
    }

    public function previousQuestion(): void
    {
        if ($this->currentQuestionIndex > 0) {
            $this->currentQuestionIndex--;
        }
    }

    public function getCurrentQuestionProperty()
    {
        return $this->freeResponseQuestions[$this->currentQuestionIndex] ?? null;
    }

    public function getCurrentAnswerProperty()
    {
        if (!$this->currentQuestion) {
            return null;
        }

        return $this->freeResponseAnswers[$this->currentQuestion['id']] ?? null;
    }

    public function getHasNextQuestionProperty(): bool
    {
        return $this->currentQuestionIndex < count($this->freeResponseQuestions) - 1;
    }

    public function getHasPreviousQuestionProperty(): bool
    {
        return $this->currentQuestionIndex > 0;
    }

    public function getTotalQuestionsProperty(): int
    {
        return count($this->freeResponseQuestions);
    }

    public function form(Form $form): Form
    {
        $multipleChoiceScore = $this->getCurrentGradeWithoutFreeResponseProperty();

        ray($multipleChoiceScore);

        return $form
            ->schema([
                TextInput::make('grade')
                    ->label('Grade')
                    ->numeric()
                    ->required()
                    ->minValue($multipleChoiceScore) // Prevent grades lower than multiple choice score
                    ->maxValue(100)
                    ->suffix('%')
                    ->maxWidth('xs')
                    ->helperText("Minimum grade is {$multipleChoiceScore}% (multiple choice score)"),
                Textarea::make('notes')
                    ->label('Notes or Feedback')
                    ->rows(4)
                    ->placeholder('Enter feedback for the student...'),
            ])
            ->columns(1)
            ->statePath('data');
    }

    public function save(): void
    {
        $data = $this->form->getState();
        $multipleChoiceScore = $this->getCurrentGradeWithoutFreeResponseProperty();

        // Double-check that grade isn't below multiple choice score
        if ($data['grade'] < $multipleChoiceScore) {
            $data['grade'] = $multipleChoiceScore;
        }

        // Update the test attempt with grading information
        $this->testAttempt->update([
            'score' => $data['grade'],
            'feedback' => $data['notes'],
            'graded_at' => now(),
            'status' => TestStatus::Graded,
        ]);

        Notification::make()
            ->success()
            ->title('Success')
            ->body('Exam graded successfully')
            ->send();
    }

    public function getPassingGradeProperty(): int
    {
        return $this->testAttempt->test->passing_score ?? 70;
    }

    public function getCurrentGradeWithoutFreeResponseProperty(): int
    {
        try {
            return $this->testAttempt->calculateMultipleChoiceScore();
        } catch (\Exception $e) {
            ray($e);
            return 0;
        }
    }

    public function getCurrentGradeWithFreeResponseProperty(): ?int
    {
        if (!$this->testAttempt->graded_at) {
            return null;
        }

        return $this->testAttempt->score;
    }

    public function getFreeResponseQuestionProperty()
    {
        return $this->testAttempt->test->questions->first();
    }

    public function getFreeResponseAnswerProperty()
    {
        if (!$this->freeResponseQuestion) {
            return null;
        }

        return $this->testAttempt->questionResponses
            ->where('question_id', $this->freeResponseQuestion->id)
            ->first();
    }

    public function render(): View
    {
        return view('livewire.x-factor.grade-exam-view');
    }
}
