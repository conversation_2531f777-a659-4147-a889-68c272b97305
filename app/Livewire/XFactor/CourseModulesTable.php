<?php

namespace App\Livewire\XFactor;

use Livewire\Component;
use App\Models\Course;
use App\Models\Module;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\DB;
use Filament\Notifications\Notification;

class CourseModulesTable extends Component implements HasTable, HasForms
{
    use InteractsWithTable;
    use InteractsWithForms;

    public Course $course;

    public function mount(Course $course): void
    {
        $this->course = $course;
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(
                Module::query()
                    ->select([
                        'modules.*',
                        'course_module.order'
                    ])
                    ->join('course_module', 'modules.id', '=', 'course_module.module_id')
                    ->where('course_module.course_id', $this->course->id)
            )
            ->columns([
                TextColumn::make('name')
                    ->label('Module Name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('type')
                    ->label('Type')
                    ->sortable(),
                TextColumn::make('order')
                    ->label('Order')
                    ->sortable(),
                IconColumn::make('published')
                    ->label('Status')
                    ->boolean()
                    ->sortable(),
            ])
            ->actions([
                Action::make('edit')
                    ->icon('heroicon-m-pencil-square')
                    ->url(fn(Module $record): string =>
                        route('filament.admin.pages.x-factor.courses.{course}.modules.{module}.edit', [
                            'course' => $this->course,
                            'module' => $record
                        ])
                    )
            ])
            ->reorderable('order')
            ->defaultSort('course_module.order', 'asc');
    }

    public function reorderTable(array $order): void
    {
        try {
            DB::beginTransaction();

            // First, set all orders to temporary high numbers to avoid conflicts
            $tempOrderStart = 100000;
            foreach ($order as $position => $item) {
                $moduleId = (int)$item;
                $tempOrder = $tempOrderStart + $position;

                $this->course->modules()->updateExistingPivot($moduleId, [
                    'order' => $tempOrder,
                ]);
            }

            // Then, set the final order numbers
            foreach ($order as $position => $item) {
                $moduleId = (int)$item;
                $finalOrder = $position + 1;

                $this->course->modules()->updateExistingPivot($moduleId, [
                    'order' => $finalOrder,
                ]);
            }

            DB::commit();

            Notification::make()
                ->success()
                ->title('Module order updated')
                ->send();

        } catch (\Exception $e) {
            DB::rollBack();

            ray('ERROR', $e);

            Notification::make()
                ->danger()
                ->title('Failed to update module order')
                ->body('An error occurred while updating the module order.')
                ->send();
        }
    }

    public function render(): View
    {
        return view('livewire.x-factor.course-modules-table');
    }
}
