<?php

namespace App\Livewire\XFactor;

use Livewire\Component;
use App\Models\Topic;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Contracts\View\View;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Grid;
use Filament\Forms\Form;
use Illuminate\Support\Facades\DB;
use Filament\Notifications\Notification;

class CategoriesView extends Component implements HasTable, HasForms
{
    use InteractsWithTable;
    use InteractsWithForms;

    public ?array $data = [];
    public ?Topic $topic = null;

    public function mount(): void
    {
        $this->form->fill([
            'name' => '',
        ]);
    }

    public function edit(Topic $topic): void
    {
        $this->topic = $topic;
        $this->data = [
            'name' => $topic->name,
        ];
        $this->form->fill($this->data);
        $this->dispatch('open-modal', id: 'manage-category');
    }

    public function delete(Topic $topic): void
    {
        try {
            DB::beginTransaction();

            // Check if topic has any associated modules or courses
            if ($topic->modules()->exists() || $topic->courses()->exists()) {
                throw new \Exception('Cannot delete category that has associated modules or courses.');
            }

            $topic->delete();

            DB::commit();

            Notification::make()
                ->success()
                ->title('Success')
                ->body('Category deleted successfully.')
                ->send();

        } catch (\Exception $e) {
            DB::rollBack();

            Notification::make()
                ->danger()
                ->title('Error')
                ->body('Failed to delete category: ' . $e->getMessage())
                ->send();
        }
    }

    public function deleteFromModal(): void
    {
        if (!$this->topic) {
            return;
        }

        try {
            DB::beginTransaction();

            // Check if topic has any associated modules or courses
            if ($this->topic->modules()->exists() || $this->topic->courses()->exists()) {
                throw new \Exception('Cannot delete category that has associated modules or courses.');
            }

            $this->topic->delete();

            DB::commit();

            // Reset form and topic
            $this->resetForm();

            $this->dispatch('close-modal', id: 'manage-category');

            Notification::make()
                ->success()
                ->title('Success')
                ->body('Category deleted successfully.')
                ->send();

        } catch (\Exception $e) {
            DB::rollBack();

            Notification::make()
                ->danger()
                ->title('Error')
                ->body('Failed to delete category: ' . $e->getMessage())
                ->send();
        }
    }

    public function save(): void
    {
        $data = $this->form->getState();

        try {
            DB::beginTransaction();

            $topic = $this->topic ?? new Topic();
            $topic->fill([
                'name' => $data['name'],
            ]);
            $topic->save();

            DB::commit();

            // Reset form and topic
            $this->resetForm();

            // Close modal and show success notification
            $this->dispatch('close-modal', id: 'manage-category');

            Notification::make()
                ->success()
                ->title('Success')
                ->body($this->topic ? 'Category updated successfully.' : 'Category created successfully.')
                ->send();

        } catch (\Exception $e) {
            DB::rollBack();

            Notification::make()
                ->danger()
                ->title('Error')
                ->body('Failed to save category: ' . $e->getMessage())
                ->send();
        }
    }

    private function resetForm(): void
    {
        $this->form->fill([
            'name' => '',
        ]);
        $this->data = [
            'name' => '',
        ];
        $this->topic = null;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make()
                    ->schema([
                        TextInput::make('name')
                            ->label('Category Name')
                            ->required()
                            ->maxLength(255)
                            ->unique(
                                Topic::class,
                                'name',
                                ignorable: fn () => $this->topic
                            )
                            ->live(),
                    ])
                    ->columns(1),
            ])
            ->statePath('data');
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(Topic::query())
            ->columns([
                TextColumn::make('name')
                    ->label('Category Name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('modules_count')
                    ->label('Modules')
                    ->counts('modules')
                    ->sortable(),
                TextColumn::make('courses_count')
                    ->label('Courses')
                    ->counts('courses')
                    ->sortable(),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->defaultSort('name')
            ->actions([
                Action::make('edit')
                    ->icon('heroicon-o-pencil-square')
                    ->action(fn (Topic $record) => $this->edit($record)),
                Action::make('delete')
                    ->icon('heroicon-o-trash')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading('Delete Category')
                    ->modalDescription('Are you sure you want to delete this category? This action cannot be undone.')
                    ->modalSubmitActionLabel('Yes, delete it')
                    ->action(fn (Topic $record) => $this->delete($record)),
            ]);
    }

    public function render(): View
    {
        return view('livewire.x-factor.categories-view');
    }
}
