<?php

namespace App\Livewire\XFactor;

use App\Enums\ModuleType;
use Livewire\Component;
use App\Models\Course;
use App\Models\TestAttempt;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Actions\Action;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Grid;

class CourseDetailView extends Component implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    public Course $course;
    public ?array $data = [];
    public $isDetailsExpanded = true;

    public function mount(Course $course): void
    {
        $this->course = $course;
        $this->updateFormData();
    }

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getTableQuery())
            ->columns([
                ImageColumn::make('avatar')
                    ->getStateUsing(fn(TestAttempt $record) => $record->user->getFirstMediaUrl('profile_photos'))
                    ->label('')
                    ->circular()
                    ->width(40)
                    ->height(40)
                    ->alignment('center')
                    ->grow(false),
                TextColumn::make('name')
                    ->getStateUsing(fn(TestAttempt $record) => $record->user->first_name . ' ' . $record->user->last_name)
                    ->label('Name')
                    ->searchable(['first_name', 'last_name'])
                    ->sortable()
                    ->alignment('start')
                    ->grow(false),
                TextColumn::make('user.graduation_year')
                    ->label('HS Grad Year')
                    ->sortable()
                    ->alignment('center')
                    ->grow(false),
                TextColumn::make('user.state_code')
                    ->label('State')
                    ->sortable()
                    ->alignment('start')
                    ->grow(false),
                TextColumn::make('')
                    ->grow(true),

            ])
            ->actions([
                Action::make('grade')
                    ->label('Grade')
                    ->icon('heroicon-m-pencil-square')
                    ->button()
                    ->size('sm')
                    ->url(function (TestAttempt $record) {
                        return route('filament.admin.pages.x-factor.courses.{course}.grade-exam.{testAttempt}', [
                            'course' => $this->course,
                            'testAttempt' => $record,
                        ]);
                    })
            ])
            ->defaultSort('created_at', 'desc')
            ->paginated(false);
    }

    protected function getTableQuery(): Builder
    {
        // Get all exam modules for the course
        $examModules = $this->course->modules()
            ->where('type', ModuleType::Exam)
            ->with('test')
            ->get();

        // Get all test IDs from exam modules that have a test
        $testIds = $examModules->pluck('test.id')->filter();

        // If no exam modules with tests exist, return empty collection
        if ($testIds->isEmpty()) {
            return TestAttempt::query()->whereRaw('1 = 0');
        }

        return TestAttempt::query()
            ->whereIn('test_id', $testIds)
            ->whereIn('status', ['complete', 'pending_review'])
            ->whereNull('graded_at')
            ->whereHas('test.questions', function ($query) {
                $query->whereHas('responses', function ($query) {
                    $query->whereColumn('test_attempt_id', 'test_attempts.id');
                });
            }, '=', function ($query) {
                $query->select(DB::raw('count(*)'))
                    ->from('questions')
                    ->whereColumn('test_id', 'test_attempts.test_id');
            })
            ->with(['user' => function ($query) {
                $query->with(['media' => fn($query) => $query->where('collection_name', 'profile_photos')])
                    ->select('id', 'first_name', 'last_name', 'graduation_year', 'state_code');
            }]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->model($this->course)
            ->schema([
                Grid::make()
                    ->columns(3)
                    ->schema([
                        Grid::make()
                            ->columns(2)
                            ->columnSpan(2)
                            ->schema([
                                FileUpload::make('cover')
                                    ->label('Course Banner')
                                    ->image()
                                    ->imageEditor()
                                    ->disk('public')
                                    ->visibility('public')
                                    ->downloadable()
                                    ->openable()
                                    ->preserveFilenames()
                                    ->imagePreviewHeight('250')
                                    ->panelAspectRatio('2:1')
                                    ->panelLayout('integrated')
                                    ->removeUploadedFileButtonPosition('right')
                                    ->uploadProgressIndicatorPosition('left')
                                    ->columnSpan(2),
                            ]),

                        Grid::make()
                            ->columnSpan(1)
                            ->columns(1)
                            ->schema([
                                TextInput::make('title')
                                    ->label('Course Title')
                                    ->required()
                                    ->maxLength(255)
                                    ->columnSpan(1),

                                Textarea::make('description')
                                    ->label('Description')
                                    ->required()
                                    ->rows(4)
                                    ->columnSpan(1),

                                TextInput::make('presenter')
                                    ->label('Presenter')
                                    ->maxLength(255)
                                    ->columnSpan(1),

                                Toggle::make('published')
                                    ->label('Published')
                                    ->default(false)
                                    ->inline(false),

                                Toggle::make('featured')
                                    ->label('Featured Course')
                                    ->default(false)
                                    ->inline(false),

                                Select::make('topics')
                                    ->label('Categories')
                                    ->relationship('topics', 'name')
                                    ->preload()
                                    ->searchable(),

                                TextInput::make('order')
                                    ->label('Display Order')
                                    ->numeric()
                                    ->minValue(0),
                            ]),
                    ]),
            ])
            ->statePath('data');
    }

    protected function updateFormData(): void
    {
        // Get the existing cover image if it exists
        $coverMedia = $this->course->getFirstMedia('cover');
        $existingCover = null;

        if ($coverMedia) {
            $existingCover = [
                'name' => $coverMedia->file_name,
                'size' => $coverMedia->size,
                'type' => $coverMedia->mime_type,
                'path' => $coverMedia->getPathRelativeToRoot(),
            ];
        }

        $this->form->fill([
            'title' => $this->course->title,
            'presenter' => $this->course->presenter,
            'description' => $this->course->description,
            'order' => $this->course->order,
            'published' => $this->course->published,
            'featured' => $this->course->featured,
            'topics' => $this->course->topics->pluck('id')->toArray(),
            'cover' => !empty($existingCover['path']) ? $existingCover['path'] : null,
        ]);
    }

    public function toggleDetails(): void
    {
        $this->isDetailsExpanded = !$this->isDetailsExpanded;
    }

    public function saveChanges(): void
    {
        $data = $this->form->getState();

        try {
            DB::beginTransaction();

            $this->course->fill([
                'title' => $data['title'],
                'presenter' => $data['presenter'],
                'description' => $data['description'],
                'order' => $data['order'],
                'published' => $data['published'],
                'featured' => $data['featured'],
            ]);

            // Handle cover image
            if (!empty($data['cover'])) {
                // Check if this is a new upload by comparing with the current image path
                $currentMedia = $this->course->getFirstMedia('cover');
                $isNewUpload = !$currentMedia || $data['cover'] !== $currentMedia->getPathRelativeToRoot();

                if ($isNewUpload) {
                    // Clear existing media first
                    $this->course->clearMediaCollection('cover');
                    // Add the new image
                    $this->course->addMedia(storage_path('app/public/' . $data['cover']))
                        ->toMediaCollection('cover');
                }
            } else {
                // If cover was removed, clear the media
                $this->course->clearMediaCollection('cover');
            }

            // Sync relationships
            if (isset($data['topics'])) {
                $this->course->topics()->sync($data['topics']);
            }

            $this->course->save();

            DB::commit();

            // Update the form data to reflect the new state
            $this->updateFormData();

            Notification::make()
                ->success()
                ->title('Success')
                ->body('Course details updated successfully.')
                ->send();
        } catch (\Exception $e) {
            DB::rollBack();

            Notification::make()
                ->danger()
                ->title('Error')
                ->body('Failed to save course: ' . $e->getMessage())
                ->send();
        }
    }

    public function render(): View
    {
        $coverImageUrl = $this->course->getFirstMediaUrl('cover', 'full');

        return view('livewire.x-factor.course-detail-view', [
            'coverImageUrl' => $coverImageUrl,
        ]);
    }
}
