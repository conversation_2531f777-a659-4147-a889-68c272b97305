<?php

namespace App\Livewire\XFactor;

use Livewire\Component;
use App\Models\Course;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Contracts\View\View;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Form;
use Illuminate\Support\Facades\DB;
use Filament\Notifications\Notification;
use Filament\Forms\Components\FileUpload;

class CoursesView extends Component implements HasTable, HasForms
{
    use InteractsWithTable;
    use InteractsWithForms;

    public ?array $data = [];
    public ?Course $course = null;

    public function mount(): void
    {
        $this->form->fill();
    }

    public function resetForm(): void
    {
        $this->form->fill();
        $this->course = null;
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(Course::query())
            ->columns([
                TextColumn::make('title')
                    ->searchable()
                    ->sortable()
                    ->url(fn (Course $record): string => route('filament.admin.pages.x-factor.courses.{course}', ['course' => $record->id]))
                    ->openUrlInNewTab(false),
                TextColumn::make('presenter')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('topics.name')
                    ->label('Category')
                    ->sortable(),
                TextColumn::make('modules_count')
                    ->label('Modules')
                    ->counts('modules')
                    ->alignCenter()
                    ->sortable(),
                IconColumn::make('published')
                    ->boolean()
                    ->sortable(),
                IconColumn::make('featured')
                    ->boolean()
                    ->sortable(),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->defaultSort('created_at', 'desc')
            ->actions([
                Action::make('view')
                    ->icon('heroicon-o-eye')
                    ->url(fn(Course $record): string => route('filament.admin.pages.x-factor.courses.{course}', ['course' => $record->id]))
                    ->openUrlInNewTab(false),
                Action::make('edit')
                    ->icon('heroicon-o-pencil-square')
                    ->action(fn(Course $record) => $this->edit($record)),
                Action::make('delete')
                    ->icon('heroicon-o-trash')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading('Delete Course')
                    ->modalDescription('Are you sure you want to delete this course? This action cannot be undone.')
                    ->modalSubmitActionLabel('Yes, delete it')
                    ->action(fn(Course $record) => $this->delete($record)),
            ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->model(Course::class)
            ->schema([
                Grid::make()
                    ->columns(2)
                    ->schema([
                        Section::make('Course Details')
                            ->columnSpan(1)
                            ->schema([
                                TextInput::make('title')
                                    ->label('Course Title')
                                    ->required()
                                    ->maxLength(255),

                                TextInput::make('presenter')
                                    ->label('Presenter')
                                    ->maxLength(255),

                                Textarea::make('description')
                                    ->label('Description')
                                    ->required()
                                    ->rows(4),

                                TextInput::make('order')
                                    ->label('Display Order')
                                    ->numeric()
                                    ->minValue(0),

                                FileUpload::make('cover')
                                    ->label('Course Banner')
                                    ->image()
                                    ->imageEditor()
                                    ->disk('public')
                                    ->visibility('public')
                                    ->downloadable()
                                    ->openable()
                                    ->preserveFilenames()
                                    ->imagePreviewHeight('250')
                                    ->panelAspectRatio('2:1')
                                    ->panelLayout('integrated')
                                    ->removeUploadedFileButtonPosition('right')
                                    ->uploadProgressIndicatorPosition('left'),
                            ]),

                        Section::make('Settings')
                            ->columnSpan(1)
                            ->schema([
                                Toggle::make('published')
                                    ->label('Published')
                                    ->default(false)
                                    ->inline(false),

                                Toggle::make('featured')
                                    ->label('Featured Course')
                                    ->default(false)
                                    ->inline(false),

                                Select::make('topics')
                                    ->label('Categories')
                                    ->relationship('topics', 'name')
                                    ->preload()
                                    ->searchable(),
                            ]),
                    ]),
            ])
            ->statePath('data');
    }

    public function edit(Course $course): void
    {
        $this->course = $course;

        // Get the existing cover image if it exists
        $coverMedia = $course->getFirstMedia('cover');
        $existingCover = null;

        if ($coverMedia) {
            $existingCover = [
                'name' => $coverMedia->file_name,
                'size' => $coverMedia->size,
                'type' => $coverMedia->mime_type,
                'path' => $coverMedia->getPathRelativeToRoot(),
            ];
        }

        $this->form->fill([
            'title' => $course->title,
            'presenter' => $course->presenter,
            'description' => $course->description,
            'order' => $course->order,
            'published' => $course->published,
            'featured' => $course->featured,
            'topics' => $course->topics->pluck('id')->toArray(),
            'cover' => !empty($existingCover['path']) ? $existingCover['path'] : null,
        ]);

        $this->dispatch('open-modal', id: 'manage-course');
    }

    public function save(): void
    {
        $data = $this->form->getState();

        try {
            DB::beginTransaction();

            $course = $this->course ?? new Course();
            $course->fill([
                'title' => $data['title'],
                'presenter' => $data['presenter'],
                'description' => $data['description'],
                'order' => $data['order'],
                'published' => $data['published'],
                'featured' => $data['featured'],
            ]);
            $course->save();

            // Handle cover image
            if (!empty($data['cover'])) {
                // Check if this is a new upload by comparing with the current image path
                $currentMedia = $course->getFirstMedia('cover');
                $isNewUpload = !$currentMedia || $data['cover'] !== $currentMedia->getPathRelativeToRoot();

                if ($isNewUpload) {
                    // Clear existing media first
                    $course->clearMediaCollection('cover');
                    // Add the new image
                    $course->addMedia(storage_path('app/public/' . $data['cover']))
                        ->toMediaCollection('cover');
                }
            } else {
                // If cover was removed, clear the media
                $course->clearMediaCollection('cover');
            }

            // Sync relationships
            if (isset($data['topics'])) {
                $course->topics()->sync($data['topics']);
            }

            DB::commit();

            // Reset form and course
            $this->resetForm();

            // Close modal and show success notification
            $this->dispatch('close-modal', id: 'manage-course');

            Notification::make()
                ->success()
                ->title('Success')
                ->body($this->course ? 'Course updated successfully.' : 'Course created successfully.')
                ->send();
        } catch (\Exception $e) {
            DB::rollBack();

            Notification::make()
                ->danger()
                ->title('Error')
                ->body('Failed to save course: ' . $e->getMessage())
                ->send();
        }
    }

    public function delete(Course $course): void
    {
        try {
            DB::beginTransaction();

            // Delete relationships first
            $course->topics()->detach();
            $course->modules()->detach();
            $course->delete();

            DB::commit();

            Notification::make()
                ->success()
                ->title('Success')
                ->body('Course deleted successfully.')
                ->send();
        } catch (\Exception $e) {
            DB::rollBack();

            Notification::make()
                ->danger()
                ->title('Error')
                ->body('Failed to delete course: ' . $e->getMessage())
                ->send();
        }
    }

    public function render(): View
    {
        return view('livewire.x-factor.courses-view');
    }
}
