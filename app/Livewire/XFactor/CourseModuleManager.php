<?php

namespace App\Livewire\XFactor;

use Livewire\Component;
use App\Models\Module;
use App\Models\Course;
use App\Models\Test;
use App\Models\Question;
use App\Models\Answer;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Form;
use Filament\Forms\Set;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Filament\Notifications\Notification;
use Illuminate\Contracts\View\View;
use Filament\Forms\Components\Radio;
use Filament\Forms\Get;
use Filament\Forms\Components\RichEditor;
use App\Enums\QuestionType;
use App\Enums\ModuleType;
use App\Rules\VideoTimeRange;

class CourseModuleManager extends Component implements HasForms
{
    use InteractsWithForms;

    public ?array $data = [];
    public ?Module $module = null;
    public Course $course;

    public function mount(Course $course, ?Module $module = null): void
    {
        $this->course = $course;

        if ($module && $module->exists) {
            $this->edit($module);
        } else {
            $this->form->fill();
        }

        if ($this->module) {
            $this->form->fill([
                'name' => $this->module->name,
                'description' => $this->module->description,
                'type' => $this->module->type->value,
                'minutes' => $this->module->minutes,
                'published' => $this->module->published,
                'video_url' => $this->module->video_url,
                'video_start_time' => $this->module->video_start_time,
                'video_end_time' => $this->module->video_end_time,
                'content' => $this->module->content,
            ]);
        }
    }

    public function determineModuleType(Module $module): ModuleType
    {
        // If the module already has a type set, use it
        if ($module->type instanceof ModuleType) {
            return $module->type;
        }

        // Check if it's an exam
        if ($module->test && $module->test->type === 'exam') {
            return ModuleType::Exam;
        }

        // Check if it's a video
        if ($module->video_url) {
            return ModuleType::Video;
        }

        // If it has content, it's an article
        if ($module->content) {
            return ModuleType::Article;
        }

        // Default to video for new modules
        return ModuleType::Video;
    }

    public function edit(Module $module): void
    {
        $this->module = $module;

        // Get the test data if it exists
        $test = $module->test;
        $questions = [];

        if ($test) {
            foreach ($test->questions as $question) {
                $answers = [];
                if ($question->type === QuestionType::MultipleChoice) {
                    foreach ($question->answers as $answer) {
                        $answers[] = [
                            'answer' => $answer->answer,
                            'is_correct' => $answer->is_correct,
                        ];
                    }
                }

                $questions[] = [
                    'question' => $question->question,
                    'type' => $question->type->value,
                    'answers' => $answers,
                ];
            }
        }

        // Get the existing cover image if it exists
        $coverMedia = $module->getFirstMedia('cover');
        $existingCover = null;

        if ($coverMedia) {
            $existingCover = [
                'name' => $coverMedia->file_name,
                'size' => $coverMedia->size,
                'type' => $coverMedia->mime_type,
                'path' => $coverMedia->getPathRelativeToRoot(),
            ];
        }

        // Build form data based on module type
        $moduleType = $this->determineModuleType($module);

        $formData = [
            'name' => $module->name,
            'slug' => $module->slug,
            'description' => $module->description,
            'published' => $module->published,
            'type' => $moduleType,
            'minutes' => $module->minutes,
            'questions' => $questions,
            'cover' => !empty($existingCover['path']) ? $existingCover['path'] : null,
            'video_url' => $module->video_url,
            'video_start_time' => $module->video_start_time,
            'video_end_time' => $module->video_end_time,
            'content' => $module->content,
            'passing_score' => $test?->passing_score ?? 70,
            'time_limit' => $test?->time_limit ?? 3600, // Default to 60 minutes for exams
        ];

        $this->form->fill($formData);
    }

    public function save(): void
    {
        $data = $this->form->getState();

        try {
            DB::beginTransaction();

            $module = $this->module ?? new Module();

            // Common fields for all types
            $moduleData = [
                'name' => $data['name'],
                'slug' => $data['slug'],
                'description' => $data['description'],
                'published' => $data['published'],
            ];

            // Set the type using ModuleType enum
            $moduleData['type'] = match($this->module ? $this->determineModuleType($this->module) : $data['type']) {
                'video', ModuleType::Video => ModuleType::Video,
                'article', ModuleType::Article => ModuleType::Article,
                'exam', ModuleType::Exam => ModuleType::Exam,
                default => ModuleType::Video,
            };

            // Type-specific fields
            switch ($moduleData['type']) {
                case ModuleType::Video:
                    $moduleData['video_url'] = $data['video_url'];
                    $moduleData['video_start_time'] = $data['video_start_time'] ?? null;
                    $moduleData['video_end_time'] = $data['video_end_time'] ?? null;
                    $moduleData['content'] = null;
                    $moduleData['minutes'] = $data['minutes'];
                    break;
                case ModuleType::Article:
                    $moduleData['content'] = $data['content'];
                    $moduleData['video_url'] = null;
                    $moduleData['minutes'] = $data['minutes'];
                    break;
                case ModuleType::Exam:
                    $moduleData['content'] = null;
                    $moduleData['video_url'] = null;
                    $moduleData['minutes'] = null;
                    break;
            }

            $module->fill($moduleData);
            $module->save();

            // Handle course relationship
            if (!$this->module) {
                // If this is a new module, attach it to the course
                // Get the highest order number and add 1
                $maxOrder = $this->course->modules()->max('course_module.order') ?? 0;
                $this->course->modules()->attach($module->id, ['order' => $maxOrder + 1]);
            }

            // Handle cover image
            if (!empty($data['cover'])) {
                // Check if this is a new upload by comparing with the current image path
                $currentMedia = $module->getFirstMedia('cover');
                $isNewUpload = !$currentMedia || $data['cover'] !== $currentMedia->getPathRelativeToRoot();

                if ($isNewUpload) {
                    // Clear existing media first
                    $module->clearMediaCollection('cover');
                    // Add the new image
                    $module->addMedia(storage_path('app/public/' . $data['cover']))
                        ->toMediaCollection('cover');
                }
            } else {
                // If cover was removed, clear the media
                $module->clearMediaCollection('cover');
            }

            // Handle quiz/exam questions
            if (!empty($data['questions'])) {
                // Delete existing test if editing
                if ($this->module) {
                    $module->tests()->delete();
                }

                $test = new Test();
                $test->fill([
                    'type' => $moduleData['type'] === ModuleType::Exam ? 'exam' : 'quiz',
                    'testable_type' => Module::class,
                    'testable_id' => $module->id,
                    'passing_score' => $moduleData['type'] === ModuleType::Exam ? $data['passing_score'] : null,
                    'time_limit' => $moduleData['type'] === ModuleType::Exam
                        ? ($data['time_limit_in_minutes'] ? ($data['time_limit'] * 60) : $data['time_limit'])
                        : null,
                ]);
                $test->save();

                foreach ($data['questions'] as $questionData) {
                    $question = new Question();
                    $question->fill([
                        'test_id' => $test->id,
                        'question' => $questionData['question'],
                        'type' => $questionData['type'] ?? QuestionType::MultipleChoice->value,
                    ]);
                    $question->save();

                    // Only create answers for multiple choice questions
                    if ($question->type === QuestionType::MultipleChoice && !empty($questionData['answers'])) {
                        foreach ($questionData['answers'] as $answerData) {
                            $answer = new Answer();
                            $answer->fill([
                                'question_id' => $question->id,
                                'answer' => $answerData['answer'],
                                'is_correct' => $answerData['is_correct'],
                            ]);
                            $answer->save();
                        }
                    }
                }
            }

            DB::commit();

            // Reset form and module
            $this->form->fill();
            $this->module = null;

            // Show success notification and redirect
            Notification::make()
                ->success()
                ->title('Success')
                ->body($module->wasRecentlyCreated ? 'Module created successfully.' : 'Module updated successfully.')
                ->send();

            $this->redirect(route('filament.admin.pages.x-factor.courses.{course}', ['course' => $this->course]));
        } catch (\Exception $e) {
            DB::rollBack();

            Notification::make()
                ->danger()
                ->title('Error')
                ->body('Failed to save module: ' . $e->getMessage())
                ->send();
        }
    }

    public function delete(): void
    {
        try {
            DB::beginTransaction();

            // Detach from course first
            $this->course->modules()->detach($this->module->id);

            // Delete associated tests and media
            $this->module->tests()->delete();
            $this->module->clearMediaCollection('cover');
            $this->module->delete();

            DB::commit();

            // Reset form and module
            $this->form->fill();
            $this->module = null;

            $this->dispatch('close-modal', id: 'manage-course-module');
            $this->dispatch('module-deleted'); // For refreshing the modules table

            Notification::make()
                ->success()
                ->title('Success')
                ->body('Module deleted successfully.')
                ->send();
        } catch (\Exception $e) {
            DB::rollBack();

            Notification::make()
                ->danger()
                ->title('Error')
                ->body('Failed to delete module: ' . $e->getMessage())
                ->send();
        }
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make()
                    ->columns(3)
                    ->schema([
                        Grid::make()
                            ->columnSpan(2)
                            ->schema([
                                Section::make('Module Thumbnail')
                                    ->schema([
                                        FileUpload::make('cover')
                                            ->label('Module Cover')
                                            ->image()
                                            ->imageEditor()
                                            ->disk('public')
                                            ->visibility('public')
                                            ->columnSpan('full')
                                            ->downloadable()
                                            ->openable()
                                            ->preserveFilenames()
                                            ->imagePreviewHeight('250')
                                            ->panelAspectRatio('2:1')
                                            ->panelLayout('integrated')
                                            ->removeUploadedFileButtonPosition('right')
                                            ->uploadProgressIndicatorPosition('left'),
                                    ])
                                    ->collapsible(),

                                Section::make('Content')
                                    ->schema([
                                        TextInput::make('video_url')
                                            ->label('Video URL')
                                            ->url()
                                            ->required()
                                            ->placeholder('https://www.youtube.com/watch?v=...')
                                            ->columnSpan('full')
                                            ->visible(
                                                fn(Get $get): bool =>
                                                !$this->module
                                                    ? $get('type') === ModuleType::Video->value
                                                    : $this->determineModuleType($this->module) === ModuleType::Video
                                            ),

                                        Grid::make()
                                            ->columns(2)
                                            ->schema([
                                                TextInput::make('video_start_time')
                                                    ->label('Video Start Time (seconds)')
                                                    ->numeric()
                                                    ->minValue(0)
                                                    ->placeholder('e.g., 30')
                                                    ->helperText('Optional. Time in seconds from the start of the video.')
                                                    ->visible(
                                                        fn(Get $get): bool =>
                                                        !$this->module
                                                            ? $get('type') === ModuleType::Video->value
                                                            : $this->determineModuleType($this->module) === ModuleType::Video
                                                    ),

                                                TextInput::make('video_end_time')
                                                    ->label('Video End Time (seconds)')
                                                    ->numeric()
                                                    ->minValue(0)
                                                    ->placeholder('e.g., 120')
                                                    ->helperText('Optional. Time in seconds from the start of the video.')
                                                    ->rules([new VideoTimeRange()])
                                                    ->visible(
                                                        fn(Get $get): bool =>
                                                        !$this->module
                                                            ? $get('type') === ModuleType::Video->value
                                                            : $this->determineModuleType($this->module) === ModuleType::Video
                                                    ),
                                            ]),

                                        RichEditor::make('content')
                                            ->toolbarButtons([
                                                'bold',
                                                'italic',
                                                'underline',
                                                'link',
                                                'bulletList',
                                                'orderedList',
                                                'h2',
                                                'h3',
                                                'attachFiles'
                                            ])
                                            ->fileAttachmentsDisk('public')
                                            ->fileAttachmentsDirectory('article-images')
                                            ->fileAttachmentsVisibility('public')
                                            ->required()
                                            ->visible(
                                                fn(Get $get): bool =>
                                                !$this->module
                                                    ? $get('type') === ModuleType::Article->value
                                                    : $this->determineModuleType($this->module) === ModuleType::Article
                                            ),
                                    ])
                                    ->collapsible()
                                    ->visible(
                                        fn(Get $get): bool =>
                                        !$this->module
                                            ? in_array($get('type'), [ModuleType::Video->value, ModuleType::Article->value])
                                            : in_array($this->determineModuleType($this->module), [ModuleType::Video, ModuleType::Article])
                                    ),

                                Section::make('Questions')
                                    ->schema([
                                        Repeater::make('questions')
                                            ->schema([
                                                Grid::make()
                                                    ->schema([
                                                        Radio::make('type')
                                                            ->label('Question Type')
                                                            ->options([
                                                                'multiple_choice' => 'Multiple Choice',
                                                                'long_text' => 'Free Response',
                                                            ])
                                                            ->default('multiple_choice')
                                                            ->live()
                                                            ->visible(fn(Get $get): bool =>
                                                                !$this->module
                                                                    ? $get('../../type') === ModuleType::Exam->value
                                                                    : $this->determineModuleType($this->module) === ModuleType::Exam
                                                            )
                                                            ->columnSpan(1),

                                                        TextInput::make('question')
                                                            ->label('Question')
                                                            ->required()
                                                            ->maxLength(255)
                                                            ->visible(fn(Get $get): bool => $get('type') === 'multiple_choice')
                                                            ->columnSpan(2),

                                                        Textarea::make('question')
                                                            ->label('Question')
                                                            ->required()
                                                            ->maxLength(2000)
                                                            ->rows(4)
                                                            ->visible(fn(Get $get): bool => $get('type') === 'long_text')
                                                            ->placeholder('Enter your free response question here...')
                                                            ->helperText('Students will be able to write a detailed response to this question.')
                                                            ->columnSpan(2),
                                                    ])
                                                    ->columns(3)
                                                    ->columnSpan('full'),

                                                Repeater::make('answers')
                                                    ->schema([
                                                        TextInput::make('answer')
                                                            ->label('Answer Option')
                                                            ->required()
                                                            ->maxLength(255),

                                                        Toggle::make('is_correct')
                                                            ->label('Correct Answer')
                                                            ->inline(false)
                                                            ->default(false)
                                                            ->afterStateUpdated(function ($state, Set $set, $context) {
                                                                if ($state) {
                                                                    $answers = data_get($context, 'answers', []);
                                                                    foreach ($answers as $key => $answer) {
                                                                        if ($key !== $context->key) {
                                                                            $set("answers.{$key}.is_correct", false);
                                                                        }
                                                                    }
                                                                }
                                                            }),
                                                    ])
                                                    ->columns(2)
                                                    ->minItems(2)
                                                    ->maxItems(4)
                                                    ->addActionLabel('Add Answer Option')
                                                    ->columnSpan('full')
                                                    ->visible(fn(Get $get): bool => $get('type') === 'multiple_choice'),
                                            ])
                                            ->collapsible()
                                            ->itemLabel(fn(array $state): ?string => $state['question'] ?? null)
                                            ->addActionLabel('Add Question')
                                            ->defaultItems(0)
                                    ])
                                    ->collapsible()
                                    ->label(
                                        fn(Get $get): string =>
                                        !$this->module
                                            ? ($get('type') === ModuleType::Exam->value ? 'Exam Questions' : 'Quiz Questions')
                                            : ($this->determineModuleType($this->module) === ModuleType::Exam ? 'Exam Questions' : 'Quiz Questions')
                                    ),
                            ]),

                        Section::make('Details')
                            ->columnSpan(1)
                            ->schema([
                                Toggle::make('published')
                                    ->label('Published')
                                    ->default(false)
                                    ->inline(false),

                                TextInput::make('name')
                                    ->label('Module Name')
                                    ->required()
                                    ->maxLength(255),

                                TextInput::make('slug')
                                    ->label('URL Slug')
                                    ->required()
                                    ->unique(
                                        Module::class,
                                        'slug',
                                        ignorable: fn() => $this->module
                                    )
                                    ->maxLength(255)
                                    ->rules(['alpha_dash'])
                                    ->afterStateUpdated(function (string $operation, $state, Set $set) {
                                        if ($operation !== 'create') {
                                            return;
                                        }

                                        $set('slug', Str::slug($state));
                                    }),

                                Radio::make('type')
                                    ->label(false)
                                    ->options([
                                        ModuleType::Video->value => 'Video',
                                        ModuleType::Article->value => 'Article',
                                        ModuleType::Exam->value => 'Exam',
                                    ])
                                    ->default(ModuleType::Video->value)
                                    ->live()
                                    ->visible(fn(): bool => !$this->module),

                                TextInput::make('minutes')
                                    ->label('Time to Complete (minutes)')
                                    ->numeric()
                                    ->minValue(1)
                                    ->required()
                                    ->visible(
                                        fn(Get $get): bool =>
                                        !$this->module
                                            ? in_array($get('type'), [ModuleType::Video->value, ModuleType::Article->value])
                                            : in_array($this->determineModuleType($this->module), [ModuleType::Video, ModuleType::Article])
                                    )
                                    ->helperText('Estimated time to watch video or read article'),

                                TextInput::make('passing_score')
                                    ->label('Passing Score (%)')
                                    ->numeric()
                                    ->minValue(0)
                                    ->maxValue(100)
                                    ->default(70)
                                    ->required()
                                    ->visible(
                                        fn(Get $get): bool =>
                                        !$this->module
                                            ? $get('type') === ModuleType::Exam->value
                                            : $this->determineModuleType($this->module) === ModuleType::Exam
                                    )
                                    ->helperText('Score required to pass the exam'),

                                Grid::make()
                                    ->schema([
                                        TextInput::make('time_limit')
                                            ->label('Time Limit')
                                            ->numeric()
                                            ->minValue(fn (Get $get): int => $get('time_limit_in_minutes') ? 5 : 300)
                                            ->default(3600)
                                            ->required()
                                            ->live(onBlur: true)
                                            ->visible(
                                                fn(Get $get): bool =>
                                                !$this->module
                                                    ? $get('type') === ModuleType::Exam->value
                                                    : $this->determineModuleType($this->module) === ModuleType::Exam
                                            )
                                            ->helperText(fn (Get $get): string =>
                                                $get('time_limit_in_minutes')
                                                    ? 'Enter time in minutes'
                                                    : 'Enter time in seconds'
                                            ),

                                        Toggle::make('time_limit_in_minutes')
                                            ->label('Show in Minutes')
                                            ->default(true)
                                            ->live()
                                            ->inline()
                                            ->afterStateUpdated(function ($state, Set $set, Get $get) {
                                                $currentValue = $get('time_limit');
                                                if (!$currentValue) return;

                                                // If toggling to minutes, divide by 60
                                                // If toggling to seconds, multiply by 60
                                                $newValue = $state
                                                    ? $currentValue / 60  // Converting to minutes
                                                    : $currentValue * 60; // Converting to seconds

                                                $set('time_limit', $newValue);
                                            })
                                            ->visible(
                                                fn(Get $get): bool =>
                                                !$this->module
                                                    ? $get('type') === ModuleType::Exam->value
                                                    : $this->determineModuleType($this->module) === ModuleType::Exam
                                            )
                                    ])
                                    ->columns(2),

                                Textarea::make('description')
                                    ->label('Description')
                                    ->rows(3),

                            ])->columns(1),
                    ]),
            ])
            ->statePath('data');
    }

    public function render(): View
    {
        return view('livewire.x-factor.course-module-manager');
    }
}
