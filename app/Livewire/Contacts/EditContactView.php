<?php

namespace App\Livewire\Contacts;

use App\Models\Contact;
use App\Models\School;
use App\Models\State;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Livewire\Component;

class EditContactView extends Component implements HasForms
{
    use InteractsWithForms;

    public Contact $contact;
    public ?array $data = [];

    public function mount(Contact $contact): void
    {
        $this->contact = $contact;
        ray($contact->state_id);
        $this->form->fill([
            'first_name' => $contact->first_name,
            'last_name' => $contact->last_name,
            'email' => $contact->email,
            'phone' => $contact->phone,
            'state_id' => $contact->state_id,
            'school_id' => $contact->school_id,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make(2)
                    ->schema([
                        TextInput::make('first_name')
                            ->required()
                            ->maxLength(255)
                            ->live(onBlur: true)
                            ->afterStateUpdated(function($state) {
                                $this->contact->update(['first_name' => $state]);
                            }),
                        TextInput::make('last_name')
                            ->required()
                            ->maxLength(255)
                            ->live(onBlur: true)
                            ->afterStateUpdated(function($state) {
                                $this->contact->update(['last_name' => $state]);
                            }),
                        TextInput::make('email')
                            ->email()
                            ->required()
                            ->maxLength(255)
                            ->live(onBlur: true)
                            ->afterStateUpdated(function($state) {
                                $this->contact->update(['email' => $state]);
                            }),
                        TextInput::make('phone')
                            ->tel()
                            ->required()
                            ->maxLength(20)
                            ->live(onBlur: true)
                            ->afterStateUpdated(function($state) {
                                $this->contact->update(['phone' => $state]);
                            }),
                        Select::make('state_id')
                            ->options(State::pluck('name', 'code'))
                            ->required()
                            ->live()
                            ->afterStateUpdated(function($state) {
                                ray($state);
                                $this->contact->update(['state_id' => $state]);
                            }),
                        Select::make('school_id')
                            ->label('School')
                            ->required()
                            ->searchable()
                            ->getSearchResultsUsing(function (string $search, Get $get): array {
                                $state = $get('state');

                                return School::query()
                                    ->when(
                                        $state,
                                        fn ($query) => $query->where('state', $state)
                                    )
                                    ->where('name', 'ilike', "%{$search}%")
                                    ->limit(50)
                                    ->pluck('name', 'id')
                                    ->toArray();
                            })
                            ->getOptionLabelUsing(fn ($value): ?string => School::find($value)?->name)
                            ->preload(false)
                            ->live()
                            ->afterStateUpdated(function($state) {
                                $this->contact->update(['school_id' => $state]);
                            }),
                    ]),
            ])
            ->statePath('data');
    }

    public function render()
    {
        return view('livewire.contacts.edit-contact-view');
    }
}
