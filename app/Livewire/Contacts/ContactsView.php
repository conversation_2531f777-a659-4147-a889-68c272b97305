<?php

namespace App\Livewire\Contacts;

use App\Models\Contact;
use App\Models\Region;
use App\Models\State;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Forms\Form;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Illuminate\Contracts\View\View;
use Livewire\Component;
use Filament\Notifications\Notification;
use App\Models\School;
use Filament\Tables\Actions\Action;

class ContactsView extends Component implements HasTable, HasForms
{
    use InteractsWithTable, InteractsWithForms;

    public ?string $selectedRegion = null;

    public ?array $data = [];

    /**
     * Listen for region filter changes
     */
    protected $listeners = ['region-changed' => 'handleRegionChange'];

    public function mount(): void
    {
        $this->resetForm();
    }

    public function resetForm(): void
    {
        $this->form->fill([
            'first_name' => '',
            'last_name' => '',
            'school_id' => null,
            'state_id' => '',
            'email' => '',
            'phone' => '',
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('first_name')
                    ->required()
                    ->maxLength(255),
                TextInput::make('last_name')
                    ->required()
                    ->maxLength(255),
                Select::make('school_id')
                    ->label('School')
                    ->searchable()
                    ->getSearchResultsUsing(
                        fn (string $search): array =>
                        School::query()
                            ->where('name', 'ilike', "%{$search}%")
                            ->limit(50)
                            ->pluck('name', 'id')
                            ->toArray()
                    )
                    ->placeholder('Search for a school'),
                Select::make('state_id')
                    ->label('State')
                    ->options(fn() => $this->getStatesProperty())
                    ->required(),
                TextInput::make('email')
                    ->email()
                    ->required()
                    ->maxLength(255),
                TextInput::make('phone')
                    ->tel()
                    ->maxLength(255),
            ])->statePath('data');
    }

    /**
     * Handle region selection change from filter bar
     */
    public function handleRegionChange(?string $regionId): void
    {
        $this->selectedRegion = $regionId;
        $this->resetTable();
    }

    /**
     * Get all regions for the filter bar
     */
    public function getRegionsProperty(): array
    {
        return Region::query()
            ->orderBy('name')
            ->get(['id', 'name'])
            ->toArray();
    }

    /**
     * Get all states for the form select
     */
    public function getStatesProperty(): array
    {
        return State::query()
            ->orderBy('name')
            ->pluck('name', 'code')
            ->toArray();
    }

    /**
     * Get schools for the form select
     */
    public function getSchoolsProperty(): array
    {
        return School::query()
            ->orderBy('name')
            ->pluck('name', 'id')
            ->toArray();
    }

    public function createContact(): void
    {
        $data = $this->form->getState();

        try {
            Contact::create([
                'first_name' => $data['first_name'],
                'last_name' => $data['last_name'],
                'school_id' => $data['school_id'],
                'state_id' => $data['state_id'],
                'email' => $data['email'],
                'phone' => $data['phone'],
                'type' => 'general',
                'status' => 'active',
            ]);

            $this->resetForm();
            $this->dispatch('close-modal', id: 'new-contact-modal');

            Notification::make()
                ->title('Contact created successfully')
                ->success()
                ->send();

            $this->resetTable();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Error creating contact')
                ->danger()
                ->send();
        }
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(Contact::query()
                ->with(['state', 'school.county.state'])
                ->when(
                    $this->selectedRegion,
                    fn($query) =>
                    $query->where('region_id', $this->selectedRegion)
                ))
            ->columns([
                TextColumn::make('first_name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('last_name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('school.name')
                    ->label('School')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('state_name')
                    ->label('State')
                    ->getStateUsing(function (Contact $record) {
                        // First try through school->county->state
                        if ($record->school?->county?->state) {
                            return $record->school->county->state->name;
                        }
                        // Then try direct state relationship
                        if ($record->state) {
                            return $record->state->name;
                        }
                        return null;
                    })
                    ->sortable(),
                TextColumn::make('email')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('phone')
                    ->searchable()
                    ->sortable(),
            ])
            ->actions([
                Action::make('edit')
                    ->icon('heroicon-m-pencil-square')
                    ->url(fn (Contact $record): string => "/admin/contacts/{$record->id}/edit")
                    ->openUrlInNewTab(false),
            ]);
    }

    public function render(): View
    {
        return view('livewire.contacts.contacts-view');
    }
}
