<?php

namespace App\Livewire\Sponsors;

use App\Models\Region;
use App\Models\Organization;
use App\Models\User;
use App\Enums\ProfileType;
use Livewire\Component;
use Livewire\Attributes\On;

class SponsorIndexView extends Component
{
    public array $regions = [];
    public ?int $selectedRegionId = null;

    #[On('region-changed')]
    public function updateRegionFilter(?string $regionId): void
    {
        $this->selectedRegionId = $regionId ? (int) $regionId : null;
    }

    public function mount(): void
    {
        $this->regions = Region::orderBy('name')->get()->all();
    }

    public function getActiveSponsorsCountProperty(): int
    {
        return User::query()
            ->where('profile_type', ProfileType::SPONSOR->value)
            ->whereHas('activeOrganization')
            ->when($this->selectedRegionId, function ($query) {
                $query->where('region_id', $this->selectedRegionId);
            })
            ->count();
    }

    public function getOrganizationsCountProperty(): int
    {
        return Organization::query()
            ->whereHas('activeSponsors')
            ->when($this->selectedRegionId, function ($query) {
                $query->whereHas('activeSponsors', function ($q) {
                    $q->where('region_id', $this->selectedRegionId);
                });
            })
            ->count();
    }

    public function updatedSelectedRegionId(): void
    {
        $this->dispatch('region-changed', regionId: $this->selectedRegionId);
    }

    public function render()
    {
        return view('livewire.sponsors.sponsor-index-view');
    }
}
