<?php

namespace App\Livewire\Sponsors;

use App\Enums\OpportunityStatus;
use App\Enums\OpportunitySubtype;
use App\Enums\OpportunityType;
use App\Models\Opportunity;
use App\Models\Organization;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Illuminate\Database\Eloquent\Builder;
use Livewire\Component;

class OrganizationOpportunitiesTable extends Component implements HasTable, HasForms
{
    use InteractsWithTable;
    use InteractsWithForms;

    public Organization $organization;

    public function mount(Organization $organization): void
    {
        $this->organization = $organization;
    }

    public function form(Form $form): Form
    {
        return $form->schema([]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getOpportunitiesQuery())
            ->columns([
                TextColumn::make('created_at')
                    ->label('Date Created')
                    ->date('m/d/y')
                    ->sortable(),
                TextColumn::make('title')
                    ->label('Opportunity Name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('type')
                    ->label('Category')
                    ->formatStateUsing(fn (OpportunityType $state): string => ucfirst(strtolower($state->value)))
                    ->sortable(),
                TextColumn::make('subtype')
                    ->label('Type')
                    ->formatStateUsing(function (OpportunitySubtype $state) {
                        return str_replace('_', ' ', ucfirst(strtolower($state->value)));
                    })
                    ->sortable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->formatStateUsing(fn (OpportunityStatus $state): string => ucfirst(strtolower($state->value)))
                    ->color(fn (OpportunityStatus $state): string => match ($state) {
                        OpportunityStatus::LISTED => 'success',
                        OpportunityStatus::UNLISTED => 'gray',
                        default => 'gray',
                    }),
                TextColumn::make('sponsor.full_name')
                    ->label('Sponsor')
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->whereHas('sponsor', function (Builder $query) use ($search) {
                            $query->where('first_name', 'ilike', "%{$search}%")
                                 ->orWhere('last_name', 'ilike', "%{$search}%");
                        });
                    })
                    ->sortable(),
                TextColumn::make('industries.name')
                    ->label('Industries')
                    ->badge()
                    ->color('info')
                    ->separator(',')
                    ->wrap(),
            ])
            ->actions([
                Action::make('view')
                    ->label('View')
                    ->icon('heroicon-o-eye')
                    ->url(fn (Opportunity $record): string => route('filament.admin.pages.opportunities.{opportunity}', ['opportunity' => $record])),
            ])
            ->defaultSort('created_at', 'desc')
            ->paginated([12, 24, 36, 48])
            ->bulkActions([]);
    }

    protected function getOpportunitiesQuery(): Builder
    {
        return Opportunity::query()
            ->with(['sponsor', 'industries'])
            ->where('organization_id', $this->organization->id);
    }

    public function render()
    {
        return view('livewire.sponsors.organization-opportunities-table');
    }
}
