<?php

namespace App\Livewire\Sponsors;

use App\Models\Organization;
use Livewire\Component;

class OrganizationView extends Component
{
    public Organization $organization;

    public function mount(Organization $organization): void
    {
        $this->organization = $organization;
    }

    public function render()
    {
        return view('livewire.sponsors.organization-view', [
            'organization' => $this->organization,
        ]);
    }
}
