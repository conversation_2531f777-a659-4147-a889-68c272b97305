<?php

namespace App\Livewire\Sponsors;

use App\Models\Organization;
use App\Models\User;
use App\Models\Connection;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class OrganizationMessagedAthletesTable extends Component implements HasTable, HasForms
{
    use InteractsWithTable;
    use InteractsWithForms;

    public Organization $organization;

    public function mount(Organization $organization): void
    {
        $this->organization = $organization;
    }

    public function form(Form $form): Form
    {
        return $form->schema([]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getMessagedAthletesQuery())
            ->columns([
                ImageColumn::make('profile_photo')
                    ->label('Photo')
                    ->circular()
                    ->defaultImageUrl(function (User $record): string {
                        return 'https://ui-avatars.com/api/?name=' . urlencode($record->first_name . ' ' . $record->last_name) . '&color=7F9CF5&background=EBF4FF';
                    }),
                TextColumn::make('first_name')
                    ->label('First Name')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('last_name')
                    ->label('Last Name')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('school.name')
                    ->label('School')
                    ->sortable(),
                TextColumn::make('state_code')
                    ->label('State')
                    ->sortable(),
                TextColumn::make('total_messages')
                    ->label('Number of Messages')
                    ->sortable(),
            ])
            ->actions([
                Action::make('view')
                    ->label('View')
                    ->icon('heroicon-o-eye')
                    ->url('#'),
            ])
            ->defaultSort('total_messages', 'desc');
    }

    protected function getMessagedAthletesQuery(): Builder
    {
        // Get all organization recruiter IDs using the sponsors relationship
        $recruiterIds = $this->organization->sponsors()->pluck('users.id');

        // Get all athlete users with message counts from conversations view
        return User::query()
            ->select([
                'users.*',
                'conversations.message_count as total_messages'
            ])
            ->join('conversations', function ($join) use ($recruiterIds) {
                $join->where(function ($query) use ($recruiterIds) {
                    $query->whereIn('conversations.user_one_id', $recruiterIds)
                          ->whereColumn('conversations.user_two_id', 'users.id');
                })->orWhere(function ($query) use ($recruiterIds) {
                    $query->whereIn('conversations.user_two_id', $recruiterIds)
                          ->whereColumn('conversations.user_one_id', 'users.id');
                });
            })
            ->where('users.profile_type', 'positive_athlete')
            ->where('conversations.message_count', '>', 0)
            ->groupBy([
                'users.id',
                'users.first_name',
                'users.last_name',
                'users.email',
                'users.profile_type',
                'users.state_code',
                'users.school_id',
                'conversations.message_count'
            ]);
    }

    public function render()
    {
        return view('livewire.sponsors.organization-messaged-athletes-table');
    }
}
