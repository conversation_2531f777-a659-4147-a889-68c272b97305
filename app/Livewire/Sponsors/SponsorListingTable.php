<?php

namespace App\Livewire\Sponsors;

use App\Models\User;
use App\Models\Organization;
use App\Enums\ProfileType;
use App\Data\SystemInvites\SponsorInviteData;
use App\Services\Invite\SystemInviteService;
use App\Filament\Admin\Pages\Sponsors\ViewSponsor;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Illuminate\Support\Str;
use Livewire\Component;
use Livewire\Attributes\Reactive;

class SponsorListingTable extends Component implements HasTable, HasForms
{
    use InteractsWithTable;
    use InteractsWithForms;

    #[Reactive]
    public ?int $selectedRegionId = null;

    public function table(Table $table): Table
    {
        $query = User::with('activeOrganization.activeSponsors')
            ->where('profile_type', ProfileType::SPONSOR->value)
            ->whereHas('activeOrganization');

        if ($this->selectedRegionId) {
            $query->where('region_id', $this->selectedRegionId);
        }

        return $table
            ->query($query)
            ->columns([
                \Filament\Tables\Columns\ImageColumn::make('activeOrganization.logo')
                    ->circular()
                    ->defaultImageUrl(fn(User $record) => 'https://ui-avatars.com/api/?name=' . urlencode($record->activeOrganization?->first()?->name ?? '') . '&color=7F9CF5&background=EBF4FF')
                    ->getStateUsing(fn(User $record) => $record->activeOrganization?->first()?->getFirstMediaUrl('logo'))
                    ->alignCenter(),
                TextColumn::make('activeOrganization.name')
                    ->label('Organization')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('full_name')
                    ->label('Name')
                    ->sortable(['first_name', 'last_name'])
                    ->searchable(['first_name', 'last_name']),
                TextColumn::make('state_code')
                    ->label('State')
                    ->sortable(),
                TextColumn::make('')
                    ->grow(),
            ])
            ->headerActions([
                Action::make('inviteSponsor')
                    ->label('Add New Recruiter/Sponsor')
                    ->icon('heroicon-o-plus-circle')
                    ->color('primary')
                    ->modalHeading('Add a New Recruiter/Sponsor')
                    ->modalWidth('md')
                    ->form([
                        Select::make('organization_id')
                            ->label('Organization Name')
                            ->searchable()
                            ->preload()
                            ->getSearchResultsUsing(
                                fn (string $search): array =>
                                    Organization::query()
                                        ->where('name', 'ilike', "%{$search}%")
                                        ->limit(20)
                                        ->pluck('name', 'id')
                                        ->toArray()
                            )
                            ->getOptionLabelUsing(fn ($value): ?string => Organization::find($value)?->name)
                            ->required(),
                        TextInput::make('contact_name')
                            ->label('Contact Name')
                            ->required(),
                        TextInput::make('email_address')
                            ->label('Email Address')
                            ->email()
                            ->required(),
                        TextInput::make('phone_number')
                            ->label('Phone Number')
                            ->tel(),
                    ])
                    ->action(function (array $data): void {
                        // Split contact name into first and last name
                        $nameParts = explode(' ', $data['contact_name'], 2);
                        $firstName = $nameParts[0];
                        $lastName = count($nameParts) > 1 ? $nameParts[1] : '';

                        // Get the organization
                        $organization = Organization::find($data['organization_id']);

                        // Create sponsor invite
                        $inviteData = new SponsorInviteData(
                            first_name: $firstName,
                            last_name: $lastName,
                            email: $data['email_address'],
                            company_name: $organization->name,
                            organization_id: $organization->id,
                            token: Str::random(64),
                            created_at: now()->timestamp,
                        );

                        // Send the invite
                        $inviteService = app(SystemInviteService::class);
                        $inviteService->createForSponsor($inviteData);

                        // Show success notification
                        Notification::make()
                            ->success()
                            ->title('Invite sent successfully')
                            ->send();
                    })
                    ->modalSubmitActionLabel('Save & Send Invite')
                    ->modalCancelActionLabel('Cancel'),
            ])
            ->actions([
                Action::make('view')
                    ->label('View')
                    ->icon('heroicon-o-eye')
                    ->url(fn(User $user) => ViewSponsor::getUrl([
                        'organization' => $user->activeOrganization->first()->id
                    ]))
            ])
            ->bulkActions([
                // Add bulk actions if needed
            ])
            ->defaultSort('activeOrganization.name', 'asc');
    }

    public function render()
    {
        return view('livewire.sponsors.sponsor-listing-table');
    }
}
