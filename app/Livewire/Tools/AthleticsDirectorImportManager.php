<?php

namespace App\Livewire\Tools;

use App\Jobs\ImportAthleticsDirectorsJob;
use App\Services\Import\AthleticsDirectorImportService;
use App\Traits\HasCsvTemplateDownload;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Illuminate\Bus\Batch;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;

class AthleticsDirectorImportManager extends Component implements HasForms
{
    use InteractsWithForms, HasCsvTemplateDownload;

    /**
     * The form data.
     *
     * @var array
     */
    public $data = [];

    /**
     * The error messages.
     *
     * @var array
     */
    public $errors = [];

    /**
     * Mount the component.
     */
    public function mount(): void
    {
        $this->form->fill();
    }

    /**
     * Define the form.
     */
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                FileUpload::make('csv_file')
                    ->label('CSV File')
                    ->helperText('Upload a CSV file containing Athletics Director data. Each record will create a contact with type "athletics_director".')
                    ->acceptedFileTypes(['text/csv', 'application/csv'])
                    ->maxSize(10240) // 10MB
                    ->required()
                    ->disk('local')
                    ->directory('imports/athletics_directors')
                    ->visibility('private'),
            ])
            ->statePath('data');
    }

    /**
     * Import Athletics Directors from the uploaded CSV file.
     */
    public function import(AthleticsDirectorImportService $importService)
    {
        $this->errors = [];

        try {
            // Validate the form
            $data = $this->form->getState();

            if (empty($data['csv_file'])) {
                $this->errors[] = 'Please upload a CSV file.';
                return;
            }

            // Get the relative path to the file
            $filePath = $data['csv_file'];

            // Log the file path for debugging
            Log::info('Athletics Director import file path', [
                'file_path' => $filePath,
                'full_path' => Storage::disk('local')->path($filePath),
                'exists' => Storage::disk('local')->exists($filePath)
            ]);

            // Validate the CSV structure using the full path
            $validationResult = $importService->validateCsvStructure(Storage::disk('local')->path($filePath));

            if (!$validationResult['valid']) {
                $this->errors = array_merge($this->errors, $validationResult['errors']);
                return;
            }

            // Get the current user ID
            $userId = Auth::id();

            // Create a batch job to process the CSV file - pass the relative path
            $batch = Bus::batch([
                new ImportAthleticsDirectorsJob($filePath, $userId),
            ])
                ->name('Import Athletics Directors')
                ->allowFailures()
                ->then(function (Batch $batch) use ($userId) {
                    // This will be called when the batch completes successfully
                    Log::info('Athletics Director import batch completed', [
                        'batch_id' => $batch->id,
                        'user_id' => $userId,
                    ]);
                })
                ->catch(function (Batch $batch, \Throwable $e) use ($userId) {
                    // This will be called if the batch fails
                    Log::error('Athletics Director import batch failed', [
                        'batch_id' => $batch->id,
                        'user_id' => $userId,
                        'error' => $e->getMessage(),
                    ]);
                })
                ->dispatch();

            // Store the user ID in the batch metadata
            Log::info('Athletics Director import batch created with user context', [
                'batch_id' => $batch->id,
                'user_id' => $userId,
            ]);

            // Notify the user that the import has started
            Notification::make()
                ->title('Athletics Director Import Started')
                ->body('Your Athletics Director import has been queued and will be processed in the background. This will create contact records and associate them with schools.')
                ->success()
                ->send();

            // Reset the form
            $this->form->fill();

            // Log the batch ID for reference
            Log::info('Athletics Director import batch created', [
                'batch_id' => $batch->id,
                'user_id' => $userId,
            ]);

            // Redirect to the import reports index page
            return $this->redirect(route('filament.admin.resources.imports.index'));
        } catch (\Exception $e) {
            Log::error('Error starting Athletics Director import', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            $this->errors[] = 'An error occurred while starting the import: ' . $e->getMessage();

            Notification::make()
                ->title('Import Error')
                ->body('An error occurred while starting the import: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function getTemplateFilename(): string
    {
        return 'athletics-director-import-template.csv';
    }

    protected function getTemplateHeaders(): array
    {
        return [
            'First Name',
            'Last Name',
            'Title',
            'Account Name',
            'Shipping Address',
            'City',
            'State',
            'Zip Code',
            'Phone',
            'Email',
            'County',
            'Region',
            'Market'
        ];
    }

    protected function getTemplateSampleData(): array
    {
        return [
            'John',
            'Smith',
            'AD',
            'Central High School',
            '123 Main St',
            'Pittsburgh',
            'PA',
            '15213',
            '555-0123',
            '<EMAIL>',
            'Allegheny',
            'Pennsylvania',
            'Pittsburgh'
        ];
    }

    /**
     * Render the component.
     */
    public function render()
    {
        return view('livewire.tools.athletics-director-import-manager');
    }
}
