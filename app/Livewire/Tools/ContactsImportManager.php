<?php

namespace App\Livewire\Tools;

use App\Jobs\ImportContactsJob;
use App\Services\Import\ContactsImportService;
use App\Traits\HasCsvTemplateDownload;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Illuminate\Bus\Batch;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;

class ContactsImportManager extends Component implements HasForms
{
    use InteractsWithForms, HasCsvTemplateDownload;

    /**
     * The form data.
     *
     * @var array
     */
    public $data = [];

    /**
     * The error messages.
     *
     * @var array
     */
    public $errors = [];

    /**
     * Mount the component.
     */
    public function mount(): void
    {
        $this->form->fill();
    }

    /**
     * Define the form.
     */
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                FileUpload::make('csv_file')
                    ->label('CSV File')
                    ->helperText('Upload a CSV file containing contact data. Each record will create a general contact record.')
                    ->acceptedFileTypes(['text/csv', 'application/csv'])
                    ->maxSize(10240) // 10MB
                    ->required()
                    ->disk('local')
                    ->directory('imports/contacts')
                    ->visibility('private'),
            ])
            ->statePath('data');
    }

    /**
     * Import Contacts from the uploaded CSV file.
     */
    public function import(ContactsImportService $importService)
    {
        $this->errors = [];

        try {
            // Validate the form
            $data = $this->form->getState();

            if (empty($data['csv_file'])) {
                $this->errors[] = 'Please upload a CSV file.';
                return;
            }

            // Get the relative path to the file
            $filePath = $data['csv_file'];

            // Log the file path for debugging
            Log::info('Contacts import file path', [
                'file_path' => $filePath,
                'full_path' => Storage::disk('local')->path($filePath),
                'exists' => Storage::disk('local')->exists($filePath)
            ]);

            // Validate the CSV structure using the full path
            $validationResult = $importService->validateCsvStructure(Storage::disk('local')->path($filePath));

            if (!$validationResult['valid']) {
                $this->errors = array_merge($this->errors, $validationResult['errors']);
                return;
            }

            // Get the current user ID
            $userId = Auth::id();

            // dispatch_sync(new ImportContactsJob($filePath, $userId));

            // Create a batch job to process the CSV file - pass the relative path
            $batch = Bus::batch([
                new ImportContactsJob($filePath, $userId),
            ])
                ->name('Import Contacts')
                ->allowFailures()
                ->catch(function (Batch $batch, \Throwable $e) use ($userId) {
                    // This will be called if the batch fails
                })
                ->dispatch();

            // Store the user ID in the batch metadata
            Log::info('Contacts import batch created with user context', [
                'batch_id' => $batch->id,
                'user_id' => $userId,
            ]);

            // Notify the user that the import has started
            Notification::make()
                ->title('Contacts Import Started')
                ->body('Your contacts import has been queued and will be processed in the background. This will create contact records and associate them with organizations or schools.')
                ->success()
                ->send();

            // Reset the form
            $this->form->fill();

            // Log the batch ID for reference
            Log::info('Contacts import batch created', [
                'batch_id' => $batch->id,
                'user_id' => $userId,
            ]);

            // Redirect to the import reports index page
            return $this->redirect(route('filament.admin.resources.imports.index'));
        } catch (\Exception $e) {
            Log::error('Error starting Contacts import', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            $this->errors[] = 'An error occurred while starting the import: ' . $e->getMessage();

            Notification::make()
                ->title('Import Error')
                ->body('An error occurred while starting the import: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function getTemplateFilename(): string
    {
        return 'contacts-import-template.csv';
    }

    protected function getTemplateHeaders(): array
    {
        return [
            'Account Name',
            'Account Type',
            'First Name',
            'Last Name',
            'Title',
            'Email',
            'State',
            'Market',
            'Region',
            'Sub-Region',
            'Category',
            'Level'
        ];
    }

    protected function getTemplateSampleData(): array
    {
        return [
            'ABC Corporation',
            'Business',
            'Jane',
            'Doe',
            'Marketing Director',
            '<EMAIL>',
            'PA',
            'Pittsburgh',
            'Pennsylvania',
            'Western PA',
            'Corporate Sponsor',
            'Platinum'
        ];
    }

    /**
     * Render the component.
     */
    public function render()
    {
        return view('livewire.tools.contacts-import-manager');
    }
}
