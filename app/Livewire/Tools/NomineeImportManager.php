<?php

namespace App\Livewire\Tools;

use App\Jobs\ImportNomineesJob;
use App\Services\Import\NomineeImportService;
use App\Traits\HasCsvTemplateDownload;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Illuminate\Bus\Batch;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;

class NomineeImportManager extends Component implements HasForms
{
    use InteractsWithForms, HasCsvTemplateDownload;

    /**
     * The form data.
     *
     * @var array
     */
    public $data = [];

    /**
     * The error messages.
     *
     * @var array
     */
    public $errors = [];

    /**
     * Mount the component.
     */
    public function mount(): void
    {
        $this->form->fill();
    }

    /**
     * Define the form.
     */
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                FileUpload::make('csv_file')
                    ->label('CSV File')
                    ->helperText('Upload a CSV file containing nominee data. The Type column in the CSV will determine if each record is a Positive Athlete or Positive Coach.')
                    ->acceptedFileTypes(['text/csv', 'application/csv'])
                    ->maxSize(10240) // 10MB
                    ->required()
                    ->disk('local')
                    ->directory('imports/nominees')
                    ->visibility('private'),
            ])
            ->statePath('data');
    }

    /**
     * Import nominees from the uploaded CSV file.
     */
    public function import(NomineeImportService $importService)
    {
        $this->errors = [];

        try {
            // Validate the form
            $data = $this->form->getState();

            if (empty($data['csv_file'])) {
                $this->errors[] = 'Please upload a CSV file.';
                return;
            }

            // Get the relative path to the file
            $filePath = $data['csv_file'];

            // Log the file path for debugging
            Log::info('Nominee import file path', [
                'file_path' => $filePath,
                'full_path' => Storage::disk('local')->path($filePath),
                'exists' => Storage::disk('local')->exists($filePath)
            ]);

            // Validate the CSV structure using the full path
            $validationResult = $importService->validateCsvStructure(Storage::disk('local')->path($filePath));

            if (!$validationResult['valid']) {
                $this->errors = array_merge($this->errors, $validationResult['errors']);
                return;
            }

            // Get the current user ID
            $userId = Auth::id();

            // Create a batch job to process the CSV file - pass the relative path
            $batch = Bus::batch([
                new ImportNomineesJob($filePath, $userId),
            ])
                ->name('Import Nominees')
                ->allowFailures()
                ->then(function (Batch $batch) use ($userId) {
                    // This will be called when the batch completes successfully
                    Log::info('Nominee import batch completed', [
                        'batch_id' => $batch->id,
                        'user_id' => $userId,
                    ]);
                })
                ->catch(function (Batch $batch, \Throwable $e) use ($userId) {
                    // This will be called if the batch fails
                    Log::error('Nominee import batch failed', [
                        'batch_id' => $batch->id,
                        'user_id' => $userId,
                        'error' => $e->getMessage(),
                    ]);
                })
                ->dispatch();

            // Store the user ID in the batch metadata
            // Note: Laravel doesn't have a direct way to add custom metadata to a batch
            // We'll log it for reference
            Log::info('Nominee import batch created with user context', [
                'batch_id' => $batch->id,
                'user_id' => $userId,
            ]);

            // Notify the user that the import has started
            Notification::make()
                ->title('Nominee Import Started')
                ->body('Your nominee import has been queued and will be processed in the background. This will create both contact and nomination records.')
                ->success()
                ->send();

            // Reset the form
            $this->form->fill();

            // Log the batch ID for reference
            Log::info('Nominee import batch created', [
                'batch_id' => $batch->id,
                'user_id' => $userId,
            ]);

            // Redirect to the import reports index page
            return $this->redirect(route('filament.admin.resources.imports.index'));
        } catch (\Exception $e) {
            Log::error('Error starting nominee import', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            $this->errors[] = 'An error occurred while starting the import: ' . $e->getMessage();

            Notification::make()
                ->title('Import Error')
                ->body('An error occurred while starting the import: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function getTemplateFilename(): string
    {
        return 'nominee-import-template.csv';
    }

    protected function getTemplateHeaders(): array
    {
        return [
            'Nominated Date',
            'Nominees Name',
            'State',
            'County',
            '+A Market',
            '+A Region',
            'High School Name',
            'Type',
            'Grade',
            'Gender',
            'Sport #1',
            'Sport #2',
            'Sport #3',
            'Nomination',
            'Nominee Email',
            'Nominee Phone Number',
            'Nominator Full Name',
            'Nominator Email',
            'Nominator Phone Number',
            'Relationship to Nominee'
        ];
    }

    protected function getTemplateSampleData(): array
    {
        return [
            '2024-01-15',
            'John Smith',
            'PA',
            'Allegheny',
            'Pittsburgh',
            'Pennsylvania',
            'Central High School',
            'Athlete',
            '11',
            'Male',
            'Football',
            'Track and Field',
            '',
            'Outstanding leadership and sportsmanship on and off the field',
            '<EMAIL>',
            '555-0123',
            'Jane Doe',
            '<EMAIL>',
            '555-0456',
            'Coach'
        ];
    }

    /**
     * Render the component.
     */
    public function render()
    {
        return view('livewire.tools.nominee-import-manager');
    }
}
