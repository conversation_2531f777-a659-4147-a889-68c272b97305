<?php

namespace App\Livewire\Tools;

use App\Jobs\ImportSchoolsJob;
use App\Models\User;
use App\Services\Import\SchoolImportService;
use App\Traits\HasCsvTemplateDownload;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Section;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Illuminate\Bus\Batch;
use Illuminate\Support\Facades\Bus;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class SchoolImportManager extends Component implements HasForms
{
    use InteractsWithForms, HasCsvTemplateDownload;

    public ?array $data = [];
    public array $errors = [];

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Import Schools')
                    ->description('Upload a CSV file containing school data to import into the system. The import will create or update schools, counties, markets, sub-regions, and regions as needed.')
                    ->schema([
                        FileUpload::make('csv_file')
                            ->label('CSV File')
                            ->acceptedFileTypes(['text/csv', 'application/csv', 'text/plain'])
                            ->maxSize(10240) // 10MB
                            ->required()
                            ->disk('public')
                            ->directory('csv-imports')
                            ->visibility('private')
                            ->helperText('The CSV file should include the following columns: Account Name, State, County, Market, Region, Sub-Region (optional), School Address (optional), School City (optional), School Zip Code (optional)')
                    ]),
            ])
            ->statePath('data');
    }

    public function import()
    {
        $this->validate();

        $this->errors = [];

        // Get the current user for database notifications
        $user = Auth::user();

        try {
            // Get the uploaded file
            $uploadedFile = $this->data['csv_file'];

            if (is_array($uploadedFile)) {
                $uploadedFile = array_values($uploadedFile)[0];
            }

            // Log the type of the uploaded file for debugging
            \Illuminate\Support\Facades\Log::info('Uploaded file type', [
                'type' => is_object($uploadedFile) ? get_class($uploadedFile) : gettype($uploadedFile),
                'value' => $uploadedFile,
            ]);

            // Get the file path based on the configuration
            if (is_string($uploadedFile)) {
                // If it's a string, it's likely a path relative to the disk
                $filePath = storage_path('app/public/' . $uploadedFile);
            } else {
                // For backward compatibility, handle TemporaryUploadedFile
                if ($uploadedFile instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile) {
                    $filePath = $uploadedFile->getRealPath();
                } else {
                    throw new \Exception('Unsupported file type: ' . gettype($uploadedFile));
                }
            }

            \Illuminate\Support\Facades\Log::info('File path for import', ['path' => $filePath]);

            // Validate the CSV file structure
            $service = app(SchoolImportService::class);
            $validationResult = $service->validateCsvStructure($filePath);

            if (!$validationResult['valid']) {
                $this->errors = $validationResult['errors'];

                Notification::make()
                    ->title('CSV Validation Failed')
                    ->body('The CSV file does not have the required structure. Please check the errors and try again.')
                    ->danger()
                    ->send();

                return;
            }

            // Dispatch the import job
            $batch = Bus::batch([
                new ImportSchoolsJob($filePath)
            ])
                ->withOption('userId', $user->id)
                ->then(function () {})
                ->catch(function (Batch $batch, $e) {
                    $userId = $batch->options['userId'];
                    $user = User::find($userId);
                    // This will be called if the batch fails
                    Notification::make()
                        ->title('Import Failed')
                        ->body('An error occurred during the import process: ' . $e->getMessage())
                        ->danger()
                        ->persistent()
                        ->sendToDatabase($user);
                })
                ->finally(function (Batch $batch) {
                    $userId = $batch->options['userId'];
                    $user = User::find($userId);

                    Notification::make()
                        ->title('Import Completed')
                        ->body('School import has been completed successfully.')
                        ->success()
                        ->persistent()
                        ->sendToDatabase($user);
                })
                ->dispatch();

            Notification::make()
                ->title('Import Started')
                ->body('School import has been started.')
                ->success()
                ->send();

            // Redirect to the import reports index page
            return $this->redirect(route('filament.admin.resources.imports.index'));
        } catch (\Exception $e) {
            $this->errors[] = $e->getMessage();

            Notification::make()
                ->title('Import Failed')
                ->body('An error occurred: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function getTemplateFilename(): string
    {
        return 'school-import-template.csv';
    }

    protected function getTemplateHeaders(): array
    {
        return [
            'Account Name',
            'State',
            'County',
            'Market',
            'Region',
            'Sub-Region',
            'School Address',
            'School City',
            'School Zip Code'
        ];
    }

    protected function getTemplateSampleData(): array
    {
        return [
            'Central High School',
            'PA',
            'Allegheny',
            'Pittsburgh',
            'Pennsylvania',
            'Western PA',
            '123 Main St',
            'Pittsburgh',
            '15213'
        ];
    }

    public function render()
    {
        return view('livewire.tools.school-import-manager');
    }
}
