<?php

namespace App\Livewire\Winners;

use App\Enums\AwardType;
use App\Enums\ProfileType;
use App\Enums\WinnerVerificationState;
use App\Models\Award;
use App\Models\Scholarship;
use App\Models\User;
use App\Models\Winner;
use App\Services\AwardService;
use App\Services\ScholarshipService;
use Carbon\Carbon;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\View;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Component;

class WinnerInformationManagement extends Component implements HasForms
{
    use InteractsWithForms;

    public User $user;

    // Form data
    public ?int $scholarship_id = null;
    public ?int $award_id = null;
    public bool $is_scholarship_finalist = false;
    public bool $is_scholarship_winner = false;
    public array $award_types = [];
    public string $tshirt_size = '';

    // Separate winner records
    public ?Winner $scholarshipWinner = null;
    public Collection $awardWinners;

    // Combined verification status (derived from individual records)
    public string $verification_state = 'pending_verification';
    public ?Carbon $verified_at = null;
    public ?Carbon $notified_at = null;
    public ?Carbon $acknowledged_at = null;

    // Services
    protected ScholarshipService $scholarshipService;
    protected AwardService $awardService;

    public function boot(ScholarshipService $scholarshipService, AwardService $awardService)
    {
        $this->scholarshipService = $scholarshipService;
        $this->awardService = $awardService;
    }

    public function mount(User $user): void
    {
        $this->user = $user;
        $this->awardWinners = new Collection();
        $this->loadWinnerData();
        $this->form->fill([
            'scholarship_id' => $this->scholarship_id,
            'award_id' => $this->award_id,
            'is_scholarship_finalist' => $this->is_scholarship_finalist,
            'is_scholarship_winner' => $this->is_scholarship_winner,
            'award_types' => $this->award_types,
            'tshirt_size' => $this->tshirt_size,
        ]);
    }

    public function form(Form $form): Form
    {
        // Get current academic year for scholarships and awards
        $currentScholarshipYear = $this->scholarshipService->getDefaultScholarshipYear();
        $currentAwardYear = $this->awardService->getDefaultAwardYear();

        return $form
            ->schema([
                Grid::make()
                    ->columns([
                        'default' => 1,
                        'md' => 2,
                        'lg' => $this->user->profile_type === ProfileType::POSITIVE_ATHLETE ? 3 : 2
                    ])
                    ->schema([
                        // Scholarship section
                        Section::make()
                            ->columnSpan(1)
                            ->schema([
                                Select::make('scholarship_id')
                                    ->label("Scholarship ($currentScholarshipYear Academic Year)")
                                    ->options(function () use ($currentScholarshipYear) {
                                        return Scholarship::query()
                                            ->where('is_active', true)
                                            ->where('year', $currentScholarshipYear)
                                            ->where(function ($query) {
                                                $query->whereNull('limit')
                                                    ->orWhereRaw('(select count(*) from winners where scholarship_id = scholarships.id) < scholarships.limit');
                                            })
                                            ->pluck('name', 'id');
                                    })
                                    ->searchable()
                                    ->placeholder('Select')
                                    ->live()
                                    ->getOptionLabelUsing(fn ($value): ?string => Scholarship::find($value)?->name)
                                    ->afterStateUpdated(function () {
                                        // Sync with the component's property
                                        $this->scholarship_id = $this->form->getState()['scholarship_id'];
                                        $this->updateScholarship();
                                    }),

                                Checkbox::make('is_scholarship_finalist')
                                    ->label('Scholarship Finalist')
                                    ->live()
                                    ->disabled(fn (callable $get) => !$get('scholarship_id'))
                                    ->afterStateUpdated(function () {
                                        $this->is_scholarship_finalist = $this->form->getState()['is_scholarship_finalist'];
                                        $this->updateScholarship();
                                    }),

                                Checkbox::make('is_scholarship_winner')
                                    ->label('Scholarship Winner')
                                    ->live()
                                    ->disabled(fn (callable $get) => !$get('scholarship_id'))
                                    ->afterStateUpdated(function () {
                                        $this->is_scholarship_winner = $this->form->getState()['is_scholarship_winner'];
                                        $this->updateScholarship();
                                    }),
                            ])
                            ->visible(fn () => $this->user->profile_type === \App\Enums\ProfileType::POSITIVE_ATHLETE),

                        // Award section
                        Section::make()
                            ->columnSpan(1)
                            ->schema([
                                Select::make('award_id')
                                    ->label("Most Positive Award ($currentAwardYear Academic Year)")
                                    ->options(function () use ($currentAwardYear) {
                                        return Award::query()
                                            ->where('is_active', true)
                                            ->where('year', $currentAwardYear)
                                            ->pluck('name', 'id');
                                    })
                                    ->searchable()
                                    ->placeholder('Select Sport')
                                    ->live()
                                    ->getOptionLabelUsing(fn ($value): ?string => Award::find($value)?->name)
                                    ->afterStateUpdated(function () {
                                        // Sync with the component's property
                                        $this->award_id = $this->form->getState()['award_id'];
                                        $this->updateAward();
                                    }),

                                CheckboxList::make('award_types')
                                    ->options(function () {
                                        $options = [];
                                        foreach (AwardType::cases() as $type) {
                                            $options[$type->value] = $type->label();
                                        }
                                        return $options;
                                    })
                                    ->disabled(fn (callable $get) => !$get('award_id'))
                                    ->live()
                                    ->afterStateUpdated(function () {
                                        $this->award_types = $this->form->getState()['award_types'];
                                        $this->updateAward();
                                    }),
                            ]),

                        // Verification and T-shirt section
                        Section::make()
                            ->columnSpan(1)
                            ->schema([
                                View::make('livewire.winners.verification-status')
                                    ->viewData([
                                        'verificationState' => WinnerVerificationState::tryFrom($this->verification_state) ?? WinnerVerificationState::PENDING_VERIFICATION,
                                        'verified_at' => $this->verified_at,
                                        'notified_at' => $this->notified_at,
                                        'acknowledged_at' => $this->acknowledged_at,
                                    ]),

                                Select::make('tshirt_size')
                                    ->label('T-Shirt Size')
                                    ->options([
                                        'XS' => 'XS',
                                        'S' => 'S',
                                        'M' => 'M',
                                        'L' => 'L',
                                        'XL' => 'XL',
                                        'XXL' => 'XXL',
                                        'XXXL' => 'XXXL',
                                    ])
                                    ->placeholder('Select Size')
                                    ->disabled(fn () => !$this->scholarship_id && !$this->award_id)
                                    ->live()
                                    ->afterStateUpdated(function () {
                                        $this->tshirt_size = $this->form->getState()['tshirt_size'];
                                        $this->updateTshirtSize();
                                    }),
                            ]),
                    ]),
            ]);
    }

    /**
     * Load existing winner data for this user
     */
    protected function loadWinnerData(): void
    {
        try {
            // Find scholarship winner record if exists (only for positive athletes)
            if ($this->user->profile_type === ProfileType::POSITIVE_ATHLETE) {
                $this->scholarshipWinner = Winner::where('user_id', $this->user->id)
                    ->whereNotNull('scholarship_id')
                    ->orderByDesc('created_at')
                    ->first();

                if ($this->scholarshipWinner) {
                    $this->scholarship_id = $this->scholarshipWinner->scholarship_id;
                    $this->is_scholarship_finalist = $this->scholarshipWinner->is_finalist;
                    $this->is_scholarship_winner = $this->scholarshipWinner->is_winner;
                    $this->tshirt_size = $this->scholarshipWinner->tshirt_size;
                }
            } else {
                // Ensure scholarship data is cleared for non-athletes
                $this->scholarshipWinner = null;
                $this->scholarship_id = null;
                $this->is_scholarship_finalist = false;
                $this->is_scholarship_winner = false;
            }

            // Find award winner records if exist
            $this->awardWinners = Winner::where('user_id', $this->user->id)
                ->whereNotNull('award_id')
                ->with('award')
                ->orderByDesc('created_at')
                ->get();

            if ($this->awardWinners->isNotEmpty()) {
                // Use the first award winner to get the award_id
                $firstAwardWinner = $this->awardWinners->first();
                $this->award_id = $firstAwardWinner->award_id;

                // Collect all award types
                $this->award_types = [];
                foreach ($this->awardWinners as $winner) {
                    if ($winner->award && $winner->award->type instanceof AwardType) {
                        $this->award_types[] = $winner->award->type->value;
                    }
                }

                // Use the most recent award winner's t-shirt size if no scholarship winner or if more recent
                if (!$this->scholarshipWinner ||
                    ($firstAwardWinner->updated_at &&
                     $this->scholarshipWinner->updated_at &&
                     $firstAwardWinner->updated_at->gt($this->scholarshipWinner->updated_at))) {
                    $this->tshirt_size = $firstAwardWinner->tshirt_size;
                }
            }

            // Calculate the effective verification state (the most advanced state from either record)
            $this->calculateEffectiveVerificationState();

        } catch (\Exception $e) {
            Log::error('Failed to load winner data: ' . $e->getMessage());
        }
    }

    /**
     * Calculate the effective verification state based on all winner records
     */
    protected function calculateEffectiveVerificationState(): void
    {
        $scholarshipState = $this->scholarshipWinner?->verification_state ?? null;

        // Get the most advanced state from award winners
        $awardState = null;
        $awardRank = 0;

        foreach ($this->awardWinners as $winner) {
            $state = $winner->verification_state ?? null;
            if ($state) {
                $rank = $this->getStateRank($state);
                if ($rank > $awardRank) {
                    $awardRank = $rank;
                    $awardState = $state;
                    $this->verified_at = $winner->verified_at;
                    $this->notified_at = $winner->notified_at;
                    $this->acknowledged_at = $winner->acknowledged_at;
                }
            }
        }

        // Default to pending if no records exist
        if (!$scholarshipState && !$awardState) {
            $this->verification_state = WinnerVerificationState::PENDING_VERIFICATION->value;
            return;
        }

        // If only one record exists, use its state
        if (!$scholarshipState) {
            $this->verification_state = $awardState;
            return;
        }

        if (!$awardState) {
            $this->verification_state = $scholarshipState;
            $this->verified_at = $this->scholarshipWinner?->verified_at;
            $this->notified_at = $this->scholarshipWinner?->notified_at;
            $this->acknowledged_at = $this->scholarshipWinner?->acknowledged_at;
            return;
        }

        // If both exist, use the most advanced state
        $scholarshipRank = $this->getStateRank($scholarshipState);

        if ($scholarshipRank >= $awardRank) {
            $this->verification_state = $scholarshipState;
            $this->verified_at = $this->scholarshipWinner?->verified_at;
            $this->notified_at = $this->scholarshipWinner?->notified_at;
            $this->acknowledged_at = $this->scholarshipWinner?->acknowledged_at;
        } else {
            $this->verification_state = $awardState;
        }
    }

    /**
     * Helper to rank verification states for comparison
     */
    protected function getStateRank(string $state): int
    {
        return match($state) {
            WinnerVerificationState::PENDING_VERIFICATION->value => 1,
            WinnerVerificationState::VERIFIED->value => 2,
            WinnerVerificationState::NOTIFIED->value => 3,
            WinnerVerificationState::ACKNOWLEDGED->value => 4,
            default => 0,
        };
    }

    /**
     * Update scholarship selection
     */
    public function updateScholarship(): void
    {
        // Only process for positive athletes
        if ($this->user->profile_type !== ProfileType::POSITIVE_ATHLETE) {
            return;
        }

        try {
            DB::beginTransaction();

            if ($this->scholarship_id) {
                // If an existing winner record exists
                if ($this->scholarshipWinner) {
                    $this->scholarshipWinner->scholarship_id = $this->scholarship_id;
                    $this->scholarshipWinner->is_finalist = $this->is_scholarship_finalist;
                    $this->scholarshipWinner->is_winner = $this->is_scholarship_winner;
                    $this->scholarshipWinner->tshirt_size = $this->tshirt_size;
                    $this->scholarshipWinner->save();
                } else {
                    // Create new winner record via service
                    $winnerData = [
                        'is_finalist' => $this->is_scholarship_finalist,
                        'is_winner' => $this->is_scholarship_winner,
                        'tshirt_size' => $this->tshirt_size,
                        'verification_state' => WinnerVerificationState::PENDING_VERIFICATION->value
                    ];

                    $this->scholarshipWinner = $this->scholarshipService->addWinner(
                        $this->scholarship_id,
                        $this->user->id,
                        $winnerData
                    );
                }

                // Sync T-shirt size with award winners if they exist
                if ($this->awardWinners->isNotEmpty()) {
                    foreach ($this->awardWinners as $winner) {
                        if ($winner->tshirt_size !== $this->tshirt_size) {
                            $winner->tshirt_size = $this->tshirt_size;
                            $winner->save();
                        }
                    }
                }

                DB::commit();
                $this->dispatch('winner-updated');
                Notification::make()->title('Scholarship information updated successfully')->success()->send();
            } else if ($this->scholarshipWinner) {
                // Remove scholarship winner record if scholarship is deselected
                $this->scholarshipService->removeWinner($this->scholarshipWinner->scholarship_id, $this->scholarshipWinner->id);
                $this->scholarshipWinner = null;
                $this->is_scholarship_finalist = false;
                $this->is_scholarship_winner = false;

                DB::commit();
                $this->dispatch('winner-updated');
                Notification::make()->title('Scholarship information removed')->success()->send();
            }

            // Recalculate the effective verification state
            $this->calculateEffectiveVerificationState();

        } catch (\Exception $e) {
            DB::rollBack();

            // Check if this is a validation exception with a winner limit error
            if ($e instanceof \Illuminate\Validation\ValidationException &&
                isset($e->errors()['limit']) &&
                is_array($e->errors()['limit'])) {
                Log::error('Failed to update scholarship: ' . $e->getMessage());
                Notification::make()
                    ->title('Scholarship Limit Reached')
                    ->body('This scholarship has reached its maximum number of winners and cannot accept more.')
                    ->danger()
                    ->send();
            } else {
                Log::error('Failed to update scholarship: ' . $e->getMessage());
                Notification::make()
                    ->title('Failed to update scholarship information')
                    ->body($e->getMessage())
                    ->danger()
                    ->send();
            }
        }
    }

    /**
     * Update award selection
     */
    public function updateAward(): void
    {
        try {
            DB::beginTransaction();

            if ($this->award_id) {
                // Get current award winners
                $existingWinnerTypes = [];
                foreach ($this->awardWinners as $winner) {
                    if ($winner->award && $winner->award->type instanceof AwardType) {
                        $existingWinnerTypes[] = $winner->award->type->value;
                    }
                }

                // Types that need to be added
                $typesToAdd = array_diff($this->award_types, $existingWinnerTypes);

                // Types that need to be removed
                $typesToRemove = array_diff($existingWinnerTypes, $this->award_types);

                // Add new types
                foreach ($typesToAdd as $type) {
                    // For each new type, we need to clone the award or find an existing award with same base data
                    $originalAward = Award::find($this->award_id);

                    // Check if there's already an award with the same name but different type
                    $duplicateAward = Award::where('name', $originalAward->name)
                        ->where('year', $originalAward->year)
                        ->where('region_id', $originalAward->region_id)
                        ->where('market_id', $originalAward->market_id)
                        ->where('state_id', $originalAward->state_id)
                        ->where('type', $type)
                        ->first();

                    // If no duplicate exists, create one
                    if (!$duplicateAward) {
                        $duplicateAward = $originalAward->replicate();
                        $duplicateAward->type = $type;
                        $duplicateAward->save();
                    }

                    // Now create a winner record for this duplicate award
                    $winnerData = [
                        'is_finalist' => true,
                        'is_winner' => true,
                        'tshirt_size' => $this->tshirt_size,
                        'verification_state' => WinnerVerificationState::PENDING_VERIFICATION->value
                    ];

                    $newWinner = new Winner();
                    $newWinner->user_id = $this->user->id;
                    $newWinner->award_id = $duplicateAward->id;
                    $newWinner->year = $duplicateAward->year;
                    $newWinner->is_winner = $winnerData['is_winner'];
                    $newWinner->is_finalist = $winnerData['is_finalist'];
                    $newWinner->verification_state = $winnerData['verification_state'];
                    $newWinner->tshirt_size = $winnerData['tshirt_size'];
                    $newWinner->save();

                    $this->awardWinners->push($newWinner);
                }

                // Remove old types
                foreach ($this->awardWinners as $key => $winner) {
                    if ($winner->award &&
                        $winner->award->type instanceof AwardType &&
                        in_array($winner->award->type->value, $typesToRemove)) {
                        $this->awardService->removeWinner($winner->award_id, $winner->id);
                        $this->awardWinners->forget($key);
                    }
                }

                // Update t-shirt size for all remaining winners
                foreach ($this->awardWinners as $winner) {
                    if ($winner->tshirt_size !== $this->tshirt_size) {
                        $winner->tshirt_size = $this->tshirt_size;
                        $winner->save();
                    }
                }

                // Sync t-shirt size with scholarship winner if it exists
                if ($this->scholarshipWinner && $this->scholarshipWinner->tshirt_size !== $this->tshirt_size) {
                    $this->scholarshipWinner->tshirt_size = $this->tshirt_size;
                    $this->scholarshipWinner->save();
                }

                DB::commit();
                $this->dispatch('winner-updated');
                Notification::make()->title('Award information updated successfully')->success()->send();
            } else if ($this->awardWinners->isNotEmpty()) {
                // Remove all award winner records
                foreach ($this->awardWinners as $winner) {
                    $this->awardService->removeWinner($winner->award_id, $winner->id);
                }

                $this->awardWinners = new Collection();
                $this->award_types = [];

                DB::commit();
                $this->dispatch('winner-updated');
                Notification::make()->title('Award information removed')->success()->send();
            }

            // Recalculate the effective verification state
            $this->calculateEffectiveVerificationState();

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update award: ' . $e->getMessage());
            Notification::make()
                ->title('Failed to update award information')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    /**
     * Apply verification state to all winner records
     */
    protected function applyVerificationState(string $state, string $timestampField = null): void
    {
        $timestamp = now();

        // Update scholarship winner if it exists and user is a positive athlete
        if ($this->user->profile_type === ProfileType::POSITIVE_ATHLETE && $this->scholarshipWinner) {
            $this->scholarshipWinner->verification_state = $state;

            if ($timestampField) {
                $this->scholarshipWinner->$timestampField = $timestamp;
            }

            $this->scholarshipWinner->save();
        }

        foreach ($this->awardWinners as $winner) {
            $winner->verification_state = $state;

            if ($timestampField) {
                $winner->$timestampField = $timestamp;
            }

            $winner->save();
        }

        $this->verification_state = $state;

        if ($timestampField) {
            $this->$timestampField = $timestamp;
        }
    }

        /**
     * Toggle verification state for a specific stage
     */
    public function toggleVerificationState(string $targetState): void
    {
        try {
            // For positive athletes, check for either scholarship or award winners
            // For other profiles, just check for award winners
            $hasNoWinnerRecords = ($this->user->profile_type === ProfileType::POSITIVE_ATHLETE)
                ? (!$this->scholarshipWinner && $this->awardWinners->isEmpty())
                : $this->awardWinners->isEmpty();

            if ($hasNoWinnerRecords) {
                Notification::make()->title('No awards or scholarships selected')->warning()->send();
                return;
            }

            DB::beginTransaction();

            $currentState = WinnerVerificationState::tryFrom($this->verification_state);
            $targetStateEnum = WinnerVerificationState::tryFrom($targetState);

            if (!$targetStateEnum) {
                throw new \InvalidArgumentException("Invalid verification state: $targetState");
            }

            // Special handling for pending_verification - it can't be unchecked
            if ($targetStateEnum === WinnerVerificationState::PENDING_VERIFICATION) {
                // Reset to pending verification (unchecking all other states)
                $this->applyVerificationState(
                    WinnerVerificationState::PENDING_VERIFICATION->value,
                    null
                );
                $message = 'Reset to pending verification';
            } else {
                // Determine if we're checking or unchecking
                $isCurrentlyChecked = $currentState && $currentState->rank() >= $targetStateEnum->rank();

                if (!$isCurrentlyChecked) {
                    // Moving forward in the process
                    $this->applyVerificationState(
                        $targetStateEnum->value,
                        $this->getTimestampFieldForState($targetStateEnum)
                    );

                    $message = match($targetStateEnum) {
                        WinnerVerificationState::VERIFIED => 'Nominee verified with Athletic Director',
                        WinnerVerificationState::NOTIFIED => 'Nominee notified of win',
                        WinnerVerificationState::ACKNOWLEDGED => 'Nominee acknowledgment recorded',
                        default => 'Verification status updated'
                    };
                } else {
                    // Moving backward - find the previous state
                    $previousState = $this->getPreviousState($targetStateEnum);
                    if ($previousState) {
                        $this->applyVerificationState(
                            $previousState->value,
                            null // Don't update timestamp when going backward
                        );
                    } else {
                        $this->applyVerificationState(
                            WinnerVerificationState::PENDING_VERIFICATION->value,
                            null
                        );
                    }

                    $message = 'Verification status updated';
                }
            }

            DB::commit();
            $this->dispatch('winner-updated');
            Notification::make()->title($message)->success()->send();

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to toggle verification state: ' . $e->getMessage());
            Notification::make()->title('Failed to update verification status')->danger()->send();
        }
    }

    /**
     * Get the timestamp field name for a given state
     */
    private function getTimestampFieldForState(WinnerVerificationState $state): ?string
    {
        return match($state) {
            WinnerVerificationState::VERIFIED => 'verified_at',
            WinnerVerificationState::NOTIFIED => 'notified_at',
            WinnerVerificationState::ACKNOWLEDGED => 'acknowledged_at',
            default => null
        };
    }

    /**
     * Get the previous state in the verification process
     */
    private function getPreviousState(WinnerVerificationState $state): ?WinnerVerificationState
    {
        return match($state) {
            WinnerVerificationState::ACKNOWLEDGED => WinnerVerificationState::NOTIFIED,
            WinnerVerificationState::NOTIFIED => WinnerVerificationState::VERIFIED,
            WinnerVerificationState::VERIFIED => WinnerVerificationState::PENDING_VERIFICATION,
            WinnerVerificationState::PENDING_VERIFICATION => null,
        };
    }

    /**
     * Verify nominee with Athletic Director (legacy method for backward compatibility)
     */
    public function verifyWithAD(): void
    {
        $this->toggleVerificationState(WinnerVerificationState::VERIFIED->value);
    }

    /**
     * Notify nominee of win (legacy method for backward compatibility)
     */
    public function notifyNominee(): void
    {
        $this->toggleVerificationState(WinnerVerificationState::NOTIFIED->value);
    }

    /**
     * Mark nominee as acknowledged (legacy method for backward compatibility)
     */
    public function markAcknowledged(): void
    {
        $this->toggleVerificationState(WinnerVerificationState::ACKNOWLEDGED->value);
    }

    /**
     * Update T-shirt size for all winner records
     */
    public function updateTshirtSize(): void
    {
        try {
            if (!$this->scholarshipWinner && $this->awardWinners->isEmpty()) {
                return;
            }

            DB::beginTransaction();

            if ($this->scholarshipWinner) {
                $this->scholarshipWinner->tshirt_size = $this->tshirt_size;
                $this->scholarshipWinner->save();
            }

            foreach ($this->awardWinners as $winner) {
                $winner->tshirt_size = $this->tshirt_size;
                $winner->save();
            }

            DB::commit();
            $this->dispatch('winner-updated');
            Notification::make()->title('T-shirt size updated')->success()->send();

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update t-shirt size: ' . $e->getMessage());
            Notification::make()->title('Failed to update t-shirt size')->danger()->send();
        }
    }

    public function render()
    {
        // Get current academic year for scholarships
        $currentScholarshipYear = $this->scholarshipService->getDefaultScholarshipYear();

        // Get current academic year for awards
        $currentAwardYear = $this->awardService->getDefaultAwardYear();

        return view('livewire.winners.winner-information-management', [
            'verificationState' => WinnerVerificationState::tryFrom($this->verification_state) ?? WinnerVerificationState::PENDING_VERIFICATION,
            'currentScholarshipYear' => $currentScholarshipYear,
            'currentAwardYear' => $currentAwardYear,
        ]);
    }
}
