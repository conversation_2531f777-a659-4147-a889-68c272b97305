<?php

namespace App\Livewire\Awards;

use App\Models\Award;
use App\Models\Region;
use App\Models\Market;
use App\Models\State;
use App\Models\SubRegion;
use App\Services\AwardService;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Livewire\Component;
use App\Filament\Admin\Pages\Awards\AwardEdit;
use Filament\Notifications\Notification;

class AwardsView extends Component implements HasTable, HasForms
{
    use InteractsWithTable, InteractsWithForms;

    /**
     * Currently selected region ID for filtering
     */
    public ?string $selectedRegion = null;

    /**
     * The AwardService instance
     */
    protected AwardService $awardService;

    /**
     * Constructor
     */
    public function boot(AwardService $awardService): void
    {
        $this->awardService = $awardService;
    }

    /**
     * Listen for region filter changes and award creation events
     */
    protected $listeners = [
        'award-created' => '$refresh',
        'region-changed' => 'handleRegionChange',
    ];

    /**
     * Handle region selection from region filter bar
     */
    public function handleRegionChange(?string $regionId): void
    {
        $this->selectedRegion = $regionId;
        $this->resetTable();
    }

    /**
     * Get all regions for the filter bar
     */
    public function getRegionsProperty(): array
    {
        return Region::query()
            ->orderBy('name')
            ->get(['id', 'name'])
            ->toArray();
    }

    /**
     * Build the table structure
     */
    public function table(Table $table): Table
    {
        $query = Award::query()
            ->with(['region', 'market', 'state', 'subregion']);

        // Apply region filter if selected
        if ($this->selectedRegion) {
            $query->where('region_id', $this->selectedRegion);
        }

        return $table
            ->query($query)
            ->columns([
                TextColumn::make('name')
                    ->label('Award Name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('type')
                    ->label('Type')
                    ->sortable()
                    ->formatStateUsing(fn($state) => match ($state) {
                        'regional' => 'Regional',
                        'market' => 'Market',
                        'subregional' => 'Subregional',
                        default => $state,
                    }),
                TextColumn::make('year')
                    ->label('Year')
                    ->formatStateUsing(fn($state) => "{$state}-" . ($state + 1))
                    ->searchable()
                    ->sortable(),
                TextColumn::make('market.name')
                    ->label('Market')
                    ->searchable(['markets.name'])
                    ->sortable(),
                TextColumn::make('state.name')
                    ->label('State')
                    ->searchable(['states.name'])
                    ->sortable(),
                TextColumn::make('subregion.name')
                    ->label('Subregion')
                    ->searchable(['sub_regions.name'])
                    ->sortable(),
                IconColumn::make('is_active')
                    ->label('Status')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger')
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('year')
                    ->options(function () {
                        $years = Award::query()
                            ->distinct()
                            ->orderBy('year', 'desc')
                            ->pluck('year')
                            ->toArray();

                        $options = [];
                        foreach ($years as $year) {
                            $options[$year] = "{$year}-" . ($year + 1);
                        }

                        return $options;
                    })
                    ->attribute('year'),

                SelectFilter::make('type')
                    ->label('Type')
                    ->options([
                        'regional' => 'Regional',
                        'market' => 'Market',
                        'subregional' => 'Subregional',
                    ])
                    ->attribute('type'),

                SelectFilter::make('market')
                    ->label('Market')
                    ->relationship('market', 'name')
                    ->searchable()
                    ->preload(),

                SelectFilter::make('state')
                    ->label('State')
                    ->relationship('state', 'name')
                    ->searchable()
                    ->preload(),

                SelectFilter::make('region')
                    ->label('Region')
                    ->relationship('region', 'name')
                    ->searchable()
                    ->preload(),

                SelectFilter::make('subregion')
                    ->label('Subregion')
                    ->relationship('subregion', 'name')
                    ->searchable()
                    ->preload(),

                SelectFilter::make('is_active')
                    ->options([
                        '1' => 'Active',
                        '0' => 'Inactive',
                    ])
                    ->attribute('is_active'),
            ])
            ->actions([
                Action::make('view')
                    ->label('View')
                    ->icon('heroicon-o-eye')
                    ->url(fn(Award $record) => AwardEdit::getUrl(['award' => $record]))
                    ->openUrlInNewTab(false),

                Action::make('toggle_status')
                    ->label(fn(Award $record) => $record->is_active ? 'Deactivate' : 'Activate')
                    ->icon(fn(Award $record) => $record->is_active ? 'heroicon-o-x-circle' : 'heroicon-o-check-circle')
                    ->color(fn(Award $record) => $record->is_active ? 'danger' : 'success')
                    ->action(function (Award $record) {
                        if ($record->is_active) {
                            $this->awardService->deactivateAward($record->id);
                        } else {
                            $this->awardService->activateAward($record->id);
                        }

                        Notification::make()
                            ->title($record->is_active ? 'Award deactivated' : 'Award activated')
                            ->success()
                            ->send();
                    }),

                Action::make('delete')
                    ->label('Delete')
                    ->icon('heroicon-o-trash')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading('Delete Award')
                    ->modalDescription('Are you sure you want to delete this award? This action cannot be undone.')
                    ->modalSubmitActionLabel('Yes, delete it')
                    ->action(function (Award $record) {
                        try {
                            $this->awardService->deleteAward($record->id);

                            Notification::make()
                                ->title('Award deleted successfully')
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Failed to delete award')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),
            ])
            ->bulkActions([
                BulkAction::make('delete')
                    ->label('Delete')
                    ->icon('heroicon-o-trash')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading('Delete Awards')
                    ->modalDescription('Are you sure you want to delete these awards? This action cannot be undone.')
                    ->modalSubmitActionLabel('Yes, delete them')
                    ->action(function (Collection $records) {
                        $successCount = 0;
                        $errorCount = 0;

                        $records->each(function ($record) use (&$successCount, &$errorCount) {
                            try {
                                $this->awardService->deleteAward($record->id);
                                $successCount++;
                            } catch (\Exception $e) {
                                $errorCount++;
                            }
                        });

                        if ($successCount > 0) {
                            Notification::make()
                                ->title("Successfully deleted {$successCount} award(s)")
                                ->success()
                                ->send();
                        }

                        if ($errorCount > 0) {
                            Notification::make()
                                ->title("Failed to delete {$errorCount} award(s)")
                                ->danger()
                                ->send();
                        }

                        $this->resetTable();
                    }),

                BulkAction::make('duplicate')
                    ->label('Duplicate')
                    ->icon('heroicon-o-document-duplicate')
                    ->color('gray')
                    ->action(function (Collection $records) {
                        $successCount = 0;
                        $errorCount = 0;

                        $records->each(function ($record) use (&$successCount, &$errorCount) {
                            try {
                                // Use the service method
                                $this->awardService->duplicateAward($record);
                                $successCount++;
                            } catch (\Exception $e) {
                                $errorCount++;
                            }
                        });

                        if ($successCount > 0) {
                            Notification::make()
                                ->title("Successfully duplicated {$successCount} award(s)")
                                ->success()
                                ->send();
                        }

                        if ($errorCount > 0) {
                            Notification::make()
                                ->title("Failed to duplicate {$errorCount} award(s)")
                                ->danger()
                                ->send();
                        }

                        $this->resetTable();
                    }),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public function render(): View
    {
        return view('livewire.awards.awards-view');
    }
}
