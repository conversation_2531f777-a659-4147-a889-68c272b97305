<?php

namespace App\Livewire\Awards;

use App\Enums\AwardType;
use App\Models\Award;
use App\Models\Market;
use App\Models\Region;
use App\Models\State;
use App\Models\SubRegion;
use App\Models\Winner;
use App\Services\AwardService;
use Filament\Tables\Actions\Action;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Table;
use Illuminate\Contracts\View\View;
use Livewire\Component;
use Livewire\Attributes\Computed;

class EditAwardView extends Component implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    public Award $award;
    public ?array $data = [];
    public string $newTag = '';

    // Geographic selected value properties
    public ?string $selectedRegion = null;
    public ?string $selectedMarket = null;
    public ?string $selectedState = null;
    public ?string $selectedSubRegion = null;

    // Geographic options properties
    public array $marketOptions = [];
    public array $subRegionOptions = [];

    // Loading states
    public bool $isLoadingMarkets = false;
    public bool $isLoadingSubRegions = false;

    protected $listeners = [
        'forceRefresh' => 'refreshData',
        'notesUpdated' => '$refresh',
        'tagsUpdated' => '$refresh',
    ];

    public function mount(Award $award): void
    {
        // Ensure we have the latest award data with relationships
        $this->award = $award->fresh(['winners', 'region', 'market', 'subregion', 'state']);

        // Initialize selected values first
        $this->selectedRegion = $this->award->region_id;
        $this->selectedMarket = $this->award->market_id;
        $this->selectedState = $this->award->state_id;
        $this->selectedSubRegion = $this->award->subregion_id;

        // Initialize geo options
        $this->initializeGeographicOptions();

        // Load market options if region is selected
        if ($this->selectedRegion) {
            $this->loadMarketsFromRegion();
        }

        // Load subregion options if market is selected
        if ($this->selectedMarket) {
            $this->loadSubRegionsFromMarket();
        }

        // After loading all options, then fill the form
        $this->data = [
            'name' => $this->award->name,
            'type' => $this->award->type,
            'year' => $this->award->year,
            'details' => $this->award->details,
            'is_active' => $this->award->is_active,
            'notes' => $this->award->notes,
            'region_id' => $this->award->region_id,
            'market_id' => $this->award->market_id,
            'state_id' => $this->award->state_id,
            'subregion_id' => $this->award->subregion_id,
        ];
    }

    /**
     * Initialize geographic options
     */
    private function initializeGeographicOptions(): void
    {
        // Markets and subregions are loaded based on selections
        $this->marketOptions = [];
        $this->subRegionOptions = [];
    }

    /**
     * Watch for changes to selected region
     */
    public function updatedSelectedRegion(): void
    {
        // Only reset the dependent dropdown values
        $this->selectedMarket = null;
        $this->selectedSubRegion = null;
        $this->marketOptions = [];
        $this->subRegionOptions = [];

        // Clear the dependent field values in the form data
        if (isset($this->data['market_id'])) {
            $this->data['market_id'] = null;
        }

        if (isset($this->data['subregion_id'])) {
            $this->data['subregion_id'] = null;
        }

        if (empty($this->selectedRegion)) {
            return;
        }

        $this->isLoadingMarkets = true;
        $this->loadMarketsFromRegion();
        $this->isLoadingMarkets = false;
    }

    /**
     * Watch for changes to selected market
     */
    public function updatedSelectedMarket(): void
    {
        // Only reset the dependent dropdown values
        $this->selectedSubRegion = null;
        $this->subRegionOptions = [];

        // Clear the dependent field value in the form data
        if (isset($this->data['subregion_id'])) {
            $this->data['subregion_id'] = null;
        }

        if (empty($this->selectedMarket)) {
            return;
        }

        $this->isLoadingSubRegions = true;
        $this->loadSubRegionsFromMarket();
        $this->isLoadingSubRegions = false;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make(2)
                    ->schema([
                        TextInput::make('name')
                            ->label('Award Name')
                            ->required()
                            ->maxLength(255),

                        Select::make('type')
                            ->label('Award Type')
                            ->options([
                                AwardType::REGIONAL->value => 'Regional',
                                AwardType::MARKET->value => 'Market',
                                AwardType::SUBREGIONAL->value => 'Subregional',
                            ])
                            ->required(),

                        TextInput::make('year')
                            ->label('Award Year')
                            ->required()
                            ->numeric()
                            ->minValue(now()->year)
                            ->helperText('The year this award is presented'),

                        Toggle::make('is_active')
                            ->label('Is Active')
                            ->onIcon('heroicon-o-check-circle')
                            ->offIcon('heroicon-o-x-circle')
                            ->onColor('success')
                            ->offColor('danger')
                            ->helperText('Inactive awards will not be visible to users'),
                    ]),

                Grid::make(2)
                    ->schema([
                        Select::make('region_id')
                            ->label('Region')
                            ->options(Region::query()->orderBy('name')->pluck('name', 'id')->toArray())
                            ->afterStateUpdated(function ($state) {
                                $this->selectedRegion = $state;
                                $this->updatedSelectedRegion();
                            })
                            ->searchable()
                            ->required()
                            ->reactive(),

                        Select::make('state_id')
                            ->label('State')
                            ->options(State::query()->orderBy('name')->pluck('name', 'code')->toArray())
                            ->searchable()
                            ->required(),

                        Select::make('market_id')
                            ->label('Market')
                            ->options(fn() => $this->marketOptions)
                            ->afterStateUpdated(function ($state) {
                                $this->selectedMarket = $state;
                                $this->updatedSelectedMarket();
                            })
                            ->searchable()
                            ->required()
                            ->disabled(fn() => empty($this->selectedRegion) || $this->isLoadingMarkets)
                            ->loadingMessage('Loading markets...')
                            ->helperText(fn() => empty($this->selectedRegion) ? 'Select a region first' : '')
                            ->reactive(),

                        Select::make('subregion_id')
                            ->label('Subregion')
                            ->options(fn() => $this->subRegionOptions)
                            ->afterStateUpdated(function ($state) {
                                $this->selectedSubRegion = $state;
                            })
                            ->searchable()
                            ->disabled(fn() => empty($this->selectedMarket) || $this->isLoadingSubRegions)
                            ->loadingMessage('Loading subregions...')
                            ->helperText(fn() => empty($this->selectedMarket) ? 'Select a market first' : ''),
                    ]),

                RichEditor::make('details')
                    ->label('Details')
                    ->toolbarButtons([
                        'h2',
                        'h3',
                        'bold',
                        'italic',
                        'underline',
                        'bulletList',
                        'orderedList',
                    ])
                    ->columnSpanFull(),
            ])
            ->statePath('data');
    }

    /**
     * Save changes to the award
     */
    public function update(): void
    {
        $this->award->update([
            'name' => $this->data['name'] ?? $this->award->name,
            'type' => $this->data['type'] ?? $this->award->type,
            'year' => $this->data['year'] ?? $this->award->year,
            'details' => $this->data['details'] ?? $this->award->details,
            'is_active' => $this->data['is_active'] ?? $this->award->is_active,
            'notes' => $this->data['notes'] ?? $this->award->notes,
            'region_id' => $this->data['region_id'] ?? $this->award->region_id,
            'market_id' => $this->data['market_id'] ?? $this->award->market_id,
            'state_id' => $this->data['state_id'] ?? $this->award->state_id,
            'subregion_id' => $this->data['subregion_id'] ?? $this->award->subregion_id,
        ]);

        Notification::make()
            ->title('Award updated successfully')
            ->success()
            ->send();
    }

    /**
     * Delete the award
     */
    public function deleteAward(): void
    {
        $this->dispatch('open-modal', id: 'delete-modal');
    }

    /**
     * Confirm award deletion
     */
    public function confirmDelete(): void
    {
        // Delete award
        $awardService = app(AwardService::class);
        $result = $awardService->deleteAward($this->award->id);

        if ($result) {
            Notification::make()
                ->title('Award deleted successfully')
                ->success()
                ->send();

            // Redirect to awards index
            redirect()->route('filament.admin.pages.awards');
        } else {
            Notification::make()
                ->title('Failed to delete award')
                ->danger()
                ->send();
        }
    }

    /**
     * Refresh award data
     */
    public function refreshData(): void
    {
        // Get current form data
        $currentData = $this->data;

        // Refresh the award model
        $this->award = $this->award->fresh(['winners', 'region', 'market', 'subregion', 'state']);

        // Update selected values
        $this->selectedRegion = $this->award->region_id;
        $this->selectedMarket = $this->award->market_id;
        $this->selectedState = $this->award->state_id;
        $this->selectedSubRegion = $this->award->subregion_id;

        // Reload options based on current selections
        if ($this->selectedRegion) {
            $this->loadMarketsFromRegion();
        }

        if ($this->selectedMarket) {
            $this->loadSubRegionsFromMarket();
        }

        // Update data while preserving form input that hasn't changed
        $this->data = array_merge($currentData, [
            'region_id' => $this->award->region_id,
            'market_id' => $this->award->market_id,
            'state_id' => $this->award->state_id,
            'subregion_id' => $this->award->subregion_id,
        ]);
    }

    /**
     * Load markets for a region
     */
    private function loadMarketsFromRegion(): void
    {
        if (!$this->selectedRegion) {
            $this->marketOptions = [];
            return;
        }

        $this->marketOptions = Market::query()
            ->where('region_id', $this->selectedRegion)
            ->orderBy('name')
            ->pluck('name', 'id')
            ->toArray();
    }

    /**
     * Load subregions for a market
     */
    private function loadSubRegionsFromMarket(): void
    {
        if (!$this->selectedMarket) {
            $this->subRegionOptions = [];
            return;
        }

        $this->subRegionOptions = SubRegion::query()
            ->where('market_id', $this->selectedMarket)
            ->orderBy('name')
            ->pluck('name', 'id')
            ->toArray();
    }

    #[Computed]
    public function winners()
    {
        return $this->award->winners()->with(['user', 'user.sports', 'user.state'])->get();
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(
                fn() => $this->award->winners()->with(['user', 'user.sports', 'user.state'])
            )
            ->columns([
                TextColumn::make('user.full_name')
                    ->label('Name')
                    ->sortable()
                    ->searchable()
                    ->formatStateUsing(function ($state, $record) {
                        return view('components.user-avatar-with-name', [
                            'user' => $record->user,
                            'name' => $state,
                        ]);
                    }),
                TextColumn::make('user.ai_score')
                    ->label('AI Score')
                    ->sortable()
                    ->formatStateUsing(fn($state) => $state ?? 'N/A')
                    ->color('success'),
                TextColumn::make('verification_state')
                    ->label('Award Status')
                    ->sortable()
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'Responded' => 'success',
                        'Notified' => 'info',
                        default => 'gray',
                    }),
                TextColumn::make('user.sports.name')
                    ->label('Sport(s)')
                    ->formatStateUsing(function (Winner $record) {
                        $sport = $record->user->sports->first();
                        $additional = $record->user->sports->count() > 1 ? ' +' . ($record->user->sports->count() - 1) : '';
                        return view('components.sport-badge', [
                            'name' => $sport->name,
                            'additional' => $additional,
                        ]);
                    })
                    ->html(),
                TextColumn::make('user.graduation_year')
                    ->label('HS Grad Year')
                    ->sortable()
                    ->formatStateUsing(fn($state) => $state ?? 'N/A'),
                TextColumn::make('user.state.code')
                    ->label('State')
                    ->sortable()
                    ->formatStateUsing(fn($state) => $state ?? 'N/A'),
                TextColumn::make('user.gender')
                    ->label('Gender')
                    ->sortable()
                    ->formatStateUsing(fn($state) => $state ?? 'N/A'),
                TextColumn::make('')
                    ->grow(),
            ])
            ->actions([
                Action::make('view')
                    ->label('View/Edit')
                    ->icon('heroicon-o-pencil-square')
                    ->url(fn(Winner $record) => route('filament.admin.pages.athletes.{athlete}', ['athlete' => $record->user->id]))
                    ->openUrlInNewTab(false),
            ])
            ->emptyStateHeading('No winners')
            ->emptyStateDescription('This award does not have any winners yet.');
    }

    public function render(): View
    {
        return view('livewire.awards.edit-award-view');
    }
}
