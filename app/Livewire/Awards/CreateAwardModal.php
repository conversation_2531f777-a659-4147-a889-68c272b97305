<?php

namespace App\Livewire\Awards;

use App\Enums\AwardType;
use App\Models\Market;
use App\Models\Region;
use App\Models\State;
use App\Models\SubRegion;
use App\Services\AwardService;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Grid;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Illuminate\Contracts\View\View;
use Livewire\Component;
use Livewire\Attributes\Computed;

class CreateAwardModal extends Component implements HasForms
{
    use InteractsWithForms;

    // Form state properties
    public ?string $name = null;
    public ?string $type = null;
    public ?int $year = null;
    public ?string $region_id = null;
    public ?string $state_id = null;
    public ?string $market_id = null;
    public ?string $subregion_id = null;
    public ?string $details = null;
    public bool $is_active = true;

    // Track when fields change
    public function updatedRegionId($value)
    {
        $this->market_id = null;
        $this->subregion_id = null;
    }

    public function updatedMarketId($value)
    {
        $this->subregion_id = null;
    }

    /**
     * Mount the component and initialize form
     */
    public function mount(): void
    {
        $awardService = app(AwardService::class);
        $this->year = $awardService->getDefaultAwardYear();
        $this->is_active = true;
        $this->type = AwardType::REGIONAL->value;
    }

    /**
     * Get available markets based on selected region
     */
    #[Computed]
    public function availableMarkets()
    {
        if (!$this->region_id) {
            return [];
        }

        return Market::query()
            ->where('region_id', $this->region_id)
            ->orderBy('name')
            ->pluck('name', 'id')
            ->toArray();
    }

    /**
     * Get available subregions based on selected market
     */
    #[Computed]
    public function availableSubregions()
    {
        if (!$this->market_id) {
            return [];
        }

        return SubRegion::query()
            ->where('market_id', $this->market_id)
            ->orderBy('name')
            ->pluck('name', 'id')
            ->toArray();
    }

    /**
     * Define the form schema
     */
    public function form(Form $form): Form
    {
        $awardService = app(AwardService::class);

        return $form
            ->schema([
                // Basic Information fields - 2 columns
                Grid::make(2)
                    ->schema([
                        TextInput::make('name')
                            ->label('Award Name')
                            ->required()
                            ->maxLength(255),

                        Select::make('type')
                            ->label('Award Type')
                            ->options([
                                AwardType::REGIONAL->value => 'Regional',
                                AwardType::MARKET->value => 'Market',
                                AwardType::SUBREGIONAL->value => 'Subregional',
                            ])
                            ->required()
                            ->default(AwardType::REGIONAL->value),

                        TextInput::make('year')
                            ->label('Award Year')
                            ->required()
                            ->numeric()
                            ->default($awardService->getDefaultAwardYear())
                            ->minValue(now()->year)
                            ->helperText('The year this award is presented'),

                        Toggle::make('is_active')
                            ->label('Active')
                            ->default(true)
                            ->helperText('Inactive awards will not be visible to users'),
                    ]),

                // Location fields - 2 columns
                Grid::make(2)
                    ->schema([
                        Select::make('region_id')
                            ->label('Region')
                            ->options(fn () => Region::orderBy('name')->pluck('name', 'id')->toArray())
                            ->searchable()
                            ->required()
                            ->preload()
                            ->live(),

                        Select::make('state_id')
                            ->label('State')
                            ->options(fn () => State::orderBy('name')->pluck('name', 'code')->toArray())
                            ->searchable()
                            ->required()
                            ->preload(),

                        Select::make('market_id')
                            ->label('Market')
                            ->options(fn () => $this->availableMarkets)
                            ->searchable()
                            ->required()
                            ->preload()
                            ->live(),

                        Select::make('subregion_id')
                            ->label('Subregion')
                            ->options(fn () => $this->availableSubregions)
                            ->searchable()
                            ->preload(),
                    ]),

                // Details fields
                Textarea::make('details')
                    ->label('Details')
                    ->rows(4)
                    ->maxLength(65535),
            ]);
    }

    /**
     * Reset the form to initial state
     */
    public function resetForm(): void
    {
        $awardService = app(AwardService::class);

        $this->name = null;
        $this->type = AwardType::REGIONAL->value;
        $this->year = $awardService->getDefaultAwardYear();
        $this->region_id = null;
        $this->state_id = null;
        $this->market_id = null;
        $this->subregion_id = null;
        $this->details = null;
        $this->is_active = true;
    }

    /**
     * Create a new award
     */
    public function create(): void
    {
        $data = [
            'name' => $this->name,
            'type' => $this->type,
            'year' => $this->year,
            'region_id' => $this->region_id,
            'state_id' => $this->state_id,
            'market_id' => $this->market_id,
            'subregion_id' => $this->subregion_id,
            'details' => $this->details,
            'is_active' => $this->is_active,
        ];

        try {
            // Create award using service
            $awardService = app(AwardService::class);
            $awardService->createAward($data);

            // Show success notification
            Notification::make()
                ->title('Award created successfully')
                ->success()
                ->send();

            // Reset form and close modal
            $this->resetForm();
            $this->dispatch('close-modal', id: 'new-award-modal');

            // Refresh parent component
            $this->dispatch('award-created');
        } catch (\Exception $e) {
            Notification::make()
                ->title('Error creating award')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    /**
     * Render the component
     */
    public function render(): View
    {
        return view('livewire.awards.create-award-modal');
    }
}
