<?php

namespace App\Livewire\Advertisements;

use App\Models\Advertisement;
use App\Models\Region;
use App\Models\User;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Livewire\Component;
use App\Filament\Admin\Pages\Advertisements\AdvertisementEdit;

class AdvertisementsView extends Component implements HasTable, HasForms
{
    use InteractsWithTable, InteractsWithForms;

    /**
     * Currently selected region ID for filtering
     */
    public ?string $selectedRegion = null;

    /**
     * Listen for region filter changes
     */
    protected $listeners = [
        'region-changed' => 'handleRegionChange',
        'advertisement-created' => 'refreshTable',
    ];

    /**
     * Handle region selection change from filter bar
     */
    public function handleRegionChange(?string $regionId): void
    {
        $this->selectedRegion = $regionId;
        $this->resetTable();
    }

    /**
     * Refresh the table data
     */
    public function refreshTable(): void
    {
        $this->resetTable();
    }

    /**
     * Open the create advertisement modal
     */
    public function createAdvertisement(): void
    {
        $this->dispatch('createAdvertisement');
    }

    /**
     * Get all regions for the filter bar
     */
    public function getRegionsProperty(): array
    {
        return Region::query()
            ->orderBy('name')
            ->get(['id', 'name'])
            ->toArray();
    }

    public function table(Table $table): Table
    {
        $query = Advertisement::query()
            ->with([
                'user',
                'regions',
                'media' => fn($query) => $query->whereIn('collection_name', ['logo', 'background']),
            ]);

        if ($this->selectedRegion) {
            $query->whereHas('regions', function ($query) {
                $query->where('regions.id', $this->selectedRegion);
            });
        }

        return $table
            ->query($query)
            ->columns([
                ImageColumn::make('logo')
                    ->circular()
                    ->defaultImageUrl(fn(Advertisement $record) => 'https://ui-avatars.com/api/?name=' . urlencode($record->name))
                    ->getStateUsing(fn (Advertisement $record) => $record->getFirstMediaUrl('logo'))
                    ->label('')
                    ->size(40),
                TextColumn::make('name')
                    ->label('Advertisement')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('user.full_name')
                    ->label('Sponsor')
                    ->formatStateUsing(fn(Advertisement $record): ?string => $record->user?->full_name ?? 'No Sponsor')
                    ->searchable(['users.first_name', 'users.last_name'])
                    ->sortable(),
                IconColumn::make('is_listed')
                    ->label('Status')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger')
                    ->sortable(),
                TextColumn::make('impressions')
                    ->label('Impressions')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('clicks')
                    ->label('Clicks')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('click_through_rate')
                    ->label('CTR')
                    ->formatStateUsing(fn(Advertisement $record): string => number_format($record->click_through_rate, 2) . '%')
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        return $query->orderByRaw('CASE WHEN impressions = 0 THEN 0 ELSE clicks::float / impressions::float END ' . $direction);
                    }),
                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        '1' => 'Active',
                        '0' => 'Inactive',
                    ])
                    ->attribute('is_listed'),

                SelectFilter::make('sponsor')
                    ->relationship('user', 'first_name')
                    ->searchable()
                    ->preload(),

                Filter::make('created_at')
                    ->form([
                        \Filament\Forms\Components\DatePicker::make('created_from')
                            ->label('Created from'),
                        \Filament\Forms\Components\DatePicker::make('created_until')
                            ->label('Created until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    })
            ])
            ->actions([
                Action::make('view')
                    ->label('View/Edit')
                    ->icon('heroicon-o-pencil-square')
                    ->url(fn(Advertisement $record) => AdvertisementEdit::getUrl(['advertisement' => $record]))
                    ->openUrlInNewTab(false),

                Action::make('toggle_status')
                    ->label(fn(Advertisement $record) => $record->is_listed ? 'Deactivate' : 'Activate')
                    ->icon(fn(Advertisement $record) => $record->is_listed ? 'heroicon-o-x-circle' : 'heroicon-o-check-circle')
                    ->color(fn(Advertisement $record) => $record->is_listed ? 'danger' : 'success')
                    ->action(function (Advertisement $record) {
                        $record->update([
                            'is_listed' => !$record->is_listed,
                        ]);
                    }),

                Action::make('delete')
                    ->label('Delete')
                    ->icon('heroicon-o-trash')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading('Delete Advertisement')
                    ->modalDescription('Are you sure you want to delete this advertisement? This action cannot be undone.')
                    ->modalSubmitActionLabel('Yes, delete it')
                    ->action(function (Advertisement $record) {
                        $record->delete();
                    }),
            ])
            ->bulkActions([
                // Add bulk actions if needed in the future
            ])
            ->defaultSort('created_at', 'desc');
    }

    public function render(): View
    {
        return view('livewire.advertisements.advertisements-view');
    }
}
