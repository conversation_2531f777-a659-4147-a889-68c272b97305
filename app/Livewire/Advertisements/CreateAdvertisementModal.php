<?php

namespace App\Livewire\Advertisements;

use App\Enums\ProfileType;
use App\Enums\UiRegion;
use App\Models\Market;
use App\Models\Region;
use App\Models\State;
use App\Models\SubRegion;
use App\Models\User;
use App\Services\AdvertisementService;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Grid;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Illuminate\Contracts\View\View;
use Livewire\Component;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class CreateAdvertisementModal extends Component implements HasForms
{
    use InteractsWithForms;

    public ?array $data = [];

    /**
     * Properties for geographic selection
     */
    public array $selectedRegions = [];
    public array $selectedMarkets = [];
    public array $selectedStates = [];
    public array $selectedSubRegions = [];

    // Options for select fields
    public array $regionOptions = [];
    public array $marketOptions = [];
    public array $stateOptions = [];
    public array $subRegionOptions = [];

    // Loading states
    public bool $isLoadingMarkets = false;
    public bool $isLoadingStates = false;
    public bool $isLoadingSubRegions = false;

    protected $listeners = [
        'forceRefresh' => '$refresh',
    ];

    public function mount(): void
    {
        // Initialize regionOptions
        $this->initializeGeographicOptions();

        // Initialize the form with empty values
        $this->form->fill([
            'regions' => [],
            'markets' => [],
            'states' => [],
            'sub_regions' => [],
        ]);
    }

    /**
     * Initialize geographic options with initial data
     */
    private function initializeGeographicOptions(): void
    {
        // Load initial region options
        $this->regionOptions = Region::query()
            ->orderBy('name')
            ->pluck('name', 'id')
            ->toArray();

        // Some states aren't tied to regions directly, so we load them all initially
        $this->stateOptions = State::query()
            ->orderBy('name')
            ->pluck('name', 'code')
            ->toArray();
    }

    /**
     * Handle changes to selectedRegions
     */
    public function updatedSelectedRegions(): void
    {
        $this->reset(['selectedMarkets', 'selectedSubRegions', 'marketOptions', 'subRegionOptions']);

        if (empty($this->selectedRegions)) {
            return;
        }

        $this->isLoadingMarkets = true;

        $this->marketOptions = Market::query()
            ->whereIn('region_id', $this->selectedRegions)
            ->orderBy('name')
            ->pluck('name', 'id')
            ->toArray();

        $this->isLoadingMarkets = false;
    }

    /**
     * Handle changes to selectedMarkets
     */
    public function updatedSelectedMarkets(): void
    {
        $this->reset(['selectedSubRegions', 'subRegionOptions']);

        if (empty($this->selectedMarkets)) {
            return;
        }

        $this->isLoadingSubRegions = true;

        $this->subRegionOptions = SubRegion::query()
            ->whereIn('market_id', $this->selectedMarkets)
            ->orderBy('name')
            ->pluck('name', 'id')
            ->toArray();

        $this->isLoadingSubRegions = false;
    }

    /**
     * Handle changes to selectedStates - currently a no-op since states are independent
     */
    public function updatedSelectedStates(): void
    {
        // States are independent of other geographic entities
        // No additional actions needed
    }

    /**
     * Handle changes to selectedSubRegions - currently a no-op
     */
    public function updatedSelectedSubRegions(): void
    {
        // No additional actions needed
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Basic Information')
                    ->columns(2)
                    ->schema([
                        TextInput::make('name')
                            ->label('Advertisement Name')
                            ->required()
                            ->maxLength(255),

                        Select::make('user_id')
                            ->label('Associated Recruiter/Sponsor')
                            ->options(
                                User::query()
                                    ->where('profile_type', ProfileType::SPONSOR->value)
                                    ->orderBy('first_name')
                                    ->get()
                                    ->pluck('full_name', 'id')
                            )
                            ->searchable()
                            ->required(),

                        Toggle::make('is_listed')
                            ->label('Is Active')
                            ->default(true)
                            ->onIcon('heroicon-o-check-circle')
                            ->offIcon('heroicon-o-x-circle')
                            ->onColor('success')
                            ->offColor('danger'),
                    ]),

                Section::make('Media')
                    ->columns(2)
                    ->schema([
                        FileUpload::make('logo')
                            ->label('Logo Image')
                            ->image()
                            ->maxSize(2048) // 2MB
                            ->imageEditor()
                            ->imageCropAspectRatio('1:1')
                            ->imageResizeTargetWidth('600')
                            ->imageResizeTargetHeight('600')
                            ->directory('advertisement-logos')
                            ->visibility('public')
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/jpg']),

                        FileUpload::make('background')
                            ->label('Background Image')
                            ->image()
                            ->maxSize(5120) // 5MB
                            ->imageEditor()
                            ->imageCropAspectRatio('3:2')
                            ->imageResizeTargetWidth('1200')
                            ->imageResizeTargetHeight('800')
                            ->directory('advertisement-backgrounds')
                            ->visibility('public')
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/jpg']),
                    ]),

                Section::make('Content')
                    ->columns(1)
                    ->schema([
                        RichEditor::make('copy')
                            ->label('Advertisement Copy')
                            ->fileAttachmentsDisk('public')
                            ->fileAttachmentsDirectory('advertisement-media')
                            ->fileAttachmentsVisibility('public')
                            ->toolbarButtons([
                                'bold',
                                'italic',
                                'underline',
                                'undo',
                                'redo',
                            ]),

                        Grid::make(2)
                            ->schema([
                                TextInput::make('cta_text')
                                    ->label('CTA Button Text')
                                    ->maxLength(50),

                                TextInput::make('cta_url')
                                    ->label('CTA Button URL')
                                    ->url()
                                    ->maxLength(255),
                            ]),
                    ]),

                Section::make('Targeting')
                    ->columns(2)
                    ->schema([
                        Select::make('profile_types')
                            ->label('Profile Types')
                            ->multiple()
                            ->options([
                                ProfileType::POSITIVE_ATHLETE->value => 'Positive Athlete',
                                ProfileType::POSITIVE_COACH->value => 'Positive Coach',
                                ProfileType::ATHLETICS_DIRECTOR->value => 'Athletics Director',
                                ProfileType::PARENT->value => 'Parent',
                                ProfileType::COLLEGE_ATHLETE->value => 'College Athlete',
                                ProfileType::PROFESSIONAL->value => 'Professional',
                            ])
                            ->required(),

                        Select::make('ui_regions')
                            ->label('UI Regions')
                            ->multiple()
                            ->options(UiRegion::asSelectArray())
                            ->required(),

                        // Hierarchical geographic selects
                        Select::make('regions')
                            ->label('Regions')
                            ->multiple()
                            ->options(fn() => $this->regionOptions)
                            ->reactive()
                            ->afterStateUpdated(function ($state) {
                                $this->selectedRegions = $state ?: [];
                                $this->updatedSelectedRegions();
                            })
                            ->searchable(),

                        Select::make('states')
                            ->label('States')
                            ->multiple()
                            ->options(fn() => $this->stateOptions)
                            ->reactive()
                            ->afterStateUpdated(function ($state) {
                                $this->selectedStates = $state ?: [];
                                $this->updatedSelectedStates();
                            })
                            ->searchable()
                            ->disabled(fn() => $this->isLoadingStates)
                            ->loadingMessage('Loading states...'),

                        Select::make('markets')
                            ->label('Markets')
                            ->multiple()
                            ->options(fn() => $this->marketOptions)
                            ->reactive()
                            ->afterStateUpdated(function ($state) {
                                $this->selectedMarkets = $state ?: [];
                                $this->updatedSelectedMarkets();
                            })
                            ->searchable()
                            ->disabled(fn() => empty($this->selectedRegions) || $this->isLoadingMarkets)
                            ->loadingMessage('Loading markets...')
                            ->helperText(fn() => empty($this->selectedRegions) ? 'Select region(s) first' : ''),

                        Select::make('sub_regions')
                            ->label('Sub-Regions')
                            ->multiple()
                            ->options(fn() => $this->subRegionOptions)
                            ->reactive()
                            ->afterStateUpdated(function ($state) {
                                $this->selectedSubRegions = $state ?: [];
                                $this->updatedSelectedSubRegions();
                            })
                            ->searchable()
                            ->disabled(fn() => empty($this->selectedMarkets) || $this->isLoadingSubRegions)
                            ->loadingMessage('Loading sub-regions...')
                            ->helperText(fn() => empty($this->selectedMarkets) ? 'Select market(s) first' : ''),
                    ]),
            ])
            ->statePath('data');
    }

    public function create(): void
    {
        $data = $this->form->getState();

        // Prepare media items
        $mediaItems = [];

        if ($data['logo'] ?? null) {
            $mediaItems['logo'] = $this->handleUploadedFile($data['logo']);
        }

        if ($data['background'] ?? null) {
            $mediaItems['background'] = $this->handleUploadedFile($data['background']);
        }

        try {
            // Create advertisement using service
            $advertisementService = app(AdvertisementService::class);
            $advertisementService->createAdvertisement($data, $mediaItems);

            // Show success notification
            Notification::make()
                ->title('Advertisement created successfully')
                ->success()
                ->send();

            // Reset form explicitly before closing modal
            $this->resetForm();

            // Close modal
            $this->dispatch('close-modal', id: 'new-advertisement-modal');

            // Refresh parent component
            $this->dispatch('advertisement-created');
        } catch (\Exception $e) {
            Notification::make()
                ->title('Error creating advertisement')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    /**
     * Handle the uploaded file to work with media library.
     */
    private function handleUploadedFile($uploadedFile)
    {
        if (is_array($uploadedFile)) {
            // When dealing with Filament's FileUpload component, it sometimes gives an array of files
            // We need to get the first file in that case
            if (!empty($uploadedFile) && isset($uploadedFile[0])) {
                $uploadedFile = $uploadedFile[0];
            } else {
                // If there are no files or the array structure is unexpected, return null
                return null;
            }
        }

        if ($uploadedFile instanceof TemporaryUploadedFile) {
            return $uploadedFile->getRealPath();
        }

        // If it's a string path from Filament's FileUpload (like "advertisement-logos/01JSFG22TPCB6Q7BRKVAH1CKW6.jpg")
        if (is_string($uploadedFile) && !empty($uploadedFile)) {
            // Convert to full storage path that addMedia can use
            return storage_path('app/public/' . $uploadedFile);
        }

        return $uploadedFile;
    }

    /**
     * Reset the form data.
     */
    public function resetForm(): void
    {
        // Reset the form, which will properly clear all fields including file uploads
        $this->form->fill();

        $this->dispatch('forceRefresh');

        // Reset validation
        $this->resetValidation();
        $this->resetErrorBag();
    }

    public function render(): View
    {
        return view('livewire.advertisements.create-advertisement-modal');
    }
}
