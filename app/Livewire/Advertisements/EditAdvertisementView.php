<?php

namespace App\Livewire\Advertisements;

use App\Enums\ProfileType;
use App\Enums\UiRegion;
use App\Models\Advertisement;
use App\Models\User;
use App\Models\Market;
use App\Models\SubRegion;
use App\Models\Region;
use App\Models\State;
use App\Services\AdvertisementService;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Illuminate\Contracts\View\View;
use Livewire\Component;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class EditAdvertisementView extends Component implements HasForms
{
    use InteractsWithForms;

    public Advertisement $advertisement;
    public ?array $data = [];

    // Geographic selected value properties
    public ?array $selectedRegions = [];
    public ?array $selectedMarkets = [];
    public ?array $selectedStates = [];
    public ?array $selectedSubRegions = [];

    // Geographic options properties
    public array $regionOptions = [];
    public array $marketOptions = [];
    public array $stateOptions = [];
    public array $subRegionOptions = [];

    // Loading states
    public bool $isLoadingMarkets = false;
    public bool $isLoadingStates = false;
    public bool $isLoadingSubRegions = false;

    protected $listeners = [
        'forceRefresh' => 'refreshData',
    ];

    public function mount(Advertisement $advertisement): void
    {
        // Ensure we have the latest advertisement data with media relationships
        $this->advertisement = $advertisement->fresh(['profileTypes', 'uiRegions', 'regions', 'markets', 'subRegions', 'states', 'media']);

        // Get the existing logo image if it exists
        $logoMedia = $this->advertisement->getFirstMedia('logo');
        $existingLogo = null;

        if ($logoMedia) {
            $existingLogo = [
                'name' => $logoMedia->file_name,
                'size' => $logoMedia->size,
                'type' => $logoMedia->mime_type,
                'path' => $logoMedia->getPathRelativeToRoot(),
            ];
        }

        // Get the existing background image if it exists
        $backgroundMedia = $this->advertisement->getFirstMedia('background');
        $existingBackground = null;

        if ($backgroundMedia) {
            $existingBackground = [
                'name' => $backgroundMedia->file_name,
                'size' => $backgroundMedia->size,
                'type' => $backgroundMedia->mime_type,
                'path' => $backgroundMedia->getPathRelativeToRoot(),
            ];
        }

        // Initialize geo options and load current selections
        $this->initializeGeographicOptions();
        $this->loadCurrentGeographicSelections();

        // Load advertisement data for form
        $this->form->fill([
            'name' => $this->advertisement->name,
            'user_id' => $this->advertisement->user_id,
            'is_listed' => $this->advertisement->is_listed,
            'copy' => $this->advertisement->copy,
            'cta_text' => $this->advertisement->cta_text,
            'cta_url' => $this->advertisement->cta_url,
            'profile_types' => $this->advertisement->profileTypes->pluck('profile_type')->toArray(),
            'ui_regions' => $this->advertisement->uiRegions->pluck('ui_region')->toArray(),
            'regions' => $this->advertisement->regions->pluck('id')->toArray(),
            'markets' => $this->advertisement->markets->pluck('id')->toArray(),
            'sub_regions' => $this->advertisement->subRegions->pluck('id')->toArray(),
            'states' => $this->advertisement->states->pluck('code')->toArray(),
            'logo' => !empty($existingLogo['path']) ? $existingLogo['path'] : null,
            'background' => !empty($existingBackground['path']) ? $existingBackground['path'] : null,
        ]);

        // Load geographic selection options and make sure selectedRegions is initialized
        $this->selectedRegions = [];
        $this->selectedMarkets = [];
        $this->selectedStates = [];
        $this->selectedSubRegions = [];

        // Initialize options
        $this->regionOptions = [];
        $this->marketOptions = [];
        $this->stateOptions = [];
        $this->subRegionOptions = [];

        // Load available regions
        $this->loadRegionOptions();

        // Load all states (independent selection)
        $this->loadAllStates();

        // Load current geographic selections
        $this->loadCurrentGeographicSelections();
    }

    /**
     * Initialize geographic options with initial data
     */
    private function initializeGeographicOptions(): void
    {
        // Load initial region options
        $this->regionOptions = Region::query()
            ->orderBy('name')
            ->pluck('name', 'id')
            ->toArray();

        // Some states aren't tied to regions directly, so we load them all initially
        $this->stateOptions = State::query()
            ->orderBy('name')
            ->pluck('name', 'code')
            ->toArray();
    }

    /**
     * Load current geographic selections from the advertisement
     */
    protected function loadCurrentGeographicSelections(): void
    {
        if (!$this->advertisement) {
            return;
        }

        // Load regions
        $this->selectedRegions = $this->advertisement->regions
            ->pluck('id')
            ->map(fn ($id) => (string) $id)
            ->toArray();

        // Load markets if regions are selected
        if (!empty($this->selectedRegions)) {
            $this->marketOptions = Market::query()
                ->whereIn('region_id', $this->selectedRegions)
                ->orderBy('name')
                ->pluck('name', 'id')
                ->toArray();

            $this->selectedMarkets = $this->advertisement->markets
                ->pluck('id')
                ->map(fn ($id) => (string) $id)
                ->toArray();
        }

        // Load states (independent selection)
        $this->selectedStates = $this->advertisement->states
            ->pluck('code')
            ->toArray();

        // Load sub-regions if markets are selected
        if (!empty($this->selectedMarkets)) {
            $this->subRegionOptions = SubRegion::query()
                ->whereIn('market_id', $this->selectedMarkets)
                ->orderBy('name')
                ->pluck('name', 'id')
                ->toArray();

            $this->selectedSubRegions = $this->advertisement->subRegions
                ->pluck('id')
                ->map(fn ($id) => (string) $id)
                ->toArray();
        }
    }

    /**
     * Watch for changes to selected regions
     */
    public function updatedSelectedRegions(): void
    {
        $this->reset(['selectedMarkets', 'selectedSubRegions', 'marketOptions', 'subRegionOptions']);

        if (empty($this->selectedRegions)) {
            return;
        }

        $this->isLoadingMarkets = true;
        $this->loadMarketsFromRegions();
        $this->isLoadingMarkets = false;
    }

    /**
     * Watch for changes to selected markets
     */
    public function updatedSelectedMarkets(): void
    {
        $this->reset(['selectedSubRegions', 'subRegionOptions']);

        if (empty($this->selectedMarkets)) {
            return;
        }

        $this->isLoadingSubRegions = true;
        $this->loadSubRegionsFromMarkets();
        $this->isLoadingSubRegions = false;
    }

    /**
     * Watch for changes to selected states - currently a no-op since states are independent
     */
    public function updatedSelectedStates(): void
    {
        // States are independent of other geographic entities
        // No additional actions needed
    }

    /**
     * Watch for changes to selected subregions - currently a no-op
     */
    public function updatedSelectedSubRegions(): void
    {
        // No additional actions needed
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Performance Metrics')
                    ->columns(3)
                    ->schema([
                        Placeholder::make('impressions')
                            ->label('Impressions')
                            ->content(number_format($this->advertisement->impressions))
                            ->extraAttributes(['class' => 'text-lg font-bold']),

                        Placeholder::make('clicks')
                            ->label('Clicks')
                            ->content(number_format($this->advertisement->clicks))
                            ->extraAttributes(['class' => 'text-lg font-bold']),

                        Placeholder::make('ctr')
                            ->label('Click-Through Rate')
                            ->content(number_format($this->advertisement->click_through_rate, 2) . '%')
                            ->extraAttributes(['class' => 'text-lg font-bold']),
                    ]),

                Section::make('Basic Information')
                    ->columns(2)
                    ->schema([
                        TextInput::make('name')
                            ->label('Advertisement Name')
                            ->required()
                            ->maxLength(255),

                        Select::make('user_id')
                            ->label('Associated Recruiter/Sponsor')
                            ->options(
                                User::query()
                                    ->where('profile_type', ProfileType::SPONSOR->value)
                                    ->orderBy('first_name')
                                    ->get()
                                    ->pluck('full_name', 'id')
                            )
                            ->searchable()
                            ->required(),

                        Toggle::make('is_listed')
                            ->label('Is Active')
                            ->onIcon('heroicon-o-check-circle')
                            ->offIcon('heroicon-o-x-circle')
                            ->onColor('success')
                            ->offColor('danger')
                            ->helperText('Warning: Changing status will immediately affect ad visibility'),
                    ]),

                Section::make('Media')
                    ->columns(2)
                    ->schema([
                        FileUpload::make('logo')
                            ->label('Logo Image')
                            ->image()
                            ->maxSize(2048) // 2MB
                            ->imageEditor()
                            ->imageCropAspectRatio('1:1')
                            ->imageResizeTargetWidth('600')
                            ->imageResizeTargetHeight('600')
                            ->directory('advertisement-logos')
                            ->visibility('public')
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/jpg'])
                            ->helperText('Replace logo image (600x600). Leave empty to keep current image.')
                            ->placeholder('Leave empty to keep current image')
                            ->uploadProgressIndicatorPosition('left')
                            ->preserveFilenames()
                            ->openable()
                            ->downloadable()
                            ->imagePreviewHeight('250')
                            ->required(fn () => !$this->advertisement->hasMedia('logo')),

                        FileUpload::make('background')
                            ->label('Background Image')
                            ->image()
                            ->maxSize(5120) // 5MB
                            ->imageEditor()
                            ->imageCropAspectRatio('3:2')
                            ->imageResizeTargetWidth('1200')
                            ->imageResizeTargetHeight('800')
                            ->directory('advertisement-backgrounds')
                            ->visibility('public')
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/jpg'])
                            ->helperText('Replace background image (1200x800). Leave empty to keep current image.')
                            ->placeholder('Leave empty to keep current image')
                            ->uploadProgressIndicatorPosition('left')
                            ->preserveFilenames()
                            ->openable()
                            ->downloadable()
                            ->imagePreviewHeight('250')
                            ->required(fn () => !$this->advertisement->hasMedia('background')),
                    ]),

                Section::make('Content')
                    ->columns(1)
                    ->schema([
                        RichEditor::make('copy')
                            ->label('Advertisement Copy')
                            ->fileAttachmentsDisk('public')
                            ->fileAttachmentsDirectory('advertisement-media')
                            ->fileAttachmentsVisibility('public')
                            ->toolbarButtons([
                                'bold',
                                'italic',
                                'underline',
                                'undo',
                                'redo',
                            ]),

                        Grid::make(2)
                            ->schema([
                                TextInput::make('cta_text')
                                    ->label('CTA Button Text')
                                    ->maxLength(50),

                                TextInput::make('cta_url')
                                    ->label('CTA Button URL')
                                    ->url()
                                    ->maxLength(255),
                            ]),
                    ]),

                Section::make('Targeting')
                    ->columns(2)
                    ->schema([
                        Select::make('profile_types')
                            ->label('Profile Types')
                            ->options([
                                ProfileType::POSITIVE_ATHLETE->value => 'Positive Athlete',
                                ProfileType::POSITIVE_COACH->value => 'Positive Coach',
                                ProfileType::ATHLETICS_DIRECTOR->value => 'Athletics Director',
                                ProfileType::PARENT->value => 'Parent',
                                ProfileType::COLLEGE_ATHLETE->value => 'College Athlete',
                                ProfileType::PROFESSIONAL->value => 'Professional',
                                ProfileType::SPONSOR->value => 'Sponsor',
                            ])
                            ->multiple()
                            ->required(),

                        Select::make('ui_regions')
                            ->label('UI Regions')
                            ->options([
                                UiRegion::HEADER->value => 'Header',
                                UiRegion::SIDEBAR->value => 'Sidebar',
                                UiRegion::FOOTER->value => 'Footer',
                                UiRegion::DASHBOARD->value => 'Dashboard',
                                UiRegion::PROFILE->value => 'Profile',
                            ])
                            ->multiple()
                            ->required(),

                        Select::make('regions')
                            ->label('Regions')
                            ->multiple()
                            ->options(fn() => $this->regionOptions)
                            ->reactive()
                            ->afterStateUpdated(function ($state) {
                                $this->selectedRegions = $state ?: [];
                                $this->updatedSelectedRegions();
                            })
                            ->searchable(),

                        Select::make('states')
                            ->label('States')
                            ->multiple()
                            ->options(fn() => $this->stateOptions)
                            ->reactive()
                            ->afterStateUpdated(function ($state) {
                                $this->selectedStates = $state ?: [];
                            })
                            ->searchable()
                            ->disabled(fn() => $this->isLoadingStates)
                            ->loadingMessage('Loading states...'),

                        Select::make('markets')
                            ->label('Markets')
                            ->multiple()
                            ->options(fn() => $this->marketOptions)
                            ->reactive()
                            ->afterStateUpdated(function ($state) {
                                $this->selectedMarkets = $state ?: [];
                                $this->updatedSelectedMarkets();
                            })
                            ->searchable()
                            ->disabled(fn() => empty($this->selectedRegions) || $this->isLoadingMarkets)
                            ->loadingMessage('Loading markets...')
                            ->helperText(fn() => empty($this->selectedRegions) ? 'Select region(s) first' : ''),

                        Select::make('sub_regions')
                            ->label('Sub-Regions')
                            ->multiple()
                            ->options(fn() => $this->subRegionOptions)
                            ->reactive()
                            ->afterStateUpdated(function ($state) {
                                $this->selectedSubRegions = $state ?: [];
                            })
                            ->searchable()
                            ->disabled(fn() => empty($this->selectedMarkets) || $this->isLoadingSubRegions)
                            ->loadingMessage('Loading sub-regions...')
                            ->helperText(fn() => empty($this->selectedMarkets) ? 'Select market(s) first' : ''),
                    ]),
            ])
            ->statePath('data');
    }

    public function update(): void
    {
        $data = $this->form->getState();

        // Ensure array fields are actually arrays to prevent foreach issues
        $arrayFields = ['profile_types', 'ui_regions', 'regions', 'markets', 'sub_regions', 'states'];
        foreach ($arrayFields as $field) {
            if (!isset($data[$field]) || !is_array($data[$field])) {
                $data[$field] = [];
            }
        }

        // Prepare media items
        $mediaItems = [];

        // Handle logo image
        if (!empty($data['logo'])) {
            // Check if this is a new upload by comparing with the current image path
            $currentLogoMedia = $this->advertisement->getFirstMedia('logo');
            $isNewLogoUpload = !$currentLogoMedia || $data['logo'] !== $currentLogoMedia->getPathRelativeToRoot();

            if ($isNewLogoUpload && $data['logo'] instanceof TemporaryUploadedFile) {
                $mediaItems['logo'] = $data['logo'];
            } else if ($isNewLogoUpload) {
                // Handle path-based uploads (existing files in storage)
                $mediaItems['logo'] = storage_path('app/public/' . $data['logo']);
            }
        }

        // Handle background image
        if (!empty($data['background'])) {
            // Check if this is a new upload by comparing with the current image path
            $currentBackgroundMedia = $this->advertisement->getFirstMedia('background');
            $isNewBackgroundUpload = !$currentBackgroundMedia || $data['background'] !== $currentBackgroundMedia->getPathRelativeToRoot();

            if ($isNewBackgroundUpload && $data['background'] instanceof TemporaryUploadedFile) {
                $mediaItems['background'] = $data['background'];
            } else if ($isNewBackgroundUpload) {
                // Handle path-based uploads (existing files in storage)
                $mediaItems['background'] = storage_path('app/public/' . $data['background']);
            }
        }

        try {
            // Update advertisement using service
            $advertisementService = app(AdvertisementService::class);
            $updatedAdvertisement = $advertisementService->updateAdvertisement($this->advertisement->id, $data, $mediaItems);

            // Update the model with the updated advertisement
            $this->advertisement = $updatedAdvertisement->fresh(['profileTypes', 'uiRegions', 'regions', 'markets', 'subRegions', 'states', 'media']);

            // Show success notification
            Notification::make()
                ->title('Advertisement updated successfully')
                ->success()
                ->send();

            // Refresh component with latest data
            // This will trigger the refreshData method through the forceRefresh event
            $this->dispatch('forceRefresh');
        } catch (\Exception $e) {
            Notification::make()
                ->title('Error updating advertisement')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function confirmStatusChange(): void
    {
        // Get the current form state to get the new status value
        $data = $this->form->getState();
        $newStatus = $data['is_listed'];

        // Get the opposite of the current advertisement status
        $currentStatus = $this->advertisement->is_listed;

        // Only confirm if status is actually changing
        if ($newStatus !== $currentStatus) {
            $this->dispatch('open-confirmation-modal');
        } else {
            // If status didn't change, just update normally
            $this->update();
        }
    }

    /**
     * Show the delete confirmation modal
     */
    public function deleteAdvertisement(): void
    {
        $this->dispatch('open-delete-modal');
    }

    /**
     * Delete the advertisement and redirect to the advertisements list
     */
    public function confirmDelete(): void
    {
        try {
            // Get the advertisement service
            $advertisementService = app(AdvertisementService::class);

            // Delete the advertisement
            $advertisementService->deleteAdvertisement($this->advertisement->id);

            // Show success notification
            Notification::make()
                ->title('Advertisement deleted successfully')
                ->success()
                ->send();

            // Redirect to the advertisements list
            $this->redirect(route('advertisements.index'));
        } catch (\Exception $e) {
            Notification::make()
                ->title('Error deleting advertisement')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    /**
     * Handle the forceRefresh event to update the form with the latest data
     */
    public function refreshData(): void
    {
        // Refresh the advertisement model with all its relationships
        $this->advertisement = $this->advertisement->fresh(['profileTypes', 'uiRegions', 'regions', 'markets', 'subRegions', 'states', 'media']);

        // Get updated media paths
        $logoMedia = $this->advertisement->getFirstMedia('logo');
        $backgroundMedia = $this->advertisement->getFirstMedia('background');

        // Update form with fresh data
        $formData = [
            'name' => $this->advertisement->name,
            'user_id' => $this->advertisement->user_id,
            'is_listed' => $this->advertisement->is_listed,
            'copy' => $this->advertisement->copy,
            'cta_text' => $this->advertisement->cta_text,
            'cta_url' => $this->advertisement->cta_url,
            'logo' => $logoMedia ? $logoMedia->getPathRelativeToRoot() : null,
            'background' => $backgroundMedia ? $backgroundMedia->getPathRelativeToRoot() : null,
            // Ensure these are always arrays
            'profile_types' => $this->advertisement->profileTypes->pluck('profile_type')->toArray() ?: [],
            'ui_regions' => $this->advertisement->uiRegions->pluck('ui_region')->toArray() ?: [],
            'regions' => $this->advertisement->regions->pluck('id')->toArray() ?: [],
            'markets' => $this->advertisement->markets->pluck('id')->toArray() ?: [],
            'sub_regions' => $this->advertisement->subRegions->pluck('id')->toArray() ?: [],
            'states' => $this->advertisement->states->pluck('code')->toArray() ?: [],
        ];

        // Update the form with the latest data
        $this->form->fill($formData);
    }

    /**
     * Load all states for state selection
     */
    protected function loadAllStates(): void
    {
        $this->stateOptions = State::query()
            ->orderBy('name')
            ->pluck('name', 'code')
            ->toArray();
    }

    public function render(): View
    {
        return view('livewire.advertisements.edit-advertisement-view');
    }

    public function updatedForm($property, $value): void
    {
        if ($property === 'regions') {
            $this->selectedRegions = $this->form->regions;
            $this->resetMarketAndSubOptions();
            $this->loadMarketsFromRegions();
        }

        if ($property === 'markets') {
            $this->selectedMarkets = $this->form->markets;
            $this->resetSubRegionOptions();
            $this->loadSubRegionsFromMarkets();
        }

        if ($property === 'sub_regions') {
            $this->selectedSubRegions = $this->form->sub_regions;
        }

        if ($property === 'states') {
            $this->selectedStates = $this->form->states;
        }
    }

    private function resetMarketAndSubOptions(): void
    {
        $this->marketOptions = [];
        $this->subRegionOptions = [];
        $this->form->markets = [];
        $this->form->sub_regions = [];
        $this->selectedMarkets = [];
        $this->selectedSubRegions = [];
    }

    private function resetSubRegionOptions(): void
    {
        $this->subRegionOptions = [];
        $this->form->sub_regions = [];
        $this->selectedSubRegions = [];
    }

    private function loadMarketsFromRegions(): void
    {
        if (empty($this->selectedRegions)) {
            return;
        }

        $this->isLoadingMarkets = true;

        $this->marketOptions = Market::query()
            ->whereIn('region_id', $this->selectedRegions)
            ->orderBy('name')
            ->pluck('name', 'id')
            ->toArray();

        $this->isLoadingMarkets = false;
    }

    private function loadSubRegionsFromMarkets(): void
    {
        if (empty($this->selectedMarkets)) {
            return;
        }

        $this->isLoadingSubRegions = true;

        $this->subRegionOptions = SubRegion::query()
            ->whereIn('market_id', $this->selectedMarkets)
            ->orderBy('name')
            ->pluck('name', 'id')
            ->toArray();

        $this->isLoadingSubRegions = false;
    }

    /**
     * Load all available regions
     */
    protected function loadRegionOptions(): void
    {
        $this->regionOptions = Region::query()
            ->orderBy('name')
            ->pluck('name', 'id')
            ->toArray();
    }
}
