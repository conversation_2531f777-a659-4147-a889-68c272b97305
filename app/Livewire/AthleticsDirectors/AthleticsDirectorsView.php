<?php

namespace App\Livewire\AthleticsDirectors;

use App\Models\User;
use App\Models\Region;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Contracts\View\View;
use Filament\Tables\Columns\ImageColumn;
use Livewire\Component;

class AthleticsDirectorsView extends Component implements HasTable, HasForms
{
    use InteractsWithTable, InteractsWithForms;

    /**
     * Currently selected region ID for filtering
     */
    public ?string $selectedRegion = null;

    /**
     * Listen for region filter changes and AD verification
     */
    protected $listeners = [
        'region-changed' => 'handleRegionChange',
        'ad-verified' => 'handleAdVerified'
    ];

    /**
     * Handle region selection change from filter bar
     */
    public function handleRegionChange(?string $regionId): void
    {
        $this->selectedRegion = $regionId;
        $this->resetTable();
    }

    /**
     * Handle when an AD is verified
     */
    public function handleAdVerified(): void
    {
        $this->resetTable();
    }

    /**
     * Get all regions for the filter bar
     */
    public function getRegionsProperty(): array
    {
        return Region::query()
            ->orderBy('name')
            ->get(['id', 'name'])
            ->toArray();
    }

    public function table(Table $table): Table
    {
        $query = User::query()
            ->whereHas('athleticsDirectorProfile', function ($query) {
                $query->where('is_verified', true);
            })
            ->with([
                'athleticsDirectorProfile',
                'county.state',
                'school',
                'media' => fn($query) => $query->where('collection_name', 'avatar')
            ]);

        if ($this->selectedRegion) {
            $query->whereHas('school.county.market.region', function ($query) {
                $query->where('id', $this->selectedRegion);
            });
        }

        return $table
            ->query($query)
            ->columns([
                ImageColumn::make('avatar')
                    ->circular()
                    ->defaultImageUrl(fn(User $record) => 'https://ui-avatars.com/api/?name=' . urlencode($record->first_name . ' ' . $record->last_name))
                    ->getStateUsing(fn (User $record) => $record->getFirstMedia('avatar')?->getUrl())
                    ->label('')
                    ->size(40),
                TextColumn::make('first_name')
                    ->label('Name')
                    ->formatStateUsing(fn(User $record): string => "{$record->first_name} {$record->last_name}")
                    ->searchable(['first_name', 'last_name'])
                    ->sortable(['first_name', 'last_name']),
                TextColumn::make('school.name')
                    ->label('School')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('county.state.code')
                    ->label('State')
                    ->sortable(),
                TextColumn::make('athleticsDirectorProfile.verified_at')
                    ->label('Verified At')
                    ->dateTime()
                    ->sortable(),
                TextColumn::make('athleticsDirectorProfile.verifiedBy.name')
                    ->label('Verified By')
                    ->formatStateUsing(fn($state, User $record) => optional($record->athleticsDirectorProfile->verifiedBy)->name)
                    ->sortable(),
            ])
            ->filters([])
            ->actions([
                Action::make('view')
                    ->icon('heroicon-o-eye')
                    ->url(fn(User $record) => route('filament.admin.pages.athletics-directors.{director}', $record->id)),
            ])
            ->defaultSort('athleticsDirectorProfile.verified_at', 'desc');
    }

    public function render(): View
    {
        return view('livewire.athletics-directors.athletics-directors-view');
    }
}
