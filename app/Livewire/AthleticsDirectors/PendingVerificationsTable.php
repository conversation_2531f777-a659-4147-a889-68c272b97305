<?php

namespace App\Livewire\AthleticsDirectors;

use App\Models\User;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Contracts\View\View;
use Filament\Tables\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Tables\Columns\IconColumn;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Services\AthleticsDirectorProfileService;
use Filament\Tables\Filters\TernaryFilter;

class PendingVerificationsTable extends Component implements HasTable, HasForms
{
    use InteractsWithTable, InteractsWithForms;

    public ?User $recordToReview = null;

    /**
     * Currently selected region ID for filtering
     */
    public ?string $selectedRegion = null;

    /**
     * Listen for region filter changes
     */
    protected $listeners = ['region-changed' => 'handleRegionChange'];

    /**
     * Handle region selection change from filter bar
     */
    public function handleRegionChange(?string $regionId): void
    {
        $this->selectedRegion = $regionId;
        $this->resetTable();
    }

    public function verifyAthleticsDirector(User $record): void
    {
        /** @var User $user */
        $user = Auth::user();

        app(AthleticsDirectorProfileService::class)->verify($record->athleticsDirectorProfile, $user);

        $this->recordToReview = null;
        Notification::make()->success()->title('Athletics Director verified')->send();
        $this->closeTableActionModal();
        $this->dispatch('ad-verified');
    }

    public function rejectAthleticsDirector(User $record): void
    {
        /** @var User $user */
        $user = Auth::user();

        app(AthleticsDirectorProfileService::class)->reject($record->athleticsDirectorProfile, $user);

        $this->recordToReview = null;
        Notification::make()->success()->title('Athletics Director rejected')->send();
        $this->closeTableActionModal();
        $this->dispatch('ad-rejected');
    }

    public function restoreAthleticsDirector(User $record): void
    {
        /** @var User $user */
        $user = Auth::user();

        app(AthleticsDirectorProfileService::class)->restore($record->athleticsDirectorProfile, $user);

        $this->recordToReview = null;
        Notification::make()->success()->title('Athletics Director restored')->send();
        $this->dispatch('close-modal', id: 'restore-confirmation');
        $this->closeTableActionModal();
        $this->dispatch('ad-restored');
    }

    public function table(Table $table): Table
    {
        $query = User::query()
            ->whereHas('athleticsDirectorProfile', function ($query) {
                $query->withTrashed();
            })
            ->with([
                'athleticsDirectorProfile' => fn($query) => $query->withTrashed(),
                'county.state',
                'school',
                'schoolVerifiedAthleticsDirector',
            ]);

        if ($this->selectedRegion) {
            $query->whereHas('school.county.market.region', function ($query) {
                $query->where('id', $this->selectedRegion);
            });
        }

        return $table
            ->query($query)
            ->columns([
                TextColumn::make('first_name')
                    ->label('Name')
                    ->formatStateUsing(fn(User $record): string => "{$record->first_name} {$record->last_name}")
                    ->searchable(['first_name', 'last_name'])
                    ->sortable(),
                TextColumn::make('email')
                    ->label('Email')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('school.name')
                    ->label('Claimed School')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('county.state.code')
                    ->label('State')
                    ->sortable(),
                IconColumn::make('schoolVerifiedAthleticsDirector')
                    ->label('School Has Existing AD')
                    ->alignCenter()
                    ->color('danger')
                    ->icon(fn($record) => $record ? 'heroicon-m-x-circle' : null),
            ])
            ->filters([
                TernaryFilter::make('show_rejected')
                    ->label('Show Rejected')
                    ->placeholder('Active Only')
                    ->trueLabel('Rejected Only')
                    ->falseLabel('Active Only')
                    ->queries(
                        true: fn ($query) => $query->whereHas('athleticsDirectorProfile', function($query) {
                            $query->withTrashed()
                                ->where('is_verified', false)
                                ->whereNotNull('deleted_at');
                        }),
                        false: fn ($query) => $query->whereHas('athleticsDirectorProfile', function($query) {
                            $query->withTrashed()
                                ->where('is_verified', false)
                                ->whereNull('deleted_at');
                        }),
                        blank: fn ($query) => $query->whereHas('athleticsDirectorProfile', function($query) {
                            $query->withTrashed()
                                ->where('is_verified', false)
                                ->whereNull('deleted_at');
                        })
                    )
                    ->columnSpan(2),
            ])
            ->actions([
                Action::make('review')
                    ->label('Review')
                    ->icon('heroicon-m-pencil-square')
                    ->modalContent(fn (User $record) => view('livewire.athletics-directors.review-modal', [
                        'record' => $record,
                    ]))
                    ->modalSubmitAction(false)
                    ->modalCancelAction(false)
                    ->modalWidth('xl')
                    ->visible(fn (User $record) => ! $record->athleticsDirectorProfile->trashed()),
                Action::make('restore')
                    ->label('Restore')
                    ->icon('heroicon-m-arrow-uturn-left')
                    ->color('warning')
                    ->action(function (User $record) {
                        $this->restoreAthleticsDirector($record);
                    })
                    ->visible(fn (User $record) => $record->athleticsDirectorProfile->trashed()),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public function render(): View
    {
        return view('livewire.athletics-directors.pending-verifications-table');
    }
}
