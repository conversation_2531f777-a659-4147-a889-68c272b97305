<?php

namespace App\Livewire\AthleticsDirectors;

use App\Models\User;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Livewire\Component;
use Illuminate\Support\Facades\Log;

class AthleticsDirectorManagementView extends Component implements HasForms
{
    use InteractsWithForms;

    public User $director;

    // Profile status properties
    public $isVerified = false;
    public $isPending = false;
    public $isRejected = false;

    // Profile completeness tracking
    public $hasProfilePhoto = false;
    public $hasSchoolInfo = false;
    public $hasContactInfo = false;

    public function mount(User $director): void
    {
        $this->director = $director;
        $this->initializeProfileStatus();
        $this->checkProfileCompleteness();
    }

    /**
     * Initialize the profile status based on AD data
     */
    protected function initializeProfileStatus(): void
    {
        $this->isVerified = $this->director->athleticsDirector?->is_verified ?? false;
        $this->isPending = $this->director->athleticsDirector?->is_pending ?? false;
        $this->isRejected = $this->director->athleticsDirector?->is_rejected ?? false;
    }

    /**
     * Check the completeness of various profile sections
     */
    protected function checkProfileCompleteness(): void
    {
        // Check if profile photo exists
        $this->hasProfilePhoto = $this->director->getFirstMediaUrl('avatar') !== '';

        // Check if school information is complete
        $this->hasSchoolInfo = $this->director->athleticsDirector &&
            !empty($this->director->athleticsDirector->school_name) &&
            !empty($this->director->athleticsDirector->school_district);

        // Check if contact information is complete
        $this->hasContactInfo = !empty($this->director->email) &&
            !empty($this->director->phone);
    }

    /**
     * Toggle verification status
     */
    public function toggleVerificationStatus(): void
    {
        try {
            $athleticsDirector = $this->director->athleticsDirector;

            if (!$athleticsDirector) {
                throw new \Exception('Athletics Director profile not found.');
            }

            $athleticsDirector->is_verified = !$athleticsDirector->is_verified;
            $athleticsDirector->is_pending = false;
            $athleticsDirector->is_rejected = false;
            $athleticsDirector->save();

            $this->initializeProfileStatus();

            session()->flash('message', 'Verification status updated successfully.');
        } catch (\Exception $e) {
            Log::error('Failed to update verification status: ' . $e->getMessage());
            session()->flash('error', 'Failed to update verification status.');
        }
    }

    public function render()
    {
        return view('livewire.athletics-directors.athletics-director-management-view');
    }
}
