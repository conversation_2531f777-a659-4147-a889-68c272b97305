<?php

namespace App\Livewire\Admin;

use App\Models\User;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Section;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Livewire\Component;

class UserNotificationPreferences extends Component implements HasForms
{
    use InteractsWithForms;

    public ?array $data = [];
    public User $user;

    public function mount(User $user): void
    {
        $this->user = $user;
        $this->form->fill([
            'notifications' => array_keys($this->notificationOptions())
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                CheckboxList::make('notifications')
                    ->options($this->notificationOptions())
                    ->columns(1)
            ])
            ->statePath('data');
    }

    protected function notificationOptions(): array
    {
        return [
            'ad_validation' => 'AD Validation',
            'alumni_validation' => 'Alumni Validation',
            'exam_pending_grade' => 'Exam Pending Grade',
            'category_1' => 'Notification Category',
            'category_2' => 'Notification Category',
            'category_3' => 'Notification Category',
            'category_4' => 'Notification Category',
            'category_5' => 'Notification Category',
            'category_6' => 'Notification Category',
        ];
    }

    public function save(): void
    {
        $data = $this->form->getState();

        // This will contain array of selected notification keys
        $selectedNotifications = $data['notifications'];

        // Save notification preferences to user
        $this->user->update([
            'notification_preferences' => $selectedNotifications,
        ]);

        Notification::make()
            ->success()
            ->title('Notification preferences updated successfully')
            ->send();
    }

    public function render()
    {
        return view('livewire.admin.user-notification-preferences');
    }
}
