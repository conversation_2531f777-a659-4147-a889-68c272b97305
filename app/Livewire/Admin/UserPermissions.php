<?php

namespace App\Livewire\Admin;

use App\Models\User;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Livewire\Component;

class UserPermissions extends Component implements HasForms
{
    use InteractsWithForms;

    public ?array $data = [];
    public User $user;

    public function mount(User $user): void
    {
        $this->user = $user;

        // Initialize with all permissions selected
        $this->form->fill([
            'permissions' => array_keys($this->permissionOptions())
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                CheckboxList::make('permissions')
                    ->options($this->permissionOptions())
                    ->columns(2)
                    ->gridDirection('row')
            ])
            ->statePath('data');
    }

    public function save(): void
    {
        $data = $this->form->getState();

        // This will contain array of selected permission keys
        $selectedPermissions = $data['permissions'];

        // Example: Process selected permissions
        foreach ($selectedPermissions as $permission) {
            // Handle each permission here
        }

        Notification::make()
            ->success()
            ->title('Permissions updated successfully')
            ->send();
    }

    protected function permissionOptions(): array
    {
        return [
            'positive_athletes' => 'Positive Athletes',
            'positive_coaches' => 'Positive Coaches',
            'recruiters_sponsors' => 'Recruiters/Sponsors',
            'hs_team_athletes' => 'HS Team Athletes',
            'hs_team_coaches' => 'HS Team Coaches',
            'athletics_directors' => 'Athletics Directors',
            'parents' => 'Parents',
            'contacts' => 'Contacts',
            'admins' => '+A Admins',
            'x_factor' => 'X Factor',
            'opportunities' => 'Opportunities',
            'messages' => 'Messages',
            'schools' => 'Schools',
            'scholarships' => 'Scholarships',
            'ads' => 'Ads',
        ];
    }

    public function render()
    {
        return view('livewire.admin.user-permissions');
    }
}
