<?php

namespace App\Livewire\Opportunities;

use App\Enums\OpportunityStatus;
use App\Enums\ProfileType;
use App\Models\Opportunity;
use Livewire\Component;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Notifications\Notification;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Illuminate\Support\Facades\Auth;

class OpportunityView extends Component implements HasForms
{
    use InteractsWithForms;

    public ?array $data = [];
    public Opportunity $opportunity;

    public function mount(Opportunity $opportunity): void
    {
        $this->opportunity = $opportunity;
        $this->form->fill([
            'status' => $opportunity->status === OpportunityStatus::LISTED,
            'admin_disabled' => $opportunity->admin_disabled,
        ]);
    }

        public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Admin Controls')
                    ->schema([
                        Toggle::make('status')
                            ->label('Listed')
                            ->helperText('Controls whether this opportunity is visible to sponsors and can be toggled by them.')
                            ->inline()
                            ->onColor('success')
                            ->offColor('gray')
                            ->live()
                            ->afterStateUpdated(function ($state) {
                                $this->opportunity->update([
                                    'status' => $state ? OpportunityStatus::LISTED : OpportunityStatus::UNLISTED
                                ]);

                                // Refresh the model to ensure UI stays in sync
                                $this->opportunity->refresh();

                                Notification::make()
                                    ->title($state ? 'Opportunity listed' : 'Opportunity unlisted')
                                    ->success()
                                    ->send();
                            }),

                        Toggle::make('admin_disabled')
                            ->label('Admin Disabled')
                            ->helperText('When enabled, this overrides the sponsor\'s listing status and hides the opportunity from public view regardless of the Listed setting above.')
                            ->inline()
                            ->onColor('danger')
                            ->offColor('success')
                            ->live()
                            ->afterStateUpdated(function ($state) {
                                $this->opportunity->update([
                                    'admin_disabled' => $state,
                                    'admin_disabled_at' => $state ? now() : null,
                                    'admin_disabled_by' => $state ? Auth::id() : null,
                                ]);

                                Notification::make()
                                    ->title($state ? 'Opportunity disabled by admin' : 'Admin disable removed')
                                    ->success()
                                    ->send();
                            }),
                    ])
                    ->collapsible()
                    ->collapsed(false)
            ])
            ->statePath('data');
    }

    public function getDaysListedProperty(): int
    {
        // If the opportunity is not currently listed, return 0
        if ($this->opportunity->status !== OpportunityStatus::LISTED) {
            return 0;
        }

        $now = now();
        $createdAt = $this->opportunity->created_at;

        // If the opportunity is in the future, return 0
        if ($createdAt->isFuture()) {
            return 0;
        }

        return $createdAt->diffInDays($now);
    }

    public function deleteOpportunity()
    {
        $this->opportunity->delete();

        Notification::make()
            ->title('Opportunity deleted')
            ->success()
            ->send();

        return redirect()->route('opportunities.index');
    }

    public function render()
    {
        return view('livewire.opportunities.opportunity-view');
    }
}
