<?php

namespace App\Livewire\PositiveUsers\Management;

use App\Models\User;
use App\Models\Tag;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Livewire\Component;
use Illuminate\Support\Facades\Log;

class NotesAndTagsManager extends Component implements HasForms
{
    use InteractsWithForms;

    public ?User $user = null;
    public ?array $data = [];

    public function mount(User $user): void
    {
        $this->user = $user;
        $this->form->fill([
            'notes' => $user->notes ?? '',
            'tags' => $user->tags->pluck('id')->toArray(),
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->model(User::class)
            ->schema([
                Grid::make(2)->columns(2)->schema([
                    Textarea::make('notes')
                        ->label('Notes')
                        ->placeholder('Add notes about this user...')
                        ->rows(4)
                        ->live(onBlur: true)
                        ->afterStateUpdated(function ($state) {
                            try {
                                $this->user->update(['notes' => $state]);
                                // Ensure the form state is updated
                                $this->data['notes'] = $state;
                            } catch (\Exception $e) {
                                Log::error('Failed to update notes: ' . $e->getMessage());
                            }
                        }),
                    Select::make('tags')
                        ->label('Tags')
                        ->multiple()
                        ->searchable()
                        ->relationship('tags', 'name')
                        ->allowHtml()
                        ->getSearchResultsUsing(function (string $search) {
                            $search = trim(strtolower($search));

                            // Get existing tags that match the search
                            $tags = Tag::query()
                                ->where('name', 'ilike', "%{$search}%")
                                ->limit(50)
                                ->get();

                            $results = $tags->pluck('name', 'id')->toArray();

                            // Only add create option if search is not empty and no exact match exists
                            if ($search !== '' && !$tags->contains('name', $search)) {
                                $results["new-tag:$search"] = "<span class='text-brand-blue'>Create \"" . e($search) . "\"</span>";
                            }

                            return $results;
                        })
                        ->formatStateUsing(function ($state) {
                            // Don't display new-tag options in the selected state
                            if (is_string($state) && str_starts_with($state, 'new-tag:')) {
                                return '';
                            }
                            return $state;
                        })
                        ->live()
                        ->afterStateUpdated(function (Select $selectComponent, array $state) {
                            try {
                                if (!is_array($state)) {
                                    return;
                                }

                                $newTagPrefix = 'new-tag:';
                                $currentNotes = $this->user->notes; // Store current notes

                                $tags = collect($state)->map(function (string $id) use ($newTagPrefix) {
                                    if (str_starts_with($id, $newTagPrefix)) {
                                        $tagName = str_replace($newTagPrefix, '', $id);
                                        $tag = Tag::query()->firstOrCreate(['name' => $tagName]);
                                        return $tag->id;
                                    }

                                    return (int) $id;
                                });

                                $this->user->tags()->sync($tags);

                                // Reload the user to get fresh data
                                $this->user->refresh();

                                // Ensure notes are preserved
                                if ($this->user->notes !== $currentNotes) {
                                    $this->user->update(['notes' => $currentNotes]);
                                }

                                // Force update the component's state with the real tag IDs
                                $selectComponent->state($tags->toArray());

                                // Update the form data with both tags and notes
                                $this->form->fill([
                                    'tags' => $tags->toArray(),
                                    'notes' => $currentNotes,
                                ]);
                            } catch (\Exception $e) {
                                Log::error('Failed to update tags: ' . $e->getMessage());
                            }
                        }),
                ])
            ])
            ->statePath('data');
    }

    public function render()
    {
        return view('livewire.positive-users.management.notes-and-tags-manager');
    }
}
