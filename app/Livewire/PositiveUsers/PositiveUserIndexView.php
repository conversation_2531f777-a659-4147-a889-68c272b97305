<?php

namespace App\Livewire\PositiveUsers;

use App\Enums\Gender;
use App\Enums\NominationStatus;
use App\Models\User;
use App\Models\Region;
use App\Models\FilterView;
use App\Services\Filters\DynamicQueryBuilder;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Contracts\View\View;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Actions\BulkActionGroup;
use Livewire\Component;
use Illuminate\Support\Collection;
use Filament\Tables\Columns\ImageColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use App\Concerns\HasExportableColumns;
use App\Contracts\HasExportableColumnsInterface;
use App\Jobs\ProcessFilterViewExportJob;
use AnourValar\EloquentSerialize\Facades\EloquentSerializeFacade;

class PositiveUserIndexView extends Component implements HasForms, HasTable, HasExportableColumnsInterface
{
    use InteractsWithForms;
    use InteractsWithTable;
    use HasExportableColumns;

    public string $userType;
    public string $manageRoute;

    /**
     * Currently selected region ID for filtering
     */
    public ?string $selectedRegion = null;
    public bool $showCurrentYearOnly = false;

    /**
     * Currently applied dynamic filter view
     */
    public ?string $appliedFilterViewId = null;

    /**
     * Listen for region filter changes and dynamic filter changes
     */
    protected $listeners = [
        'region-changed' => 'handleRegionChange',
        'apply-filter-view' => 'handleApplyFilterView',
        'reset-filter-view' => 'handleResetFilterView',
        'toggle-current-year-filter' => 'handleToggleCurrentYearFilter',
        'reset-table' => 'handleResetTable',
    ];


    public function mount(string $userType, string $manageRoute): void
    {
        $this->userType = $userType;
        $this->manageRoute = $manageRoute;
    }

    /**
     * Handle region selection change from filter bar
     */
    public function handleRegionChange(?string $regionId): void
    {
        $this->selectedRegion = $regionId;
        $this->resetTable();
    }

    /**
     * Handle toggling the current year filter
     */
    public function handleToggleCurrentYearFilter(bool $showCurrentYearOnly): void
    {
        $this->showCurrentYearOnly = $showCurrentYearOnly;
        $this->resetTable();
    }

    /**
     * Handle applying a saved filter view
     */
    public function handleApplyFilterView(string $filterViewId, string $resourceType): void
    {
        // Only apply filters targeting this resource type
        if ($resourceType !== $this->getUserResourceType()) {
            return;
        }

        // Get the filter view from the database
        $filterView = FilterView::find($filterViewId);

        if (!$filterView) {
            Notification::make()
                ->danger()
                ->title('Filter view not found')
                ->send();
            return;
        }

        $this->appliedFilterViewId = $filterViewId;

        // Reset other filters when applying a saved filter
        $this->showCurrentYearOnly = false;
        $this->selectedRegion = null;

        $this->resetTable();
    }

    /**
     * Handle resetting filter view
     */
    public function handleResetFilterView(string $resourceType): void
    {
        // Only reset filters targeting this resource type
        if ($resourceType !== $this->getUserResourceType()) {
            return;
        }

        $this->appliedFilterViewId = null;
        $this->showCurrentYearOnly = false;
        $this->selectedRegion = null;

        $this->resetTable();
    }

    /**
     * Direct handler for resetting the table
     */
    public function handleResetTable(string $resourceType): void
    {
        // Only reset if this is the correct resource type
        if ($resourceType !== $this->getUserResourceType()) {
            return;
        }

        // Clear all filter-related state
        $this->appliedFilterViewId = null;
        $this->showCurrentYearOnly = false;
        $this->selectedRegion = null;

        // Reset the table
        $this->resetTable();
    }

    /**
     * Get the resource type for filter management
     */
    protected function getUserResourceType(): string
    {
        // Map internal user types to resource types for filter management
        $resourceType = match($this->userType) {
            'positive_athlete' => 'athletes',
            'positive_coach' => 'coaches',
            default => 'users',
        };

        return $resourceType;
    }

    /**
     * Get all regions for the filter bar
     */
    public function getRegionsProperty(): array
    {
        return Region::query()
            ->orderBy('name')
            ->get(['id', 'name'])
            ->toArray();
    }

    public function getSchoolYearRangeProperty(): string
    {
        $currentYear = now()->year;
        $currentMonth = now()->month;

        // If we're in the latter part of the year (after July), we're in the first half of the school year
        $startYear = $currentMonth < 7 ? $currentYear - 1 : $currentYear;
        $endYear = $startYear + 1;

        return sprintf('%d-%d', $startYear, $endYear % 100);
    }

    public function getCurrentYearNomineesCountProperty(): int
    {
        // Calculate the graduation year based on the current school year
        $graduationYear = now()->month < 7 ? now()->year : now()->year + 1;

        return User::query()
            ->where('profile_type', $this->userType)
            ->where('graduation_year', '>=', $graduationYear)
            ->count();
    }

    public function getAllUsersCountProperty(): int
    {
        return User::query()
            ->where('profile_type', $this->userType)
            ->count();
    }

    /**
     * Get available filter views for the current resource type
     */
    public function getFilterViewsProperty(): Collection
    {
        return FilterView::query()
            ->where('resource_type', $this->getUserResourceType())
            ->where(function (Builder $query) {
                $query->where('user_id', Auth::id())
                    ->orWhere('is_public', true);
            })
            ->orderBy('name')
            ->get();
    }

    /**
     * Get the currently applied filter view if any
     */
    public function getAppliedFilterViewProperty(): ?FilterView
    {
        if (!$this->appliedFilterViewId) {
            return null;
        }

        return FilterView::find($this->appliedFilterViewId);
    }

    /**
     * Apply the dynamic filter view to the query if one is selected
     */
    protected function applyDynamicFilters(Builder $query): Builder
    {
        if (!$this->appliedFilterViewId) {
            return $query;
        }

        $filterView = $this->getAppliedFilterViewProperty();

        if (!$filterView) {
            return $query;
        }

        try {
            // Use the dynamic query builder to apply the filter
            $dynamicQueryBuilder = new DynamicQueryBuilder();
            $resultQuery = $dynamicQueryBuilder->applyFilters($query, $filterView->filter_definition);

            return $resultQuery;
        } catch (\Exception $e) {
            // Log the error and return the original query
            \Illuminate\Support\Facades\Log::error('Error applying dynamic filters: ' . $e->getMessage(), [
                'filter_view_id' => $this->appliedFilterViewId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Show notification to the user
            Notification::make()
                ->danger()
                ->title('Error applying filter')
                ->body('There was an error applying this filter. Please try a different one or contact support.')
                ->send();

            return $query;
        }
    }

    public function table(Table $table): Table
    {
        $query = User::query()
            ->where('profile_type', $this->userType)
            ->with([
                'county.state',
                'school',
                'sports',
                'nominations' => function($query) {
                    $query->latest();  // This ensures we get the most recent nomination first
                },
                'region',
                'media' => fn($query) => $query->where('collection_name', 'avatar')
            ]);

        // Apply the standard filters (region, current year)
        if ($this->selectedRegion) {
            $query->where('region_id', $this->selectedRegion);
        }

        if ($this->showCurrentYearOnly) {
            $graduationYear = now()->month < 7 ? now()->year : now()->year + 1;
            $query->where('graduation_year', '>=', $graduationYear);
        }

        // Apply dynamic filters
        $query = $this->applyDynamicFilters($query);

        // Define base columns
        $columns = [
            ImageColumn::make('avatar')
                ->circular()
                ->defaultImageUrl(fn(User $record) => 'https://ui-avatars.com/api/?name=' . urlencode($record->first_name . ' ' . $record->last_name))
                ->getStateUsing(fn (User $record) => $record->getFirstMedia('avatar')?->getUrl())
                ->label('')
                ->size(40),
            TextColumn::make('first_name')
                ->label('Name')
                ->formatStateUsing(fn(User $record): string => "{$record->first_name} {$record->last_name}")
                ->searchable(['first_name', 'last_name'])
                ->sortable(['first_name', 'last_name']),
        ];

        // Add standard columns for other user types
        $columns = array_merge($columns, [
            TextColumn::make('nominations.ai_score')
                ->label('AI Score')
                ->formatStateUsing(function ($state) {
                    return $state ?? '0';
                })
                ->badge()
                ->color(fn($state) => match (true) {
                    $state >= 90 => 'success',
                    $state >= 70 => 'warning',
                    default => 'danger',
                }),
            TextColumn::make('nominations.status')
                ->label('Award Status')
                ->badge()
                ->formatStateUsing(function (NominationStatus $state) {
                    if (!$state) return 'No Status';
                    return $state->label();
                })
                ->color(function (NominationStatus $state) {
                    if (!$state) return 'gray';
                    return $state->color();
                }),
            TextColumn::make('sports.name')
                ->label('Sport(s)')
                ->formatStateUsing(function (User $record) {
                    $sport = $record->sports->first();
                    $additional = $record->sports->count() > 1 ? ' +' . ($record->sports->count() - 1) : '';
                    return view('components.sport-badge', [
                        'name' => $sport->name,
                        'additional' => $additional,
                    ]);
                })
                ->html()
                ->searchable(),
            TextColumn::make('graduation_year')
                ->label('HS Grad Year')
                ->sortable(),
            TextColumn::make('county.state.code')
                ->label('State')
                ->sortable(),
            TextColumn::make('gender')
                ->label('Gender')
                ->formatStateUsing(fn(Gender $state) => ucfirst(strtolower($state->value)))
                ->sortable(),
        ]);

        return $table
            ->query($query)
            ->columns($columns)
            ->filters([])
            ->actions([
                Action::make('view')
                    ->icon('heroicon-o-eye')
                    ->url(function (User $record) {
                        if ($this->userType === 'positive_coach') {
                            return route('filament.admin.pages.coaches.{coach}', ['coach' => $record]);
                        } else {
                            return route('filament.admin.pages.athletes.{athlete}', ['athlete' => $record]);
                        }
                    }),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    BulkAction::make('delete')
                        ->requiresConfirmation()
                        ->action(function (Collection $records): void {
                            $records->each->delete();

                            Notification::make()
                                ->success()
                                ->title(ucfirst(str_replace('_', ' ', $this->userType)) . 's deleted')
                                ->send();
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->paginated([10, 25, 50, 100])
            ->defaultPaginationPageOption(25);
    }

    public function render(): View
    {
        return view('livewire.positive-users.positive-user-index-view', [
            'regions' => $this->regions,
            'schoolYearRange' => $this->schoolYearRange,
            'currentYearNomineesCount' => $this->currentYearNomineesCount,
            'allUsersCount' => $this->allUsersCount,
            'filterViews' => $this->filterViews,
            'appliedFilterView' => $this->appliedFilterView,
        ]);
    }

    /**
     * Called when the component is booted
     */
    public function booted(): void
    {
        // Let the trait handle exportable columns registration
        $this->bootHasExportableColumns();
    }

    /**
     * Get column types for exportable columns
     *
     * @return array<string, string>
     */
    public function getExportableColumnTypes(): array
    {
        $columnTypes = [
            'created_at' => 'date',
            'updated_at' => 'date'
        ];

        // Add user type specific column types
        if ($this->userType === 'positive_athlete') {
            $columnTypes['nominations.created_at'] = 'date';
        }

        return $columnTypes;
    }

    /**
     * Get columns available for export
     *
     * @return array<string, string>
     */
    public function getExportableColumns(): array
    {
        $commonColumns = [
            'id' => 'ID',
            'first_name' => 'First Name',
            'last_name' => 'Last Name',
            'email' => 'Email',
            'phone' => 'Phone',
            'gender' => 'Gender',
            'graduation_year' => 'HS Grad Year',
            'school.name' => 'School',
            'county.name' => 'County',
            'county.state.name' => 'State',
            'sports.name' => 'Sports',
            'created_at' => 'Registration Date',
            'updated_at' => 'Last Updated',
        ];

        // Add specific columns for user types
        if ($this->userType === 'positive_athlete') {
            $athleteColumns = [
                'nominations.status' => 'Award Status',
                'nominations.ai_score' => 'AI Score',
                'nominations.created_at' => 'Nomination Date',
            ];

            return array_merge($commonColumns, $athleteColumns);
        }

        // Return common columns for other user types (e.g., coaches)
        return $commonColumns;
    }

    /**
     * Get default columns that should be pre-selected for export
     *
     * @return array<string>
     */
    public function getDefaultExportColumns(): array
    {
        if ($this->userType === 'positive_athlete') {
            return [
                'first_name',
                'last_name',
                'email',
                'school.name',
                'county.state.name',
                'sports.name',
                'nominations.status'
            ];
        }

        // Default columns for coaches
        return [
            'first_name',
            'last_name',
            'email',
            'phone',
            'school.name',
            'county.state.name'
        ];
    }

    /**
     * Identify which columns contain date values for filtering
     *
     * @return array<string>
     */
    public function getExportableDateColumns(): array
    {
        $dateColumns = [
            'created_at',
            'updated_at'
        ];

        if ($this->userType === 'positive_athlete') {
            $dateColumns[] = 'nominations.created_at';
        }

        return $dateColumns;
    }

    /**
     * Get relations that should be eager loaded for export
     *
     * @return array
     */
    public function getExportEagerLoadRelations(): array
    {
        return [
            'county.state',
            'school',
            'sports',
            'nominations' => function($query) {
                $query->latest(); // Ensure we get the most recent nomination first
            },
            'region'
        ];
    }

    /**
     * Get current filters for export
     *
     * @return array
     */
    protected function getExportFilters(): array
    {
        return [
            'appliedFilterViewId' => $this->appliedFilterViewId,
            'selectedRegion' => $this->selectedRegion,
            'showCurrentYearOnly' => $this->showCurrentYearOnly,
            'filterDefinition' => $this->appliedFilterView ? $this->appliedFilterView->filter_definition : null,
        ];
    }
}
