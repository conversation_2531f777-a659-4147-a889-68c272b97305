<?php

namespace App\Livewire\PositiveUsers;

use App\Models\User;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Livewire\Component;
use Illuminate\Support\Facades\Log;

class PositiveUserManageView extends Component implements HasForms
{
    use InteractsWithForms;

    public User $user;
    public string $userType;
    public string $activeView = 'manage'; // 'profile' or 'manage'

    public function mount(User $user, string $userType): void
    {
        try {
            $this->user = $user;
            $this->userType = $userType;
        } catch (\Exception $e) {
            Log::error('Failed to mount PositiveUserManageView: ' . $e->getMessage());
        }
    }

    public function toggleView(string $view): void
    {
        try {
            if ($view !== 'profile' && $view !== 'manage') {
                return;
            }

            $this->activeView = $view;
        } catch (\Exception $e) {
            Log::error('Failed to toggle view: ' . $e->getMessage());
        }
    }

    public function render()
    {
        return view('livewire.positive-users.positive-user-manage-view');
    }
}
