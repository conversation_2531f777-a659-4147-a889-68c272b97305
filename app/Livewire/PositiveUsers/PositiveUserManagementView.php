<?php

namespace App\Livewire\PositiveUsers;

use App\Models\User;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Livewire\Component;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class PositiveUserManagementView extends Component implements HasForms
{
    use InteractsWithForms;

    public User $user;
    public string $userType;

    // Profile status properties
    public $isHighSchoolStudent = true;
    public $isHighSchoolGraduate = false;
    public $isCollegeStudent = false;
    public $isCollegeGraduate = false;
    public $isProfessional = false;

    // Profile completeness tracking
    public $hasProfilePhoto = false;
    public $hasCareerInterests = false;
    public $hasStory = false;
    public $hasCommunityInvolvement = false;
    public $hasWorkExperience = false;
    public $hasLanguages = false;
    public $isRecruiterEnabled = false;
    public $hasCertifications = false;

    public function mount(User $user, string $userType): void
    {
        $this->user = $user;
        $this->userType = $userType;
        $this->initializeProfileStatus();
        $this->checkProfileCompleteness();
    }

    /**
     * Initialize the profile status based on user data
     */
    protected function initializeProfileStatus(): void
    {
        try {
            // Default to high school student if graduation year is in the future
            $currentYear = Carbon::now()->year;
            $graduationYear = $this->user->graduation_year;

            if ($graduationYear) {
                $this->isHighSchoolStudent = $graduationYear >= $currentYear;
                $this->isHighSchoolGraduate = $graduationYear < $currentYear;
            }

            // Other statuses would need to be determined based on additional fields
            // that might not be in the current schema
            $this->isCollegeStudent = false; // Placeholder - implement based on actual data
            $this->isCollegeGraduate = false; // Placeholder - implement based on actual data
            $this->isProfessional = false; // Placeholder - implement based on actual data
        } catch (\Exception $e) {
            Log::error('Failed to initialize profile status: ' . $e->getMessage());
        }
    }

    /**
     * Check the completeness of various profile sections
     */
    protected function checkProfileCompleteness(): void
    {
        try {
            // Check if profile photo exists
            $this->hasProfilePhoto = $this->user->getFirstMediaUrl('avatar') !== '';

            // Check if career interests are set
            $this->hasCareerInterests = $this->user->interests()->count() > 0;

            // Check if story/content is set
            $this->hasStory = !empty($this->user->content);

            // Check if community involvement is set
            $this->hasCommunityInvolvement = $this->user->communityInvolvements()->count() > 0;

            // Check if work experience is set
            $this->hasWorkExperience = $this->user->workExperiences()->count() > 0;

            // Check if languages are set (placeholder - implement based on actual data)
            $this->hasLanguages = false; // Placeholder - implement based on actual data

            // Check if recruiter is enabled
            $this->isRecruiterEnabled = (bool) $this->user->recruiter_enabled;

            // Check if certifications are earned (badges)
            $this->hasCertifications = $this->user->badges()->where('is_achieved', true)->count() > 0;
        } catch (\Exception $e) {
            Log::error('Failed to check profile completeness: ' . $e->getMessage());
        }
    }

    /**
     * Update the student status
     */
    public function updateStudentStatus($type, $value): void
    {
        try {
            switch ($type) {
                case 'high_school':
                    $this->isHighSchoolStudent = $value === 'high_school';
                    // If they're no longer a high school student, update graduation year if needed
                    if (!$this->isHighSchoolStudent && !$this->isHighSchoolGraduate && $this->user->graduation_year >= Carbon::now()->year) {
                        $this->user->graduation_year = Carbon::now()->year - 1;
                    }
                    break;
                case 'graduate':
                    $this->isHighSchoolGraduate = $value === 'graduate';
                    // If they're now a high school graduate, update graduation year if needed
                    if ($this->isHighSchoolGraduate && $this->user->graduation_year >= Carbon::now()->year) {
                        $this->user->graduation_year = Carbon::now()->year - 1;
                    }
                    break;
                case 'college':
                    $this->isCollegeStudent = $value === 'college';
                    // Additional logic for college student status could be added here
                    break;
                case 'college_graduate':
                    $this->isCollegeGraduate = $value === 'graduate';
                    // Additional logic for college graduate status could be added here
                    break;
                case 'professional':
                    $this->isProfessional = $value === 'professional';
                    // Additional logic for professional status could be added here
                    break;
            }

            // Save changes to the database
            $this->user->save();

            // Notify the user of success
            session()->flash('message', 'Profile status updated successfully.');
        } catch (\Exception $e) {
            // Log the error
            Log::error('Failed to update student status: ' . $e->getMessage());

            // Notify the user of the error
            session()->flash('error', 'Failed to update profile status.');
        }
    }

    /**
     * Toggle the recruiter status
     */
    public function toggleRecruiterStatus(): void
    {
        try {
            $this->user->recruiter_enabled = !$this->user->recruiter_enabled;
            $this->user->save();

            $this->isRecruiterEnabled = $this->user->recruiter_enabled;

            session()->flash('message', 'Recruiter status updated successfully.');
        } catch (\Exception $e) {
            Log::error('Failed to update recruiter status: ' . $e->getMessage());
            session()->flash('error', 'Failed to update recruiter status.');
        }
    }

    public function render()
    {
        return view('livewire.positive-users.positive-user-management-view', [
            'userTypeLabel' => ucfirst(str_replace('_', ' ', $this->userType))
        ]);
    }
}
