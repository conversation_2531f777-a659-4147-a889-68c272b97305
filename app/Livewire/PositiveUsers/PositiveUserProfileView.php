<?php

namespace App\Livewire\PositiveUsers;

use App\Models\User;
use App\Services\PositiveAthleteProfileService;
use Livewire\Component;
use Livewire\WithFileUploads;

class PositiveUserProfileView extends Component
{
    use WithFileUploads;

    public User $user;
    public string $userType;
    public int $currentPhotoIndex = 0;
    private PositiveAthleteProfileService $profileService;

    public function boot(PositiveAthleteProfileService $profileService): void
    {
        $this->profileService = $profileService;
    }

    public function mount(User $user, string $userType): void
    {
        $this->user = $user;
        $this->userType = $userType;
    }

    public function openPhotoModal(int $index): void
    {
        $this->currentPhotoIndex = $index;
        $this->dispatch('open-modal', id: 'photo-gallery');
    }

    public function nextPhoto(): void
    {
        $totalPhotos = $this->user->getMedia('profile_photos')->count();
        if ($totalPhotos > 0) {
            $this->currentPhotoIndex = ($this->currentPhotoIndex + 1) % $totalPhotos;
        }
    }

    public function previousPhoto(): void
    {
        $totalPhotos = $this->user->getMedia('profile_photos')->count();
        if ($totalPhotos > 0) {
            $this->currentPhotoIndex = ($this->currentPhotoIndex - 1 + $totalPhotos) % $totalPhotos;
        }
    }

    public function downloadPhoto(int $index)
    {
        $photos = $this->user->getMedia('profile_photos');
        if (isset($photos[$index])) {
            $photo = $photos[$index];
            $photoNumber = $index + 1;
            $fileName = "{$this->user->first_name}-{$this->user->last_name}_{$photoNumber}.{$photo->extension}";

            return response()->download($photo->getPath(), $fileName);
        }
    }

    public function render()
    {
        $user = $this->user->load([
            'school',
            'interests',
            'sports',
            'customSports',
            'workExperiences',
            'communityInvolvements',
            'parent',
            'teams',
            'nominations',
            'media'
        ]);

        $profileDetails = $this->profileService->getDetails($user);
        $involvements = $this->profileService->getInvolvements($user);
        $workExperiences = $this->profileService->getWorkExperiences($user);

        return view('livewire.positive-users.positive-user-profile-view', [
            'profileDetails' => $profileDetails,
            'involvements' => $involvements,
            'workExperiences' => $workExperiences,
            'avatar' => $user->getFirstMedia('avatar'),
            'profilePhotos' => $user->getMedia('profile_photos'),
        ]);
    }
}
