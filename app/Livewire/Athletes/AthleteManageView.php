<?php

namespace App\Livewire\Athletes;

use App\Models\User;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Livewire\Component;

class AthleteManageView extends Component implements HasForms
{
    use InteractsWithForms;

    public User $athlete;
    public string $activeView = 'manage'; // 'profile' or 'manage'

    public function mount(User $athlete): void
    {
        $this->athlete = $athlete;
    }

    public function toggleView(string $view): void
    {
        if ($view !== 'profile' && $view !== 'manage') {
            return;
        }

        $this->activeView = $view;
    }

    public function render()
    {
        return view('livewire.athletes.athlete-manage-view');
    }
}
