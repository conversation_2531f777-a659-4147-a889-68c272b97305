<?php

namespace App\Livewire\Athletes;

use App\Models\User;
use App\Services\PositiveAthleteProfileService;
use Livewire\Component;
use Livewire\WithFileUploads;

class AthleteProfileView extends Component
{
    use WithFileUploads;

    public User $athlete;
    public int $currentPhotoIndex = 0;
    private PositiveAthleteProfileService $profileService;

    public function boot(PositiveAthleteProfileService $profileService): void
    {
        $this->profileService = $profileService;
    }

    public function mount(User $athlete): void
    {
        $this->athlete = $athlete;
    }

    public function openPhotoModal(int $index): void
    {
        $this->currentPhotoIndex = $index;
        $this->dispatch('open-modal', id: 'photo-gallery');
    }

    public function nextPhoto(): void
    {
        $totalPhotos = $this->athlete->getMedia('profile_photos')->count();
        $this->currentPhotoIndex = ($this->currentPhotoIndex + 1) % $totalPhotos;
    }

    public function previousPhoto(): void
    {
        $totalPhotos = $this->athlete->getMedia('profile_photos')->count();
        $this->currentPhotoIndex = ($this->currentPhotoIndex - 1 + $totalPhotos) % $totalPhotos;
    }

    public function downloadPhoto(int $index)
    {
        $photo = $this->athlete->getMedia('profile_photos')[$index];
        $photoNumber = $index + 1;
        $fileName = "{$this->athlete->first_name}-{$this->athlete->last_name}_{$photoNumber}.{$photo->extension}";

        return response()->download($photo->getPath(), $fileName);
    }

    public function render()
    {
        $athlete = $this->athlete->load([
            'school',
            'interests',
            'sports',
            'customSports',
            'workExperiences',
            'communityInvolvements',
            'parent',
            'teams',
            'nominations',
            'media'
        ]);

        $profileDetails = $this->profileService->getDetails($athlete);
        $involvements = $this->profileService->getInvolvements($athlete);
        $workExperiences = $this->profileService->getWorkExperiences($athlete);

        return view('livewire.athletes.athlete-profile-view', [
            'profileDetails' => $profileDetails,
            'involvements' => $involvements,
            'workExperiences' => $workExperiences,
            'avatar' => $athlete->getFirstMedia('avatar'),
            'profilePhotos' => $athlete->getMedia('profile_photos'),
        ]);
    }
}
