<?php

namespace App\Livewire\Athletes;

use App\Models\User;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Livewire\Component;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class AthleteManagementView extends Component implements HasForms
{
    use InteractsWithForms;

    public User $athlete;

    // Profile status properties
    public $isHighSchoolStudent = true;
    public $isHighSchoolGraduate = false;
    public $isCollegeStudent = false;
    public $isCollegeGraduate = false;
    public $isProfessional = false;

    // Profile completeness tracking
    public $hasProfilePhoto = false;
    public $hasCareerInterests = false;
    public $hasStory = false;
    public $hasCommunityInvolvement = false;
    public $hasWorkExperience = false;
    public $hasLanguages = false;
    public $isRecruiterEnabled = false;
    public $hasCertifications = false;

    public function mount(User $athlete): void
    {
        $this->athlete = $athlete;
        $this->initializeProfileStatus();
        $this->checkProfileCompleteness();
    }

    /**
     * Initialize the profile status based on athlete data
     */
    protected function initializeProfileStatus(): void
    {
        // Default to high school student if graduation year is in the future
        $currentYear = Carbon::now()->year;
        $graduationYear = $this->athlete->graduation_year;

        if ($graduationYear) {
            $this->isHighSchoolStudent = $graduationYear >= $currentYear;
            $this->isHighSchoolGraduate = $graduationYear < $currentYear;
        }

        // Other statuses would need to be determined based on additional fields
        // that might not be in the current schema
        $this->isCollegeStudent = false; // Placeholder - implement based on actual data
        $this->isCollegeGraduate = false; // Placeholder - implement based on actual data
        $this->isProfessional = false; // Placeholder - implement based on actual data
    }

    /**
     * Check the completeness of various profile sections
     */
    protected function checkProfileCompleteness(): void
    {
        // Check if profile photo exists
        $this->hasProfilePhoto = $this->athlete->getFirstMediaUrl('avatar') !== '';

        // Check if career interests are set
        $this->hasCareerInterests = $this->athlete->interests()->count() > 0;

        // Check if story/content is set
        $this->hasStory = !empty($this->athlete->content);

        // Check if community involvement is set
        $this->hasCommunityInvolvement = $this->athlete->communityInvolvements()->count() > 0;

        // Check if work experience is set
        $this->hasWorkExperience = $this->athlete->workExperiences()->count() > 0;

        // Check if languages are set (placeholder - implement based on actual data)
        $this->hasLanguages = false; // Placeholder - implement based on actual data

        // Check if recruiter is enabled
        $this->isRecruiterEnabled = (bool) $this->athlete->recruiter_enabled;

        // Check if certifications are earned (badges)
        $this->hasCertifications = $this->athlete->badges()->where('is_achieved', true)->count() > 0;
    }

    /**
     * Update the student status
     */
    public function updateStudentStatus($type, $value): void
    {
        try {
            switch ($type) {
                case 'high_school':
                    $this->isHighSchoolStudent = $value === 'high_school';
                    // If they're no longer a high school student, update graduation year if needed
                    if (!$this->isHighSchoolStudent && !$this->isHighSchoolGraduate && $this->athlete->graduation_year >= Carbon::now()->year) {
                        $this->athlete->graduation_year = Carbon::now()->year - 1;
                    }
                    break;
                case 'graduate':
                    $this->isHighSchoolGraduate = $value === 'graduate';
                    // If they're now a high school graduate, update graduation year if needed
                    if ($this->isHighSchoolGraduate && $this->athlete->graduation_year >= Carbon::now()->year) {
                        $this->athlete->graduation_year = Carbon::now()->year - 1;
                    }
                    break;
                case 'college':
                    $this->isCollegeStudent = $value === 'college';
                    // Additional logic for college student status could be added here
                    break;
                case 'college_graduate':
                    $this->isCollegeGraduate = $value === 'graduate';
                    // Additional logic for college graduate status could be added here
                    break;
                case 'professional':
                    $this->isProfessional = $value === 'professional';
                    // Additional logic for professional status could be added here
                    break;
            }

            // Save changes to the database
            $this->athlete->save();

            // You could add additional metadata to track these statuses in the future
            // For example, storing them in a JSON column or a separate table

            // Notify the user of success
            session()->flash('message', 'Profile status updated successfully.');
        } catch (\Exception $e) {
            // Log the error
            Log::error('Failed to update student status: ' . $e->getMessage());

            // Notify the user of the error
            session()->flash('error', 'Failed to update profile status.');
        }
    }

    /**
     * Toggle the recruiter status
     */
    public function toggleRecruiterStatus(): void
    {
        try {
            $this->athlete->recruiter_enabled = !$this->athlete->recruiter_enabled;
            $this->athlete->save();

            $this->isRecruiterEnabled = $this->athlete->recruiter_enabled;

            session()->flash('message', 'Recruiter status updated successfully.');
        } catch (\Exception $e) {
            Log::error('Failed to update recruiter status: ' . $e->getMessage());
            session()->flash('error', 'Failed to update recruiter status.');
        }
    }

    public function render()
    {
        return view('livewire.athletes.athlete-management-view');
    }
}
