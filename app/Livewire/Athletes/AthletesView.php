<?php

namespace App\Livewire\Athletes;

use App\Enums\Gender;
use App\Enums\ProfileType;
use App\Enums\NominationStatus;
use App\Models\User;
use App\Models\Region;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Contracts\View\View;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Actions\BulkActionGroup;
use Livewire\Component;
use Illuminate\Support\Collection;
use Filament\Tables\Columns\ImageColumn;
use App\Filament\Admin\Pages\Athletes\AthleteManage;

class AthletesView extends Component implements HasTable, HasForms
{
    use InteractsWithTable, InteractsWithForms;

    /**
     * Currently selected region ID for filtering
     */
    public ?string $selectedRegion = null;
    public bool $showCurrentYearOnly = false;

    /**
     * Currently applied dynamic filter view
     */
    public ?string $appliedFilterView = null;

    /**
     * Listen for region filter changes and dynamic filter changes
     */
    protected $listeners = [
        'region-changed' => 'handleRegionChange',
        'apply-filter-view' => 'handleApplyFilterView',
        'reset-filter-view' => 'handleResetFilterView',
    ];

    /**
     * Handle region selection change from filter bar
     */
    public function handleRegionChange(?string $regionId): void
    {
        $this->selectedRegion = $regionId;
        $this->resetTable();
    }

    /**
     * Handle applying a saved filter view
     */
    public function handleApplyFilterView(string $filterView, string $resourceType): void
    {
        // Only apply filters targeting this resource type
        if ($resourceType !== 'athletes') {
            return;
        }

        $this->appliedFilterView = $filterView;

        // In a real implementation, this would load the filter configuration
        // and apply it to the query. For now, we're just using it to control
        // predefined filters for demonstration purposes.

        if ($filterView === 'all-athletes') {
            $this->showCurrentYearOnly = true;
            $this->selectedRegion = null;
        } elseif ($filterView === 'regional-athletes') {
            $this->selectedRegion = 'south-texas'; // This would be a real region ID
            $this->showCurrentYearOnly = false;
        } elseif ($filterView === 'high-performers') {
            // This is just a placeholder - in Phase 2 we'd apply complex filters
            $this->showCurrentYearOnly = true;
            $this->selectedRegion = null;
        }

        $this->resetTable();
    }

    /**
     * Handle resetting filter view
     */
    public function handleResetFilterView(string $resourceType): void
    {
        // Only reset filters targeting this resource type
        if ($resourceType !== 'athletes') {
            return;
        }

        $this->appliedFilterView = null;
        $this->showCurrentYearOnly = false;
        $this->selectedRegion = null;
        $this->resetTable();
    }

    /**
     * Get all regions for the filter bar
     */
    public function getRegionsProperty(): array
    {
        return Region::query()
            ->orderBy('name')
            ->get(['id', 'name'])
            ->toArray();
    }

    public function getSchoolYearRangeProperty(): string
    {
        $currentYear = now()->year;
        $currentMonth = now()->month;

        // If we're in the latter part of the year (after July), we're in the first half of the school year
        $startYear = $currentMonth < 7 ? $currentYear : $currentYear - 1;
        $endYear = $startYear + 1;

        return sprintf('%d-%d', $startYear, $endYear % 100);
    }

    public function getCurrentYearNomineesCountProperty(): int
    {
        // Calculate the graduation year based on the current school year
        $graduationYear = now()->month < 7 ? now()->year : now()->year + 1;

        return User::query()
            ->where('profile_type', ProfileType::POSITIVE_ATHLETE->value)
            ->where('graduation_year', '>=', $graduationYear)
            ->count();
    }

    public function getAllAthletesCountProperty(): int
    {
        return User::query()
            ->where('profile_type', ProfileType::POSITIVE_ATHLETE->value)
            ->count();
    }

    public function table(Table $table): Table
    {
        $query = User::query()
            ->where('profile_type', ProfileType::POSITIVE_ATHLETE->value)
            ->with([
                'county.state',
                'school',
                'sports',
                'nominations',
                'region',
                'media' => fn($query) => $query->where('collection_name', 'avatar')
            ]);

        if ($this->selectedRegion) {
            $query->where('region_id', $this->selectedRegion);
        }

        if ($this->showCurrentYearOnly) {
            $query->where('graduation_year', '>=', 2025);
        }

        return $table
            ->query($query)
            ->headerActions([
                Action::make('filterManager')
                    ->label('Filters')
                    ->icon('heroicon-o-funnel')
                    ->color('gray')
                    ->button()
                    ->size('sm')
                    ->extraAttributes([
                        'class' => 'mr-auto', // Position at the start of header actions
                    ])
                    ->view('livewire.shared.dynamic-filters.filter-manager', [
                        'resourceType' => 'athletes'
                    ]),

                Action::make('showCurrentYearOnly')
                    ->label(fn() => $this->showCurrentYearOnly ? 'All Historical Nominees' : '2025-26 Nominees Only')
                    ->icon(fn() => $this->showCurrentYearOnly ? 'heroicon-o-x-mark' : '')
                    ->color('gray')
                    ->button()
                    ->outlined()
                    ->size('sm')
                    ->action(function () {
                        $this->showCurrentYearOnly = !$this->showCurrentYearOnly;
                        $this->resetTable();
                    })
            ])
            ->columns([
                ImageColumn::make('avatar')
                    ->circular()
                    ->defaultImageUrl(fn(User $record) => 'https://ui-avatars.com/api/?name=' . urlencode($record->first_name . ' ' . $record->last_name))
                    ->getStateUsing(fn (User $record) => $record->getFirstMedia('avatar')?->getUrl())
                    ->label('')
                    ->size(40),
                TextColumn::make('first_name')
                    ->label('Name')
                    ->formatStateUsing(fn(User $record): string => "{$record->first_name} {$record->last_name}")
                    ->searchable(['first_name', 'last_name'])
                    ->sortable(['first_name', 'last_name']),
                TextColumn::make('nominations.ai_score')
                    ->label('AI Score')
                    ->formatStateUsing(function ($state) {
                        return $state ?? '0';
                    })
                    ->badge()
                    ->color(fn($state) => match (true) {
                        $state >= 90 => 'success',
                        $state >= 70 => 'warning',
                        default => 'danger',
                    }),
                TextColumn::make('nominations.status')
                    ->label('Award Status')
                    ->badge()
                    ->formatStateUsing(function (NominationStatus $state) {
                        if (!$state) return 'No Status';
                        return $state->label();
                    })
                    ->color(function (NominationStatus $state) {
                        if (!$state) return 'gray';
                        return $state->color();
                    }),
                TextColumn::make('sports.name')
                    ->label('Sport(s)')
                    ->formatStateUsing(function (User $record) {
                        $sport = $record->sports->first();
                        $additional = $record->sports->count() > 1 ? ' +' . ($record->sports->count() - 1) : '';
                        return view('components.sport-badge', [
                            'name' => $sport->name,
                            'additional' => $additional,
                        ]);
                    })
                    ->html()
                    ->searchable(),
                TextColumn::make('graduation_year')
                    ->label('HS Grad Year')
                    ->sortable(),
                TextColumn::make('county.state.code')
                    ->label('State')
                    ->sortable(),
                TextColumn::make('gender')
                    ->label('Gender')
                    ->formatStateUsing(fn(Gender $state) => ucfirst(strtolower($state->value)))
                    ->sortable(),
            ])
            ->filters([])
            ->actions([
                Action::make('view')
                    ->icon('heroicon-o-eye')
                    ->url(fn(User $record): string => AthleteManage::getUrl(['athlete' => $record])),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    BulkAction::make('delete')
                        ->requiresConfirmation()
                        ->action(function (Collection $records): void {
                            $records->each->delete();

                            Notification::make()
                                ->success()
                                ->title('Athletes deleted')
                                ->send();
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public function render(): View
    {
        return view('livewire.athletes.athletes-view');
    }
}
