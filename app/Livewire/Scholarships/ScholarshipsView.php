<?php

namespace App\Livewire\Scholarships;

use App\Models\Scholarship;
use App\Models\Region;
use App\Models\Market;
use App\Models\State;
use App\Models\SubRegion;
use App\Services\ScholarshipService;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Livewire\Component;
use App\Filament\Admin\Pages\Scholarships\ScholarshipEdit;
use Filament\Notifications\Notification;

class ScholarshipsView extends Component implements HasTable, HasForms
{
    use InteractsWithTable, InteractsWithForms;

    /**
     * Currently selected region ID for filtering
     */
    public ?string $selectedRegion = null;

    /**
     * View properties
     */
    public ?string $selectView = '';
    public ?string $searchQuery = '';
    public bool $showOnlyCurrentYear = false;

    /**
     * The ScholarshipService instance
     */
    protected ScholarshipService $scholarshipService;

    /**
     * Constructor
     */
    public function boot(ScholarshipService $scholarshipService): void
    {
        $this->scholarshipService = $scholarshipService;
    }

    /**
     * Listen for region filter changes and scholarship creation
     */
    protected $listeners = [
        'region-changed' => 'handleRegionChange',
        'scholarship-created' => 'refreshTable',
        'region-filter-changed' => 'onRegionFilterChanged',
    ];

    /**
     * Handle region selection change from the reusable region filter bar
     */
    public function onRegionFilterChanged(string $regionId = null): void
    {
        $this->selectedRegion = $regionId === 'all' ? null : $regionId;
        $this->resetTable();
    }

    /**
     * Handle region selection change from filter bar
     * @deprecated Use onRegionFilterChanged instead
     */
    public function handleRegionChange(?string $regionId): void
    {
        $this->selectedRegion = $regionId;
        $this->resetTable();
    }

    /**
     * Refresh the table data
     */
    public function refreshTable(): void
    {
        $this->resetTable();
    }

    /**
     * Open the create scholarship modal
     */
    public function createScholarship(): void
    {
        $this->dispatch('createScholarship');
    }

    /**
     * Toggle the current year filter
     */
    public function updatedShowOnlyCurrentYear(): void
    {
        $this->resetTable();
    }

    /**
     * Search for scholarships
     */
    public function updatedSearchQuery(): void
    {
        $this->resetTable();
    }

    /**
     * Get all regions for the filter bar
     */
    public function getRegionsProperty(): array
    {
        return Region::query()
            ->orderBy('name')
            ->get(['id', 'name'])
            ->toArray();
    }

    /**
     * Build the table structure
     */
    public function table(Table $table): Table
    {
        $query = Scholarship::query()
            ->with(['region', 'market', 'state', 'subregion']);

        // Apply region filter if selected
        if ($this->selectedRegion) {
            $query->where('region_id', $this->selectedRegion);
        }

        // Apply current year filter if enabled
        if ($this->showOnlyCurrentYear) {
            $query->where('year', 2025);
        }

        // Apply search query if provided
        if ($this->searchQuery) {
            $query->where(function (Builder $builder) {
                $builder->where('name', 'like', "%{$this->searchQuery}%")
                    ->orWhereHas('market', function (Builder $builder) {
                        $builder->where('name', 'like', "%{$this->searchQuery}%");
                    })
                    ->orWhereHas('state', function (Builder $builder) {
                        $builder->where('name', 'like', "%{$this->searchQuery}%");
                    })
                    ->orWhereHas('subregion', function (Builder $builder) {
                        $builder->where('name', 'like', "%{$this->searchQuery}%");
                    });
            });
        }

        return $table
            ->query($query)
            ->columns([
                TextColumn::make('name')
                    ->label('Scholarship Name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('year')
                    ->label('Year')
                    ->formatStateUsing(fn($state) => "{$state}-" . ($state + 1))
                    ->searchable()
                    ->sortable(),
                TextColumn::make('market.name')
                    ->label('Market')
                    ->searchable(['markets.name'])
                    ->sortable(),
                TextColumn::make('state.name')
                    ->label('State')
                    ->searchable(['states.name'])
                    ->sortable(),
                TextColumn::make('subregion.name')
                    ->label('Subregion')
                    ->searchable(['sub_regions.name'])
                    ->sortable(),
                IconColumn::make('is_active')
                    ->label('Status')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger')
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('year')
                    ->options(function () {
                        $years = Scholarship::query()
                            ->distinct()
                            ->orderBy('year', 'desc')
                            ->pluck('year')
                            ->toArray();

                        $options = [];
                        foreach ($years as $year) {
                            $options[$year] = "{$year}-" . ($year + 1);
                        }

                        return $options;
                    })
                    ->attribute('year'),

                SelectFilter::make('market')
                    ->label('Market')
                    ->relationship('market', 'name')
                    ->searchable()
                    ->preload(),

                SelectFilter::make('state')
                    ->label('State')
                    ->relationship('state', 'name')
                    ->searchable()
                    ->preload(),

                SelectFilter::make('region')
                    ->label('Region')
                    ->relationship('region', 'name')
                    ->searchable()
                    ->preload(),

                SelectFilter::make('subregion')
                    ->label('Subregion')
                    ->relationship('subregion', 'name')
                    ->searchable()
                    ->preload(),

                SelectFilter::make('is_active')
                    ->options([
                        '1' => 'Active',
                        '0' => 'Inactive',
                    ])
                    ->attribute('is_active'),
            ])
            ->actions([
                Action::make('view')
                    ->label('View')
                    ->icon('heroicon-o-eye')
                    ->url(fn(Scholarship $record) => ScholarshipEdit::getUrl(['scholarship' => $record]))
                    ->openUrlInNewTab(false),

                Action::make('toggle_status')
                    ->label(fn(Scholarship $record) => $record->is_active ? 'Deactivate' : 'Activate')
                    ->icon(fn(Scholarship $record) => $record->is_active ? 'heroicon-o-x-circle' : 'heroicon-o-check-circle')
                    ->color(fn(Scholarship $record) => $record->is_active ? 'danger' : 'success')
                    ->action(function (Scholarship $record) {
                        if ($record->is_active) {
                            $this->scholarshipService->deactivateScholarship($record->id);
                        } else {
                            $this->scholarshipService->activateScholarship($record->id);
                        }

                        Notification::make()
                            ->title($record->is_active ? 'Scholarship deactivated' : 'Scholarship activated')
                            ->success()
                            ->send();
                    }),

                Action::make('delete')
                    ->label('Delete')
                    ->icon('heroicon-o-trash')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading('Delete Scholarship')
                    ->modalDescription('Are you sure you want to delete this scholarship? This action cannot be undone.')
                    ->modalSubmitActionLabel('Yes, delete it')
                    ->action(function (Scholarship $record) {
                        try {
                            $this->scholarshipService->deleteScholarship($record->id);

                            Notification::make()
                                ->title('Scholarship deleted successfully')
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Failed to delete scholarship')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),
            ])
            ->bulkActions([
                BulkAction::make('delete')
                    ->label('Delete')
                    ->icon('heroicon-o-trash')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading('Delete Scholarships')
                    ->modalDescription('Are you sure you want to delete these scholarships? This action cannot be undone.')
                    ->modalSubmitActionLabel('Yes, delete them')
                    ->action(function (Collection $records) {
                        $successCount = 0;
                        $errorCount = 0;

                        $records->each(function ($record) use (&$successCount, &$errorCount) {
                            try {
                                $this->scholarshipService->deleteScholarship($record->id);
                                $successCount++;
                            } catch (\Exception $e) {
                                $errorCount++;
                            }
                        });

                        if ($successCount > 0) {
                            Notification::make()
                                ->title("Successfully deleted {$successCount} scholarship(s)")
                                ->success()
                                ->send();
                        }

                        if ($errorCount > 0) {
                            Notification::make()
                                ->title("Failed to delete {$errorCount} scholarship(s)")
                                ->danger()
                                ->send();
                        }

                        $this->refreshTable();
                    }),

                BulkAction::make('duplicate')
                    ->label('Duplicate')
                    ->icon('heroicon-o-document-duplicate')
                    ->color('gray')
                    ->action(function (Collection $records) {
                        $successCount = 0;
                        $errorCount = 0;

                        $records->each(function ($record) use (&$successCount, &$errorCount) {
                            try {
                                // Use the service method instead of inline logic
                                $this->scholarshipService->duplicateScholarship($record);
                                $successCount++;
                            } catch (\Exception $e) {
                                $errorCount++;
                            }
                        });

                        if ($successCount > 0) {
                            Notification::make()
                                ->title("Successfully duplicated {$successCount} scholarship(s)")
                                ->success()
                                ->send();
                        }

                        if ($errorCount > 0) {
                            Notification::make()
                                ->title("Failed to duplicate {$errorCount} scholarship(s)")
                                ->danger()
                                ->send();
                        }

                        $this->refreshTable();
                    }),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public function render(): View
    {
        return view('livewire.scholarships.scholarships-view');
    }
}
