<?php

namespace App\Livewire\Shared;

use App\Models\Region;
use Livewire\Component;
use Illuminate\View\View;

class RegionFilterBar extends Component
{
    /**
     * Array of regions to display in the filter bar
     *
     * @var Region[]
     */
    public array $regions = [];

    /**
     * Currently selected region ID
     *
     * @var string|null
     */
    public ?string $activeRegion = null;

    /**
     * Mount the component with regions
     *
     * @param Region[] $regions
     */
    public function mount(array $regions): void
    {
        $this->regions = $regions;
    }

    /**
     * Handle region selection
     *
     * @param string|null $regionId
     */
    public function selectRegion(?string $regionId = null): void
    {
        $this->activeRegion = $regionId;
        $this->dispatch('region-changed', regionId: $regionId);
    }

    /**
     * Render the component
     */
    public function render(): View
    {
        return view('livewire.shared.region-filter-bar');
    }
}
