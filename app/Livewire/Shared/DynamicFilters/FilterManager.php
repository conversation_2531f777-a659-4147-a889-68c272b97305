<?php

namespace App\Livewire\Shared\DynamicFilters;

use Livewire\Component;
use Illuminate\Contracts\View\View;

class FilterManager extends Component
{
    // Resource type that this filter manager is handling
    public string $resourceType;

    // Flag for edit mode
    public bool $isEditMode = false;
    public ?string $currentViewId = null;

    // Listeners for events from child components
    protected $listeners = [
        'create-filter-view' => 'handleCreateView',
        'edit-filter-view' => 'handleEditView',
        'filter-view-changed' => 'handleFilterViewChanged',
        'filter-view-saved' => 'handleFilterViewSaved',
        'reset-table' => 'handleResetTable',
        'set-filter-form-mode' => 'forwardFilterFormMode',
    ];

    public function mount(string $resourceType): void
    {
        $this->resourceType = $resourceType;
    }

    /**
     * Forward filter form mode event to all child components
     */
    public function forwardFilterFormMode(bool $isEdit, ?string $viewId = null, ?string $resourceType = null): void
    {

        $this->isEditMode = $isEdit;
        $this->currentViewId = $viewId;

        // We don't need to re-dispatch as each component is already listening for the original event
    }

    public function handleCreateView(): void
    {
        $this->isEditMode = false;
        $this->currentViewId = null;

        $this->dispatch('set-filter-form-mode', isEdit: false, viewId: null, resourceType: $this->resourceType);
        $this->dispatch('open-modal', id: 'manage-filter-view');
    }

    public function handleEditView(string $viewId): void
    {
        $this->isEditMode = true;
        $this->currentViewId = $viewId;

        $this->dispatch('set-filter-form-mode', isEdit: true, viewId: $viewId, resourceType: $this->resourceType);
        $this->dispatch('open-modal', id: 'manage-filter-view');
    }

    public function handleFilterViewChanged(string $filterViewId): void
    {
        // This would apply the filter to the parent component
        // We'll emit an event to the parent component
        $this->dispatch('apply-filter-view', filterViewId: $filterViewId, resourceType: $this->resourceType);
    }

    public function handleFilterViewSaved(string $filterId = null): void
    {
        if ($filterId) {
            $this->isEditMode = true;
            $this->currentViewId = $filterId;
        }

        // This would refresh the filter selector after saving
        $this->dispatch('refresh-filter-selector', filterId: $filterId);

        // Ensure child components know we're in edit mode now
        $this->dispatch('set-filter-form-mode', isEdit: true, viewId: $filterId, resourceType: $this->resourceType);
    }

    /**
     * Pass through the reset-table event to ensure it reaches the parent
     */
    public function handleResetTable(string $resourceType): void
    {
        // Pass through the reset-table event
        $this->dispatch('reset-table', resourceType: $resourceType);
    }

    /**
     * Handle saving of filter view and sharing settings
     * This will dispatch a save event that child components listen for
     */
    public function saveFilterView(): void
    {
        // Dispatch a save event that child components will listen for
        $this->dispatch('filter-manager-save');
    }

    /**
     * Handle canceling of filter view editing
     */
    public function cancelFilterView(): void
    {
        // Close the modal
        $this->dispatch('close-modal', id: 'manage-filter-view');
    }

    public function render(): View
    {
        return view('livewire.shared.dynamic-filters.filter-manager');
    }
}
