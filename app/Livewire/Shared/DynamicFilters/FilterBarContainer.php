<?php

namespace App\Livewire\Shared\DynamicFilters;

use Livewire\Component;
use Illuminate\Contracts\View\View;

class FilterBarContainer extends Component
{
    /**
     * Resource type that this filter bar is handling
     */
    public string $resourceType;

    /**
     * Flag to indicate if we're showing current year only
     */
    public bool $showCurrentYearOnly = false;

    /**
     * School year range for the button label
     */
    public string $schoolYearRange;

    protected $listeners = [
        'toggle-current-year-filter' => 'handleToggleCurrentYearFilter',
    ];

    /**
     * Mount the component
     */
    public function mount(string $resourceType, bool $showCurrentYearOnly, string $schoolYearRange): void
    {
        $this->resourceType = $resourceType;
        $this->showCurrentYearOnly = $showCurrentYearOnly;
        $this->schoolYearRange = $schoolYearRange;
    }

    /**
     * Toggle the current year filter
     */
    public function toggleCurrentYearFilter(): void
    {
        $this->showCurrentYearOnly = !$this->showCurrentYearOnly;

        // Emit event to parent table component to update the filter
        $this->dispatch('toggle-current-year-filter',
            showCurrentYearOnly: $this->showCurrentYearOnly
        );
    }

    /**
     * Render the component
     */
    public function render(): View
    {
        return view('livewire.shared.dynamic-filters.filter-bar-container');
    }
}
