<?php

namespace App\Livewire\Shared\DynamicFilters;

use App\Exports\ExportableColumnsRegistry;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Livewire\Component;
use Novadaemon\FilamentCombobox\Combobox;

class CsvExportForm extends Component implements Forms\Contracts\HasForms
{
    use Forms\Concerns\InteractsWithForms;

    /**
     * The resource type for the current view
     */
    public ?string $resourceType = null;

    /**
     * Form data
     */
    public array $data = [];

    /**
     * Cached columns from the registry to handle Livewire's stateless nature
     */
    public array $cachedAvailableColumns = [];

    /**
     * Cached date columns to maintain consistency
     */
    public array $cachedDateColumns = [];

    /**
     * Mount the component
     */
    public function mount(?string $resourceType = null)
    {
        $this->resourceType = $resourceType;

        // Cache available columns and date columns to handle registry state issues
        if ($this->resourceType) {
            // Get and cache available columns
            $this->cachedAvailableColumns = ExportableColumnsRegistry::getForResource($this->resourceType);

            // Get and cache date columns
            $this->cachedDateColumns = ExportableColumnsRegistry::getDateColumns($this->resourceType);
        }

        // Set default selected columns from the registry
        if ($this->resourceType) {
            $this->data['selectedColumns'] = ExportableColumnsRegistry::getDefaultColumns($this->resourceType);

            // If no defaults are registered, use empty array
            if (empty($this->data['selectedColumns'])) {
                $this->data['selectedColumns'] = [];
            }
        } else {
            $this->data['selectedColumns'] = [];
        }

        $this->form->fill($this->data);
    }

    /**
     * Define the form schema
     */
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Combobox::make('selectedColumns')
                    ->label('')
                    ->options($this->getAvailableColumns())
                    ->dehydrated(true)
                    ->live()
                    ->afterStateUpdated(function () {
                        // Clear the date column selection when changing column selections
                        if (isset($this->data['date_column'])) {
                            $this->data['date_column'] = null;
                        }
                    }),

                // Date filtering options (only shown if user selected any date columns)
                Forms\Components\Grid::make('date_filter_section')
                    ->columns([
                        'sm' => 1,
                        'md' => 4
                    ])
                    ->schema([
                        Forms\Components\Select::make('date_column')
                            ->label('Filter By Date')
                            ->options(function (Forms\Get $get) {
                                $selectedColumns = $get('selectedColumns') ?? [];
                                $availableColumns = $this->getAvailableColumns();

                                // Find date columns among the user's selection
                                $dateOptions = [];
                                foreach ($selectedColumns as $column) {
                                    // Use our isDateColumn method which checks both cache and fallbacks
                                    if ($this->isDateColumn($column)) {
                                        // Get label from available columns or format the column name
                                        $label = $availableColumns[$column] ?? $this->formatColumnName($column);
                                        $dateOptions[$column] = $label;
                                    }
                                }

                                return $dateOptions;
                            })
                            ->placeholder('Select date field')
                            ->columnSpan(1)
                            ->reactive()
                            ->live(),

                        Forms\Components\DatePicker::make('date_from')
                            ->label('From')
                            ->required()
                            ->columnSpan(1)
                            ->visible(fn(Forms\Get $get) => !empty($get('date_column'))),

                        Forms\Components\DatePicker::make('date_to')
                            ->label('To')
                            ->required()
                            ->after('date_from')
                            ->default(now()->toDateString())
                            ->columnSpan(1)
                            ->visible(fn(Forms\Get $get) => !empty($get('date_column'))),

                        Forms\Components\Actions::make([
                            Forms\Components\Actions\Action::make('clear_date')
                                ->label('Clear')
                                ->icon('heroicon-o-x-mark')
                                ->color('danger')
                                ->size('xs')
                                ->action(function (Forms\Set $set) {
                                    $set('date_column', null);
                                    $set('date_from', null);
                                    $set('date_to', null);
                                })
                                ->visible(fn(Forms\Get $get) => !empty($get('date_column')))
                        ])
                        ->columnSpan(1)
                        ->alignment('right')
                    ])
                    ->visible(function (Forms\Get $get) {
                        // Check if user has selected any date columns
                        $selectedColumns = $get('selectedColumns') ?? [];

                        $hasDateColumn = false;
                        foreach ($selectedColumns as $column) {
                            // Use our isDateColumn method for consistent detection
                            if ($this->isDateColumn($column)) {
                                $hasDateColumn = true;
                                break;
                            }
                        }

                        return $hasDateColumn;
                    })
            ])
            ->statePath('data');
    }

    /**
     * Handle CSV download request
     */
    public function downloadCsv()
    {
        $selectedColumns = $this->data['selectedColumns'] ?? [];

        if (empty($selectedColumns)) {
            // Notify user to select columns first
            Notification::make()
                ->warning()
                ->title('No columns selected')
                ->body('Please select at least one column to export')
                ->send();

            return;
        }

        // Prepare date filter parameters if date column is selected
        $dateFilter = null;
        if (!empty($this->data['date_column'])) {
            // Validate date fields are present
            if (empty($this->data['date_from']) || empty($this->data['date_to'])) {
                Notification::make()
                    ->warning()
                    ->title('Date filter incomplete')
                    ->body('Please provide both From and To dates for filtering')
                    ->send();

                return;
            }

            $dateFilter = [
                'column' => $this->data['date_column'],
                'from' => $this->data['date_from'],
                'to' => $this->data['date_to'],
            ];
        }

        // Show notification that export has been initiated
        Notification::make()
            ->success()
            ->title('CSV Export Initiated')
            ->body('Your export request has been submitted and is being processed.')
            ->send();

        // Dispatch event to parent with export data
        $exportData = [
            'columns' => $selectedColumns,
            'resource_type' => $this->resourceType,
            'date_filter' => $dateFilter,
        ];

        // Dispatch the event
        $this->dispatch('export-csv', data: $exportData);
    }

    /**
     * Get available columns for the current resource type
     */
    protected function getAvailableColumns(): array
    {
        // First check if we have cached columns
        if (!empty($this->cachedAvailableColumns)) {
            return $this->cachedAvailableColumns;
        }

        // If we have a resource type, fetch columns from the registry
        if ($this->resourceType) {
            $columns = ExportableColumnsRegistry::getForResource($this->resourceType);

            if (!empty($columns)) {
                // Cache the columns for future use
                $this->cachedAvailableColumns = $columns;
                return $columns;
            }

            // If no columns registered yet, fallback to example columns based on resource type
            return $this->getFallbackColumns();
        }

        // Fallback to dummy options if no resource type
        return $this->getFallbackColumns();
    }

    /**
     * Get fallback columns while we're implementing the registry
     */
    protected function getFallbackColumns(): array
    {
        // Return different examples based on resource type
        if ($this->resourceType === 'sponsors') {
            return [
                'first_name' => 'First Name',
                'last_name' => 'Last Name',
                'email' => 'Email',
                'organization_name' => 'Organization',
                'state' => 'State',
                'created_at' => 'Registration Date',
            ];
        } elseif (in_array($this->resourceType, ['athletes', 'users', 'coaches'])) {
            return [
                'first_name' => 'First Name',
                'last_name' => 'Last Name',
                'email' => 'Email',
                'phone' => 'Phone',
                'gender' => 'Gender',
                'graduation_year' => 'HS Grad Year',
                'school_name' => 'School',
                'county' => 'County',
                'state' => 'State',
                'sports' => 'Sports',
                'nomination_status' => 'Award Status',
                'ai_score' => 'AI Score',
                'created_at' => 'Registration Date',
                'updated_at' => 'Last Updated',
            ];
        }

        // Generic fallback
        return [
            'id' => 'ID',
            'name' => 'Name',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Check if a column is a date column
     */
    protected function isDateColumn(string $column): bool
    {
        // First check the cached date columns
        if (in_array($column, $this->cachedDateColumns)) {
            return true;
        }

        // Then check with the registry if this column is a date column
        if ($this->resourceType && ExportableColumnsRegistry::isDateColumn($this->resourceType, $column)) {
            return true;
        }

        // Fallback: Check for common date column patterns
        return $this->isCommonDateColumn($column);
    }

    /**
     * Format a column name into a human-readable label
     *
     * @param string $columnName
     * @return string
     */
    protected function formatColumnName(string $columnName): string
    {
        // First try to get the label from the cached columns
        if (isset($this->cachedAvailableColumns[$columnName])) {
            return $this->cachedAvailableColumns[$columnName];
        }

        // Then try the registry if resource type is set
        if ($this->resourceType) {
            $columns = ExportableColumnsRegistry::getForResource($this->resourceType);
            if (isset($columns[$columnName])) {
                return $columns[$columnName];
            }
        }

        // Special cases for common columns when registry fails
        $specialCases = [
            'nominations.created_at' => 'Nomination Date',
            'created_at' => 'Registration Date',
            'updated_at' => 'Last Updated',
            'deleted_at' => 'Deleted Date'
        ];

        if (isset($specialCases[$columnName])) {
            return $specialCases[$columnName];
        }

        // Handle relationship fields (contains dots)
        if (str_contains($columnName, '.')) {
            $parts = explode('.', $columnName);
            $name = end($parts);
        } else {
            $name = $columnName;
        }

        // Convert snake_case to Title Case
        $name = str_replace('_', ' ', $name);
        $name = ucwords($name);

        // Handle common abbreviations
        $name = str_replace('Id', 'ID', $name);
        $name = str_replace('Ai', 'AI', $name);

        return $name;
    }

    /**
     * Check if a column matches common date column patterns
     * This is a fallback for when the registry doesn't have the column registered
     */
    protected function isCommonDateColumn(string $column): bool
    {
        // Special cases
        if (in_array($column, ['created_at', 'updated_at', 'deleted_at', 'nominations.created_at'])) {
            return true;
        }

        // Check for common patterns
        return str_contains(strtolower($column), 'date') ||
               preg_match('/_at$/', $column) ||
               preg_match('/_on$/', $column) ||
               str_contains(strtolower($column), 'time');
    }

    /**
     * Render the component
     */
    public function render()
    {
        return view('livewire.shared.dynamic-filters.csv-export-form');
    }
}
