<?php

namespace App\Livewire\Shared\DynamicFilters;

use App\Models\FilterView;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Livewire\Component;
use Illuminate\Contracts\View\View;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Illuminate\Support\Facades\Auth;

class FilterSelector extends Component implements HasForms, HasActions
{
    use InteractsWithForms;
    use InteractsWithActions;

    public ?string $selectedViewId = null;
    public string $resourceType = '';
    public array $data = [];

    protected $listeners = [
        'refresh-filter-selector' => 'refreshFilterViews',
        'filter-view-saved' => 'handleFilterViewSaved',
    ];

    // To store the ID of the most recently created/updated filter
    public ?string $lastSavedFilterId = null;

    public function mount(string $resourceType = 'users'): void
    {
        $this->resourceType = $resourceType;
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('selectedViewId')
                    ->label('')
                    ->placeholder('Select View')
                    ->searchable()
                    ->options(function () {
                        return $this->getFilterViewOptions();
                    })
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->selectedViewId = $state;
                        if ($state) {
                            $this->dispatch('filter-view-changed', filterViewId: $state);
                        } else {
                            // If the filter view has been cleared (X clicked), reset the filters
                            $this->dispatch('reset-filter-view', resourceType: $this->resourceType);
                        }
                    })
            ])
            ->statePath('data');
    }

    public function getFilterViewOptions(): array
    {
        return FilterView::query()
            ->where('resource_type', $this->resourceType)
            ->accessibleBy(Auth::user())
            ->orderBy('name')
            ->pluck('name', 'id')
            ->toArray();
    }

    public function handleFilterViewSaved(?string $filterId = null): void
    {
        if ($filterId) {
            $this->lastSavedFilterId = $filterId;
        }
    }

    public function refreshFilterViews(?string $filterId = null): void
    {
        // If a specific filterId is passed, use that
        if ($filterId) {
            $this->lastSavedFilterId = $filterId;
        }

        // If we have a lastSavedFilterId (from creation or update), select it
        if ($this->lastSavedFilterId) {
            $this->selectedViewId = $this->lastSavedFilterId;

            // Fill the form with the selected filter
            $this->form->fill([
                'selectedViewId' => $this->lastSavedFilterId
            ]);

            // Apply the filter
            $this->dispatch('filter-view-changed', filterViewId: $this->lastSavedFilterId);

            // Reset lastSavedFilterId after using it
            $this->lastSavedFilterId = null;
        } else {
            // If no lastSavedFilterId, maintain the current selection
            // (unless it's been deleted, which we'll detect in the template)
            $this->form->fill([
                'selectedViewId' => $this->selectedViewId
            ]);
        }
    }

    public function editViewAction(): Action
    {
        return Action::make('editView')
            ->label('Edit')
            ->disabled(fn() => !$this->selectedViewId)
            ->button()
            ->size('sm')
            ->color('gray')
            ->action(function () {
                $this->dispatch('edit-filter-view', viewId: $this->selectedViewId);
            });
    }

    public function deleteViewAction(): Action
    {
        return Action::make('deleteView')
            ->label('Delete')
            ->disabled(fn () => !$this->selectedViewId)
            ->button()
            ->size('sm')
            ->color('gray')
            ->requiresConfirmation()
            ->action(function () {
                try {
                    $filterView = FilterView::find($this->selectedViewId);

                    if ($filterView) {
                        // Check if the user owns this filter
                        if ($filterView->user_id !== Auth::id() && !Auth::user()->can('delete-any-filter-view')) {
                            Notification::make()
                                ->danger()
                                ->title('You do not have permission to delete this filter view')
                                ->send();
                            return;
                        }

                        $filterView->delete();

                        Notification::make()
                            ->success()
                            ->title('Filter view deleted successfully')
                            ->send();
                    }
                } catch (\Exception $e) {
                    Notification::make()
                        ->danger()
                        ->title('Error deleting filter view')
                        ->body($e->getMessage())
                        ->send();

                    logger('Error deleting filter view: ' . $e->getMessage());
                }

                // Reset state
                $this->selectedViewId = null;
                $this->lastSavedFilterId = null;

                // Reset the form state
                $this->form->fill([
                    'selectedViewId' => null
                ]);
            });
    }

    public function createViewAction(): Action
    {
        return Action::make('createView')
            ->label('Create New')
            ->button()
            ->size('sm')
            ->action(function () {
                $this->dispatch('create-filter-view');
            });
    }

    public function render(): View
    {
        return view('livewire.shared.dynamic-filters.filter-selector');
    }
}
