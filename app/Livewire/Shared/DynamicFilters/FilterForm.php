<?php

namespace App\Livewire\Shared\DynamicFilters;

use App\Filters\Filter;
use App\Filters\FilterDefinition;
use App\Filters\Transformers\FilterDefinitionTransformer;
use App\Filters\Validators\FilterDefinitionValidator;
use App\Models\FilterView;
use App\Models\User;
use Filament\Forms\Components\Component as ComponentsComponent;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Illuminate\Contracts\View\View;

class FilterForm extends Component implements HasForms
{
    use InteractsWithForms;

    public bool $isEdit = false;
    public ?string $viewId = null;
    public string $resourceType = '';

    public array $data = [];
    public array $filterGroups = [];

    /**
     * Cache for filter registry to avoid multiple calls
     */
    protected ?array $filterRegistry = null;

    /**
     * Map resource types to model classes
     */
    protected array $resourceTypeToModelMap = [
        'athletes' => User::class,
        'coaches' => User::class,
        'sponsors' => User::class,
        'users' => User::class,
    ];

    /**
     * Define available operators with human-readable labels
     */
    protected array $operatorLabels = [
        '=' => 'Is',
        '!=' => 'Is Not',
        '>' => 'Greater Than',
        '<' => 'Less Than',
        '>=' => 'Greater Than or Equal To',
        '<=' => 'Less Than or Equal To',
        'contains' => 'Contains',
        'starts_with' => 'Starts With',
        'ends_with' => 'Ends With',
        'in' => 'In',
        'not_in' => 'Not In',
        'between' => 'Between',
        'null' => 'Is Empty',
        'not_null' => 'Is Not Empty',
    ];

    // List of user groups for visibility settings
    protected array $userGroups = [
        'group-a' => 'Group A',
        'group-b' => 'Group B',
        'group-c' => 'Group C',
    ];

    protected $listeners = [
        'set-filter-form-mode' => 'setMode',
        'filter-manager-save' => 'save',
    ];

    // Add property for delete confirmation
    public bool $confirmDelete = false;

    /**
     * Transformer instance for converting between form data and filter definitions
     */
    protected ?FilterDefinitionTransformer $transformer = null;

    /**
     * Validator instance for validating filter definitions
     */
    protected ?FilterDefinitionValidator $validator = null;

    public function mount(string $resourceType = 'users'): void
    {
        $this->resourceType = $resourceType;
        $this->transformer = new FilterDefinitionTransformer();
        $this->validator = new FilterDefinitionValidator();

        $this->form->fill([
            'viewName' => '',
            'filterGroups' => [
                [
                    'conditions' => [
                        [
                            'field' => $this->getDefaultFieldForResource(),
                            'operator' => '=',
                            'text_value' => '',
                        ],
                    ],
                    'groupConjunction' => 'or',
                ],
            ],
        ]);
    }

    /**
     * Get the first available field from the filter registry as default
     */
    protected function getDefaultFieldForResource(): string
    {
        $filters = $this->getFilterRegistry();
        if (!empty($filters)) {
            return $filters[0]->field;
        }
        return 'first_name'; // Fallback default
    }

    /**
     * Get the filter registry for the current resource type
     */
    protected function getFilterRegistry(): array
    {
        if ($this->filterRegistry !== null) {
            return $this->filterRegistry;
        }

        $this->filterRegistry = \App\Filters\FilterRegistry::getForResource($this->resourceType);

        // Fallback to model-based filters if registry is empty
        if (empty($this->filterRegistry)) {
            $modelClass = $this->resourceTypeToModelMap[$this->resourceType] ?? User::class;

            if (method_exists($modelClass, 'getFilters')) {
                $this->filterRegistry = $modelClass::getFilters();
            }
        }

        return $this->filterRegistry;
    }

    /**
     * Get filter options for the field select dropdown
     */
    protected function getFilterFieldOptions(): array
    {
        $filters = $this->getFilterRegistry();
        $options = [];

        foreach ($filters as $filter) {
            $options[$filter->field] = $filter->label;
        }

        return $options;
    }

    /**
     * Get a specific filter by field name
     */
    protected function getFilterByField(?string $field): ?Filter
    {
        if ($field === null || $field === '') {
            // If no field is provided, return the default filter
            $filters = $this->getFilterRegistry();
            return !empty($filters) ? $filters[0] : null;
        }

        $filters = $this->getFilterRegistry();

        foreach ($filters as $filter) {
            if ($filter->field === $field) {
                return $filter;
            }
        }

        return null;
    }

    /**
     * Get operator options for a specific field
     */
    protected function getOperatorOptionsForField(string $field): array
    {
        $filter = $this->getFilterByField($field);

        if (!$filter) {
            // Fallback to default operators
            return [
                '=' => $this->operatorLabels['='],
                '!=' => $this->operatorLabels['!='],
            ];
        }

        $operators = [];
        foreach ($filter->operators as $operator) {
            $operators[$operator] = $this->operatorLabels[$operator] ?? $operator;
        }

        return $operators;
    }

    /**
     * Get value options for a specific field based on its type
     */
    protected function getValueOptionsForField(string $field, string $operator): array
    {
        $filter = $this->getFilterByField($field);

        if (!$filter) {
            return [];
        }

        // For fields with predefined options
        if ($filter->type === 'select') {
            return $filter->getOptions();
        }

        // For fields with dynamic values based on the field type
        return match ($filter->type) {
            'date' => [], // Empty for date picker
            'number' => [], // Empty for number input
            'boolean' => ['true' => 'Yes', 'false' => 'No'],
            default => [], // Empty for text input
        };
    }

    /**
     * Create an appropriate form component for a filter field based on its type and operator
     */
    protected function createInputComponentForField(string $field, string $operator): ComponentsComponent
    {
        $filter = $this->getFilterByField($field);

        // If no filter found or operator is null/not_null, return a hidden input
        if (!$filter || in_array($operator, ['null', 'not_null'])) {
            return Hidden::make('value')
                ->default('');
        }

        // Create components based on filter type
        return match ($filter->type) {
            'select' => $this->createSelectComponent($filter, $operator),
            'date' => $this->createDateComponent($filter, $operator),
            'number' => $this->createNumberComponent($filter, $operator),
            'boolean' => $this->createBooleanComponent($filter, $operator),
            default => $this->createTextComponent($filter, $operator),
        };
    }

    /**
     * Create a select component for fields with predefined options
     */
    protected function createSelectComponent(Filter $filter, string $operator): ComponentsComponent
    {
        // For operators that work with multiple values
        if ($operator === 'in' || $operator === 'not_in') {
            return TagsInput::make('value')
                ->label('')
                ->placeholder('Select values')
                ->suggestions($filter->getOptions());
        }

        // For operators that work with ranges
        if ($operator === 'between') {
            return Fieldset::make('value')
                ->label('')
                ->columns(2)
                ->schema([
                    Select::make('min')
                        ->label('Min')
                        ->options($filter->getOptions())
                        ->required(),
                    Select::make('max')
                        ->label('Max')
                        ->options($filter->getOptions())
                        ->required(),
                ]);
        }

        // Default single select
        return Select::make('value')
            ->label('')
            ->options($filter->getOptions())
            ->required();
    }

    /**
     * Create a date component for date fields
     */
    protected function createDateComponent(Filter $filter, string $operator): ComponentsComponent
    {
        // For operators that work with ranges
        if ($operator === 'between') {
            return Fieldset::make('value')
                ->label('')
                ->columns(2)
                ->schema([
                    DatePicker::make('min')
                        ->label('Start Date')
                        ->required(),
                    DatePicker::make('max')
                        ->label('End Date')
                        ->required()
                        // Validate end date >= start date
                        ->after('min'),
                ]);
        }

        // Default single date
        return DatePicker::make('value')
            ->label('')
            ->required();
    }

    /**
     * Create a number component for numeric fields
     */
    protected function createNumberComponent(Filter $filter, string $operator): ComponentsComponent
    {
        // For operators that work with ranges
        if ($operator === 'between') {
            return Fieldset::make('value')
                ->label('')
                ->columns(2)
                ->schema([
                    TextInput::make('min')
                        ->label('Min')
                        ->numeric()
                        ->required(),
                    TextInput::make('max')
                        ->label('Max')
                        ->numeric()
                        ->required()
                        ->markAsRequired(false)
                        ->rules([
                            fn (callable $get): string => 'gt:' . $get('min'),
                        ]),
                ]);
        }

        // Default single number input
        return TextInput::make('value')
            ->label('')
            ->numeric()
            ->required()
            ->formatStateUsing(function ($state) {
                if ($state === null) {
                    return '';
                }

                return $state;
            })
            ->visible(function (callable $get) {
                $field = $get('field');
                $operator = $get('operator');

                if (in_array($operator, ['null', 'not_null', 'between'])) {
                    return false;
                }

                $filter = $this->getFilterByField($field);
                return $filter && $filter->type === 'number';
            })
            ->columnSpan(1);
    }

    /**
     * Create a boolean component for boolean fields
     */
    protected function createBooleanComponent(Filter $filter, string $operator): ComponentsComponent
    {
        // Boolean values are typically represented as a toggle or select
        if (in_array($operator, ['=', '!='])) {
            return Toggle::make('value')
                ->label('')
                ->onColor('success')
                ->offColor('danger')
                ->required();
        }

        // Fallback to select for complex operators
        return Select::make('value')
            ->label('')
            ->options(['true' => 'Yes', 'false' => 'No'])
            ->required();
    }

    /**
     * Create a text component for string fields
     */
    protected function createTextComponent(Filter $filter, string $operator): ComponentsComponent
    {
        // For operators that work with multiple values
        if ($operator === 'in' || $operator === 'not_in') {
            return TagsInput::make('value_tags')
                ->label('')
                ->placeholder('Enter values')
                ->required();
        }

        // For operators that work with ranges
        if ($operator === 'between') {
            return Fieldset::make('value')
                ->label('')
                ->columns(2)
                ->schema([
                    TextInput::make('min')
                        ->label('Min')
                        ->required()
                        ->maxLength(255),
                    TextInput::make('max')
                        ->label('Max')
                        ->required()
                        ->maxLength(255),
                ]);
        }

        // Default text input
        return TextInput::make('text_value')
            ->label('')
            ->required()
            ->default('')
            ->maxLength(255)
            // Add specific validation for string comparison operators
            ->rule(function (string $operator) {
                if (in_array($operator, ['contains', 'starts_with', 'ends_with'])) {
                    // These operators need at least 2 characters typically
                    return 'min:2';
                }
                return null;
            });
    }

    public function form(Form $form): Form
    {
        return $form
            ->statePath('data')
            ->schema([
                TextInput::make('viewName')
                    ->label('')
                    ->required()
                    ->markAsRequired(false)
                    ->maxLength(50)
                    ->placeholder('Enter a name for this filter view')
                    ->extraInputAttributes(['class' => 'text-lg'])
                    ->live(),

                // Outer repeater for filter groups (AND/OR between groups)
                Repeater::make('filterGroups')
                    ->schema([
                        // Inner repeater for filter conditions within a group
                        Repeater::make('conditions')
                            ->label('')
                            ->extraAttributes(['class' => 'conditions-repeater p-0'])
                            ->schema([
                                Grid::make(4)
                                    ->extraAttributes(['class' => 'filter-conditions-grid items-center custom-filter-grid'])
                                    ->schema([
                                        // For the first item in a group, we don't show the AND/OR selector
                                        Select::make('conjunction')
                                            ->label('')
                                            ->options([
                                                'and' => 'And',
                                                'or' => 'Or',
                                            ])
                                            ->default('and')
                                            ->extraAttributes(['class' => 'conjunction-select'])
                                            ->hidden(fn($get, $record) => $record?->getIndex() === 0)
                                            ->columnSpan(1),

                                        Select::make('field')
                                            ->label('')
                                            ->options(fn () => $this->getFilterFieldOptions())
                                            ->required()
                                            ->markAsRequired(false)
                                            ->default(fn() => $this->getDefaultFieldForResource())
                                            ->live()
                                            ->afterStateUpdated(function ($state, callable $set) {
                                                // When field changes, update operator options
                                                $operators = $this->getOperatorOptionsForField($state);
                                                $defaultOperator = array_key_first($operators);
                                                $set('operator', $defaultOperator);

                                                // Also reset all value fields
                                                $set('text_value', '');
                                                $set('select_value', '');
                                                $set('date_value', null);
                                                $set('numeric_value', null);
                                                $set('boolean_value', false);
                                                $set('value_tags', []);
                                                $set('value_tags_select', []);
                                            })
                                            ->columnSpan(1),

                                        Select::make('operator')
                                            ->label('')
                                            ->options(function (callable $get) {
                                                $field = $get('field');
                                                return $this->getOperatorOptionsForField($field);
                                            })
                                            ->required()
                                            ->markAsRequired(false)
                                            ->default('=')
                                            ->live()
                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                // Reset all value fields when operator changes
                                                $set('text_value', '');
                                                $set('select_value', '');
                                                $set('date_value', null);
                                                $set('numeric_value', null);
                                                $set('boolean_value', false);
                                                $set('value_tags', []);
                                                $set('value_tags_select', []);

                                                // If the operator is null/not_null, we hide the value field
                                                // and set a placeholder value
                                                if (in_array($state, ['null', 'not_null'])) {
                                                    $set('text_value', '__NO_VALUE_NEEDED__');
                                                    $set('select_value', '__NO_VALUE_NEEDED__');
                                                    $set('numeric_value', '__NO_VALUE_NEEDED__');
                                                }
                                            })
                                            ->columnSpan(1),

                                        // Base value field (Select for dropdown options)
                                        Select::make('select_value')
                                            ->label('')
                                            ->options(function (callable $get) {
                                                $field = $get('field');
                                                $operator = $get('operator');
                                                return $this->getValueOptionsForField($field, $operator);
                                            })
                                            ->required()
                                            ->markAsRequired(false)
                                            ->default('')
                                            ->visible(function (callable $get) {
                                                $field = $get('field');
                                                $operator = $get('operator');

                                                if (in_array($operator, ['null', 'not_null'])) {
                                                    return false;
                                                }

                                                $filter = $this->getFilterByField($field);
                                                return $filter && $filter->type === 'select' && !in_array($operator, ['between', 'in', 'not_in']);
                                            })
                                            ->columnSpan(1),

                                        // Text input for string fields
                                        TextInput::make('text_value')
                                            ->label('')
                                            ->required()
                                            ->markAsRequired(false)
                                            ->default('')
                                            ->maxLength(255)
                                            ->visible(function (callable $get) {
                                                $field = $get('field');
                                                $operator = $get('operator');

                                                if (in_array($operator, ['null', 'not_null', 'between', 'in', 'not_in'])) {
                                                    return false;
                                                }

                                                $filter = $this->getFilterByField($field);
                                                return $filter && $filter->type === 'text';
                                            })
                                            ->extraAttributes(['class' => 'pa-text-input'])
                                            ->columnSpan(1),

                                        // Date picker for date fields
                                        DatePicker::make('date_value')
                                            ->label('')
                                            ->required()
                                            ->markAsRequired(false)
                                            ->visible(function (callable $get) {
                                                $field = $get('field');
                                                $operator = $get('operator');

                                                if (in_array($operator, ['null', 'not_null', 'between'])) {
                                                    return false;
                                                }

                                                $filter = $this->getFilterByField($field);
                                                return $filter && $filter->type === 'date';
                                            })
                                            ->columnSpan(1),

                                        // Number input for numeric fields
                                        TextInput::make('numeric_value')
                                            ->label('')
                                            ->numeric()
                                            ->required()
                                            ->markAsRequired(false)
                                            ->default('')
                                            ->visible(function (callable $get) {
                                                $field = $get('field');
                                                $operator = $get('operator');

                                                if (in_array($operator, ['null', 'not_null', 'between'])) {
                                                    return false;
                                                }

                                                $filter = $this->getFilterByField($field);
                                                return $filter && $filter->type === 'number';
                                            })
                                            ->extraAttributes(['class' => 'pa-text-input'])
                                            ->columnSpan(1),

                                        // Toggle for boolean fields
                                        Toggle::make('boolean_value')
                                            ->label('')
                                            ->onColor('success')
                                            ->offColor('danger')
                                            ->required()
                                            ->markAsRequired(false)
                                            ->visible(function (callable $get) {
                                                $field = $get('field');
                                                $operator = $get('operator');

                                                if (in_array($operator, ['null', 'not_null'])) {
                                                    return false;
                                                }

                                                $filter = $this->getFilterByField($field);
                                                return $filter && $filter->type === 'boolean' && in_array($operator, ['=', '!=']);
                                            })
                                            ->columnSpan(1),

                                        // Between fieldset for numeric fields
                                        Fieldset::make('between_number')
                                            ->label('')
                                            ->columns(2)
                                            ->schema([
                                                TextInput::make('min')
                                                    ->label('Min')
                                                    ->numeric()
                                                    ->required()
                                                    ->markAsRequired(false),
                                                TextInput::make('max')
                                                    ->label('Max')
                                                    ->numeric()
                                                    ->required()
                                                    ->rules([
                                                        fn (callable $get): string => 'gt:' . $get('min'),
                                                    ]),
                                            ])
                                            ->visible(function (callable $get) {
                                                $field = $get('field');
                                                $operator = $get('operator');

                                                $filter = $this->getFilterByField($field);
                                                return $filter && $filter->type === 'number' && $operator === 'between';
                                            })
                                            ->columnSpan(1),

                                        // Between fieldset for date fields
                                        Fieldset::make('between_date')
                                            ->label('')
                                            ->columns(2)
                                            ->schema([
                                                DatePicker::make('min')
                                                    ->label('Start Date')
                                                    ->required()
                                                    ->markAsRequired(false),
                                                DatePicker::make('max')
                                                    ->label('End Date')
                                                    ->required()
                                                    ->markAsRequired(false)
                                                    ->after('min'),
                                            ])
                                            ->visible(function (callable $get) {
                                                $field = $get('field');
                                                $operator = $get('operator');

                                                $filter = $this->getFilterByField($field);
                                                return $filter && $filter->type === 'date' && $operator === 'between';
                                            })
                                            ->columnSpan(1),

                                        // Between fieldset for text fields
                                        Fieldset::make('between_text')
                                            ->label('')
                                            ->columns(2)
                                            ->schema([
                                                TextInput::make('min')
                                                    ->label('Min')
                                                    ->required()
                                                    ->markAsRequired(false)
                                                    ->maxLength(255),
                                                TextInput::make('max')
                                                    ->label('Max')
                                                    ->required()
                                                    ->markAsRequired(false)
                                                    ->maxLength(255),
                                            ])
                                            ->visible(function (callable $get) {
                                                $field = $get('field');
                                                $operator = $get('operator');

                                                $filter = $this->getFilterByField($field);
                                                return $filter && $filter->type === 'text' && $operator === 'between';
                                            })
                                            ->columnSpan(1),

                                        // Select for 'in' and 'not_in' operators with text/number fields
                                        Select::make('value_tags')
                                            ->label('')
                                            ->placeholder('Enter values')
                                            ->multiple()
                                            ->searchable()
                                            ->required()
                                            ->markAsRequired(false)
                                            ->visible(function (callable $get) {
                                                $field = $get('field');
                                                $operator = $get('operator');

                                                $filter = $this->getFilterByField($field);
                                                return $filter && in_array($operator, ['in', 'not_in']) &&
                                                    in_array($filter->type, ['text', 'number']);
                                            })
                                            ->columnSpan(1),

                                        // Select for 'in' and 'not_in' operators with select fields
                                        Select::make('value_tags_select')
                                            ->label('')
                                            ->placeholder('Select values')
                                            ->multiple()
                                            ->searchable()
                                            ->options(function (callable $get) {
                                                $field = $get('field');
                                                $filter = $this->getFilterByField($field);

                                                if ($filter && $filter->type === 'select') {
                                                    return $filter->getOptions();
                                                }

                                                return [];
                                            })
                                            ->required()
                                            ->markAsRequired(false)
                                            ->visible(function (callable $get) {
                                                $field = $get('field');
                                                $operator = $get('operator');

                                                $filter = $this->getFilterByField($field);
                                                return $filter && in_array($operator, ['in', 'not_in']) &&
                                                    $filter->type === 'select';
                                            })
                                            ->columnSpan(1),
                                    ])
                            ])
                            ->defaultItems(1)
                            ->itemLabel(function (array $state): ?string {
                                // Don't show any label for items
                                return null;
                            })
                            ->addActionLabel('Add a Filter')
                            ->addAction(function (\Filament\Forms\Components\Actions\Action $action) {
                                return $action
                                    ->button()
                                    ->outlined()
                                    ->color('gray')
                                    ->size('sm')
                                    ->label('Add a Filter');
                            })
                            ->columns(1)
                            ->columnSpanFull()
                            ->reorderable(false)
                            ->deletable(true)
                            ->deleteAction(
                                fn (\Filament\Forms\Components\Actions\Action $action) => $action
                                    ->icon('heroicon-o-x-circle')
                                    ->iconButton()
                                    ->color('gray')
                                    ->extraAttributes(['class' => 'delete-action-circle ml-auto'])
                            ),

                        // Add a select for the between-group operator (AND/OR)
                        Select::make('groupConjunction')
                            ->label('')
                            ->options([
                                'and' => 'And',
                                'or' => 'Or',
                            ])
                            ->default('or')
                            ->extraAttributes([
                                'class' => 'mt-4 mb-4 conjunction-select w-full max-w-[88px]'
                            ]),
                    ])
                    ->itemLabel(function (array $state): ?string {
                        // Don't show any label for filter groups
                        return null;
                    })
                    ->addActionLabel('Add a Filter Group')
                    ->addAction(
                        fn(\Filament\Forms\Components\Actions\Action $action) => $action
                            ->button()
                            ->outlined()
                            ->extraAttributes(['class' => 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'])
                    )
                    ->defaultItems(1)
                    ->columns(1)
                    ->reorderable(false)
                    ->collapsible(false)
                    ->collapsed(false)
                    // Add inline class directly to the repeater items
                    ->extraAttributes([
                        'class' => 'filter-groups-repeater p-0 border-none',
                        'style' => '--c-background: #f9fafb; --c-border: transparent;'
                    ]),
            ]);
    }

    /**
     * Set the mode of the filter form (create or edit)
     */
    public function setMode(bool $isEdit, ?string $viewId = null, string $resourceType = 'users'): void
    {

        $this->isEdit = $isEdit;
        $this->viewId = $viewId;
        $this->resourceType = $resourceType;

        // Reset filter registry cache since resource type might have changed
        $this->filterRegistry = null;

        if (!$isEdit || !$viewId) {
            $this->resetForm();
            return;
        }

        $filterView = FilterView::find($viewId);
        if (!$filterView) {
            $this->resetForm();
            return;
        }

        $this->loadFilterViewData($filterView);
    }

    /**
     * Load filter view data into the form
     */
    protected function loadFilterViewData(FilterView $filterView): void
    {
        $filterDefinition = $filterView->filter_definition;

        if (isset($filterDefinition['conditions'])) {
            $this->loadFlatFilterStructure($filterView, $filterDefinition);
            return;
        }

        $this->loadNestedFilterStructure($filterView, $filterDefinition);
    }

    /**
     * Load a filter with flat structure (single group of conditions)
     */
    protected function loadFlatFilterStructure(FilterView $filterView, array $filterDefinition): void
    {
        $formattedConditions = $this->transformFilterDefinitionToFormData($filterDefinition);

        $filterGroups = [
            [
                'conditions' => $formattedConditions,
                'groupConjunction' => 'or',
            ]
        ];

        $this->form->fill([
            'viewName' => $filterView->name,
            'filterGroups' => $filterGroups,
        ]);
    }

    /**
     * Load a filter with nested structure (multiple groups of conditions)
     */
    protected function loadNestedFilterStructure(FilterView $filterView, array $filterDefinition): void
    {
        if (!$this->isNestedStructure($filterDefinition)) {
            $this->resetForm();
            return;
        }

        $filterGroups = [];
        $groupConjunctions = $filterDefinition['group_conjunctions'] ?? [];

        $counter = 0;
        foreach ($filterDefinition as $key => $group) {
            if (is_numeric($key) && isset($group['conditions'])) {
                $formattedConditions = $this->transformFilterDefinitionToFormData($group);

                // Get the correct conjunction for this group
                // The conjunction is between this group and the next group
                // So use the conjunction at position 'counter'
                $groupConjunction = $groupConjunctions[$counter] ?? 'or';
                if ($counter === count($groupConjunctions)) {
                    // Last group doesn't need a conjunction since there's no next group
                    $groupConjunction = 'or'; // Default for the last group
                }

                $filterGroups[] = [
                    'conditions' => $formattedConditions,
                    'groupConjunction' => $groupConjunction,
                ];

                $counter++;
            }
        }

        $this->form->fill([
            'viewName' => $filterView->name,
            'filterGroups' => $filterGroups,
        ]);
    }

    /**
     * Check if the filter definition has a nested structure with multiple groups
     */
    protected function isNestedStructure(array $filterDefinition): bool
    {
        foreach ($filterDefinition as $key => $value) {
            if (is_numeric($key) && isset($value['conditions'])) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get the transformer instance, creating it if it doesn't exist
     */
    protected function getTransformer(): FilterDefinitionTransformer
    {
        if ($this->transformer === null) {
            $this->transformer = new FilterDefinitionTransformer();
        }
        return $this->transformer;
    }

    /**
     * Get the validator instance, creating it if it doesn't exist
     */
    protected function getValidator(): FilterDefinitionValidator
    {
        if ($this->validator === null) {
            $this->validator = new FilterDefinitionValidator();
        }
        return $this->validator;
    }

    /**
     * Transform a filter definition to form data format
     */
    protected function transformFilterDefinitionToFormData(array $filterDefinition): array
    {
        // Create a temporary filter definition object to use with the transformer
        $definition = FilterDefinition::fromArray($this->resourceType, $filterDefinition);

        // Use the transformer to convert the filter definition to form data
        $formData = $this->getTransformer()->toFormData($definition);

        return $formData['filterGroups'][0]['conditions'] ?? [];
    }

    /**
     * Reset the form to default values
     */
    private function resetForm(): void
    {
        $this->form->fill([
            'viewName' => '',
            'filterGroups' => [
                [
                    'conditions' => [
                        [
                            'field' => $this->getDefaultFieldForResource(),
                            'operator' => '=',
                            'text_value' => '',
                        ],
                    ],
                    'groupConjunction' => 'or',
                ],
            ],
        ]);
    }

    /**
     * Save the filter view
     */
    public function save(): void
    {
        // Initialize variables with default values to avoid undefined variable errors in catch block
        $data = [];
        $filterDefinition = null; // Will be properly initialized in the try block
        $savedFilterId = null;

        try {
            // Get form data
            $data = $this->form->getState();

            // Validate view name (add explicit validation here)
            $this->validateOnly('data.viewName', [
                'data.viewName' => 'required|string|max:50',
            ]);

            // Validate that the filter has at least one condition
            if (empty($data['filterGroups']) || empty($data['filterGroups'][0]['conditions'])) {
                $this->addError('data.filterGroups', 'Filter must have at least one condition');
                Notification::make()
                    ->danger()
                    ->title('Filter must have at least one condition')
                    ->send();
                return;
            }

            // Use our transformer to create the filter definition
            $filterDefinition = $this->getTransformer()->fromFormData([
                'filterGroups' => $data['filterGroups']
            ], $this->resourceType);

            $validationResult = $this->getValidator()->validate($filterDefinition);
            if (!$validationResult->isValid()) {
                $this->addError('filter_definition', $validationResult->getMessage());
                Notification::make()
                    ->danger()
                    ->title('Validation failed')
                    ->body($validationResult->getMessage())
                    ->send();
                return;
            }

            // Prepare the filter definition for storage
            $storageDefinition = $this->prepareFilterDefinitionForStorage($filterDefinition);

            // Save or update the filter view
            if ($this->isEdit && $this->viewId) {
                // Update existing
                $filterView = FilterView::find($this->viewId);

                if (!$filterView) {
                    Notification::make()
                        ->danger()
                        ->title('Filter view not found')
                        ->send();
                    return;
                }

                // Update the existing filter
                $filterView->update([
                    'name' => $data['viewName'],
                    'filter_definition' => $storageDefinition,
                ]);

                $savedFilterId = $filterView->id;

                Notification::make()
                    ->success()
                    ->title('Filter view updated successfully')
                    ->send();
            } else {
                // Create new
                $filterView = FilterView::create([
                    'name' => $data['viewName'],
                    'resource_type' => $this->resourceType,
                    'filter_definition' => $storageDefinition,
                    'user_id' => Auth::id(),
                ]);

                $savedFilterId = $filterView->id;

                Notification::make()
                    ->success()
                    ->title('Filter view created successfully')
                    ->send();
            }

            // Dispatch an event with the filter ID after save
            $this->dispatch('filter-view-saved', filterId: $savedFilterId);

            // Close the modal after creating a new filter
            $this->dispatch('close-modal', id: 'manage-filter-view');

            // Reset form if needed
            if ($this->isEdit) {
                // Stay in edit mode, but refresh the form with latest data
                $this->setMode(true, $savedFilterId, $this->resourceType);
            } else {
                // Reset to create mode
                $this->isEdit = false;
                $this->viewId = null;
                $this->resetForm();
            }
        } catch (\Exception $e) {
            // Show error notification
            Notification::make()
                ->danger()
                ->title('Error saving filter')
                ->body('An unexpected error occurred: ' . $e->getMessage())
                ->send();

            // Log the error
            \Illuminate\Support\Facades\Log::error('Error saving filter view: ' . $e->getMessage(), [
                'exception' => $e,
                'data' => $data ?? [],
                'filter_definition' => $filterDefinition instanceof FilterDefinition ? $filterDefinition->toArray() : null,
                'isEdit' => $this->isEdit,
                'viewId' => $this->viewId,
            ]);
        }
    }

    /**
     * Prepare filter definition for storage, ensuring proper format for multiple groups
     */
    protected function prepareFilterDefinitionForStorage(FilterDefinition $filterDefinition): array
    {
        $data = $filterDefinition->toArray();

        // If this is a simple structure (single group with 'conditions'), convert to a numerically indexed structure
        if (isset($data['conditions'])) {
            $groups = [
                '0' => [
                    'conditions' => $data['conditions'],
                    'conjunction' => $data['conjunction'] ?? 'and'
                ]
            ];

            return $groups;
        }

        // For multiple groups, ensure consistent numeric keys
        $groups = [];
        $groupConjunctions = $data['group_conjunctions'] ?? [];

        // Remove group_conjunctions if present to rebuild the array
        if (isset($data['group_conjunctions'])) {
            unset($data['group_conjunctions']);
        }

        // Add explicit numeric keys to groups
        $counter = 0;
        foreach ($data as $key => $group) {
            if (isset($group['conditions'])) {
                $groups[(string)$counter] = $group;  // Force string keys like "0", "1"
                $counter++;
            }
        }

        // Add back group_conjunctions if they exist
        if (!empty($groupConjunctions)) {
            $groups['group_conjunctions'] = $groupConjunctions;
        }

        return $groups;
    }

    /**
     * Validate filter data before saving
     */
    protected function validateFilterData(array $data): array
    {
        // This method is being replaced by FilterDefinitionValidator
        // Keep it for backward compatibility
        return ['valid' => true, 'message' => ''];
    }

    /**
     * Transforms form data into filter definition structure
     */
    private function transformFormDataToFilterDefinition(array $filterGroups): array
    {
        if (empty($filterGroups)) {
            return [];
        }

        // Use the transformer to convert form data to a filter definition
        $filterDefinition = $this->getTransformer()->fromFormData([
            'filterGroups' => $filterGroups
        ], $this->resourceType);

        // Convert the filter definition object to an array
        return $filterDefinition->toArray();
    }

    /**
     * Create a nested filter structure with separate groups
     */
    private function createNestedFilterStructure(array $filterGroups): array
    {
        // This method is being replaced by FilterDefinition domain model
        // Keep it for backward compatibility, but it should not be called
        return $this->transformFormDataToFilterDefinition($filterGroups);
    }

    /**
     * Create a flat filter structure for simple filters (single group)
     */
    private function createFlatFilterStructure(array $filterGroups): array
    {
        // This method is being replaced by FilterDefinition domain model
        // Keep it for backward compatibility, but it should not be called
        return $this->transformFormDataToFilterDefinition($filterGroups);
    }

    /**
     * Prepare a single condition's data structure
     */
    private function prepareConditionData(array $condition, int $index): ?array
    {
        // This method is being replaced by FilterDefinitionTransformer
        // Keep it for backward compatibility, but it should not be called
        $conditionData = [
            'field' => $condition['field'],
            'operator' => $condition['operator'],
        ];

        // Add conjunction if not the first condition
        if ($index > 0) {
            $conditionData['conjunction'] = $condition['conjunction'] ?? 'and';
        }

        // ... rest of the method unchanged for backward compatibility ...

        return $conditionData;
    }

    /**
     * Prepare a new item in the repeater with default values
     */
    public function prepareCreateState(array $state, string $repeaterStatePath): array
    {
        // Check if this is a conditions repeater
        if (str_contains($repeaterStatePath, 'conditions')) {
            $state['field'] = $this->getDefaultFieldForResource();
            $state['operator'] = '=';
            $state['text_value'] = '';
        }

        return $state;
    }

    /**
     * Delete the current filter view
     */
    public function delete(): void
    {
        // Check if we're in edit mode with a valid ID
        if (!$this->isEdit || !$this->viewId) {
            Notification::make()
                ->danger()
                ->title('Cannot delete filter view')
                ->body('No filter view selected for deletion')
                ->send();
            return;
        }

        // Find the filter view
        $filterView = FilterView::find($this->viewId);

        if (!$filterView) {
            Notification::make()
                ->danger()
                ->title('Filter view not found')
                ->send();
            return;
        }

        // Check permissions - only the owner can delete (or admin in the future)
        if ($filterView->user_id !== Auth::id()) {
            Notification::make()
                ->danger()
                ->title('Permission denied')
                ->body('You do not have permission to delete this filter view')
                ->send();
            return;
        }

        try {
            // Delete the filter view
            $filterView->delete();

            // Show success notification
            Notification::make()
                ->success()
                ->title('Filter view deleted successfully')
                ->send();

            // Close the modal
            $this->dispatch('close-modal', id: 'manage-filter-view');
        } catch (\Exception $e) {
            // Show error notification
            Notification::make()
                ->danger()
                ->title('Error deleting filter view')
                ->body($e->getMessage())
                ->send();

            // Log the error
            logger('Error deleting filter view: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString(),
                'filter_view_id' => $this->viewId
            ]);
        }
    }

    /**
     * Show confirmation dialog before deleting
     */
    public function confirmDelete(): void
    {
        // Only proceed if confirmDelete is true
        if (!$this->confirmDelete) {
            return;
        }

        // Perform the actual deletion
        $this->delete();

        // Reset confirmation flag
        $this->confirmDelete = false;
    }

    public function render(): View
    {
        return view('livewire.shared.dynamic-filters.filter-form');
    }
}
