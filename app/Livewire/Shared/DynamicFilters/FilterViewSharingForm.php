<?php

namespace App\Livewire\Shared\DynamicFilters;

use App\Models\FilterView;
use App\Models\Group;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Log;
use Livewire\Component;

class FilterViewSharingForm extends Component implements HasForms
{
    use InteractsWithForms;

    public ?FilterView $filterView = null;
    public ?string $currentFilterViewId = null;
    public array $data = [];
    public bool $isGroupSharingEnabled = false;
    public bool $isEditMode = false;
    public bool $hasUnsavedChanges = false;

    protected $listeners = [
        'set-filter-form-mode' => 'handleFilterFormMode',
        'filter-view-saved' => 'handleFilterViewSaved',
        'filter-manager-save' => 'updateFilterViewSharing',
    ];

    public function mount(): void
    {
        $this->initForm();
    }

    protected function initForm(): void
    {
        $this->form->fill([
            'is_public' => false,
            'enable_group_sharing' => false,
            'shared_groups' => [],
        ]);
        $this->hasUnsavedChanges = false;
    }

    public function handleFilterFormMode(bool $isEdit, ?string $viewId = null, ?string $resourceType = null): void
    {
        $this->isEditMode = $isEdit;
        $this->currentFilterViewId = $viewId;
        $this->hasUnsavedChanges = false;

        if ($isEdit && $viewId) {
            $this->loadFilterView($viewId);
        } else {
            // New filter view, reset form
            $this->filterView = null;
            $this->initForm();
            $this->isGroupSharingEnabled = false;
        }
    }

    public function handleFilterViewSaved(?string $filterId = null): void
    {
        if ($filterId) {
            $this->isEditMode = true;
            $this->loadFilterView($filterId);
        }
    }

    protected function loadFilterView(string $viewId): void
    {
        // Don't proceed if not in edit mode
        if (!$this->isEditMode) {
            return;
        }

        try {
            $this->filterView = FilterView::find($viewId);

            if (!$this->filterView) {
                $this->initForm();
                $this->isGroupSharingEnabled = false;
                return;
            }

            // Check if the sharedGroups relationship exists first
            if (!method_exists($this->filterView, 'sharedGroups')) {
                Log::error('sharedGroups relationship not found on FilterView model');
                $this->isGroupSharingEnabled = false;
                $sharedGroups = [];
            } else {
                // Load the shared groups for this filter view
                $sharedGroups = $this->filterView->sharedGroups;
                $this->isGroupSharingEnabled = $sharedGroups->isNotEmpty();
            }

            // Update the form state
            $this->form->fill([
                'is_public' => $this->filterView->is_public,
                'enable_group_sharing' => $this->isGroupSharingEnabled,
                'shared_groups' => $this->isGroupSharingEnabled ? $sharedGroups->pluck('id')->toArray() : [],
            ]);

            $this->hasUnsavedChanges = false;
        } catch (\Exception $e) {
            Log::error('Error loading filter view', [
                'viewId' => $viewId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $this->initForm();
            $this->isGroupSharingEnabled = false;
        }
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make()
                    ->schema([
                        Checkbox::make('is_public')
                            ->label('Make View visible to all other users')
                            ->live(onBlur: false)
                            ->afterStateUpdated(function ($state) {
                                // Get current form state
                                $currentState = $this->form->getState();

                                // Prepare new state with updated values
                                $newState = [
                                    'is_public' => $state,
                                    'enable_group_sharing' => $state ? false : $currentState['enable_group_sharing'],
                                    'shared_groups' => $state ? [] : ($currentState['shared_groups'] ?? []),
                                ];

                                // Update form with complete state
                                $this->form->fill($newState);

                                // Update component state
                                if ($state) {
                                    $this->isGroupSharingEnabled = false;
                                }

                                $this->hasUnsavedChanges = true;
                            }),

                        Checkbox::make('enable_group_sharing')
                            ->label('Make View visible to specific user groups')
                            ->live(onBlur: false)
                            ->disabled(fn ($get) => $get('is_public'))
                            ->afterStateUpdated(function ($state) {
                                // Get current form state
                                $currentState = $this->form->getState();

                                // Prepare updated state
                                $newState = [
                                    'is_public' => $currentState['is_public'],
                                    'enable_group_sharing' => $state,
                                    'shared_groups' => $state ? ($currentState['shared_groups'] ?? []) : [],
                                ];

                                // Update form with complete state
                                $this->form->fill($newState);

                                // Update component state
                                $this->isGroupSharingEnabled = $state;
                                $this->hasUnsavedChanges = true;
                            }),

                        Select::make('shared_groups')
                            ->label('Select User Groups')
                            ->multiple()
                            ->searchable()
                            ->preload()
                            ->options(function () {
                                try {
                                    return Group::query()->orderBy('name')->pluck('name', 'id')->toArray();
                                } catch (\Exception $e) {
                                    Log::error('Error loading groups', ['error' => $e->getMessage()]);
                                    return [];
                                }
                            })
                            ->visible(fn () => $this->isGroupSharingEnabled)
                            ->disabled(fn ($get) => $get('is_public'))
                            ->live(onBlur: false)
                            ->afterStateUpdated(function ($state) {
                                $this->hasUnsavedChanges = true;
                            }),
                    ])
            ])
            ->statePath('data');
    }

    /**
     * Ensure state is preserved when component is dehydrated
     */
    public function dehydrate(): void
    {
        // Ensure the form state is synced with component state
        $formState = $this->form->getState();

        // Update the local component properties to match form state
        if (isset($formState['is_public'])) {
            $this->isGroupSharingEnabled = !$formState['is_public'] && ($formState['enable_group_sharing'] ?? false);
        }
    }

    public function updateFilterViewSharing(): void
    {
        // Don't update if not in edit mode or no filter view is selected
        if (!$this->isEditMode || !$this->filterView || !$this->filterView->exists) {
            return;
        }

        // Don't update if there are no unsaved changes
        if (!$this->hasUnsavedChanges) {
            return;
        }

        try {
            $data = $this->form->getState();

            // Update is_public status
            $this->filterView->update(['is_public' => $data['is_public']]);

            // Only proceed if the sharedGroups relationship exists
            if (!method_exists($this->filterView, 'sharedGroups')) {
                Log::error('Cannot update sharing - sharedGroups relationship not found');
                Notification::make()
                    ->danger()
                    ->title('Error updating sharing settings')
                    ->body('The filter view sharing feature is not properly set up.')
                    ->send();
                return;
            }

            // Update group sharing
            if (!empty($data['enable_group_sharing']) && !$data['is_public']) {
                $groupIds = $data['shared_groups'] ?? [];
                $this->filterView->sharedGroups()->sync($groupIds);
            } else {
                $this->filterView->sharedGroups()->detach();
            }

            Notification::make()
                ->success()
                ->title('Sharing settings updated')
                ->send();

            $this->hasUnsavedChanges = false;
        } catch (\Exception $e) {
            Log::error('Error updating filter view sharing', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            Notification::make()
                ->danger()
                ->title('Error updating sharing settings')
                ->body('There was a problem updating the sharing settings.')
                ->send();
        }
    }

    public function render(): View
    {
        return view('livewire.shared.dynamic-filters.filter-view-sharing-form');
    }
}
