<?php

namespace App\Livewire\Shared;

use App\Models\Contact;
use App\Models\Tag;
use App\Models\User;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Illuminate\Database\Eloquent\Model;
use Livewire\Component;

class NotesAndTagsManager extends Component implements HasForms
{
    use InteractsWithForms;

    public ?Model $record = null;
    public ?array $data = [];

    public function mount(Model $record): void
    {
        $this->record = $record;

        // Store initial data in the component
        $this->data = [
            'notes' => $record->notes ?? '',
            'tags' => $record->tags?->pluck('id')->toArray() ?? [],
        ];

        // Fill the form with the initial data
        $this->form->fill($this->data);
    }

    public function form(Form $form): Form
    {
        return $form
            ->model($this->record)
            ->schema([
                Grid::make(2)->columns(2)->schema([
                    Textarea::make('notes')
                        ->label('Notes')
                        ->placeholder('Add notes about this record...')
                        ->rows(4)
                        ->reactive()
                        ->live(onBlur: true)
                        ->afterStateUpdated(function ($state) {
                            // Store state locally in addition to saving to database
                            $this->data['notes'] = $state;

                            // Update the record
                            $this->record->update(['notes' => $state]);
                        }),
                    Select::make('tags')
                        ->label('Tags')
                        ->multiple()
                        ->searchable()
                        ->relationship('tags', 'name')
                        ->allowHtml()
                        ->getSearchResultsUsing(function (string $search) {
                            $search = trim(strtolower($search));

                            // Get existing tags that match the search
                            $tags = Tag::query()
                                ->where('name', 'ilike', "%{$search}%")
                                ->limit(50)
                                ->get();

                            $results = $tags->pluck('name', 'id')->toArray();

                            // Only add create option if search is not empty and no exact match exists
                            if ($search !== '' && !$tags->contains('name', $search)) {
                                $results["new-tag:$search"] = "<span class='text-brand-blue'>Create \"" . e($search) . "\"</span>";
                            }

                            return $results;
                        })
                        ->formatStateUsing(function ($state) {
                            // Don't display new-tag options in the selected state
                            if (is_string($state) && str_starts_with($state, 'new-tag:')) {
                                return '';
                            }
                            return $state;
                        })
                        ->live()
                        ->afterStateUpdated(function (Select $selectComponent, array $state) {
                            if (!is_array($state)) {
                                return;
                            }

                            $newTagPrefix = 'new-tag:';

                            $tags = collect($state)->map(function (string $id) use ($newTagPrefix) {
                                if (str_starts_with($id, $newTagPrefix)) {
                                    $tagName = str_replace($newTagPrefix, '', $id);
                                    $tag = Tag::query()->firstOrCreate(['name' => $tagName]);
                                    return $tag->id;
                                }

                                return (int) $id;
                            });

                            $this->record->tags()->sync($tags);

                            // Force update the component's state with the real tag IDs
                            // but preserve existing data from other fields
                            $selectComponent->state($tags->toArray());

                            // Get current form data to preserve it
                            $currentData = $this->data;

                            // Update only the tags field, keeping all other fields
                            $currentData['tags'] = $tags->toArray();

                            // Fill the form with the updated data
                            $this->form->fill($currentData);
                        }),
                ])
            ])
            ->statePath('data');
    }

    public function render()
    {
        return view('livewire.shared.notes-and-tags-manager');
    }
}
