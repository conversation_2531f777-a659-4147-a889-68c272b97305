<?php

namespace App\Validators;

use App\Data\Sponsor\CreateOpportunityRequest;
use App\Models\User;
use Illuminate\Auth\Access\AuthorizationException;

class CreateOpportunityRequestValidator
{
    public function __construct(
        private readonly User $user,
    ) {}

    /**
     * Validate that the user has access to the organization in the request
     *
     * @param CreateOpportunityRequest $request
     * @throws AuthorizationException
     */
    public function validate(CreateOpportunityRequest $request): void
    {
        $organizationId = $request->organizationId;

        $hasAccess = $this->user->organizations()
            ->where('organization_id', $organizationId)
            ->wherePivotNull('deactivated_at')
            ->exists();

        if (!$hasAccess) {
            throw new AuthorizationException("You don't have access to create opportunities for this organization.");
        }
    }
}
