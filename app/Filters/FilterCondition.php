<?php

namespace App\Filters;

use Illuminate\Support\Facades\Log;

/**
 * Represents a single filter condition in a filter definition
 */
class FilterCondition
{
    /**
     * The field to filter on
     */
    private string $field;

    /**
     * The operator to apply
     */
    private string $operator;

    /**
     * The value to filter by (can be various types)
     */
    private mixed $value;

    /**
     * The conjunction with the previous condition (and/or)
     */
    private ?string $conjunction = null;

    /**
     * Create a new filter condition
     */
    public function __construct(string $field, string $operator, mixed $value, ?string $conjunction = null)
    {
        $this->field = $field;
        $this->operator = $operator;
        $this->value = $value;
        $this->conjunction = $conjunction;
    }

    /**
     * Get the field name
     */
    public function getField(): string
    {
        return $this->field;
    }

    /**
     * Get the operator
     */
    public function getOperator(): string
    {
        return $this->operator;
    }

    /**
     * Get the filter value
     */
    public function getValue(): mixed
    {
        return $this->value;
    }

    /**
     * Get the conjunction (and/or)
     */
    public function getConjunction(): ?string
    {
        return $this->conjunction;
    }

    /**
     * Set the conjunction
     */
    public function setConjunction(?string $conjunction): self
    {
        $this->conjunction = $conjunction;
        return $this;
    }

    /**
     * Convert to an array representation
     */
    public function toArray(): array
    {
        $result = [
            'field' => $this->field,
            'operator' => $this->operator,
            'value' => $this->value,
        ];

        if ($this->conjunction !== null) {
            $result['conjunction'] = $this->conjunction;
        }

        return $result;
    }

    /**
     * Create from an array representation
     */
    public static function fromArray(array $data): self
    {
        $field = $data['field'] ?? '';
        $operator = $data['operator'] ?? '=';
        $value = $data['value'] ?? null;
        $conjunction = $data['conjunction'] ?? null;

        $condition = new self($field, $operator, $value, $conjunction);

        return $condition;
    }

    /**
     * Check if the condition is a null/not_null operator that doesn't need a value
     */
    public function isNullOperator(): bool
    {
        return in_array($this->operator, ['null', 'not_null']);
    }

    /**
     * Check if the condition uses a between operator
     */
    public function isBetweenOperator(): bool
    {
        return $this->operator === 'between';
    }

    /**
     * Check if the condition uses an in/not_in operator
     */
    public function isInOperator(): bool
    {
        return in_array($this->operator, ['in', 'not_in']);
    }
}
