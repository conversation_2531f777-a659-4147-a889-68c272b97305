<?php

namespace App\Filters\Validators;

use App\Filters\FilterCondition;
use App\Filters\FilterDefinition;
use App\Filters\FilterGroup;

/**
 * Validation result class for better type safety
 */
class ValidationResult
{
    private bool $valid;
    private string $message;

    public function __construct(bool $valid, string $message = '')
    {
        $this->valid = $valid;
        $this->message = $message;
    }

    public function isValid(): bool
    {
        return $this->valid;
    }

    public function getMessage(): string
    {
        return $this->message;
    }
}

/**
 * Validates filter definitions for correctness
 */
class FilterDefinitionValidator
{
    /**
     * Validate a filter definition
     */
    public function validate(FilterDefinition $filterDefinition): ValidationResult
    {
        // Validate that the filter has at least one group with conditions
        if ($filterDefinition->countGroups() === 0) {
            return new ValidationResult(false, 'Filter must have at least one group');
        }

        // Validate all groups and their conditions
        foreach ($filterDefinition->getGroups() as $groupIndex => $group) {
            // Validate that the group has conditions
            if ($group->count() === 0) {
                return new ValidationResult(false, 'Filter group must have at least one condition');
            }

            // Validate each condition in the group
            foreach ($group->getConditions() as $conditionIndex => $condition) {
                $validationResult = $this->validateCondition($condition, $conditionIndex);
                if (!$validationResult->isValid()) {
                    return $validationResult;
                }
            }
        }

        return new ValidationResult(true);
    }

    /**
     * Validate a single condition
     */
    private function validateCondition(FilterCondition $condition, int $index): ValidationResult
    {
        // Validate required fields
        if (empty($condition->getField())) {
            return new ValidationResult(false, 'Field is required for all conditions');
        }

        if (empty($condition->getOperator())) {
            return new ValidationResult(false, 'Operator is required for all conditions');
        }

        // Get field type to know which value field to validate
        $filterType = $this->determineFilterType($condition->getField());
        if (!$filterType) {
            return new ValidationResult(false, "Invalid field: {$condition->getField()}");
        }

        // Skip validation for null/not_null operators
        if ($condition->isNullOperator()) {
            return new ValidationResult(true);
        }

        // For between operators, check range values
        if ($condition->isBetweenOperator()) {
            if (!is_array($condition->getValue()) ||
                !isset($condition->getValue()['min']) ||
                !isset($condition->getValue()['max'])) {
                return new ValidationResult(false, "Both min and max values are required for {$condition->getField()}");
            }

            $min = $condition->getValue()['min'];
            $max = $condition->getValue()['max'];

            // Type-specific validation for ranges
            switch ($filterType) {
                case 'number':
                    if (!is_numeric($min) || !is_numeric($max)) {
                        return new ValidationResult(false, "Numeric values required for {$condition->getField()}");
                    }

                    if ((float)$min >= (float)$max) {
                        return new ValidationResult(false, "Min value must be less than max value for {$condition->getField()}");
                    }
                    break;

                case 'date':
                    try {
                        $startDate = new \DateTime($min);
                        $endDate = new \DateTime($max);

                        if ($startDate > $endDate) {
                            return new ValidationResult(false, "Start date must be before end date for {$condition->getField()}");
                        }
                    } catch (\Exception $e) {
                        return new ValidationResult(false, "Invalid date format for {$condition->getField()}");
                    }
                    break;
            }
        }
        // For in/not_in operators, check array values
        else if ($condition->isInOperator()) {
            if (!is_array($condition->getValue()) || empty($condition->getValue())) {
                return new ValidationResult(false, "At least one value is required for {$condition->getField()} with {$condition->getOperator()} operator");
            }
        }
        // For other operators, check the value
        else {
            $value = $condition->getValue();

            // Validate based on field type
            switch ($filterType) {
                case 'number':
                    if (!is_numeric($value) && $value !== '0' && $value !== 0) {
                        return new ValidationResult(false, "Numeric value required for {$condition->getField()}");
                    }
                    break;

                case 'date':
                    if (empty($value)) {
                        return new ValidationResult(false, "Date is required for {$condition->getField()}");
                    }

                    try {
                        new \DateTime($value);
                    } catch (\Exception $e) {
                        return new ValidationResult(false, "Invalid date format for {$condition->getField()}");
                    }
                    break;

                default:
                    if (empty($value) && $value !== '0' && $value !== 0) {
                        return new ValidationResult(false, "Value is required for {$condition->getField()} with {$condition->getOperator()} operator");
                    }
                    break;
            }
        }

        return new ValidationResult(true);
    }

    /**
     * Determine the filter type based on the field name
     *
     * Note: This is a temporary implementation. In the next refactoring step,
     * we will move this to the FieldType system.
     */
    private function determineFilterType(string $field): ?string
    {
        // This is a simplified version of the field type detection
        // Later we will replace this with proper injection of field type registry
        $fieldTypeMap = [
            'first_name' => 'text',
            'last_name' => 'text',
            'email' => 'text',
            'graduation_year' => 'number',
            'date_of_birth' => 'date',
            'active' => 'boolean',
            // Add more field mappings here
        ];

        return $fieldTypeMap[$field] ?? 'text';
    }
}
