<?php

namespace App\Filters;

use Illuminate\Support\Facades\Log;

/**
 * Represents a group of filter conditions
 */
class FilterGroup
{
    /**
     * Conditions within this group
     *
     * @var FilterCondition[]
     */
    private array $conditions = [];

    /**
     * Conjunction used within the group (and/or)
     */
    private string $conjunction;

    /**
     * Create a new filter group
     */
    public function __construct(array $conditions = [], string $conjunction = 'and')
    {
        $this->conjunction = $conjunction;

        foreach ($conditions as $condition) {
            $this->addCondition($condition);
        }
    }

    /**
     * Add a condition to the group
     */
    public function addCondition(FilterCondition $condition): self
    {
        $this->conditions[] = $condition;
        return $this;
    }

    /**
     * Get all conditions in the group
     *
     * @return FilterCondition[]
     */
    public function getConditions(): array
    {
        return $this->conditions;
    }

    /**
     * Set the conjunction for this group
     */
    public function setConjunction(string $conjunction): self
    {
        $this->conjunction = $conjunction;
        return $this;
    }

    /**
     * Get the conjunction for this group
     */
    public function getConjunction(): string
    {
        return $this->conjunction;
    }

    /**
     * Convert to an array representation
     */
    public function toArray(): array
    {
        $conditions = [];

        foreach ($this->conditions as $index => $condition) {
            $conditionArray = $condition->toArray();
            $conditions[] = $conditionArray;
        }

        $result = [
            'conditions' => $conditions,
            'conjunction' => $this->conjunction,
        ];

        return $result;
    }

    /**
     * Create from an array representation
     */
    public static function fromArray(array $data): self
    {
        $conditions = [];
        $conjunction = $data['conjunction'] ?? 'and';

        if (isset($data['conditions']) && is_array($data['conditions'])) {
            foreach ($data['conditions'] as $index => $conditionData) {
                $conditions[] = FilterCondition::fromArray($conditionData);
            }
        }

        $group = new self($conditions, $conjunction);

        return $group;
    }

    /**
     * Check if the group has any conditions
     */
    public function hasConditions(): bool
    {
        return !empty($this->conditions);
    }

    /**
     * Count the number of conditions in this group
     */
    public function count(): int
    {
        return count($this->conditions);
    }
}
