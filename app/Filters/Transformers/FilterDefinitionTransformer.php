<?php

namespace App\Filters\Transformers;

use App\Filters\FilterCondition;
use App\Filters\FilterDefinition;
use App\Filters\FilterGroup;
use App\Filters\FilterRegistry;

/**
 * Transforms between filter definitions and form data
 */
class FilterDefinitionTransformer
{
    /**
     * Tracks the current resource type being processed
     */
    private string $currentResourceType = 'users';

    /**
     * Transform a filter definition to form data format
     */
    public function toFormData(FilterDefinition $filterDefinition): array
    {
        // Store the resource type for context
        $this->currentResourceType = $filterDefinition->getResourceType();

        $filterGroups = [];

        foreach ($filterDefinition->getGroups() as $index => $group) {
            $formattedConditions = $this->transformConditionsToFormData($group->getConditions());

            $filterGroups[] = [
                'conditions' => $formattedConditions,
                'groupConjunction' => $index < count($filterDefinition->getGroupConjunctions())
                    ? $filterDefinition->getGroupConjunctions()[$index]
                    : 'or',
            ];
        }

        return [
            'filterGroups' => $filterGroups,
        ];
    }

    /**
     * Transform form data to a filter definition
     */
    public function fromFormData(array $formData, string $resourceType): FilterDefinition
    {
        // Store the resource type for context
        $this->currentResourceType = $resourceType;

        if (empty($formData['filterGroups'])) {
            return FilterDefinition::createEmpty($resourceType);
        }

        $groups = [];
        $groupConjunctions = [];

        foreach ($formData['filterGroups'] as $groupIndex => $groupData) {
            if (empty($groupData['conditions'])) {
                continue;
            }

            $conditions = $this->transformFormDataToConditions($groupData['conditions']);

            // Skip empty groups
            if (empty($conditions)) {
                continue;
            }

            $groups[] = new FilterGroup($conditions, 'and');

            // Store the conjunction between this group and the next
            if ($groupIndex < count($formData['filterGroups']) - 1) {
                $conjunction = $groupData['groupConjunction'] ?? 'or';
                $groupConjunctions[] = $conjunction;
            }
        }

        // If no valid groups, return empty filter definition
        if (empty($groups)) {
            return FilterDefinition::createEmpty($resourceType);
        }

        $filterDefinition = new FilterDefinition($resourceType, $groups);

        // Set group conjunctions
        foreach ($groupConjunctions as $index => $conjunction) {
            $filterDefinition->setGroupConjunction($index, $conjunction);
        }

        return $filterDefinition;
    }

    /**
     * Transform filter conditions to form data format
     *
     * @param FilterCondition[] $conditions
     */
    private function transformConditionsToFormData(array $conditions): array
    {
        $formattedConditions = [];

        foreach ($conditions as $index => $condition) {
            $formattedCondition = [
                'field' => $condition->getField(),
                'operator' => $condition->getOperator(),
            ];

            // Add conjunction if applicable
            if ($condition->getConjunction() !== null) {
                $formattedCondition['conjunction'] = $condition->getConjunction();
            }

            // Format value based on the operator
            switch ($condition->getOperator()) {
                case 'null':
                case 'not_null':
                    $formattedCondition['text_value'] = '__NO_VALUE_NEEDED__';
                    break;

                case 'between':
                    $value = $condition->getValue();
                    if (is_array($value)) {
                        // Handle numeric between value
                        if (isset($value['min']) && isset($value['max'])) {
                            // Format as between_number with min/max structure
                            $formattedCondition['between_number'] = [
                                'min' => $value['min'],
                                'max' => $value['max']
                            ];
                        } else if (count($value) >= 2) {
                            // Handle array format [min, max]
                            $formattedCondition['between_number'] = [
                                'min' => $value[0],
                                'max' => $value[1]
                            ];
                        }
                    }
                    break;

                case 'in':
                case 'not_in':
                    // Add a field-specific array value
                    $filterType = $this->determineFilterType($condition->getField());

                    // For select type fields or specifically county.state.code,
                    // use value_tags_select for compatibility with the UI component
                    if ($filterType === 'select' || $condition->getField() === 'county.state.code') {
                        $formattedCondition['value_tags_select'] = $condition->getValue();
                    } else {
                        // For other field types, use the dynamic field name based on type
                        $formattedCondition['value_' . $filterType . 's'] = $condition->getValue();
                    }

                    break;

                default:
                    // Handle regular values
                    $filterType = $this->determineFilterType($condition->getField());

                    // Hard-code known numeric fields to use numeric_value
                    if (in_array($condition->getField(), ['graduation_year', 'gpa', 'height_in_inches', 'nominations.ai_score'])) {
                        $formattedCondition['numeric_value'] = $condition->getValue();
                    } else {
                        // BUGFIX: Convert 'number' type to 'numeric' for form field naming convention
                        $formFieldType = $filterType === 'number' ? 'numeric' : $filterType;
                        $formattedCondition[$formFieldType . '_value'] = $condition->getValue();
                    }
                    break;
            }

            $formattedConditions[] = $formattedCondition;
        }

        return $formattedConditions;
    }

    /**
     * Transform form data conditions to filter conditions
     */
    private function transformFormDataToConditions(array $formConditions): array
    {
        $conditions = [];

        foreach ($formConditions as $index => $formCondition) {
            // Skip incomplete conditions
            if (empty($formCondition['field'])) {
                continue;
            }

            $conditionData = $this->prepareConditionData($formCondition, $index);
            if ($conditionData) {
                $conditions[] = new FilterCondition(
                    $conditionData['field'],
                    $conditionData['operator'],
                    $conditionData['value'],
                    $conditionData['conjunction'] ?? null
                );
            }
        }

        return $conditions;
    }

    /**
     * Prepare condition data from form data
     */
    private function prepareConditionData(array $condition, int $index): ?array
    {
        $conditionData = [
            'field' => $condition['field'],
            'operator' => $condition['operator'],
        ];

        // Add conjunction if not the first condition
        if ($index > 0) {
            $conditionData['conjunction'] = $condition['conjunction'] ?? 'and';
        }

        // Handle different value formats based on the operator
        if (in_array($condition['operator'], ['null', 'not_null'])) {
            // No value needed for null/not_null operators
            $conditionData['value'] = null;
        }
        // Between operator handling
        elseif ($condition['operator'] === 'between') {
            // Handle range values from fieldset components
            $filterType = $this->determineFilterType($condition['field']);
            $fieldsetValues = null;

            switch ($filterType) {
                case 'number':
                    $fieldsetValues = $condition['between_number'] ?? null;
                    break;
                case 'date':
                    $fieldsetValues = $condition['between_date'] ?? null;
                    break;
                case 'text':
                default:
                    $fieldsetValues = $condition['between_text'] ?? null;
                    break;
            }

            if (is_array($fieldsetValues) && isset($fieldsetValues['min'], $fieldsetValues['max'])) {
                $conditionData['value'] = [
                    'min' => $fieldsetValues['min'],
                    'max' => $fieldsetValues['max'],
                ];
            } else {
                // Skip if fieldset values not found
                return null;
            }
        }
        // In/not_in operator handling
        elseif (in_array($condition['operator'], ['in', 'not_in'])) {
            // Handle array values from TagsInput components
            $filterType = $this->determineFilterType($condition['field']);

            // For the 'in' operator, prioritize checking both value_tags and value_tags_select
            if (!empty($condition['value_tags_select'])) {
                $conditionData['value'] = $condition['value_tags_select'];
            } elseif (!empty($condition['value_tags'])) {
                $conditionData['value'] = $condition['value_tags'];
            } else {
                $conditionData['value'] = [];
            }

            // Skip if empty array
            if (empty($conditionData['value'])) {
                return null;
            }
        }
        // Standard operator handling
        else {
            // Standard single value - extract from the appropriate typed field
            $filterType = $this->determineFilterType($condition['field']);

            switch ($filterType) {
                case 'text':
                    $conditionData['value'] = $condition['text_value'] ?? '';
                    break;
                case 'select':
                    $conditionData['value'] = $condition['select_value'] ?? '';
                    break;
                case 'number':
                    // BUGFIX: Check both number_value and numeric_value for backward compatibility
                    if (isset($condition['numeric_value'])) {
                        $conditionData['value'] = $condition['numeric_value'];
                    } else if (isset($condition['number_value'])) {
                        $conditionData['value'] = $condition['number_value'];
                    } else {
                        $conditionData['value'] = '';
                    }
                    break;
                case 'date':
                    $conditionData['value'] = $condition['date_value'] ?? null;
                    break;
                case 'boolean':
                    $conditionData['value'] = $condition['boolean_value'] ?? false;
                    break;
                default:
                    $conditionData['value'] = $condition['text_value'] ?? '';
                    break;
            }

            // Skip if value is empty (except for numeric 0 which is valid)
            if (($conditionData['value'] === '' || $conditionData['value'] === null) &&
                !($filterType === 'number' && (string)$conditionData['value'] === '0')) {
                return null;
            }
        }

        return $conditionData;
    }

    /**
     * Determine the filter type for a field
     */
    protected function determineFilterType(string $field): string
    {
        // First try to get from the filter registry for the current resource type
        $resourceType = $this->getResourceTypeFromContext();

        $registry = FilterRegistry::getForResource($resourceType);

        // If we didn't find any filters for this resource type, try the "users" type as fallback
        if (empty($registry) && $resourceType !== 'users') {
            $registry = FilterRegistry::getForResource('users');
        }

        foreach ($registry as $filter) {
            if ($filter->field === $field) {
                return $filter->type;
            }
        }

        // If the field is graduation_year or gpa, we know they should be numeric
        if (in_array($field, ['graduation_year', 'gpa', 'height_in_inches', 'nominations.ai_score'])) {
            return 'number';
        }

        // Default to text
        return 'text';
    }

    /**
     * Get the resource type from the current context
     */
    private function getResourceTypeFromContext(): string
    {
        return $this->currentResourceType;
    }
}
