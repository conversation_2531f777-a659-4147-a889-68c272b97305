<?php

namespace App\Filters;

class Filter
{
    /**
     * Callback for dynamically loading options
     */
    protected $optionsCallback = null;

    public function __construct(
        public readonly string $field,
        public readonly string $label,
        public readonly string $type, // text, select, date, number, boolean, etc.
        public readonly array $operators = [], // Available operators
        public readonly array $options = [], // For select fields (static options)
        public readonly ?string $relationship = null, // For relationship fields
        public readonly ?string $relationshipField = null, // Field on related model
        public readonly ?string $sqlType = null // Database column type (optional)
    ) {}

    public static function make(string $field, string $label, string $type): self
    {
        return new self($field, $label, $type);
    }

    public function withOperators(array $operators): self
    {
        return new self(
            $this->field,
            $this->label,
            $this->type,
            $operators,
            $this->options,
            $this->relationship,
            $this->relationshipField,
            $this->sqlType
        );
    }

    public function withOptions(array $options): self
    {
        return new self(
            $this->field,
            $this->label,
            $this->type,
            $this->operators,
            $options,
            $this->relationship,
            $this->relationshipField,
            $this->sqlType
        );
    }

    /**
     * Set a callback for dynamically fetching options
     * Useful for relationship fields where options need to be fetched from the database
     */
    public function withDynamicOptions(callable $optionsCallback): self
    {
        $filter = new self(
            $this->field,
            $this->label,
            $this->type,
            $this->operators,
            [], // Empty static options when using dynamic
            $this->relationship,
            $this->relationshipField,
            $this->sqlType
        );

        $filter->optionsCallback = $optionsCallback;

        return $filter;
    }

    public function withRelationship(string $relationship, string $field): self
    {
        return new self(
            $this->field,
            $this->label,
            $this->type,
            $this->operators,
            $this->options,
            $relationship,
            $field,
            $this->sqlType
        );
    }

    /**
     * Specify the SQL column type for more precise filtering behavior
     */
    public function withSqlType(string $sqlType): self
    {
        return new self(
            $this->field,
            $this->label,
            $this->type,
            $this->operators,
            $this->options,
            $this->relationship,
            $this->relationshipField,
            $sqlType
        );
    }

    /**
     * Get all available options for this filter
     * If a dynamic options callback is set, it will be called
     *
     * @return array
     */
    public function getOptions(): array
    {
        if (isset($this->optionsCallback) && is_callable($this->optionsCallback)) {
            return call_user_func($this->optionsCallback);
        }

        return $this->options;
    }

    /**
     * Check if this filter has dynamic options
     *
     * @return bool
     */
    public function hasDynamicOptions(): bool
    {
        return isset($this->optionsCallback) && is_callable($this->optionsCallback);
    }

    /**
     * Get appropriate default operators based on field type
     *
     * @param string $type
     * @return array
     */
    public static function getDefaultOperatorsForType(string $type): array
    {
        return match ($type) {
            'text' => ['=', '!=', 'contains', 'starts_with', 'ends_with'],
            'select' => ['=', '!=', 'in', 'not_in'],
            'number' => ['=', '!=', '>', '<', '>=', '<='],
            'boolean' => ['='],
            'date' => ['=', '!=', '>', '<', '>=', '<=', 'between'],
            default => ['=', '!='],
        };
    }
}
