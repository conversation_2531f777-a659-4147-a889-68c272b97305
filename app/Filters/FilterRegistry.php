<?php

namespace App\Filters;

use Illuminate\Support\Str;

/**
 * Central registry for filter definitions
 */
class FilterRegistry
{
    /**
     * Global registry of filters indexed by resource type
     *
     * @var array<string, array<Filter>>
     */
    protected static array $filters = [];

    /**
     * Register a set of filters for a resource type
     *
     * @param string $resourceType Resource type (e.g. 'users', 'athletes')
     * @param array<Filter> $filters Array of filter definitions
     * @return void
     */
    public static function register(string $resourceType, array $filters): void
    {
        static::$filters[$resourceType] = $filters;
    }

    /**
     * Extend existing filters for a resource type
     */
    public static function extend(string $resourceType, array $filterDefinitions): void
    {
        if (!isset(static::$filters[$resourceType])) {
            static::$filters[$resourceType] = [];
        }

        static::$filters[$resourceType] = array_merge(
            static::$filters[$resourceType],
            $filterDefinitions
        );
    }

    /**
     * Get all filters for a specific resource type
     *
     * @param string $resourceType Resource type (e.g. 'users', 'athletes')
     * @return array<Filter>
     */
    public static function getForResource(string $resourceType): array
    {
        $filters = static::$filters[$resourceType] ?? [];

        return $filters;
    }

    /**
     * Get a specific filter by field name for a resource type
     *
     * @param string $resourceType Resource type
     * @param string $field Field name
     * @return Filter|null
     */
    public static function getFilter(string $resourceType, string $field): ?Filter
    {
        $filters = static::getForResource($resourceType);

        foreach ($filters as $filter) {
            if ($filter->field === $field) {
                return $filter;
            }
        }

        return null;
    }

    /**
     * Get the filter type for a specific field
     *
     * @param string $resourceType Resource type
     * @param string $field Field name
     * @return string|null The filter type or null if not found
     */
    public static function getFilterType(string $resourceType, string $field): ?string
    {
        $filter = static::getFilter($resourceType, $field);
        $type = $filter?->type;

        return $type;
    }

    /**
     * Check if a field is filterable for a resource type
     *
     * @param string $resourceType Resource type
     * @param string $field Field name
     * @return bool
     */
    public static function isFilterable(string $resourceType, string $field): bool
    {
        return static::getFilter($resourceType, $field) !== null;
    }

    /**
     * Map a model class to the appropriate resource type
     * This allows the same model to have different resource types based on context
     *
     * @param string $modelClass Fully qualified model class name
     * @param string|null $profileType Optional profile type for context (e.g. 'positive_athlete')
     * @return string Resource type
     */
    public static function mapModelToResourceType(string $modelClass, ?string $profileType = null): string
    {
        // Model-specific mappings based on profile type or other context
        if ($modelClass === 'App\\Models\\User') {
            return match($profileType) {
                'positive_athlete' => 'athletes',
                'positive_coach' => 'coaches',
                'sponsor' => 'sponsors',
                default => 'users',
            };
        }

        // Default to the standard pluralized, snake-cased model name
        return static::getResourceTypeFromModelClass($modelClass);
    }

    /**
     * Get resource type from model class
     *
     * @param string $modelClass Fully qualified model class name
     * @return string Resource type (pluralized, snake-cased model name)
     */
    public static function getResourceTypeFromModelClass(string $modelClass): string
    {
        $className = class_basename($modelClass);
        return Str::plural(Str::snake($className));
    }

    /**
     * Reset all filters (mainly for testing)
     */
    public static function reset(): void
    {
        static::$filters = [];
    }

    /**
     * Get all resource types that have registered filters
     * For debugging purposes only
     *
     * @return array
     */
    public static function getRegisteredResourceTypes(): array
    {
        return array_keys(static::$filters);
    }
}
