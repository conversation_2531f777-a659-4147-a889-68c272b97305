<?php

namespace App\Filters;

use Illuminate\Support\Facades\Log;

/**
 * Represents a complete filter definition, containing one or more filter groups
 */
class FilterDefinition
{
    /**
     * Filter groups
     *
     * @var FilterGroup[]
     */
    private array $groups = [];

    /**
     * Conjunctions between groups (indexed by group position)
     */
    private array $groupConjunctions = [];

    /**
     * Resource type this filter applies to
     */
    private string $resourceType;

    /**
     * Create a new filter definition
     */
    public function __construct(string $resourceType, array $groups = [])
    {
        $this->resourceType = $resourceType;

        foreach ($groups as $group) {
            $this->addGroup($group);
        }

        // Default group conjunction is 'or'
        if (count($groups) > 1 && empty($this->groupConjunctions)) {
            $this->groupConjunctions = array_fill(0, count($groups) - 1, 'or');
        }
    }

    /**
     * Add a filter group
     */
    public function addGroup(FilterGroup $group): self
    {
        $this->groups[] = $group;

        // If this isn't the first group, add a default conjunction
        if (count($this->groups) > 1 && !isset($this->groupConjunctions[count($this->groups) - 2])) {
            $this->groupConjunctions[count($this->groups) - 2] = 'or';
        }

        return $this;
    }

    /**
     * Get all filter groups
     *
     * @return FilterGroup[]
     */
    public function getGroups(): array
    {
        return $this->groups;
    }

    /**
     * Set the conjunction between groups
     */
    public function setGroupConjunction(int $index, string $conjunction): self
    {
        $this->groupConjunctions[$index] = $conjunction;
        return $this;
    }

    /**
     * Get the conjunction between groups
     */
    public function getGroupConjunctions(): array
    {
        return $this->groupConjunctions;
    }

    /**
     * Get resource type
     */
    public function getResourceType(): string
    {
        return $this->resourceType;
    }

    /**
     * Set resource type
     */
    public function setResourceType(string $resourceType): self
    {
        $this->resourceType = $resourceType;
        return $this;
    }

    /**
     * Count the number of groups
     */
    public function countGroups(): int
    {
        return count($this->groups);
    }

    /**
     * Check if this filter definition has any groups with conditions
     */
    public function hasConditions(): bool
    {
        foreach ($this->groups as $group) {
            if ($group->hasConditions()) {
                return true;
            }
        }

        return false;
    }

    /**
     * Convert to array representation for storage
     */
    public function toArray(): array
    {
        // Always use the consistent numerically indexed structure
        $result = [];

        // Using numeric array for groups, not associative
        foreach ($this->groups as $index => $group) {
            $groupArray = $group->toArray();

            // Use string keys as that's what the database stores
            $result[(string)$index] = $groupArray;
        }

        if (!empty($this->groupConjunctions)) {
            $result['group_conjunctions'] = array_values($this->groupConjunctions);
        }

        return $result;
    }

    /**
     * Create from an array representation
     */
    public static function fromArray(string $resourceType, array $data): self
    {
        // Check if this is a simple structure (single group) or complex (multiple groups)
        if (isset($data['conditions']) && is_array($data['conditions'])) {
            // Simple structure with just one group
            $group = FilterGroup::fromArray($data);
            return new self($resourceType, [$group]);
        }

        // Complex structure with multiple groups
        $groups = [];
        $groupConjunctions = [];

        // Extract group conjunctions if present
        if (isset($data['group_conjunctions']) && is_array($data['group_conjunctions'])) {
            $groupConjunctions = $data['group_conjunctions'];
            unset($data['group_conjunctions']);
        }

        // Process each group - handle both numeric keys and sequential arrays
        foreach ($data as $key => $groupData) {
            // Skip non-array entries and the group_conjunctions key we already processed
            if (!is_array($groupData) || $key === 'group_conjunctions') {
                continue;
            }

            // Check if this array element has conditions (meaning it's a group)
            if (isset($groupData['conditions'])) {
                // Use numeric index if available to maintain order
                if (is_numeric($key)) {
                    $groups[(int)$key] = FilterGroup::fromArray($groupData);
                } else {
                    $groups[] = FilterGroup::fromArray($groupData);
                }
            }
        }

        // Ensure the groups array is properly ordered by key
        ksort($groups);
        $groups = array_values($groups);

        // Create the filter definition
        $filterDefinition = new self($resourceType, $groups);

        // Set group conjunctions
        foreach ($groupConjunctions as $index => $conjunction) {
            if ($index < count($groups) - 1) {
                $filterDefinition->setGroupConjunction($index, $conjunction);
            }
        }

        return $filterDefinition;
    }

    /**
     * Create an empty filter definition
     */
    public static function createEmpty(string $resourceType): self
    {
        $emptyGroup = new FilterGroup();
        return new self($resourceType, [$emptyGroup]);
    }
}
