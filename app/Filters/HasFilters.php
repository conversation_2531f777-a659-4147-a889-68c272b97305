<?php

namespace App\Filters;

trait HasFilters
{
    /**
     * Boot the trait.
     * This will register the model's filters with the FilterRegistry when the model boots.
     *
     * @return void
     */
    public static function bootHasFilters(): void
    {
        // Register filters when model is booted
        static::registerFilters();
    }

    /**
     * Register this model's filters with the FilterRegistry
     *
     * @return void
     */
    protected static function registerFilters(): void
    {
        // Skip registration if getFilters is not implemented
        if (!method_exists(static::class, 'getFilters')) {
            return;
        }

        $filters = static::getFilters();
        $resourceType = static::getResourceType();

        // Register filters using the resource type determined from model class
        FilterRegistry::register(
            $resourceType,
            $filters
        );
    }

    /**
     * Get the resource type for this model
     *
     * If model has profile_type property or method, use it for contextual mapping
     *
     * @return string
     */
    public static function getResourceType(): string
    {
        // For models that need contextual mapping (like User with profile_type)
        if (property_exists(static::class, 'profile_type') || method_exists(static::class, 'getProfileType')) {
            // Create a temporary instance to check profile type
            // Note: This should only happen during model boot
            $instance = new static;
            $profileType = $instance->profile_type ?? ($instance->getProfileType() ?? null);

            if (is_object($profileType) && method_exists($profileType, 'value')) {
                // Handle enums by accessing their value
                $profileType = $profileType->value;
            }

            return FilterRegistry::mapModelToResourceType(static::class, $profileType);
        }

        // For standard models, use the default mapping
        return FilterRegistry::getResourceTypeFromModelClass(static::class);
    }
}
