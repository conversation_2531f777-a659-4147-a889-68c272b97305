<?php

namespace App\Exports;

use Illuminate\Support\Str;

/**
 * Central registry for exportable columns definitions
 */
class ExportableColumnsRegistry
{
    /**
     * Global registry of exportable columns indexed by resource type
     *
     * @var array<string, array<string, string>>
     */
    protected static array $columns = [];

    /**
     * Default columns selection by resource type
     *
     * @var array<string, array<string>>
     */
    protected static array $defaultColumns = [];

    /**
     * Date columns by resource type
     *
     * @var array<string, array<string>>
     */
    protected static array $dateColumns = [];

    /**
     * Column types by resource type
     *
     * @var array<string, array<string, string>>
     */
    protected static array $columnTypes = [];

    /**
     * Register exportable columns for a resource type
     *
     * @param string $resourceType Resource type (e.g. 'users', 'athletes')
     * @param array<string, string> $columns Array of column keys => labels
     * @param array<string> $defaultColumns Default selected columns
     * @param array<string> $dateColumns Columns containing date values
     * @param array<string, string> $columnTypes Array of column keys => types (date, text, number, etc.)
     * @return void
     */
    public static function register(
        string $resourceType,
        array $columns,
        array $defaultColumns = [],
        array $dateColumns = [],
        array $columnTypes = []
    ): void {
        static::$columns[$resourceType] = $columns;
        static::$defaultColumns[$resourceType] = $defaultColumns;
        static::$dateColumns[$resourceType] = $dateColumns;
        static::$columnTypes[$resourceType] = $columnTypes;
    }

    /**
     * Get all exportable columns for a specific resource type
     *
     * @param string $resourceType Resource type (e.g. 'users', 'athletes')
     * @return array<string, string>
     */
    public static function getForResource(string $resourceType): array
    {
        $columns = static::$columns[$resourceType] ?? [];
        return $columns;
    }

    /**
     * Get default selected columns for a resource type
     *
     * @param string $resourceType Resource type
     * @return array<string>
     */
    public static function getDefaultColumns(string $resourceType): array
    {
        return static::$defaultColumns[$resourceType] ?? [];
    }

    /**
     * Get date columns for a resource type
     *
     * @param string $resourceType Resource type
     * @return array<string>
     */
    public static function getDateColumns(string $resourceType): array
    {
        return static::$dateColumns[$resourceType] ?? [];
    }

    /**
     * Get column types for a resource
     *
     * @param string $resourceType Resource type
     * @return array<string, string>
     */
    public static function getColumnTypes(string $resourceType): array
    {
        return static::$columnTypes[$resourceType] ?? [];
    }

    /**
     * Check if a column is of a specific type
     *
     * @param string $resourceType Resource type
     * @param string $column Column key
     * @param string $type Type to check for (e.g. 'date')
     * @return bool
     */
    public static function isColumnOfType(string $resourceType, string $column, string $type): bool
    {
        $columnTypes = static::getColumnTypes($resourceType);
        return ($columnTypes[$column] ?? '') === $type;
    }

    /**
     * Check if a column is a date column
     *
     * @param string $resourceType Resource type
     * @param string $column Column key
     * @return bool
     */
    public static function isDateColumn(string $resourceType, string $column): bool
    {
        // Check explicitly registered date columns
        if (in_array($column, static::getDateColumns($resourceType))) {
            return true;
        }

        // Check column types
        return static::isColumnOfType($resourceType, $column, 'date');
    }

    /**
     * Map a model class to the appropriate resource type
     * This allows the same model to have different resource types based on context
     *
     * @param string $modelClass Fully qualified model class name
     * @param string|null $profileType Optional profile type for context (e.g. 'positive_athlete')
     * @return string Resource type
     */
    public static function mapModelToResourceType(string $modelClass, ?string $profileType = null): string
    {
        // Model-specific mappings based on profile type or other context
        if ($modelClass === 'App\\Models\\User') {
            return match($profileType) {
                'positive_athlete' => 'athletes',
                'positive_coach' => 'coaches',
                'sponsor' => 'sponsors',
                default => 'users',
            };
        }

        // Default to the standard pluralized, snake-cased model name
        return static::getResourceTypeFromModelClass($modelClass);
    }

    /**
     * Get resource type from model class
     *
     * @param string $modelClass Fully qualified model class name
     * @return string Resource type (pluralized, snake-cased model name)
     */
    public static function getResourceTypeFromModelClass(string $modelClass): string
    {
        $className = class_basename($modelClass);
        return Str::plural(Str::snake($className));
    }

    /**
     * Reset all registrations (mainly for testing)
     */
    public static function reset(): void
    {
        static::$columns = [];
        static::$defaultColumns = [];
        static::$dateColumns = [];
        static::$columnTypes = [];
    }

    /**
     * Get all resource types that have registered exportable columns
     *
     * @return array
     */
    public static function getRegisteredResourceTypes(): array
    {
        return array_keys(static::$columns);
    }
}
