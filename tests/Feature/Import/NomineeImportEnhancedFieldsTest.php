<?php

namespace Tests\Feature\Import;

use Tests\TestCase;
use App\Services\Import\NomineeImportService;
use App\Data\Nomination\NominationData;
use Illuminate\Foundation\Testing\RefreshDatabase;

class NomineeImportEnhancedFieldsTest extends TestCase
{
    use RefreshDatabase;

    protected NomineeImportService $importService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->importService = app(NomineeImportService::class);
    }

    /** @test */
    public function it_converts_graduation_year_to_grade_correctly()
    {
        $reflection = new \ReflectionClass($this->importService);
        $method = $reflection->getMethod('convertGraduationYearToGrade');
        $method->setAccessible(true);

        // Test current year 2025 scenarios
        $this->assertEquals('12', $method->invoke($this->importService, '2026')); // Senior
        $this->assertEquals('11', $method->invoke($this->importService, '2027')); // Junior
        $this->assertEquals('10', $method->invoke($this->importService, '2028')); // Sophomore
        $this->assertEquals('9', $method->invoke($this->importService, '2029'));  // Freshman
        
        // Test edge cases
        $this->assertNull($method->invoke($this->importService, '2030')); // Too far in future
        $this->assertNull($method->invoke($this->importService, '2024')); // Already graduated
        $this->assertNull($method->invoke($this->importService, null));   // Null input
        $this->assertNull($method->invoke($this->importService, ''));     // Empty string
    }

    /** @test */
    public function it_extracts_parent_guardian_info_when_relationship_is_parent()
    {
        $reflection = new \ReflectionClass($this->importService);
        
        $firstNameMethod = $reflection->getMethod('extractParentGuardianFirstName');
        $firstNameMethod->setAccessible(true);
        
        $lastNameMethod = $reflection->getMethod('extractParentGuardianLastName');
        $lastNameMethod->setAccessible(true);
        
        $emailMethod = $reflection->getMethod('extractParentGuardianEmail');
        $emailMethod->setAccessible(true);
        
        $phoneMethod = $reflection->getMethod('extractParentGuardianPhone');
        $phoneMethod->setAccessible(true);

        // Test parent relationship
        $parentRow = [
            'Relationship to Nominee' => 'Parent',
            'Nominator Full Name' => 'John Smith',
            'Nominator Email' => '<EMAIL>',
            'Nominator Phone Number' => '(*************'
        ];

        $this->assertEquals('John', $firstNameMethod->invoke($this->importService, $parentRow));
        $this->assertEquals('Smith', $lastNameMethod->invoke($this->importService, $parentRow));
        $this->assertEquals('<EMAIL>', $emailMethod->invoke($this->importService, $parentRow));
        $this->assertEquals('(*************', $phoneMethod->invoke($this->importService, $parentRow));

        // Test non-parent relationship
        $coachRow = [
            'Relationship to Nominee' => 'Head Coach',
            'Nominator Full Name' => 'Jane Doe',
            'Nominator Email' => '<EMAIL>',
            'Nominator Phone Number' => '(*************'
        ];

        $this->assertNull($firstNameMethod->invoke($this->importService, $coachRow));
        $this->assertNull($lastNameMethod->invoke($this->importService, $coachRow));
        $this->assertNull($emailMethod->invoke($this->importService, $coachRow));
        $this->assertNull($phoneMethod->invoke($this->importService, $coachRow));
    }

    /** @test */
    public function it_extracts_social_media_handles_from_nomination_text()
    {
        $reflection = new \ReflectionClass($this->importService);
        
        $instagramMethod = $reflection->getMethod('extractInstagramHandle');
        $instagramMethod->setAccessible(true);
        
        $twitterMethod = $reflection->getMethod('extractTwitterHandle');
        $twitterMethod->setAccessible(true);

        // Test Instagram extraction
        $textWithInstagram = "Follow him on Instagram @john_athlete123 for updates!";
        $this->assertEquals('john_athlete123', $instagramMethod->invoke($this->importService, $textWithInstagram));

        $textWithInstagramUrl = "Check out instagram.com/jane_runner for her achievements.";
        $this->assertEquals('jane_runner', $instagramMethod->invoke($this->importService, $textWithInstagramUrl));

        // Test Twitter extraction
        $textWithTwitter = "Follow updates at twitter.com/athlete_mike";
        $this->assertEquals('athlete_mike', $twitterMethod->invoke($this->importService, $textWithTwitter));

        $textWithX = "Check out x.com/sarah_sports for more info";
        $this->assertEquals('sarah_sports', $twitterMethod->invoke($this->importService, $textWithX));

        // Test no matches
        $textWithoutSocial = "This is a great athlete with no social media mentioned.";
        $this->assertNull($instagramMethod->invoke($this->importService, $textWithoutSocial));
        $this->assertNull($twitterMethod->invoke($this->importService, $textWithoutSocial));
    }

    /** @test */
    public function it_extracts_referral_source_correctly()
    {
        $reflection = new \ReflectionClass($this->importService);
        $method = $reflection->getMethod('extractReferralSource');
        $method->setAccessible(true);

        $row = [
            'Relationship to Nominee' => 'Head Coach',
            'High School Name' => 'Lincoln High School'
        ];

        $result = $method->invoke($this->importService, $row);
        $this->assertEquals('Head Coach at Lincoln High School', $result);

        // Test with missing school name
        $rowNoSchool = [
            'Relationship to Nominee' => 'Parent',
            'High School Name' => ''
        ];

        $result = $method->invoke($this->importService, $rowNoSchool);
        $this->assertEquals('Parent', $result);
    }
}
