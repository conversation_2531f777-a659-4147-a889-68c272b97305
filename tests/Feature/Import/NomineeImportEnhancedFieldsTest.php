<?php

use App\Services\Import\NomineeImportService;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->importService = app(NomineeImportService::class);
});

test('it converts graduation year to grade correctly', function () {
    $reflection = new \ReflectionClass($this->importService);
    $method = $reflection->getMethod('convertGraduationYearToGrade');
    $method->setAccessible(true);

    // Test current year 2025 scenarios
    expect($method->invoke($this->importService, '2026'))->toBe('12'); // Senior
    expect($method->invoke($this->importService, '2027'))->toBe('11'); // Junior
    expect($method->invoke($this->importService, '2028'))->toBe('10'); // Sophomore
    expect($method->invoke($this->importService, '2029'))->toBe('9');  // Freshman

    // Test edge cases
    expect($method->invoke($this->importService, '2030'))->toBeNull(); // Too far in future
    expect($method->invoke($this->importService, '2024'))->toBeNull(); // Already graduated
    expect($method->invoke($this->importService, null))->toBeNull();   // Null input
    expect($method->invoke($this->importService, ''))->toBeNull();     // Empty string
});

test('it extracts parent guardian info when relationship is parent', function () {
    $reflection = new \ReflectionClass($this->importService);

    $firstNameMethod = $reflection->getMethod('extractParentGuardianFirstName');
    $firstNameMethod->setAccessible(true);

    $lastNameMethod = $reflection->getMethod('extractParentGuardianLastName');
    $lastNameMethod->setAccessible(true);

    $emailMethod = $reflection->getMethod('extractParentGuardianEmail');
    $emailMethod->setAccessible(true);

    $phoneMethod = $reflection->getMethod('extractParentGuardianPhone');
    $phoneMethod->setAccessible(true);

    // Test parent relationship
    $parentRow = [
        'Relationship to Nominee' => 'Parent',
        'Nominator Full Name' => 'John Smith',
        'Nominator Email' => '<EMAIL>',
        'Nominator Phone Number' => '(*************'
    ];

    expect($firstNameMethod->invoke($this->importService, $parentRow))->toBe('John');
    expect($lastNameMethod->invoke($this->importService, $parentRow))->toBe('Smith');
    expect($emailMethod->invoke($this->importService, $parentRow))->toBe('<EMAIL>');
    expect($phoneMethod->invoke($this->importService, $parentRow))->toBe('(*************');

    // Test non-parent relationship
    $coachRow = [
        'Relationship to Nominee' => 'Head Coach',
        'Nominator Full Name' => 'Jane Doe',
        'Nominator Email' => '<EMAIL>',
        'Nominator Phone Number' => '(*************'
    ];

    expect($firstNameMethod->invoke($this->importService, $coachRow))->toBeNull();
    expect($lastNameMethod->invoke($this->importService, $coachRow))->toBeNull();
    expect($emailMethod->invoke($this->importService, $coachRow))->toBeNull();
    expect($phoneMethod->invoke($this->importService, $coachRow))->toBeNull();
});

test('it extracts social media handles from nomination text', function () {
    $reflection = new \ReflectionClass($this->importService);

    $instagramMethod = $reflection->getMethod('extractInstagramHandle');
    $instagramMethod->setAccessible(true);

    $twitterMethod = $reflection->getMethod('extractTwitterHandle');
    $twitterMethod->setAccessible(true);

    // Test Instagram extraction
    $textWithInstagram = "Follow him on Instagram @john_athlete123 for updates!";
    expect($instagramMethod->invoke($this->importService, $textWithInstagram))->toBe('john_athlete123');

    $textWithInstagramUrl = "Check out instagram.com/jane_runner for her achievements.";
    expect($instagramMethod->invoke($this->importService, $textWithInstagramUrl))->toBe('jane_runner');

    // Test Twitter extraction
    $textWithTwitter = "Follow updates at twitter.com/athlete_mike";
    expect($twitterMethod->invoke($this->importService, $textWithTwitter))->toBe('athlete_mike');

    $textWithX = "Check out x.com/sarah_sports for more info";
    expect($twitterMethod->invoke($this->importService, $textWithX))->toBe('sarah_sports');

    // Test no matches
    $textWithoutSocial = "This is a great athlete with no social media mentioned.";
    expect($instagramMethod->invoke($this->importService, $textWithoutSocial))->toBeNull();
    expect($twitterMethod->invoke($this->importService, $textWithoutSocial))->toBeNull();
});

test('it extracts referral source correctly', function () {
    $reflection = new \ReflectionClass($this->importService);
    $method = $reflection->getMethod('extractReferralSource');
    $method->setAccessible(true);

    $row = [
        'Relationship to Nominee' => 'Head Coach',
        'High School Name' => 'Lincoln High School'
    ];

    $result = $method->invoke($this->importService, $row);
    expect($result)->toBe('Head Coach at Lincoln High School');

    // Test with missing school name
    $rowNoSchool = [
        'Relationship to Nominee' => 'Parent',
        'High School Name' => ''
    ];

    $result = $method->invoke($this->importService, $rowNoSchool);
    expect($result)->toBe('Parent');
});
