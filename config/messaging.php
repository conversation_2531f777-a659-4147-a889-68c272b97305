<?php

return [

    /*
    |--------------------------------------------------------------------------
    | External Message Sending
    |--------------------------------------------------------------------------
    |
    | This option controls whether the application will actually send messages
    | via external services (Campaign Nucleus, Twilio) or only log them
    | locally for development purposes.
    |
    */

    'send_external_messages' => env('MESSAGING_SEND_EXTERNAL', false),

    /*
    |--------------------------------------------------------------------------
    | Email Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for email messaging including the driver and service
    | specific settings.
    |
    */

    'email' => [
        'enabled' => env('MESSAGING_EMAIL_ENABLED', true),
        'driver' => env('MESSAGING_EMAIL_DRIVER', 'smtp'),
        'from_address' => env('MESSAGING_EMAIL_FROM', env('MAIL_FROM_ADDRESS')),
        'from_name' => env('MESSAGING_EMAIL_FROM_NAME', env('MAIL_FROM_NAME')),

        // Campaign Nucleus settings
        'campaign_nucleus' => [
            'api_key' => env('CAMPAIGN_NUCLEUS_API_KEY'),
            'api_url' => env('CAMPAIGN_NUCLEUS_API_URL', 'https://api.campaignnucleus.com'),
            'list_id' => env('CAMPAIGN_NUCLEUS_LIST_ID'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | SMS Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for SMS messaging via Twilio.
    |
    */

    'sms' => [
        'enabled' => env('MESSAGING_SMS_ENABLED', true),
        'driver' => env('MESSAGING_SMS_DRIVER', 'twilio'),
        'from_number' => env('MESSAGING_SMS_FROM'),

        // Twilio settings
        'twilio' => [
            'account_sid' => env('TWILIO_ACCOUNT_SID'),
            'auth_token' => env('TWILIO_AUTH_TOKEN'),
            'from_number' => env('TWILIO_FROM_NUMBER'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Environment Settings
    |--------------------------------------------------------------------------
    |
    | Environment-specific behavior for message handling.
    |
    */

    'environment' => [
        'current' => env('APP_ENV', 'local'),
        'log_all_messages' => env('MESSAGING_LOG_ALL', true),
        'dev_recipient_override' => env('MESSAGING_DEV_OVERRIDE'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    |
    | Rate limiting configuration for message sending to comply with
    | service provider limits and prevent abuse.
    |
    */

    'rate_limits' => [
        'email_per_minute' => env('MESSAGING_EMAIL_RATE_LIMIT', 60),
        'sms_per_minute' => env('MESSAGING_SMS_RATE_LIMIT', 10),
        'per_user_per_hour' => env('MESSAGING_USER_RATE_LIMIT', 20),
    ],

    /*
    |--------------------------------------------------------------------------
    | Template Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for email template processing and variables.
    |
    */

    'templates' => [
        'variable_format' => env('MESSAGING_VARIABLE_FORMAT', '{%s}'),
        'cache_processed_templates' => env('MESSAGING_CACHE_TEMPLATES', true),
        'cache_ttl' => env('MESSAGING_CACHE_TTL', 3600), // 1 hour
    ],

    /*
    |--------------------------------------------------------------------------
    | Queue Configuration
    |--------------------------------------------------------------------------
    |
    | Queue settings for background message processing.
    |
    */

    'queue' => [
        'connection' => env('MESSAGING_QUEUE_CONNECTION', env('QUEUE_CONNECTION', 'sync')),
        'email_queue' => env('MESSAGING_EMAIL_QUEUE', 'emails'),
        'sms_queue' => env('MESSAGING_SMS_QUEUE', 'sms'),
        'retry_attempts' => env('MESSAGING_RETRY_ATTEMPTS', 3),
        'retry_delay' => env('MESSAGING_RETRY_DELAY', 60), // seconds
    ],

];
