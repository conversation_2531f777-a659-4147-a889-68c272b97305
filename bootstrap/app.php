<?php

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: [
            __DIR__.'/../routes/web.php',
        ],
        api: [
            'path' => __DIR__.'/../routes/api.php',
            __DIR__.'/../routes/auth.php',
        ],
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
        apiPrefix: 'api/v1'
    )
    ->withMiddleware(function (Middleware $middleware) {
        // Configure API middleware
        $middleware->api(prepend: [
            \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
            // \App\Http\Middleware\HttpMethodOverrideMiddleware::class,
        ]);

        // Configure web middleware
        $middleware->web(prepend: [
            // Add any web-specific middleware here
        ]);

        // Register middleware aliases
        $middleware->alias([
            /**
             * The system invite acts as email verification for users, and for admins is not required.
             */
            // 'verified' => \App\Http\Middleware\EnsureEmailIsVerified::class,
            'valid-invite' => \App\Http\Middleware\ValidSystemInvite::class,
            'ensure.sponsor' => \App\Http\Middleware\EnsureSponsorUser::class,
            'resolve.parent-child' => \App\Http\Middleware\ResolveParentChildMiddleware::class,
            'optional-sanctum-auth' => \App\Http\Middleware\OptionalSanctumAuth::class,
            'webhook.token' => \App\Http\Middleware\VerifyWebhookToken::class,
        ]);

        // Group specific middleware
        $middleware->group('admin', [
            // Filament specific middleware
            'web',
            'auth',
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })
    ->withSchedule(function (Schedule $schedule) {
        // Schedule commands here
        $schedule->command('locations:geocode --limit=10')->everyMinute();

        // Run daily aggregation at 1:00 AM for the previous day's data
        $schedule->command('engagements:aggregate')
            ->dailyAt('01:00')
            ->withoutOverlapping()
            ->runInBackground()
            ->appendOutputTo(storage_path('logs/engagement-aggregation.log'));

        // Note: The cleanup command is intentionally not scheduled to preserve all detailed data
        // for future migration to a more robust analytics system
        // $schedule->command('engagements:cleanup --days=90')->monthly();
    })->create();
